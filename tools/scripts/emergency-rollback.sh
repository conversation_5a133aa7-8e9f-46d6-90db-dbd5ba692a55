#!/bin/bash

# Emergency Rollback Script
# Quickly reverts system to previous stable state

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_URL=${API_URL:-"http://localhost:3002"}
BACKUP_DIR=${BACKUP_DIR:-"./backups"}
LOG_FILE="emergency-rollback-$(date +%Y%m%d-%H%M%S).log"

# Logging functions
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ ERROR: $1${NC}" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}" | tee -a "$LOG_FILE"
}

# Check if system is healthy before rollback
check_system_health() {
    log "Checking current system health..."
    
    # Check API
    if curl -f -s "$API_URL/api/health" > /dev/null; then
        success "API is responding"
        return 0
    else
        error "API is not responding"
        return 1
    fi
}

# Disable all new features immediately
disable_all_features() {
    log "Disabling all new features..."
    
    # Create emergency feature flag override
    cat > .env.emergency <<EOF
# Emergency Rollback - All new features disabled
FEATURE_FLAG_NEW_CONTROLLER_LAYER=false
FEATURE_FLAG_NEW_SERVICE_LAYER=false
FEATURE_FLAG_NEW_REPOSITORY_PATTERN=false
FEATURE_FLAG_NEW_MIDDLEWARE_STACK=false
FEATURE_FLAG_NEW_CHAT_SERVICE=false
FEATURE_FLAG_NEW_FEATURE_SLICE_ARCHITECTURE=false
FEATURE_FLAG_NEW_GRAPH_COMPONENTS=false
FEATURE_FLAG_NEW_CHAT_INTERFACE=false
FEATURE_FLAG_NEW_STATE_MANAGEMENT=false
FEATURE_FLAG_TRAFFIC_PERCENTAGE_NEW_API=0
FEATURE_FLAG_TRAFFIC_PERCENTAGE_NEW_UI=0

# Keep safety features enabled
FEATURE_FLAG_CIRCUIT_BREAKER_ENABLED=true
FEATURE_FLAG_DUAL_EXECUTION_MODE=false
FEATURE_FLAG_PERFORMANCE_MONITORING=true
FEATURE_FLAG_AUTOMATIC_ROLLBACK=true
FEATURE_FLAG_DEBUG_MODE=true
FEATURE_FLAG_VERBOSE_LOGGING=true
EOF
    
    # Backup current .env and replace with emergency config
    if [ -f ".env" ]; then
        cp .env .env.backup.$(date +%Y%m%d-%H%M%S)
        success "Current .env backed up"
    fi
    
    # Merge emergency config with existing .env
    if [ -f ".env" ]; then
        # Remove existing feature flags from .env
        grep -v "FEATURE_FLAG_" .env > .env.temp || true
        cat .env.temp .env.emergency > .env
        rm .env.temp .env.emergency
    else
        mv .env.emergency .env
    fi
    
    success "Emergency feature flags applied"
}

# Restart services with rollback configuration
restart_services() {
    log "Restarting services with rollback configuration..."
    
    # Kill existing processes
    info "Stopping current services..."
    
    # Stop Node.js processes
    pkill -f "node.*server.js" || true
    pkill -f "npm.*start" || true
    
    # Wait for processes to stop
    sleep 5
    
    # Start backend with emergency config
    info "Starting backend with emergency configuration..."
    cd 360t-kg-api
    npm start > ../rollback-backend.log 2>&1 &
    BACKEND_PID=$!
    cd ..
    
    # Wait for backend to start
    sleep 10
    
    # Verify backend is running
    if curl -f -s "$API_URL/api/health" > /dev/null; then
        success "Backend restarted successfully (PID: $BACKEND_PID)"
        echo $BACKEND_PID > rollback-backend.pid
    else
        error "Backend failed to start"
        return 1
    fi
}

# Verify rollback success
verify_rollback() {
    log "Verifying rollback success..."
    
    # Check API health
    if ! curl -f -s "$API_URL/api/health" > /dev/null; then
        error "API health check failed"
        return 1
    fi
    success "API health check passed"
    
    # Check feature flags are disabled
    FEATURE_FLAGS=$(curl -s "$API_URL/api/feature-flags" | jq -r '.migrationPhase.backend.percentage' 2>/dev/null || echo "unknown")
    
    if [ "$FEATURE_FLAGS" = "0" ]; then
        success "All migration features disabled (0% backend migration)"
    else
        warning "Migration features status: $FEATURE_FLAGS%"
    fi
    
    # Check traffic routing
    API_TRAFFIC=$(curl -s "$API_URL/api/feature-flags" | jq -r '.migrationPhase.traffic.api' 2>/dev/null || echo "unknown")
    
    if [ "$API_TRAFFIC" = "0" ]; then
        success "All traffic routed to legacy system (0% new API traffic)"
    else
        warning "New API traffic: $API_TRAFFIC%"
    fi
    
    return 0
}

# Create rollback report
create_rollback_report() {
    log "Creating rollback report..."
    
    REPORT_FILE="rollback-report-$(date +%Y%m%d-%H%M%S).json"
    
    cat > "$REPORT_FILE" <<EOF
{
    "rollback": {
        "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "reason": "$ROLLBACK_REASON",
        "triggered_by": "$USER",
        "duration_seconds": $(($(date +%s) - $ROLLBACK_START_TIME)),
        "success": true
    },
    "system_state": {
        "api_health": "$(curl -s "$API_URL/api/health" | jq -r '.status' 2>/dev/null || echo 'unknown')",
        "migration_progress": "$(curl -s "$API_URL/api/feature-flags" | jq -r '.migrationPhase.backend.percentage' 2>/dev/null || echo 'unknown')%",
        "api_traffic": "$(curl -s "$API_URL/api/feature-flags" | jq -r '.migrationPhase.traffic.api' 2>/dev/null || echo 'unknown')%"
    },
    "actions_taken": [
        "Disabled all migration feature flags",
        "Restarted services with emergency configuration",
        "Verified system health",
        "Created rollback report"
    ],
    "next_steps": [
        "Investigate root cause of rollback",
        "Review system logs for errors",
        "Plan remediation strategy",
        "Test fixes in staging environment",
        "Gradually re-enable features when ready"
    ]
}
EOF
    
    success "Rollback report created: $REPORT_FILE"
}

# Send notifications
send_notifications() {
    log "Sending rollback notifications..."
    
    # Log to monitoring system
    echo "$(date -u +%Y-%m-%dT%H:%M:%SZ) - EMERGENCY ROLLBACK EXECUTED: $ROLLBACK_REASON" >> tools/monitoring/migration-monitor.log
    
    # Create alert file for monitoring dashboard
    cat > tools/monitoring/emergency-alert.json <<EOF
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "type": "EMERGENCY_ROLLBACK",
    "severity": "critical",
    "message": "Emergency rollback executed: $ROLLBACK_REASON",
    "actions_required": [
        "Investigate root cause",
        "Review system logs",
        "Plan remediation"
    ]
}
EOF
    
    success "Notifications sent"
}

# Main rollback execution
execute_rollback() {
    ROLLBACK_START_TIME=$(date +%s)
    ROLLBACK_REASON=${1:-"Manual emergency rollback"}
    
    log "🚨 EMERGENCY ROLLBACK INITIATED"
    log "Reason: $ROLLBACK_REASON"
    log "Started by: $USER"
    log "Timestamp: $(date)"
    
    # Step 1: Check current system state
    if check_system_health; then
        info "System is currently responding"
    else
        warning "System appears to be down - proceeding with rollback"
    fi
    
    # Step 2: Disable all new features
    disable_all_features
    
    # Step 3: Restart services
    if restart_services; then
        success "Services restarted successfully"
    else
        error "Service restart failed"
        return 1
    fi
    
    # Step 4: Verify rollback
    if verify_rollback; then
        success "Rollback verification passed"
    else
        error "Rollback verification failed"
        return 1
    fi
    
    # Step 5: Create report and notifications
    create_rollback_report
    send_notifications
    
    success "🎉 EMERGENCY ROLLBACK COMPLETED SUCCESSFULLY"
    log "Duration: $(($(date +%s) - $ROLLBACK_START_TIME)) seconds"
    log "System is now running in legacy mode"
    log "Next steps: Investigate root cause and plan remediation"
    
    return 0
}

# Show usage
show_usage() {
    echo "Emergency Rollback Script"
    echo "Usage: $0 [reason]"
    echo ""
    echo "Examples:"
    echo "  $0 \"High error rate detected\""
    echo "  $0 \"Performance degradation\""
    echo "  $0 \"User complaints\""
    echo ""
    echo "This script will:"
    echo "  1. Disable all migration features"
    echo "  2. Restart services in legacy mode"
    echo "  3. Verify system health"
    echo "  4. Create rollback report"
    echo "  5. Send notifications"
}

# Main execution
case "${1:-execute}" in
    "help"|"-h"|"--help")
        show_usage
        exit 0
        ;;
    "test")
        log "Testing rollback procedures (dry run)"
        check_system_health
        exit 0
        ;;
    *)
        execute_rollback "$1"
        exit $?
        ;;
esac
