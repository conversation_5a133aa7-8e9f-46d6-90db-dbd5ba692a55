#!/bin/bash

# Migration Safety Check Script
# This script validates system health before, during, and after migration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
API_URL=${API_URL:-"http://localhost:3002"}
DATABASE_URL=${DATABASE_URL:-"neo4j://localhost:7687"}
BACKUP_DIR=${BACKUP_DIR:-"./backups"}
LOG_FILE="migration-safety-$(date +%Y%m%d-%H%M%S).log"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ ERROR: $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

# System Health Checks
check_system_health() {
    log "Starting system health check..."
    
    # Check if API is responding
    if ! curl -f -s "$API_URL/api/health" > /dev/null; then
        error "API health check failed at $API_URL/api/health"
    fi
    success "API health check passed"
    
    # Check database connection via API
    if ! curl -f -s "$API_URL/api/metadata" > /dev/null; then
        error "Database connection check failed"
    fi
    success "Database connection check passed"
    
    # Check disk space
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -gt 85 ]; then
        warning "Disk usage is at ${DISK_USAGE}% - consider cleaning up before migration"
    fi
    success "Disk space check passed (${DISK_USAGE}% used)"
    
    # Check memory usage
    MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$MEMORY_USAGE" -gt 90 ]; then
        warning "Memory usage is at ${MEMORY_USAGE}% - system may be under stress"
    fi
    success "Memory usage check passed (${MEMORY_USAGE}% used)"
}

# Performance Baseline Collection
collect_performance_baseline() {
    log "Collecting performance baseline..."
    
    # API response time test
    API_RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' "$API_URL/api/health")
    log "API response time baseline: ${API_RESPONSE_TIME}s"
    
    # Database query performance via API
    DB_QUERY_START=$(date +%s.%N)
    curl -f -s "$API_URL/api/metadata" > /dev/null
    DB_QUERY_END=$(date +%s.%N)
    DB_QUERY_TIME=$(echo "$DB_QUERY_END - $DB_QUERY_START" | bc -l)
    log "Database query time baseline: $DB_QUERY_TIME"
    
    # Save baseline to file
    cat > baseline-metrics.json <<EOF
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "api_response_time": $API_RESPONSE_TIME,
    "database_query_time": "$DB_QUERY_TIME",
    "memory_usage": $MEMORY_USAGE,
    "disk_usage": $DISK_USAGE
}
EOF
    success "Performance baseline collected and saved"
}

# Create System Backup
create_backup() {
    log "Creating system backup..."
    
    # Create backup directory
    BACKUP_TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    CURRENT_BACKUP_DIR="$BACKUP_DIR/migration-backup-$BACKUP_TIMESTAMP"
    mkdir -p "$CURRENT_BACKUP_DIR"
    
    # Backup database (simplified approach without cypher-shell)
    log "Creating database backup via API..."
    curl -s "$API_URL/api/metadata" > "$CURRENT_BACKUP_DIR/database-metadata.json" || error "Database metadata backup failed"
    log "Database metadata backed up successfully"
    
    # Backup application code
    log "Backing up application code..."
    tar -czf "$CURRENT_BACKUP_DIR/code-backup.tar.gz" \
        --exclude=node_modules \
        --exclude=.git \
        --exclude=dist \
        --exclude=build \
        . || error "Code backup failed"
    
    # Backup configuration files
    log "Backing up configuration..."
    cp -r .env* "$CURRENT_BACKUP_DIR/" 2>/dev/null || true
    cp -r docker-compose*.yml "$CURRENT_BACKUP_DIR/" 2>/dev/null || true
    
    echo "$CURRENT_BACKUP_DIR" > latest-backup.txt
    success "System backup completed: $CURRENT_BACKUP_DIR"
}

# Validate Migration Readiness
validate_migration_readiness() {
    log "Validating migration readiness..."
    
    # Check if required tools are installed
    command -v node >/dev/null 2>&1 || error "Node.js is not installed"
    command -v npm >/dev/null 2>&1 || error "npm is not installed"
    command -v docker >/dev/null 2>&1 || error "Docker is not installed"
    command -v python >/dev/null 2>&1 || error "Python is not installed"
    success "Required tools are installed"
    
    # Check if backup exists
    if [ ! -f latest-backup.txt ]; then
        error "No backup found. Please create a backup before migration."
    fi
    success "Backup verification passed"
    
    # Check if feature flags are configured
    if [ ! -f .env ] || ! grep -q "FEATURE_FLAGS" .env; then
        warning "Feature flags not configured. Consider adding feature flag support."
    fi
    
    # Validate test suite
    if [ -f package.json ] && grep -q "test" package.json; then
        log "Running test suite..."
        npm test || error "Test suite failed"
        success "Test suite passed"
    fi
}

# Monitor Migration Progress
monitor_migration() {
    log "Starting migration monitoring..."
    
    BASELINE_FILE="baseline-metrics.json"
    if [ ! -f "$BASELINE_FILE" ]; then
        error "Baseline metrics not found. Please run collect_performance_baseline first."
    fi
    
    BASELINE_API_TIME=$(jq -r '.api_response_time' "$BASELINE_FILE")
    
    while true; do
        # Check current performance
        CURRENT_API_TIME=$(curl -o /dev/null -s -w '%{time_total}' "$API_URL/api/health" 2>/dev/null || echo "0")
        
        if [ "$CURRENT_API_TIME" != "0" ]; then
            # Calculate performance ratio
            PERFORMANCE_RATIO=$(echo "$CURRENT_API_TIME / $BASELINE_API_TIME" | bc -l 2>/dev/null || echo "1")
            
            # Check if performance degraded significantly
            if (( $(echo "$PERFORMANCE_RATIO > 2.0" | bc -l) )); then
                warning "Performance degradation detected: ${PERFORMANCE_RATIO}x slower than baseline"
                warning "Consider rollback if performance doesn't improve"
            fi
            
            log "Current API response time: ${CURRENT_API_TIME}s (baseline: ${BASELINE_API_TIME}s)"
        else
            error "API is not responding during migration monitoring"
        fi
        
        sleep 30
    done
}

# Rollback System
rollback_system() {
    log "Initiating system rollback..."
    
    if [ ! -f latest-backup.txt ]; then
        error "No backup reference found. Cannot perform rollback."
    fi
    
    BACKUP_DIR_TO_RESTORE=$(cat latest-backup.txt)
    
    if [ ! -d "$BACKUP_DIR_TO_RESTORE" ]; then
        error "Backup directory not found: $BACKUP_DIR_TO_RESTORE"
    fi
    
    # Stop services
    log "Stopping services..."
    docker-compose down 2>/dev/null || true
    
    # Restore code
    log "Restoring application code..."
    tar -xzf "$BACKUP_DIR_TO_RESTORE/code-backup.tar.gz" || error "Code restore failed"
    
    # Restore database (simplified approach)
    log "Database restore would require manual intervention..."
    log "Backup metadata available at: $BACKUP_DIR_TO_RESTORE/database-metadata.json"
    
    # Restart services
    log "Restarting services..."
    docker-compose up -d 2>/dev/null || npm start &
    
    # Wait for services to be ready
    sleep 30
    
    # Validate rollback
    if check_system_health; then
        success "System rollback completed successfully"
    else
        error "System rollback failed - manual intervention required"
    fi
}

# Main execution
case "${1:-help}" in
    "health")
        check_system_health
        ;;
    "baseline")
        collect_performance_baseline
        ;;
    "backup")
        create_backup
        ;;
    "validate")
        validate_migration_readiness
        ;;
    "monitor")
        monitor_migration
        ;;
    "rollback")
        rollback_system
        ;;
    "full-check")
        check_system_health
        collect_performance_baseline
        create_backup
        validate_migration_readiness
        success "Full migration safety check completed"
        ;;
    "help"|*)
        echo "Migration Safety Check Script"
        echo "Usage: $0 {health|baseline|backup|validate|monitor|rollback|full-check}"
        echo ""
        echo "Commands:"
        echo "  health     - Check system health"
        echo "  baseline   - Collect performance baseline"
        echo "  backup     - Create system backup"
        echo "  validate   - Validate migration readiness"
        echo "  monitor    - Monitor migration progress"
        echo "  rollback   - Rollback to previous state"
        echo "  full-check - Run all safety checks"
        exit 1
        ;;
esac
