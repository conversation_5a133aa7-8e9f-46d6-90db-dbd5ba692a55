#!/usr/bin/env node

/**
 * Comprehensive Performance Baseline Collection
 * Collects detailed metrics for migration comparison
 */

const fs = require('fs');
const { performance } = require('perf_hooks');
const { execSync } = require('child_process');

const API_URL = process.env.API_URL || 'http://localhost:3002';

async function collectPerformanceBaseline() {
    console.log('🔍 Collecting comprehensive performance baseline...');
    
    const baseline = {
        timestamp: new Date().toISOString(),
        system: await getSystemMetrics(),
        api: await getAPIMetrics(),
        database: await getDatabaseMetrics(),
        endpoints: await getEndpointMetrics()
    };
    
    // Save baseline
    const filename = `baseline-metrics-${new Date().toISOString().split('T')[0]}.json`;
    fs.writeFileSync(filename, JSON.stringify(baseline, null, 2));
    fs.writeFileSync('latest-baseline.json', JSON.stringify(baseline, null, 2));
    
    console.log('✅ Performance baseline saved to:', filename);
    console.log('📊 Summary:');
    console.log(`   API Health: ${baseline.api.health.responseTime}ms`);
    console.log(`   Database Query: ${baseline.database.metadata.responseTime}ms`);
    console.log(`   Memory Usage: ${baseline.system.memory.percentage}%`);
    console.log(`   Disk Usage: ${baseline.system.disk.percentage}%`);
    
    return baseline;
}

async function getSystemMetrics() {
    try {
        // Get disk usage
        const diskUsage = execSync("df / | awk 'NR==2 {print $5}' | sed 's/%//'", { encoding: 'utf8' }).trim();
        
        // Get memory usage (macOS compatible)
        let memoryUsage = 0;
        try {
            const memInfo = execSync("vm_stat | grep 'Pages free\\|Pages active\\|Pages inactive\\|Pages speculative\\|Pages wired down'", { encoding: 'utf8' });
            const lines = memInfo.split('\n').filter(line => line.trim());
            let totalPages = 0;
            let freePages = 0;
            
            lines.forEach(line => {
                const match = line.match(/(\d+)/);
                if (match) {
                    const pages = parseInt(match[1]);
                    if (line.includes('Pages free')) {
                        freePages = pages;
                    }
                    totalPages += pages;
                }
            });
            
            if (totalPages > 0) {
                memoryUsage = Math.round(((totalPages - freePages) / totalPages) * 100);
            }
        } catch (e) {
            console.warn('Could not get memory usage:', e.message);
        }
        
        return {
            disk: {
                percentage: parseInt(diskUsage) || 0,
                timestamp: new Date().toISOString()
            },
            memory: {
                percentage: memoryUsage,
                timestamp: new Date().toISOString()
            },
            platform: process.platform,
            nodeVersion: process.version
        };
    } catch (error) {
        console.warn('Error collecting system metrics:', error.message);
        return {
            disk: { percentage: 0, error: error.message },
            memory: { percentage: 0, error: error.message },
            platform: process.platform,
            nodeVersion: process.version
        };
    }
}

async function getAPIMetrics() {
    const metrics = {};
    
    // Test API health endpoint
    try {
        const start = performance.now();
        const response = await fetch(`${API_URL}/api/health`);
        const end = performance.now();
        const data = await response.json();
        
        metrics.health = {
            responseTime: Math.round(end - start),
            status: response.status,
            data: data,
            timestamp: new Date().toISOString()
        };
    } catch (error) {
        metrics.health = {
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
    
    // Test feature flags endpoint
    try {
        const start = performance.now();
        const response = await fetch(`${API_URL}/api/feature-flags`);
        const end = performance.now();
        const data = await response.json();
        
        metrics.featureFlags = {
            responseTime: Math.round(end - start),
            status: response.status,
            flagCount: Object.keys(data.flags || {}).length,
            timestamp: new Date().toISOString()
        };
    } catch (error) {
        metrics.featureFlags = {
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
    
    return metrics;
}

async function getDatabaseMetrics() {
    const metrics = {};
    
    // Test database via metadata endpoint
    try {
        const start = performance.now();
        const response = await fetch(`${API_URL}/api/metadata`);
        const end = performance.now();
        const data = await response.json();
        
        metrics.metadata = {
            responseTime: Math.round(end - start),
            status: response.status,
            nodeLabels: data.nodeLabels?.length || 0,
            relationshipTypes: data.relationshipTypes?.length || 0,
            totalNodes: data.nodeLabels?.reduce((sum, label) => sum + label.count, 0) || 0,
            totalRelationships: data.relationshipTypes?.reduce((sum, rel) => sum + rel.count, 0) || 0,
            timestamp: new Date().toISOString()
        };
    } catch (error) {
        metrics.metadata = {
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
    
    return metrics;
}

async function getEndpointMetrics() {
    const endpoints = [
        '/api/health',
        '/api/feature-flags',
        '/api/metadata',
        '/metrics'
    ];
    
    const metrics = {};
    
    for (const endpoint of endpoints) {
        try {
            const start = performance.now();
            const response = await fetch(`${API_URL}${endpoint}`);
            const end = performance.now();
            
            metrics[endpoint] = {
                responseTime: Math.round(end - start),
                status: response.status,
                contentLength: response.headers.get('content-length') || 0,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            metrics[endpoint] = {
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    
    return metrics;
}

// Run if called directly
if (require.main === module) {
    collectPerformanceBaseline().catch(console.error);
}

module.exports = { collectPerformanceBaseline };
