#!/usr/bin/env node

/**
 * Migration Monitoring Script
 * Continuously monitors system health and migration progress
 */

const fs = require('fs');
const path = require('path');

const API_URL = process.env.API_URL || 'http://localhost:3002';
const MONITOR_INTERVAL = process.env.MONITOR_INTERVAL || 30000; // 30 seconds
const LOG_FILE = path.join(__dirname, 'migration-monitor.log');

class MigrationMonitor {
    constructor() {
        this.baseline = null;
        this.alerts = [];
        this.metrics = [];
        this.loadBaseline();
    }
    
    async loadBaseline() {
        try {
            const baselinePath = path.join(__dirname, '../../latest-baseline.json');
            if (fs.existsSync(baselinePath)) {
                this.baseline = JSON.parse(fs.readFileSync(baselinePath, 'utf8'));
                this.log('✅ Baseline metrics loaded');
            }
        } catch (error) {
            this.log(`⚠️ Could not load baseline: ${error.message}`);
        }
    }
    
    async collectMetrics() {
        try {
            const startTime = Date.now();
            
            // Collect current metrics
            const [healthResponse, flagsResponse, metadataResponse] = await Promise.all([
                fetch(`${API_URL}/api/health`),
                fetch(`${API_URL}/api/feature-flags`),
                fetch(`${API_URL}/api/metadata`)
            ]);
            
            const health = await healthResponse.json();
            const flags = await flagsResponse.json();
            const metadata = await metadataResponse.json();
            
            const metrics = {
                timestamp: new Date().toISOString(),
                collectionTime: Date.now() - startTime,
                health: {
                    status: health.status,
                    responseTime: Date.now() - startTime
                },
                migration: flags.migrationPhase,
                flags: flags.flags,
                database: {
                    nodeLabels: metadata.nodeLabels?.length || 0,
                    relationshipTypes: metadata.relationshipTypes?.length || 0,
                    totalNodes: metadata.nodeLabels?.reduce((sum, label) => sum + label.count, 0) || 0,
                    totalRelationships: metadata.relationshipTypes?.reduce((sum, rel) => sum + rel.count, 0) || 0
                }
            };
            
            this.metrics.push(metrics);
            
            // Keep only last 100 metrics
            if (this.metrics.length > 100) {
                this.metrics = this.metrics.slice(-100);
            }
            
            // Analyze metrics for alerts
            this.analyzeMetrics(metrics);
            
            return metrics;
            
        } catch (error) {
            this.log(`❌ Error collecting metrics: ${error.message}`);
            this.addAlert('COLLECTION_ERROR', `Failed to collect metrics: ${error.message}`, 'error');
            return null;
        }
    }
    
    analyzeMetrics(current) {
        if (!this.baseline) return;

        // Check API response time degradation
        const baselineApiTime = this.baseline.api.health.responseTime;
        const currentApiTime = current.health.responseTime;
        const apiRatio = currentApiTime / baselineApiTime;

        if (apiRatio > 2.0) {
            this.addAlert('PERFORMANCE_DEGRADATION',
                `API response time is ${apiRatio.toFixed(1)}x slower than baseline (${currentApiTime}ms vs ${baselineApiTime}ms)`,
                'warning');
        }

        // Trigger automatic rollback if enabled and conditions are met
        if (current.flags.AUTOMATIC_ROLLBACK && this.shouldTriggerRollback(current, apiRatio)) {
            this.triggerEmergencyRollback(`Automatic rollback: API response time ${apiRatio.toFixed(1)}x slower than baseline`);
        }
        
        // Check migration progress
        const backendProgress = current.migration.backend.percentage;
        if (backendProgress > 0) {
            this.log(`📊 Migration Progress: Backend ${backendProgress}%, API Traffic ${current.migration.traffic.api}%`);
        }
        
        // Check safety flags
        if (!current.flags.CIRCUIT_BREAKER_ENABLED && current.flags.NEW_CHAT_SERVICE) {
            this.addAlert('SAFETY_WARNING', 
                'Chat service enabled without circuit breaker protection', 
                'warning');
        }
        
        if (!current.flags.DUAL_EXECUTION_MODE && backendProgress > 0 && backendProgress < 100) {
            this.addAlert('SAFETY_WARNING', 
                'Migration in progress without dual execution mode', 
                'warning');
        }
    }

    shouldTriggerRollback(current, apiRatio) {
        // Rollback conditions
        const conditions = [
            apiRatio > 3.0, // API 3x slower than baseline
            current.health.status !== 'ok', // API health check failing
            // Add more conditions as needed
        ];

        return conditions.some(condition => condition);
    }

    async triggerEmergencyRollback(reason) {
        this.log(`🚨 TRIGGERING EMERGENCY ROLLBACK: ${reason}`);

        try {
            const { spawn } = require('child_process');
            const rollbackScript = path.join(__dirname, '../scripts/emergency-rollback.sh');

            const rollback = spawn('bash', [rollbackScript, reason], {
                stdio: 'inherit',
                cwd: path.join(__dirname, '../..')
            });

            rollback.on('close', (code) => {
                if (code === 0) {
                    this.log('✅ Emergency rollback completed successfully');
                    this.addAlert('ROLLBACK_SUCCESS', 'Emergency rollback completed', 'info');
                } else {
                    this.log(`❌ Emergency rollback failed with code ${code}`);
                    this.addAlert('ROLLBACK_FAILED', `Emergency rollback failed with code ${code}`, 'error');
                }
            });

        } catch (error) {
            this.log(`❌ Failed to trigger rollback: ${error.message}`);
            this.addAlert('ROLLBACK_ERROR', `Failed to trigger rollback: ${error.message}`, 'error');
        }
    }

    addAlert(type, message, severity = 'info') {
        const alert = {
            timestamp: new Date().toISOString(),
            type,
            message,
            severity
        };
        
        this.alerts.push(alert);
        
        // Keep only last 50 alerts
        if (this.alerts.length > 50) {
            this.alerts = this.alerts.slice(-50);
        }
        
        // Log alert
        const emoji = severity === 'error' ? '🚨' : severity === 'warning' ? '⚠️' : 'ℹ️';
        this.log(`${emoji} ${type}: ${message}`);
        
        // Save alerts to file
        this.saveAlerts();
    }
    
    saveAlerts() {
        try {
            const alertsFile = path.join(__dirname, 'alerts.json');
            fs.writeFileSync(alertsFile, JSON.stringify(this.alerts, null, 2));
        } catch (error) {
            console.error('Failed to save alerts:', error);
        }
    }
    
    saveMetrics() {
        try {
            const metricsFile = path.join(__dirname, 'metrics.json');
            fs.writeFileSync(metricsFile, JSON.stringify(this.metrics, null, 2));
        } catch (error) {
            console.error('Failed to save metrics:', error);
        }
    }
    
    log(message) {
        const timestamp = new Date().toISOString();
        const logEntry = `${timestamp} - ${message}\n`;
        
        console.log(message);
        
        try {
            fs.appendFileSync(LOG_FILE, logEntry);
        } catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }
    
    async start() {
        this.log('🚀 Migration monitor started');
        this.log(`📊 Monitoring interval: ${MONITOR_INTERVAL}ms`);
        this.log(`🎯 API URL: ${API_URL}`);
        
        // Initial collection
        await this.collectMetrics();
        
        // Set up interval
        setInterval(async () => {
            const metrics = await this.collectMetrics();
            if (metrics) {
                this.saveMetrics();
            }
        }, MONITOR_INTERVAL);
        
        // Save metrics every 5 minutes
        setInterval(() => {
            this.saveMetrics();
        }, 5 * 60 * 1000);
        
        // Handle graceful shutdown
        process.on('SIGINT', () => {
            this.log('🛑 Monitor stopping...');
            this.saveMetrics();
            this.saveAlerts();
            process.exit(0);
        });
    }
    
    getStatus() {
        const latest = this.metrics[this.metrics.length - 1];
        const recentAlerts = this.alerts.filter(alert => 
            Date.now() - new Date(alert.timestamp).getTime() < 5 * 60 * 1000 // Last 5 minutes
        );
        
        return {
            status: latest ? 'healthy' : 'unknown',
            lastUpdate: latest?.timestamp,
            recentAlerts: recentAlerts.length,
            migrationProgress: latest?.migration,
            metricsCount: this.metrics.length
        };
    }
}

// Create and start monitor
const monitor = new MigrationMonitor();

// Handle command line arguments
const command = process.argv[2];

if (command === 'status') {
    console.log(JSON.stringify(monitor.getStatus(), null, 2));
    process.exit(0);
} else if (command === 'alerts') {
    console.log(JSON.stringify(monitor.alerts, null, 2));
    process.exit(0);
} else {
    monitor.start();
}

module.exports = MigrationMonitor;
