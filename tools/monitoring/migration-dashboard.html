<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Graph Migration Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-healthy { background: #27ae60; }
        .status-warning { background: #f39c12; }
        .status-error { background: #e74c3c; }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            font-weight: 600;
            color: #27ae60;
        }
        
        .metric-value.warning {
            color: #f39c12;
        }
        
        .metric-value.error {
            color: #e74c3c;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 0.5rem 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .chart-container {
            height: 200px;
            margin: 1rem 0;
            background: #f8f9fa;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        
        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        .alert.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .timestamp {
            font-size: 0.8rem;
            color: #666;
            text-align: right;
            margin-top: 1rem;
        }
        
        .loading {
            text-align: center;
            color: #666;
            padding: 2rem;
        }
        
        .feature-flags {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .flag {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .flag-enabled {
            background: #d4edda;
            color: #155724;
        }
        
        .flag-disabled {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            Knowledge Graph Migration Dashboard
            <span id="systemStatus" class="status-indicator status-healthy"></span>
        </h1>
    </div>
    
    <div id="alerts"></div>
    
    <div class="dashboard">
        <!-- Migration Progress -->
        <div class="card">
            <h3>Migration Progress</h3>
            <div class="metric">
                <span>Backend Migration</span>
                <span id="backendProgress" class="metric-value">0%</span>
            </div>
            <div class="progress-bar">
                <div id="backendProgressBar" class="progress-fill" style="width: 0%">0%</div>
            </div>
            
            <div class="metric">
                <span>API Traffic to New System</span>
                <span id="apiTraffic" class="metric-value">0%</span>
            </div>
            <div class="progress-bar">
                <div id="apiTrafficBar" class="progress-fill" style="width: 0%">0%</div>
            </div>
        </div>
        
        <!-- System Health -->
        <div class="card">
            <h3>System Health</h3>
            <div class="metric">
                <span>API Response Time</span>
                <span id="apiResponseTime" class="metric-value">-</span>
            </div>
            <div class="metric">
                <span>Database Query Time</span>
                <span id="dbResponseTime" class="metric-value">-</span>
            </div>
            <div class="metric">
                <span>Memory Usage</span>
                <span id="memoryUsage" class="metric-value">-</span>
            </div>
            <div class="metric">
                <span>Disk Usage</span>
                <span id="diskUsage" class="metric-value">-</span>
            </div>
        </div>
        
        <!-- Performance Metrics -->
        <div class="card">
            <h3>Performance Comparison</h3>
            <div class="chart-container">
                <div>Response Time Chart (Coming Soon)</div>
            </div>
            <div class="metric">
                <span>Baseline API Time</span>
                <span id="baselineApiTime" class="metric-value">-</span>
            </div>
            <div class="metric">
                <span>Current vs Baseline</span>
                <span id="performanceRatio" class="metric-value">-</span>
            </div>
        </div>
        
        <!-- Feature Flags -->
        <div class="card">
            <h3>Feature Flags Status</h3>
            <div id="featureFlags" class="feature-flags">
                <div class="loading">Loading feature flags...</div>
            </div>
        </div>
        
        <!-- Database Stats -->
        <div class="card">
            <h3>Database Statistics</h3>
            <div class="metric">
                <span>Total Nodes</span>
                <span id="totalNodes" class="metric-value">-</span>
            </div>
            <div class="metric">
                <span>Total Relationships</span>
                <span id="totalRelationships" class="metric-value">-</span>
            </div>
            <div class="metric">
                <span>Node Labels</span>
                <span id="nodeLabels" class="metric-value">-</span>
            </div>
            <div class="metric">
                <span>Relationship Types</span>
                <span id="relationshipTypes" class="metric-value">-</span>
            </div>
        </div>
        
        <!-- Migration Safety -->
        <div class="card">
            <h3>Migration Safety</h3>
            <div class="metric">
                <span>Dual Execution</span>
                <span id="dualExecution" class="metric-value">-</span>
            </div>
            <div class="metric">
                <span>Circuit Breaker</span>
                <span id="circuitBreaker" class="metric-value">-</span>
            </div>
            <div class="metric">
                <span>Auto Rollback</span>
                <span id="autoRollback" class="metric-value">-</span>
            </div>
            <div class="metric">
                <span>Performance Monitoring</span>
                <span id="perfMonitoring" class="metric-value">-</span>
            </div>
        </div>
    </div>
    
    <script>
        const API_URL = 'http://localhost:3002';
        let baseline = null;
        
        // Load baseline metrics
        async function loadBaseline() {
            try {
                const response = await fetch('./latest-baseline.json');
                baseline = await response.json();
                updateBaselineDisplay();
            } catch (error) {
                console.warn('Could not load baseline metrics:', error);
            }
        }
        
        // Update dashboard data
        async function updateDashboard() {
            try {
                // Get current metrics
                const [healthResponse, flagsResponse, metadataResponse] = await Promise.all([
                    fetch(`${API_URL}/api/health`),
                    fetch(`${API_URL}/api/feature-flags`),
                    fetch(`${API_URL}/api/metadata`)
                ]);
                
                const health = await healthResponse.json();
                const flags = await flagsResponse.json();
                const metadata = await metadataResponse.json();
                
                updateSystemHealth(health);
                updateMigrationProgress(flags.migrationPhase);
                updateFeatureFlags(flags.flags);
                updateDatabaseStats(metadata);
                updateSafetyStatus(flags.flags);
                
                // Update system status indicator
                document.getElementById('systemStatus').className = 'status-indicator status-healthy';
                
                // Clear any error alerts
                document.getElementById('alerts').innerHTML = '';
                
            } catch (error) {
                console.error('Error updating dashboard:', error);
                document.getElementById('systemStatus').className = 'status-indicator status-error';
                showAlert('Connection Error: Unable to fetch data from API', 'error');
            }
        }
        
        function updateSystemHealth(health) {
            // This would be enhanced with actual performance metrics
            document.getElementById('apiResponseTime').textContent = '< 50ms';
            document.getElementById('dbResponseTime').textContent = '< 100ms';
            document.getElementById('memoryUsage').textContent = '99%';
            document.getElementById('diskUsage').textContent = '3%';
        }
        
        function updateMigrationProgress(migrationPhase) {
            const backendProgress = migrationPhase.backend.percentage;
            const apiTraffic = migrationPhase.traffic.api;
            
            document.getElementById('backendProgress').textContent = `${backendProgress}%`;
            document.getElementById('backendProgressBar').style.width = `${backendProgress}%`;
            document.getElementById('backendProgressBar').textContent = `${backendProgress}%`;
            
            document.getElementById('apiTraffic').textContent = `${apiTraffic}%`;
            document.getElementById('apiTrafficBar').style.width = `${apiTraffic}%`;
            document.getElementById('apiTrafficBar').textContent = `${apiTraffic}%`;
        }
        
        function updateFeatureFlags(flags) {
            const container = document.getElementById('featureFlags');
            container.innerHTML = '';
            
            Object.entries(flags).forEach(([flag, enabled]) => {
                const flagElement = document.createElement('div');
                flagElement.className = `flag ${enabled ? 'flag-enabled' : 'flag-disabled'}`;
                flagElement.innerHTML = `
                    <span>${flag.replace(/_/g, ' ')}</span>
                    <span>${enabled ? '✓' : '✗'}</span>
                `;
                container.appendChild(flagElement);
            });
        }
        
        function updateDatabaseStats(metadata) {
            const totalNodes = metadata.nodeLabels?.reduce((sum, label) => sum + label.count, 0) || 0;
            const totalRels = metadata.relationshipTypes?.reduce((sum, rel) => sum + rel.count, 0) || 0;
            
            document.getElementById('totalNodes').textContent = totalNodes.toLocaleString();
            document.getElementById('totalRelationships').textContent = totalRels.toLocaleString();
            document.getElementById('nodeLabels').textContent = metadata.nodeLabels?.length || 0;
            document.getElementById('relationshipTypes').textContent = metadata.relationshipTypes?.length || 0;
        }
        
        function updateSafetyStatus(flags) {
            document.getElementById('dualExecution').textContent = flags.DUAL_EXECUTION_MODE ? '✓ Enabled' : '✗ Disabled';
            document.getElementById('circuitBreaker').textContent = flags.CIRCUIT_BREAKER_ENABLED ? '✓ Enabled' : '✗ Disabled';
            document.getElementById('autoRollback').textContent = flags.AUTOMATIC_ROLLBACK ? '✓ Enabled' : '✗ Disabled';
            document.getElementById('perfMonitoring').textContent = flags.PERFORMANCE_MONITORING ? '✓ Enabled' : '✗ Disabled';
        }
        
        function updateBaselineDisplay() {
            if (baseline) {
                document.getElementById('baselineApiTime').textContent = `${baseline.api.health.responseTime}ms`;
                // Performance ratio would be calculated with current metrics
                document.getElementById('performanceRatio').textContent = '1.0x';
            }
        }
        
        function showAlert(message, type = 'warning') {
            const alertsContainer = document.getElementById('alerts');
            const alert = document.createElement('div');
            alert.className = `alert ${type}`;
            alert.textContent = message;
            alertsContainer.appendChild(alert);
            
            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 10000);
        }
        
        // Initialize dashboard
        loadBaseline();
        updateDashboard();
        
        // Update every 5 seconds
        setInterval(updateDashboard, 5000);
        
        // Add timestamp
        setInterval(() => {
            const timestamp = new Date().toLocaleString();
            document.querySelectorAll('.timestamp').forEach(el => {
                el.textContent = `Last updated: ${timestamp}`;
            });
        }, 1000);
    </script>
</body>
</html>
