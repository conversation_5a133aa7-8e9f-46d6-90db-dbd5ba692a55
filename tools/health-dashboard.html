<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KnowledgeGraphVisualizer - Health Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .status-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #ddd;
        }

        .status-card.healthy {
            border-left-color: #27ae60;
        }

        .status-card.unhealthy {
            border-left-color: #e74c3c;
        }

        .status-card.degraded {
            border-left-color: #f39c12;
        }

        .status-card h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.healthy {
            background: #27ae60;
        }

        .status-indicator.unhealthy {
            background: #e74c3c;
        }

        .status-indicator.degraded {
            background: #f39c12;
        }

        .metrics {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }

        .metric-label {
            font-size: 0.9em;
            color: #7f8c8d;
        }

        .refresh-controls {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn.danger {
            background: #e74c3c;
        }

        .btn.danger:hover {
            background: #c0392b;
        }

        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .error-details {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 0.9em;
            color: #c53030;
        }

        .last-updated {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .loading .status-card {
            animation: pulse 1.5s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 KnowledgeGraphVisualizer Health Dashboard</h1>
            <p>Real-time monitoring of all system services</p>
            <div class="last-updated" id="lastUpdated">Last updated: Never</div>
        </div>

        <div class="refresh-controls">
            <div>
                <button class="btn" onclick="refreshHealth()">🔄 Refresh Now</button>
                <button class="btn danger" onclick="clearData()">🗑️ Clear Data</button>
            </div>
            <div class="auto-refresh">
                <label>
                    <input type="checkbox" id="autoRefresh" onchange="toggleAutoRefresh()">
                    Auto-refresh (30s)
                </label>
            </div>
        </div>

        <div class="status-overview" id="statusOverview">
            <!-- Service status cards will be populated here -->
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isLoading = false;

        // Mock health check function (replace with actual API call)
        async function checkHealth() {
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Mock health data
            return {
                timestamp: new Date().toISOString(),
                overall: Math.random() > 0.2,
                responseTime: Math.floor(Math.random() * 500) + 100,
                services: {
                    database: Math.random() > 0.1,
                    api: Math.random() > 0.1,
                    llm: Math.random() > 0.3,
                    frontend: Math.random() > 0.1,
                    proxy: Math.random() > 0.2
                },
                details: {
                    database: {
                        status: Math.random() > 0.1 ? 'healthy' : 'unhealthy',
                        responseTime: Math.floor(Math.random() * 200) + 50,
                        error: Math.random() > 0.9 ? 'Connection timeout' : undefined
                    },
                    api: {
                        status: Math.random() > 0.1 ? 'healthy' : 'unhealthy',
                        responseTime: Math.floor(Math.random() * 300) + 100
                    },
                    llm: {
                        status: Math.random() > 0.3 ? 'healthy' : 'degraded',
                        responseTime: Math.floor(Math.random() * 1000) + 200,
                        metadata: { configuredProviders: ['ollama', 'anthropic'] }
                    },
                    frontend: {
                        status: Math.random() > 0.1 ? 'healthy' : 'unhealthy',
                        responseTime: Math.floor(Math.random() * 100) + 50
                    },
                    proxy: {
                        status: Math.random() > 0.2 ? 'healthy' : 'unhealthy',
                        responseTime: Math.floor(Math.random() * 150) + 75
                    }
                }
            };
        }

        function renderHealthStatus(healthData) {
            const container = document.getElementById('statusOverview');
            const services = ['database', 'api', 'llm', 'frontend', 'proxy'];
            
            container.innerHTML = services.map(service => {
                const isHealthy = healthData.services[service];
                const detail = healthData.details[service];
                const status = detail?.status || (isHealthy ? 'healthy' : 'unhealthy');
                const responseTime = detail?.responseTime || 0;
                const error = detail?.error;

                return `
                    <div class="status-card ${status}">
                        <h3>
                            <span class="status-indicator ${status}"></span>
                            ${service.charAt(0).toUpperCase() + service.slice(1)}
                        </h3>
                        <div class="metrics">
                            <div class="metric">
                                <div class="metric-value">${status.toUpperCase()}</div>
                                <div class="metric-label">Status</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">${responseTime}ms</div>
                                <div class="metric-label">Response Time</div>
                            </div>
                        </div>
                        ${error ? `<div class="error-details">Error: ${error}</div>` : ''}
                    </div>
                `;
            }).join('');

            // Update last updated time
            document.getElementById('lastUpdated').textContent = 
                `Last updated: ${new Date(healthData.timestamp).toLocaleString()}`;
        }

        async function refreshHealth() {
            if (isLoading) return;
            
            isLoading = true;
            document.getElementById('statusOverview').classList.add('loading');
            
            try {
                const healthData = await checkHealth();
                renderHealthStatus(healthData);
            } catch (error) {
                console.error('Failed to fetch health data:', error);
                // Show error state
            } finally {
                isLoading = false;
                document.getElementById('statusOverview').classList.remove('loading');
            }
        }

        function toggleAutoRefresh() {
            const checkbox = document.getElementById('autoRefresh');
            
            if (checkbox.checked) {
                autoRefreshInterval = setInterval(refreshHealth, 30000);
                refreshHealth(); // Immediate refresh
            } else {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
            }
        }

        function clearData() {
            document.getElementById('statusOverview').innerHTML = 
                '<p style="text-align: center; color: #7f8c8d; grid-column: 1 / -1;">No data available. Click "Refresh Now" to load health status.</p>';
            document.getElementById('lastUpdated').textContent = 'Last updated: Never';
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', () => {
            refreshHealth();
        });
    </script>
</body>
</html>
