{"name": "kg-qa-proxy-server", "version": "1.0.0", "description": "Node.js/Express proxy server for Knowledge Graph QA system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["proxy", "express", "knowledge-graph", "qa", "chat"], "author": "", "license": "ISC", "engines": {"node": ">=18.0.0"}, "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "5.1.0", "express-rate-limit": "5.3.0", "express-session": "1.17.2", "helmet": "^8.0.0", "http-proxy-middleware": "^3.0.3", "opossum": "^9.0.0", "winston": "3.3.3"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.0", "supertest": "^7.0.0"}}