/**
 * Chat Service Client
 * 
 * Enhanced HTTP client for communicating with the FastAPI chat service.
 * Provides authentication, retry logic, circuit breaker pattern,
 * and comprehensive error handling.
 */

const axios = require('axios');
const winston = require('winston');
const CircuitBreaker = require('opossum');

class ChatServiceClient {
  constructor(options = {}) {
    this.baseURL = options.baseURL || process.env.FASTAPI_URL || 'http://localhost:8000';
    this.apiKey = options.apiKey || process.env.CHAT_SERVICE_API_KEY;
    this.timeout = options.timeout || 60000; // 60 seconds
    this.retryAttempts = options.retryAttempts || 3;
    this.retryDelay = options.retryDelay || 1000; // 1 second
    
    // Initialize logger
    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { service: 'chat-service-client' },
      transports: [
        new winston.transports.File({ filename: 'logs/chat-service-client.log' }),
        new winston.transports.Console({
          format: winston.format.simple(),
          level: 'info'
        })
      ],
    });
    
    // Initialize HTTP client
    this.httpClient = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'NodeJS-Backend/1.0.0',
        ...(this.apiKey && { 'X-API-Key': this.apiKey })
      }
    });
    
    // Add request interceptor
    this.httpClient.interceptors.request.use(
      (config) => {
        config.metadata = { startTime: Date.now() };
        this.logger.debug('Sending request to chat service', {
          method: config.method,
          url: config.url,
          headers: this._sanitizeHeaders(config.headers)
        });
        return config;
      },
      (error) => {
        this.logger.error('Request interceptor error', { error: error.message });
        return Promise.reject(error);
      }
    );
    
    // Add response interceptor
    this.httpClient.interceptors.response.use(
      (response) => {
        const duration = Date.now() - response.config.metadata.startTime;
        this.logger.debug('Received response from chat service', {
          status: response.status,
          duration: `${duration}ms`,
          url: response.config.url
        });
        return response;
      },
      (error) => {
        const duration = error.config?.metadata ? 
          Date.now() - error.config.metadata.startTime : 0;
        
        this.logger.error('Response interceptor error', {
          error: error.message,
          status: error.response?.status,
          duration: `${duration}ms`,
          url: error.config?.url
        });
        return Promise.reject(error);
      }
    );
    
    // Initialize circuit breaker
    this.circuitBreakerOptions = {
      timeout: this.timeout,
      errorThresholdPercentage: 50,
      resetTimeout: 30000, // 30 seconds
      rollingCountTimeout: 60000, // 1 minute
      rollingCountBuckets: 10,
      name: 'ChatServiceCircuitBreaker',
      fallback: this._fallbackHandler.bind(this)
    };
    
    this.circuitBreaker = new CircuitBreaker(
      this._makeRequest.bind(this),
      this.circuitBreakerOptions
    );
    
    // Circuit breaker event handlers
    this.circuitBreaker.on('open', () => {
      this.logger.warn('Chat service circuit breaker opened');
    });
    
    this.circuitBreaker.on('halfOpen', () => {
      this.logger.info('Chat service circuit breaker half-open');
    });
    
    this.circuitBreaker.on('close', () => {
      this.logger.info('Chat service circuit breaker closed');
    });
    
    this.circuitBreaker.on('fallback', (result) => {
      this.logger.warn('Chat service fallback triggered', { result });
    });
    
    // Statistics
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      fallbackRequests: 0,
      averageResponseTime: 0,
      lastRequestTime: null
    };
    
    this.logger.info('Chat service client initialized', {
      baseURL: this.baseURL,
      timeout: this.timeout,
      hasApiKey: !!this.apiKey
    });
  }
  
  /**
   * Send a chat message to the FastAPI service
   */
  async sendMessage(message, options = {}) {
    const requestData = {
      message: message,
      conversation_id: options.conversationId,
      context: options.context || {},
      options: {
        max_tokens: options.maxTokens,
        temperature: options.temperature,
        group_id: options.groupId,
        ...options.llmOptions
      }
    };
    
    const headers = {
      'X-Session-ID': options.sessionId,
      'X-Correlation-ID': options.correlationId || this._generateCorrelationId()
    };
    
    return this._executeRequest('POST', '/api/v1/chat/message', requestData, headers);
  }
  
  /**
   * Stream a chat message from the FastAPI service
   */
  async streamMessage(message, options = {}) {
    const requestData = {
      message: message,
      conversation_id: options.conversationId,
      context: options.context || {},
      options: {
        max_tokens: options.maxTokens,
        temperature: options.temperature,
        group_id: options.groupId,
        ...options.llmOptions
      }
    };
    
    const headers = {
      'X-Session-ID': options.sessionId,
      'X-Correlation-ID': options.correlationId || this._generateCorrelationId(),
      'Accept': 'text/event-stream'
    };
    
    return this._executeStreamRequest('POST', '/api/v1/chat/stream', requestData, headers);
  }
  
  /**
   * Get conversation history
   */
  async getConversationHistory(conversationId, options = {}) {
    const headers = {
      'X-Session-ID': options.sessionId,
      'X-Correlation-ID': options.correlationId || this._generateCorrelationId()
    };
    
    return this._executeRequest('GET', `/api/v1/chat/conversations/${conversationId}`, null, headers);
  }
  
  /**
   * List conversations
   */
  async listConversations(options = {}) {
    const params = {
      limit: options.limit || 50,
      offset: options.offset || 0
    };
    
    const headers = {
      'X-Session-ID': options.sessionId,
      'X-Correlation-ID': options.correlationId || this._generateCorrelationId()
    };
    
    return this._executeRequest('GET', '/api/v1/chat/conversations', null, headers, params);
  }
  
  /**
   * Health check
   */
  async healthCheck() {
    try {
      const response = await this._executeRequest('GET', '/api/v1/health');
      return {
        status: 'healthy',
        service: 'chat-service',
        response: response.data,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        service: 'chat-service',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * Get service statistics
   */
  getStatistics() {
    return {
      ...this.stats,
      circuitBreaker: {
        state: this.circuitBreaker.stats.state,
        failures: this.circuitBreaker.stats.failures,
        fallbacks: this.circuitBreaker.stats.fallbacks,
        successes: this.circuitBreaker.stats.successes,
        rejects: this.circuitBreaker.stats.rejects,
        fires: this.circuitBreaker.stats.fires
      }
    };
  }
  
  /**
   * Execute request with circuit breaker protection
   */
  async _executeRequest(method, url, data = null, headers = {}, params = {}) {
    const requestConfig = {
      method,
      url,
      data,
      headers: { ...headers },
      params
    };
    
    try {
      this.stats.totalRequests++;
      const startTime = Date.now();
      
      const response = await this.circuitBreaker.fire(requestConfig);
      
      const responseTime = Date.now() - startTime;
      this._updateStats(true, responseTime);
      
      return response;
      
    } catch (error) {
      this._updateStats(false);
      throw this._handleError(error, requestConfig);
    }
  }
  
  /**
   * Execute streaming request
   */
  async _executeStreamRequest(method, url, data = null, headers = {}) {
    const requestConfig = {
      method,
      url,
      data,
      headers: { ...headers },
      responseType: 'stream'
    };
    
    try {
      this.stats.totalRequests++;
      const startTime = Date.now();
      
      // Streaming requests bypass circuit breaker for now
      const response = await this._makeRequest(requestConfig);
      
      const responseTime = Date.now() - startTime;
      this._updateStats(true, responseTime);
      
      return response;
      
    } catch (error) {
      this._updateStats(false);
      throw this._handleError(error, requestConfig);
    }
  }
  
  /**
   * Make HTTP request
   */
  async _makeRequest(config) {
    return await this.httpClient.request(config);
  }
  
  /**
   * Fallback handler for circuit breaker
   */
  async _fallbackHandler(error, config) {
    this.stats.fallbackRequests++;
    
    this.logger.warn('Using fallback for chat service request', {
      method: config.method,
      url: config.url,
      error: error.message
    });
    
    // Return a fallback response
    return {
      data: {
        message: {
          role: 'assistant',
          content: 'I apologize, but the chat service is temporarily unavailable. Please try again in a few moments.',
          timestamp: new Date().toISOString()
        },
        conversation_id: config.data?.conversation_id || null,
        metadata: {
          service_mode: 'fallback',
          error: 'Chat service unavailable',
          timestamp: new Date().toISOString()
        }
      },
      status: 200,
      statusText: 'OK (Fallback)'
    };
  }
  
  /**
   * Handle and transform errors
   */
  _handleError(error, config) {
    const errorInfo = {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: config.url,
      method: config.method
    };
    
    this.logger.error('Chat service request failed', errorInfo);
    
    // Transform error for consistent handling
    const transformedError = new Error(error.message);
    transformedError.code = error.code;
    transformedError.status = error.response?.status;
    transformedError.response = error.response;
    transformedError.config = config;
    
    return transformedError;
  }
  
  /**
   * Update request statistics
   */
  _updateStats(success, responseTime = 0) {
    if (success) {
      this.stats.successfulRequests++;
      
      // Update average response time
      const totalSuccessful = this.stats.successfulRequests;
      this.stats.averageResponseTime = 
        (this.stats.averageResponseTime * (totalSuccessful - 1) + responseTime) / totalSuccessful;
    } else {
      this.stats.failedRequests++;
    }
    
    this.stats.lastRequestTime = new Date().toISOString();
  }
  
  /**
   * Generate correlation ID
   */
  _generateCorrelationId() {
    return `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * Sanitize headers for logging
   */
  _sanitizeHeaders(headers) {
    const sanitized = { ...headers };
    if (sanitized['X-API-Key']) {
      sanitized['X-API-Key'] = '***';
    }
    if (sanitized['Authorization']) {
      sanitized['Authorization'] = '***';
    }
    return sanitized;
  }
}

module.exports = ChatServiceClient;
