/**
 * Service Discovery Client
 * 
 * Node.js client for service discovery and health monitoring.
 * Integrates with FastAPI monitoring endpoints to discover services
 * and monitor their health status.
 */

const axios = require('axios');
const winston = require('winston');
const EventEmitter = require('events');

class ServiceDiscoveryClient extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.monitoringUrl = options.monitoringUrl || process.env.FASTAPI_URL || 'http://localhost:8000';
    this.apiKey = options.apiKey || process.env.CHAT_SERVICE_API_KEY;
    this.pollInterval = options.pollInterval || 30000; // 30 seconds
    this.timeout = options.timeout || 5000; // 5 seconds
    
    // Initialize logger
    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { service: 'service-discovery' },
      transports: [
        new winston.transports.File({ filename: 'logs/service-discovery.log' }),
        new winston.transports.Console({
          format: winston.format.simple(),
          level: 'info'
        })
      ],
    });
    
    // Initialize HTTP client
    this.httpClient = axios.create({
      baseURL: this.monitoringUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'NodeJS-ServiceDiscovery/1.0.0',
        ...(this.apiKey && { 'X-API-Key': this.apiKey })
      }
    });
    
    // Service cache
    this.services = new Map();
    this.lastUpdate = null;
    
    // Polling state
    this.polling = false;
    this.pollTimer = null;
    
    // Statistics
    this.stats = {
      totalDiscoveries: 0,
      successfulDiscoveries: 0,
      failedDiscoveries: 0,
      lastDiscoveryTime: null,
      averageResponseTime: 0
    };
    
    this.logger.info('Service discovery client initialized', {
      monitoringUrl: this.monitoringUrl,
      pollInterval: this.pollInterval,
      hasApiKey: !!this.apiKey
    });
  }
  
  /**
   * Start service discovery polling
   */
  async startDiscovery() {
    if (this.polling) {
      return;
    }
    
    this.polling = true;
    
    // Initial discovery
    await this.discoverServices();
    
    // Start polling
    this.pollTimer = setInterval(async () => {
      try {
        await this.discoverServices();
      } catch (error) {
        this.logger.error('Service discovery polling failed', { error: error.message });
      }
    }, this.pollInterval);
    
    this.logger.info('Service discovery started', { pollInterval: this.pollInterval });
    this.emit('discovery-started');
  }
  
  /**
   * Stop service discovery polling
   */
  stopDiscovery() {
    if (!this.polling) {
      return;
    }
    
    this.polling = false;
    
    if (this.pollTimer) {
      clearInterval(this.pollTimer);
      this.pollTimer = null;
    }
    
    this.logger.info('Service discovery stopped');
    this.emit('discovery-stopped');
  }
  
  /**
   * Discover services from monitoring API
   */
  async discoverServices(tag = null, status = null) {
    const startTime = Date.now();
    
    try {
      this.stats.totalDiscoveries++;
      
      // Build query parameters
      const params = {};
      if (tag) params.tag = tag;
      if (status) params.status = status;
      params.use_cache = false; // Always get fresh data
      
      // Make discovery request
      const response = await this.httpClient.get('/api/v1/monitoring/services', { params });
      
      const responseTime = Date.now() - startTime;
      this.updateStats(true, responseTime);
      
      // Update service cache
      const discoveredServices = response.data.services || [];
      this.updateServiceCache(discoveredServices);
      
      this.logger.debug('Services discovered', {
        totalCount: response.data.total_count,
        healthyCount: response.data.healthy_count,
        responseTime: `${responseTime}ms`
      });
      
      this.emit('services-discovered', {
        services: discoveredServices,
        totalCount: response.data.total_count,
        healthyCount: response.data.healthy_count
      });
      
      return discoveredServices;
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateStats(false, responseTime);
      
      this.logger.error('Service discovery failed', {
        error: error.message,
        responseTime: `${responseTime}ms`
      });
      
      this.emit('discovery-error', error);
      throw error;
    }
  }
  
  /**
   * Get a specific service by name
   */
  async getService(serviceName) {
    try {
      const response = await this.httpClient.get(`/api/v1/monitoring/services/${serviceName}`);
      
      this.logger.debug('Service info retrieved', { serviceName });
      
      return response.data;
      
    } catch (error) {
      if (error.response && error.response.status === 404) {
        return null;
      }
      
      this.logger.error('Failed to get service info', {
        serviceName,
        error: error.message
      });
      
      throw error;
    }
  }
  
  /**
   * Get healthy service instance
   */
  getHealthyService(serviceName) {
    const service = this.services.get(serviceName);
    
    if (service && service.status === 'healthy') {
      return service;
    }
    
    return null;
  }
  
  /**
   * Get all services with optional filtering
   */
  getServices(filter = {}) {
    const allServices = Array.from(this.services.values());
    
    let filtered = allServices;
    
    // Filter by status
    if (filter.status) {
      filtered = filtered.filter(service => service.status === filter.status);
    }
    
    // Filter by tag
    if (filter.tag) {
      filtered = filtered.filter(service => 
        service.tags && service.tags.includes(filter.tag)
      );
    }
    
    // Filter by name pattern
    if (filter.namePattern) {
      const pattern = new RegExp(filter.namePattern, 'i');
      filtered = filtered.filter(service => pattern.test(service.name));
    }
    
    return filtered;
  }
  
  /**
   * Register this Node.js service
   */
  async registerSelf(serviceInfo) {
    try {
      const registrationData = {
        name: serviceInfo.name || 'nodejs-backend',
        url: serviceInfo.url || 'http://localhost:3003',
        version: serviceInfo.version || '1.0.0',
        tags: serviceInfo.tags || ['backend', 'nodejs', 'api'],
        metadata: {
          description: 'Node.js backend service',
          environment: process.env.NODE_ENV || 'development',
          ...serviceInfo.metadata
        }
      };
      
      const response = await this.httpClient.post('/api/v1/monitoring/services/register', registrationData);
      
      this.logger.info('Service registered successfully', {
        serviceName: registrationData.name,
        serviceUrl: registrationData.url
      });
      
      this.emit('service-registered', registrationData);
      
      return response.data;
      
    } catch (error) {
      this.logger.error('Failed to register service', {
        error: error.message,
        serviceInfo
      });
      
      throw error;
    }
  }
  
  /**
   * Get health status from monitoring API
   */
  async getHealthStatus(useCache = true) {
    try {
      const response = await this.httpClient.get('/api/v1/monitoring/health', {
        params: { use_cache: useCache }
      });
      
      this.logger.debug('Health status retrieved', {
        overallStatus: response.data.overall_status,
        activeAlerts: response.data.active_alerts
      });
      
      return response.data;
      
    } catch (error) {
      this.logger.error('Failed to get health status', { error: error.message });
      throw error;
    }
  }
  
  /**
   * Get alerts from monitoring API
   */
  async getAlerts(filter = {}) {
    try {
      const params = {};
      if (filter.resolved !== undefined) params.resolved = filter.resolved;
      if (filter.level) params.level = filter.level;
      if (filter.serviceName) params.service_name = filter.serviceName;
      if (filter.limit) params.limit = filter.limit;
      
      const response = await this.httpClient.get('/api/v1/monitoring/alerts', { params });
      
      this.logger.debug('Alerts retrieved', {
        totalCount: response.data.total_count,
        filters: filter
      });
      
      return response.data;
      
    } catch (error) {
      this.logger.error('Failed to get alerts', { error: error.message });
      throw error;
    }
  }
  
  /**
   * Update service cache with discovered services
   */
  updateServiceCache(services) {
    // Clear existing cache
    this.services.clear();
    
    // Add discovered services
    for (const service of services) {
      this.services.set(service.name, {
        ...service,
        discoveredAt: new Date().toISOString()
      });
    }
    
    this.lastUpdate = new Date().toISOString();
    
    // Emit cache update event
    this.emit('cache-updated', {
      serviceCount: services.length,
      lastUpdate: this.lastUpdate
    });
  }
  
  /**
   * Update discovery statistics
   */
  updateStats(success, responseTime) {
    if (success) {
      this.stats.successfulDiscoveries++;
    } else {
      this.stats.failedDiscoveries++;
    }
    
    this.stats.lastDiscoveryTime = new Date().toISOString();
    
    // Update average response time
    const totalSuccessful = this.stats.successfulDiscoveries;
    if (totalSuccessful > 0) {
      this.stats.averageResponseTime = 
        (this.stats.averageResponseTime * (totalSuccessful - 1) + responseTime) / totalSuccessful;
    }
  }
  
  /**
   * Get discovery statistics
   */
  getStatistics() {
    return {
      ...this.stats,
      serviceCount: this.services.size,
      lastUpdate: this.lastUpdate,
      polling: this.polling,
      pollInterval: this.pollInterval
    };
  }
  
  /**
   * Get service discovery status
   */
  getStatus() {
    const healthyServices = this.getServices({ status: 'healthy' });
    const totalServices = Array.from(this.services.values());
    
    return {
      status: this.polling ? 'active' : 'inactive',
      totalServices: totalServices.length,
      healthyServices: healthyServices.length,
      healthPercentage: totalServices.length > 0 ? 
        Math.round((healthyServices.length / totalServices.length) * 100) : 0,
      lastUpdate: this.lastUpdate,
      statistics: this.getStatistics()
    };
  }
  
  /**
   * Cleanup resources
   */
  cleanup() {
    this.stopDiscovery();
    this.removeAllListeners();
    this.services.clear();
    
    this.logger.info('Service discovery client cleaned up');
  }
}

module.exports = ServiceDiscoveryClient;
