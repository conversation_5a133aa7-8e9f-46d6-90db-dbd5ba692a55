{"folders": [{"name": "🏠 Root", "path": "."}, {"name": "📦 Packages", "path": "./packages"}, {"name": "🔧 Shared", "path": "./packages/shared"}, {"name": "🌐 Graph API", "path": "./packages/graph-api"}, {"name": "💬 Chat Service", "path": "./packages/chat-service"}, {"name": "🎨 Web UI", "path": "./packages/web-ui"}, {"name": "🔍 Monitoring", "path": "./tools/monitoring"}, {"name": "📚 Documentation", "path": "./docs"}], "settings": {"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "typescript.workspaceSymbols.scope": "allOpenProjects", "eslint.workingDirectories": ["packages/shared", "packages/graph-api", "packages/web-ui", "packages/chat-service"], "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.next": true, "**/coverage": true, "**/.nyc_output": true, "**/*.log": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.next": true, "**/coverage": true, "**/.nyc_output": true, "**/package-lock.json": true}, "npm.packageManager": "npm", "npm.enableScriptExplorer": true, "git.ignoreLimitWarning": true, "git.autofetch": true, "workbench.colorCustomizations": {"activityBar.background": "#2c3e50", "activityBar.foreground": "#ecf0f1", "statusBar.background": "#27ae60", "statusBar.foreground": "#ffffff", "titleBar.activeBackground": "#34495e", "titleBar.activeForeground": "#ecf0f1"}, "workbench.iconTheme": "vscode-icons", "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"package.json": "package-lock.json,yarn.lock,pnpm-lock.yaml", "tsconfig.json": "tsconfig.*.json", "jest.config.js": "jest.setup.js", ".eslintrc.js": ".<PERSON><PERSON><PERSON><PERSON>,.pretti<PERSON><PERSON>,.prettierignore", "README.md": "CHANGELOG.md,LICENSE,CONTRIBUTING.md", "docker-compose.yml": "docker-compose.*.yml,<PERSON>er<PERSON>le*"}, "terminal.integrated.defaultProfile.osx": "bash", "terminal.integrated.cwd": "${workspaceFolder}/packages", "debug.allowBreakpointsEverywhere": true, "jest.jestCommandLine": "npm test", "jest.autoRun": "off", "jest.showCoverageOnLoad": false}, "extensions": {"recommendations": ["ms-vscode.vscode-typescript-next", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.vscode-docker", "github.vscode-pull-request-github", "eamodio.gitlens", "ms-playwright.playwright", "orta.vscode-jest", "neo4j.cypher", "ms-vscode.vscode-thunder-client", "github.copilot", "github.copilot-chat"]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "🚀 Start Development", "type": "shell", "command": "npm run dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "dedicated"}, "options": {"cwd": "${workspaceFolder}/packages"}}, {"label": "🔨 Build All", "type": "shell", "command": "npm run build", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}/packages"}}, {"label": "🧪 Run Tests", "type": "shell", "command": "npm test", "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}/packages"}}, {"label": "🔍 Migration Health Check", "type": "shell", "command": "npm run migration:health", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "dedicated"}, "options": {"cwd": "${workspaceFolder}/packages"}}]}, "launch": {"version": "0.2.0", "configurations": [{"name": "🐛 Debug Full Stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/packages/graph-api/src/server.ts", "runtimeArgs": ["-r", "ts-node/register"], "env": {"NODE_ENV": "development", "DEBUG": "*"}, "console": "integratedTerminal", "restart": true, "skipFiles": ["<node_internals>/**"]}]}}