/**
 * Service Communication Integration Tests
 * 
 * Tests for inter-service communication and data flow
 */

describe('Service Communication Integration', () => {
  // Mock services
  const mockApiService = {
    get: jest.fn(),
    post: jest.fn(),
    healthCheck: jest.fn()
  };

  const mockDatabaseService = {
    query: jest.fn(),
    transaction: jest.fn(),
    healthCheck: jest.fn()
  };

  const mockAnalysisService = {
    analyzeClusters: jest.fn(),
    findHiddenLinks: jest.fn(),
    calculateCentrality: jest.fn(),
    healthCheck: jest.fn()
  };

  const mockChatService = {
    sendMessage: jest.fn(),
    getHistory: jest.fn(),
    healthCheck: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('API to Database Communication', () => {
    test('should fetch graph data through API to database', async () => {
      const mockDbResult = {
        records: [
          { get: jest.fn(() => ({ properties: { id: '1', name: 'Node 1' } })) },
          { get: jest.fn(() => ({ properties: { id: '2', name: 'Node 2' } })) }
        ]
      };

      const expectedApiResponse = {
        nodes: [
          { id: '1', name: 'Node 1' },
          { id: '2', name: 'Node 2' }
        ],
        metadata: { count: 2 }
      };

      mockDatabaseService.query.mockResolvedValue(mockDbResult);
      mockApiService.get.mockResolvedValue(expectedApiResponse);

      // Simulate API service calling database service
      const getGraphData = async () => {
        const dbResult = await mockDatabaseService.query('MATCH (n) RETURN n LIMIT 10');
        const nodes = dbResult.records.map(record => record.get('n').properties);
        
        const apiResponse = {
          nodes,
          metadata: { count: nodes.length }
        };

        return apiResponse;
      };

      const result = await getGraphData();

      expect(mockDatabaseService.query).toHaveBeenCalledWith('MATCH (n) RETURN n LIMIT 10');
      expect(result.nodes).toHaveLength(2);
      expect(result.metadata.count).toBe(2);
    });

    test('should handle database errors in API layer', async () => {
      const dbError = new Error('Database connection failed');
      dbError.code = 'ServiceUnavailable';

      mockDatabaseService.query.mockRejectedValue(dbError);

      const getGraphDataWithErrorHandling = async () => {
        try {
          const dbResult = await mockDatabaseService.query('MATCH (n) RETURN n');
          return { success: true, data: dbResult };
        } catch (error) {
          if (error.code === 'ServiceUnavailable') {
            return { 
              success: false, 
              error: 'Database service is temporarily unavailable',
              retryable: true 
            };
          }
          throw error;
        }
      };

      const result = await getGraphDataWithErrorHandling();

      expect(result.success).toBe(false);
      expect(result.error).toContain('temporarily unavailable');
      expect(result.retryable).toBe(true);
    });
  });

  describe('API to Analysis Service Communication', () => {
    test('should trigger analysis through API', async () => {
      const mockGraphData = {
        nodes: [
          { id: '1', name: 'Node 1', type: 'Person' },
          { id: '2', name: 'Node 2', type: 'Company' },
          { id: '3', name: 'Node 3', type: 'Person' }
        ],
        edges: [
          { source: '1', target: '2', type: 'WORKS_FOR' },
          { source: '3', target: '2', type: 'WORKS_FOR' }
        ]
      };

      const mockClusterResult = {
        clusters: [
          { id: 'cluster1', nodes: ['1', '3'], size: 2, type: 'Person' },
          { id: 'cluster2', nodes: ['2'], size: 1, type: 'Company' }
        ],
        modularity: 0.75,
        algorithm: 'louvain'
      };

      mockApiService.get.mockResolvedValue(mockGraphData);
      mockAnalysisService.analyzeClusters.mockResolvedValue(mockClusterResult);

      const performClusterAnalysis = async () => {
        // 1. Get graph data from API
        const graphData = await mockApiService.get('/graph/initial');
        
        // 2. Send to analysis service
        const clusterResult = await mockAnalysisService.analyzeClusters(graphData, {
          algorithm: 'louvain',
          resolution: 1.0
        });

        return {
          graphData,
          analysis: clusterResult
        };
      };

      const result = await performClusterAnalysis();

      expect(mockApiService.get).toHaveBeenCalledWith('/graph/initial');
      expect(mockAnalysisService.analyzeClusters).toHaveBeenCalledWith(
        mockGraphData,
        { algorithm: 'louvain', resolution: 1.0 }
      );
      expect(result.analysis.clusters).toHaveLength(2);
      expect(result.analysis.modularity).toBe(0.75);
    });

    test('should handle analysis service failures', async () => {
      const analysisError = new Error('Analysis computation failed');
      analysisError.code = 'COMPUTATION_ERROR';

      mockAnalysisService.analyzeClusters.mockRejectedValue(analysisError);

      const performAnalysisWithFallback = async (graphData) => {
        try {
          return await mockAnalysisService.analyzeClusters(graphData);
        } catch (error) {
          if (error.code === 'COMPUTATION_ERROR') {
            // Fallback to simple analysis
            return {
              clusters: [],
              error: 'Advanced analysis failed, using basic grouping',
              fallback: true
            };
          }
          throw error;
        }
      };

      const result = await performAnalysisWithFallback({ nodes: [], edges: [] });

      expect(result.fallback).toBe(true);
      expect(result.error).toContain('Advanced analysis failed');
    });
  });

  describe('Chat Service Integration', () => {
    test('should integrate chat with graph context', async () => {
      const mockGraphContext = {
        nodes: [{ id: '1', name: 'John Doe', type: 'Person' }],
        edges: [],
        selectedNode: '1'
      };

      const mockChatResponse = {
        message: 'John Doe is a Person node in the graph with connections to 3 other entities.',
        context: mockGraphContext,
        timestamp: new Date().toISOString()
      };

      mockApiService.get.mockResolvedValue(mockGraphContext);
      mockChatService.sendMessage.mockResolvedValue(mockChatResponse);

      const sendContextualMessage = async (message, nodeId) => {
        // 1. Get graph context
        const context = await mockApiService.get(`/graph/context/${nodeId}`);
        
        // 2. Send message with context
        const response = await mockChatService.sendMessage(message, {
          graphContext: context,
          selectedNode: nodeId
        });

        return response;
      };

      const result = await sendContextualMessage('Tell me about this node', '1');

      expect(mockApiService.get).toHaveBeenCalledWith('/graph/context/1');
      expect(mockChatService.sendMessage).toHaveBeenCalledWith(
        'Tell me about this node',
        { graphContext: mockGraphContext, selectedNode: '1' }
      );
      expect(result.message).toContain('John Doe');
    });

    test('should handle chat service unavailability', async () => {
      const chatError = new Error('Chat service unavailable');
      chatError.code = 'SERVICE_UNAVAILABLE';

      mockChatService.sendMessage.mockRejectedValue(chatError);

      const sendMessageWithFallback = async (message) => {
        try {
          return await mockChatService.sendMessage(message);
        } catch (error) {
          if (error.code === 'SERVICE_UNAVAILABLE') {
            return {
              message: 'Chat service is currently unavailable. Please try again later.',
              error: true,
              fallback: true
            };
          }
          throw error;
        }
      };

      const result = await sendMessageWithFallback('Hello');

      expect(result.error).toBe(true);
      expect(result.fallback).toBe(true);
      expect(result.message).toContain('currently unavailable');
    });
  });

  describe('Service Health Monitoring', () => {
    test('should check health of all services', async () => {
      const mockHealthResponses = {
        api: { status: 'healthy', timestamp: new Date().toISOString() },
        database: { status: 'healthy', timestamp: new Date().toISOString() },
        analysis: { status: 'healthy', timestamp: new Date().toISOString() },
        chat: { status: 'degraded', timestamp: new Date().toISOString(), message: 'High latency' }
      };

      mockApiService.healthCheck.mockResolvedValue(mockHealthResponses.api);
      mockDatabaseService.healthCheck.mockResolvedValue(mockHealthResponses.database);
      mockAnalysisService.healthCheck.mockResolvedValue(mockHealthResponses.analysis);
      mockChatService.healthCheck.mockResolvedValue(mockHealthResponses.chat);

      const checkAllServicesHealth = async () => {
        const healthChecks = await Promise.allSettled([
          mockApiService.healthCheck(),
          mockDatabaseService.healthCheck(),
          mockAnalysisService.healthCheck(),
          mockChatService.healthCheck()
        ]);

        return {
          api: healthChecks[0].status === 'fulfilled' ? healthChecks[0].value : { status: 'unhealthy' },
          database: healthChecks[1].status === 'fulfilled' ? healthChecks[1].value : { status: 'unhealthy' },
          analysis: healthChecks[2].status === 'fulfilled' ? healthChecks[2].value : { status: 'unhealthy' },
          chat: healthChecks[3].status === 'fulfilled' ? healthChecks[3].value : { status: 'unhealthy' }
        };
      };

      const result = await checkAllServicesHealth();

      expect(result.api.status).toBe('healthy');
      expect(result.database.status).toBe('healthy');
      expect(result.analysis.status).toBe('healthy');
      expect(result.chat.status).toBe('degraded');
      expect(result.chat.message).toBe('High latency');
    });

    test('should handle service health check failures', async () => {
      const healthError = new Error('Health check timeout');
      
      mockApiService.healthCheck.mockResolvedValue({ status: 'healthy' });
      mockDatabaseService.healthCheck.mockRejectedValue(healthError);
      mockAnalysisService.healthCheck.mockResolvedValue({ status: 'healthy' });
      mockChatService.healthCheck.mockRejectedValue(healthError);

      const checkServicesWithErrorHandling = async () => {
        const services = ['api', 'database', 'analysis', 'chat'];
        const healthCheckers = [
          mockApiService.healthCheck,
          mockDatabaseService.healthCheck,
          mockAnalysisService.healthCheck,
          mockChatService.healthCheck
        ];

        const results = {};

        for (let i = 0; i < services.length; i++) {
          try {
            results[services[i]] = await healthCheckers[i]();
          } catch (error) {
            results[services[i]] = {
              status: 'unhealthy',
              error: error.message,
              timestamp: new Date().toISOString()
            };
          }
        }

        return results;
      };

      const result = await checkServicesWithErrorHandling();

      expect(result.api.status).toBe('healthy');
      expect(result.database.status).toBe('unhealthy');
      expect(result.database.error).toBe('Health check timeout');
      expect(result.analysis.status).toBe('healthy');
      expect(result.chat.status).toBe('unhealthy');
    });
  });

  describe('Data Flow Integration', () => {
    test('should handle complete data flow from UI to database', async () => {
      const userAction = {
        type: 'CREATE_NODE',
        data: { name: 'New Node', type: 'Person', properties: { age: 30 } }
      };

      const mockDbResponse = {
        records: [{ get: jest.fn(() => ({ properties: { id: 'new-id', ...userAction.data } })) }]
      };

      const mockApiResponse = {
        id: 'new-id',
        ...userAction.data,
        created: new Date().toISOString()
      };

      mockDatabaseService.transaction.mockResolvedValue(mockDbResponse);
      mockApiService.post.mockResolvedValue(mockApiResponse);

      const processUserAction = async (action) => {
        // 1. Validate action
        if (!action.data.name || !action.data.type) {
          throw new Error('Invalid node data');
        }

        // 2. Send to API
        const apiResult = await mockApiService.post('/graph/nodes', action.data);

        // 3. API processes and stores in database
        const dbResult = await mockDatabaseService.transaction(async (tx) => {
          return await tx.run(
            'CREATE (n:Node $props) RETURN n',
            { props: action.data }
          );
        });

        return {
          success: true,
          node: apiResult,
          dbResult: dbResult.records[0].get('n').properties
        };
      };

      const result = await processUserAction(userAction);

      expect(mockApiService.post).toHaveBeenCalledWith('/graph/nodes', userAction.data);
      expect(mockDatabaseService.transaction).toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(result.node.id).toBe('new-id');
    });

    test('should handle rollback on transaction failure', async () => {
      const userAction = {
        type: 'CREATE_MULTIPLE_NODES',
        data: [
          { name: 'Node 1', type: 'Person' },
          { name: 'Node 2', type: 'Person' }
        ]
      };

      const transactionError = new Error('Constraint violation');
      mockDatabaseService.transaction.mockRejectedValue(transactionError);

      const processMultipleNodesWithRollback = async (action) => {
        try {
          const result = await mockDatabaseService.transaction(async (tx) => {
            const results = [];
            for (const nodeData of action.data) {
              const result = await tx.run(
                'CREATE (n:Node $props) RETURN n',
                { props: nodeData }
              );
              results.push(result);
            }
            return results;
          });

          return { success: true, results: result };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            rollback: true
          };
        }
      };

      const result = await processMultipleNodesWithRollback(userAction);

      expect(result.success).toBe(false);
      expect(result.rollback).toBe(true);
      expect(result.error).toBe('Constraint violation');
    });
  });
});
