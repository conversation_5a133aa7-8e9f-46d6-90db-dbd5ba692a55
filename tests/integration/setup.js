/**
 * Integration Test Setup
 * 
 * Setup for integration tests that require running services
 */

// Extend timeout for integration tests
jest.setTimeout(60000);

// Global setup for integration tests
beforeAll(async () => {
  // Check if services are running
  const checkService = async (url, name) => {
    try {
      const response = await fetch(url);
      console.log(`✅ ${name} service is running`);
      return true;
    } catch (error) {
      console.log(`⚠️  ${name} service is not running (tests will handle gracefully)`);
      return false;
    }
  };

  // Check all services
  await Promise.all([
    checkService('http://localhost:3002/api/health', 'API'),
    checkService('http://localhost:5173', 'Frontend'),
    checkService('http://localhost:7474', 'Neo4j'),
    checkService('http://localhost:3001/health', 'Proxy')
  ]);
});

// Global teardown
afterAll(async () => {
  // Cleanup any test data or connections
  console.log('Integration tests completed');
});
