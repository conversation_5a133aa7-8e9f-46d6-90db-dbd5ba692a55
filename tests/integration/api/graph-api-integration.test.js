/**
 * Graph API Integration Tests
 * 
 * Tests for graph API endpoints and service integration
 */

describe('Graph API Integration', () => {
  const API_BASE_URL = 'http://localhost:3002/api';
  
  // Mock HTTP client
  const mockHttpClient = {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Graph Data Endpoints', () => {
    test('should fetch initial graph data', async () => {
      const mockGraphData = {
        nodes: [
          { id: '1', name: 'Node 1', type: 'Person', properties: { age: 30 } },
          { id: '2', name: 'Node 2', type: 'Company', properties: { founded: 2020 } }
        ],
        edges: [
          { id: 'e1', source: '1', target: '2', type: 'WORKS_FOR', properties: { since: 2021 } }
        ],
        metadata: {
          nodeCount: 2,
          edgeCount: 1,
          nodeTypes: ['Person', 'Company'],
          edgeTypes: ['WORKS_FOR'],
          lastUpdated: '2023-12-01T10:00:00Z'
        }
      };

      mockHttpClient.get.mockResolvedValue(mockGraphData);

      const getInitialGraph = async (limit = 100) => {
        return await mockHttpClient.get(`${API_BASE_URL}/graph/initial`, { limit });
      };

      const result = await getInitialGraph(50);

      expect(mockHttpClient.get).toHaveBeenCalledWith(
        `${API_BASE_URL}/graph/initial`,
        { limit: 50 }
      );
      expect(result.nodes).toHaveLength(2);
      expect(result.edges).toHaveLength(1);
      expect(result.metadata.nodeCount).toBe(2);
    });

    test('should search graph with various parameters', async () => {
      const mockSearchResult = {
        nodes: [
          { id: '1', name: 'John Doe', type: 'Person' }
        ],
        edges: [],
        total: 1,
        query: 'John',
        executionTime: 45
      };

      mockHttpClient.get.mockResolvedValue(mockSearchResult);

      const searchGraph = async (params) => {
        return await mockHttpClient.get(`${API_BASE_URL}/graph/search`, params);
      };

      const searchParams = {
        query: 'John',
        nodeTypes: ['Person'],
        limit: 10
      };

      const result = await searchGraph(searchParams);

      expect(mockHttpClient.get).toHaveBeenCalledWith(
        `${API_BASE_URL}/graph/search`,
        searchParams
      );
      expect(result.total).toBe(1);
      expect(result.query).toBe('John');
      expect(result.executionTime).toBeLessThan(100);
    });

    test('should get node neighbors', async () => {
      const mockNeighborsData = {
        nodes: [
          { id: '1', name: 'Central Node', type: 'Person' },
          { id: '2', name: 'Neighbor 1', type: 'Person' },
          { id: '3', name: 'Neighbor 2', type: 'Company' }
        ],
        edges: [
          { id: 'e1', source: '1', target: '2', type: 'KNOWS' },
          { id: 'e2', source: '1', target: '3', type: 'WORKS_FOR' }
        ],
        metadata: {
          nodeCount: 3,
          edgeCount: 2
        }
      };

      mockHttpClient.get.mockResolvedValue(mockNeighborsData);

      const getNeighbors = async (nodeId, depth = 1) => {
        return await mockHttpClient.get(`${API_BASE_URL}/graph/neighbors`, {
          nodeId,
          depth,
          direction: 'both'
        });
      };

      const result = await getNeighbors('1', 2);

      expect(mockHttpClient.get).toHaveBeenCalledWith(
        `${API_BASE_URL}/graph/neighbors`,
        { nodeId: '1', depth: 2, direction: 'both' }
      );
      expect(result.nodes).toHaveLength(3);
      expect(result.edges).toHaveLength(2);
    });
  });

  describe('Node Operations', () => {
    test('should create new node', async () => {
      const newNode = {
        name: 'New Node',
        type: 'Person',
        properties: { age: 25, city: 'New York' }
      };

      const createdNode = {
        id: 'generated-id',
        ...newNode
      };

      mockHttpClient.post.mockResolvedValue(createdNode);

      const createNode = async (nodeData) => {
        return await mockHttpClient.post(`${API_BASE_URL}/graph/nodes`, nodeData);
      };

      const result = await createNode(newNode);

      expect(mockHttpClient.post).toHaveBeenCalledWith(
        `${API_BASE_URL}/graph/nodes`,
        newNode
      );
      expect(result.id).toBe('generated-id');
      expect(result.name).toBe('New Node');
    });

    test('should update existing node', async () => {
      const nodeId = 'existing-node-id';
      const updates = {
        name: 'Updated Name',
        properties: { age: 26 }
      };

      const updatedNode = {
        id: nodeId,
        name: 'Updated Name',
        type: 'Person',
        properties: { age: 26, city: 'New York' }
      };

      mockHttpClient.patch.mockResolvedValue(updatedNode);

      const updateNode = async (id, updates) => {
        return await mockHttpClient.patch(`${API_BASE_URL}/graph/nodes/${id}`, updates);
      };

      const result = await updateNode(nodeId, updates);

      expect(mockHttpClient.patch).toHaveBeenCalledWith(
        `${API_BASE_URL}/graph/nodes/${nodeId}`,
        updates
      );
      expect(result.name).toBe('Updated Name');
      expect(result.properties.age).toBe(26);
    });

    test('should delete node', async () => {
      const nodeId = 'node-to-delete';

      mockHttpClient.delete.mockResolvedValue(undefined);

      const deleteNode = async (id) => {
        return await mockHttpClient.delete(`${API_BASE_URL}/graph/nodes/${id}`);
      };

      await deleteNode(nodeId);

      expect(mockHttpClient.delete).toHaveBeenCalledWith(
        `${API_BASE_URL}/graph/nodes/${nodeId}`
      );
    });

    test('should get multiple nodes by IDs', async () => {
      const nodeIds = ['1', '2', '3'];
      const nodes = [
        { id: '1', name: 'Node 1', type: 'Person' },
        { id: '2', name: 'Node 2', type: 'Company' },
        { id: '3', name: 'Node 3', type: 'Product' }
      ];

      mockHttpClient.post.mockResolvedValue(nodes);

      const getNodes = async (ids) => {
        return await mockHttpClient.post(`${API_BASE_URL}/graph/nodes/batch`, { nodeIds: ids });
      };

      const result = await getNodes(nodeIds);

      expect(mockHttpClient.post).toHaveBeenCalledWith(
        `${API_BASE_URL}/graph/nodes/batch`,
        { nodeIds }
      );
      expect(result).toHaveLength(3);
      expect(result[0].id).toBe('1');
    });
  });

  describe('Edge Operations', () => {
    test('should create new edge', async () => {
      const newEdge = {
        source: 'node1',
        target: 'node2',
        type: 'CONNECTED_TO',
        properties: { weight: 0.8, created: '2023-12-01' }
      };

      const createdEdge = {
        id: 'generated-edge-id',
        ...newEdge
      };

      mockHttpClient.post.mockResolvedValue(createdEdge);

      const createEdge = async (edgeData) => {
        return await mockHttpClient.post(`${API_BASE_URL}/graph/edges`, edgeData);
      };

      const result = await createEdge(newEdge);

      expect(mockHttpClient.post).toHaveBeenCalledWith(
        `${API_BASE_URL}/graph/edges`,
        newEdge
      );
      expect(result.id).toBe('generated-edge-id');
      expect(result.type).toBe('CONNECTED_TO');
    });

    test('should update existing edge', async () => {
      const edgeId = 'existing-edge-id';
      const updates = {
        properties: { weight: 0.9, updated: '2023-12-02' }
      };

      const updatedEdge = {
        id: edgeId,
        source: 'node1',
        target: 'node2',
        type: 'CONNECTED_TO',
        properties: { weight: 0.9, updated: '2023-12-02' }
      };

      mockHttpClient.patch.mockResolvedValue(updatedEdge);

      const updateEdge = async (id, updates) => {
        return await mockHttpClient.patch(`${API_BASE_URL}/graph/edges/${id}`, updates);
      };

      const result = await updateEdge(edgeId, updates);

      expect(mockHttpClient.patch).toHaveBeenCalledWith(
        `${API_BASE_URL}/graph/edges/${edgeId}`,
        updates
      );
      expect(result.properties.weight).toBe(0.9);
    });
  });

  describe('Graph Schema and Statistics', () => {
    test('should get graph schema', async () => {
      const mockSchema = {
        nodeTypes: [
          {
            type: 'Person',
            count: 150,
            properties: [
              { name: 'name', type: 'string', required: true },
              { name: 'age', type: 'integer', required: false },
              { name: 'email', type: 'string', required: false }
            ]
          },
          {
            type: 'Company',
            count: 50,
            properties: [
              { name: 'name', type: 'string', required: true },
              { name: 'founded', type: 'integer', required: false }
            ]
          }
        ],
        edgeTypes: [
          {
            type: 'WORKS_FOR',
            count: 120,
            properties: [
              { name: 'since', type: 'integer', required: false },
              { name: 'position', type: 'string', required: false }
            ]
          }
        ]
      };

      mockHttpClient.get.mockResolvedValue(mockSchema);

      const getSchema = async () => {
        return await mockHttpClient.get(`${API_BASE_URL}/graph/schema`);
      };

      const result = await getSchema();

      expect(mockHttpClient.get).toHaveBeenCalledWith(`${API_BASE_URL}/graph/schema`);
      expect(result.nodeTypes).toHaveLength(2);
      expect(result.edgeTypes).toHaveLength(1);
      expect(result.nodeTypes[0].count).toBe(150);
    });

    test('should get graph statistics', async () => {
      const mockStats = {
        nodeCount: 200,
        edgeCount: 350,
        nodeTypes: {
          'Person': 150,
          'Company': 50
        },
        edgeTypes: {
          'WORKS_FOR': 120,
          'KNOWS': 180,
          'OWNS': 50
        },
        density: 0.0175,
        averageDegree: 3.5,
        lastUpdated: '2023-12-01T15:30:00Z'
      };

      mockHttpClient.get.mockResolvedValue(mockStats);

      const getStatistics = async () => {
        return await mockHttpClient.get(`${API_BASE_URL}/graph/statistics`);
      };

      const result = await getStatistics();

      expect(mockHttpClient.get).toHaveBeenCalledWith(`${API_BASE_URL}/graph/statistics`);
      expect(result.nodeCount).toBe(200);
      expect(result.edgeCount).toBe(350);
      expect(result.density).toBeCloseTo(0.0175);
    });
  });

  describe('Bulk Operations', () => {
    test('should handle bulk import', async () => {
      const bulkData = {
        nodes: [
          { name: 'Bulk Node 1', type: 'Person' },
          { name: 'Bulk Node 2', type: 'Company' }
        ],
        edges: [
          { source: 'bulk-1', target: 'bulk-2', type: 'WORKS_FOR' }
        ]
      };

      const importResult = {
        nodesCreated: 2,
        edgesCreated: 1,
        errors: []
      };

      mockHttpClient.post.mockResolvedValue(importResult);

      const bulkImport = async (data) => {
        return await mockHttpClient.post(`${API_BASE_URL}/graph/bulk-import`, data);
      };

      const result = await bulkImport(bulkData);

      expect(mockHttpClient.post).toHaveBeenCalledWith(
        `${API_BASE_URL}/graph/bulk-import`,
        bulkData
      );
      expect(result.nodesCreated).toBe(2);
      expect(result.edgesCreated).toBe(1);
      expect(result.errors).toHaveLength(0);
    });

    test('should handle bulk import with errors', async () => {
      const bulkData = {
        nodes: [
          { name: 'Valid Node', type: 'Person' },
          { /* missing required fields */ }
        ],
        edges: []
      };

      const importResult = {
        nodesCreated: 1,
        edgesCreated: 0,
        errors: [
          'Node at index 1: Missing required field "name"'
        ]
      };

      mockHttpClient.post.mockResolvedValue(importResult);

      const bulkImport = async (data) => {
        return await mockHttpClient.post(`${API_BASE_URL}/graph/bulk-import`, data);
      };

      const result = await bulkImport(bulkData);

      expect(result.nodesCreated).toBe(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain('Missing required field');
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors gracefully', async () => {
      const apiError = new Error('Internal Server Error');
      apiError.status = 500;
      apiError.response = {
        data: {
          message: 'Database connection failed',
          code: 'DATABASE_ERROR'
        }
      };

      mockHttpClient.get.mockRejectedValue(apiError);

      const getGraphWithErrorHandling = async () => {
        try {
          return await mockHttpClient.get(`${API_BASE_URL}/graph/initial`);
        } catch (error) {
          if (error.status === 500) {
            throw new Error('Service temporarily unavailable');
          }
          throw error;
        }
      };

      await expect(getGraphWithErrorHandling())
        .rejects.toThrow('Service temporarily unavailable');
    });

    test('should handle validation errors', async () => {
      const validationError = new Error('Validation Error');
      validationError.status = 400;
      validationError.response = {
        data: {
          message: 'Validation failed',
          details: [
            { field: 'name', message: 'Name is required' },
            { field: 'type', message: 'Type must be one of: Person, Company' }
          ]
        }
      };

      mockHttpClient.post.mockRejectedValue(validationError);

      const createNodeWithValidation = async (nodeData) => {
        try {
          return await mockHttpClient.post(`${API_BASE_URL}/graph/nodes`, nodeData);
        } catch (error) {
          if (error.status === 400) {
            const details = error.response.data.details;
            const messages = details.map(d => `${d.field}: ${d.message}`);
            throw new Error(`Validation failed: ${messages.join(', ')}`);
          }
          throw error;
        }
      };

      await expect(createNodeWithValidation({ /* invalid data */ }))
        .rejects.toThrow('Validation failed: name: Name is required, type: Type must be one of: Person, Company');
    });
  });
});
