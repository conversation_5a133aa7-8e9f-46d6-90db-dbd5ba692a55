/**
 * API Integration Tests
 * 
 * Tests for API endpoints and service integration
 */

const http = require('http');

describe('API Integration Tests', () => {
  const API_BASE_URL = 'http://localhost:3002';
  
  // Helper function to make HTTP requests
  const makeRequest = (path, options = {}) => {
    return new Promise((resolve, reject) => {
      const url = new URL(path, API_BASE_URL);
      const requestOptions = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname + url.search,
        method: options.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        timeout: 10000
      };

      const req = http.request(requestOptions, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            const jsonData = data ? JSON.parse(data) : {};
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              data: jsonData
            });
          } catch (error) {
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              data: data
            });
          }
        });
      });

      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      if (options.body) {
        req.write(JSON.stringify(options.body));
      }
      
      req.end();
    });
  };

  describe('Health Endpoints', () => {
    test('should respond to health check', async () => {
      try {
        const response = await makeRequest('/api/health');
        expect(response.statusCode).toBeLessThan(500);
      } catch (error) {
        // Service may not be running, which is acceptable for this test
        expect(error.message).toMatch(/ECONNREFUSED|Request timeout/);
      }
    });
  });

  describe('Graph Endpoints', () => {
    test('should handle graph initial request', async () => {
      try {
        const response = await makeRequest('/api/graph/initial');
        expect(response.statusCode).toBeLessThan(500);
        
        if (response.statusCode === 200) {
          expect(response.data).toBeDefined();
        }
      } catch (error) {
        expect(error.message).toMatch(/ECONNREFUSED|Request timeout/);
      }
    });

    test('should handle graph search request', async () => {
      try {
        const response = await makeRequest('/api/graph/search?query=test');
        expect(response.statusCode).toBeLessThan(500);
      } catch (error) {
        expect(error.message).toMatch(/ECONNREFUSED|Request timeout/);
      }
    });
  });

  describe('Analysis Endpoints', () => {
    test('should handle clusters request', async () => {
      try {
        const response = await makeRequest('/api/analysis/clusters');
        expect(response.statusCode).toBeLessThan(500);
      } catch (error) {
        expect(error.message).toMatch(/ECONNREFUSED|Request timeout/);
      }
    });

    test('should handle hidden links request', async () => {
      try {
        const response = await makeRequest('/api/analysis/hidden-links');
        expect(response.statusCode).toBeLessThan(500);
      } catch (error) {
        expect(error.message).toMatch(/ECONNREFUSED|Request timeout/);
      }
    });

    test('should handle centrality request', async () => {
      try {
        const response = await makeRequest('/api/analysis/centrality');
        expect(response.statusCode).toBeLessThan(500);
      } catch (error) {
        expect(error.message).toMatch(/ECONNREFUSED|Request timeout/);
      }
    });
  });

  describe('Settings Endpoints', () => {
    test('should handle settings GET request', async () => {
      try {
        const response = await makeRequest('/api/settings');
        expect(response.statusCode).toBeLessThan(500);
      } catch (error) {
        expect(error.message).toMatch(/ECONNREFUSED|Request timeout/);
      }
    });

    test('should handle settings POST request', async () => {
      try {
        const response = await makeRequest('/api/settings', {
          method: 'POST',
          body: { testSetting: 'testValue' }
        });
        expect(response.statusCode).toBeLessThan(500);
      } catch (error) {
        expect(error.message).toMatch(/ECONNREFUSED|Request timeout/);
      }
    });
  });

  describe('Chat Endpoints', () => {
    test('should handle chat request', async () => {
      try {
        const response = await makeRequest('/api/chat', {
          method: 'POST',
          body: { message: 'test message' }
        });
        expect(response.statusCode).toBeLessThan(500);
      } catch (error) {
        expect(error.message).toMatch(/ECONNREFUSED|Request timeout/);
      }
    });
  });

  describe('Error Handling', () => {
    test('should handle 404 for unknown endpoints', async () => {
      try {
        const response = await makeRequest('/api/nonexistent');
        expect([404, 500]).toContain(response.statusCode);
      } catch (error) {
        expect(error.message).toMatch(/ECONNREFUSED|Request timeout/);
      }
    });

    test('should handle malformed requests', async () => {
      try {
        const response = await makeRequest('/api/settings', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: 'invalid json'
        });
        expect(response.statusCode).toBeGreaterThanOrEqual(400);
      } catch (error) {
        expect(error.message).toMatch(/ECONNREFUSED|Request timeout/);
      }
    });
  });
});
