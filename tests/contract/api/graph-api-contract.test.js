/**
 * Graph API Contract Tests
 * 
 * Tests to ensure API contracts are maintained during refactoring
 */

describe('Graph API Contract Tests', () => {
  // Contract definitions
  const GraphNodeContract = {
    required: ['id', 'name', 'type'],
    optional: ['properties', 'x', 'y', 'size', 'color'],
    types: {
      id: 'string',
      name: 'string',
      type: 'string',
      properties: 'object',
      x: 'number',
      y: 'number',
      size: 'number',
      color: 'string'
    }
  };

  const GraphEdgeContract = {
    required: ['id', 'source', 'target', 'type'],
    optional: ['properties', 'weight', 'color'],
    types: {
      id: 'string',
      source: 'string',
      target: 'string',
      type: 'string',
      properties: 'object',
      weight: 'number',
      color: 'string'
    }
  };

  const GraphDataContract = {
    required: ['nodes', 'edges', 'metadata'],
    types: {
      nodes: 'array',
      edges: 'array',
      metadata: 'object'
    }
  };

  const GraphMetadataContract = {
    required: ['nodeCount', 'edgeCount', 'nodeTypes', 'edgeTypes', 'lastUpdated'],
    types: {
      nodeCount: 'number',
      edgeCount: 'number',
      nodeTypes: 'array',
      edgeTypes: 'array',
      lastUpdated: 'string'
    }
  };

  // Contract validation helper
  const validateContract = (data, contract) => {
    const errors = [];

    // Check required fields
    for (const field of contract.required) {
      if (!(field in data)) {
        errors.push(`Missing required field: ${field}`);
      }
    }

    // Check field types
    for (const [field, expectedType] of Object.entries(contract.types)) {
      if (field in data) {
        const actualType = Array.isArray(data[field]) ? 'array' : typeof data[field];
        if (actualType !== expectedType) {
          errors.push(`Field ${field} should be ${expectedType}, got ${actualType}`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  };

  describe('GET /api/graph/initial Contract', () => {
    test('should return data matching GraphData contract', () => {
      const mockResponse = {
        nodes: [
          {
            id: '1',
            name: 'Test Node',
            type: 'Person',
            properties: { age: 30 },
            x: 100,
            y: 200
          }
        ],
        edges: [
          {
            id: 'e1',
            source: '1',
            target: '2',
            type: 'KNOWS',
            weight: 0.8
          }
        ],
        metadata: {
          nodeCount: 1,
          edgeCount: 1,
          nodeTypes: ['Person'],
          edgeTypes: ['KNOWS'],
          lastUpdated: '2023-12-01T10:00:00Z'
        }
      };

      // Validate main response contract
      const responseValidation = validateContract(mockResponse, GraphDataContract);
      expect(responseValidation.valid).toBe(true);
      expect(responseValidation.errors).toHaveLength(0);

      // Validate node contract
      const nodeValidation = validateContract(mockResponse.nodes[0], GraphNodeContract);
      expect(nodeValidation.valid).toBe(true);

      // Validate edge contract
      const edgeValidation = validateContract(mockResponse.edges[0], GraphEdgeContract);
      expect(edgeValidation.valid).toBe(true);

      // Validate metadata contract
      const metadataValidation = validateContract(mockResponse.metadata, GraphMetadataContract);
      expect(metadataValidation.valid).toBe(true);
    });

    test('should reject invalid node data', () => {
      const invalidNode = {
        // Missing required 'id' field
        name: 'Test Node',
        type: 'Person',
        properties: { age: 'thirty' } // Wrong type for age
      };

      const validation = validateContract(invalidNode, GraphNodeContract);
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Missing required field: id');
    });

    test('should accept optional fields', () => {
      const minimalNode = {
        id: '1',
        name: 'Minimal Node',
        type: 'Person'
        // No optional fields
      };

      const validation = validateContract(minimalNode, GraphNodeContract);
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });
  });

  describe('POST /api/graph/nodes Contract', () => {
    const CreateNodeRequestContract = {
      required: ['name', 'type'],
      optional: ['properties'],
      types: {
        name: 'string',
        type: 'string',
        properties: 'object'
      }
    };

    const CreateNodeResponseContract = {
      required: ['id', 'name', 'type'],
      optional: ['properties', 'created'],
      types: {
        id: 'string',
        name: 'string',
        type: 'string',
        properties: 'object',
        created: 'string'
      }
    };

    test('should accept valid create node request', () => {
      const validRequest = {
        name: 'New Node',
        type: 'Person',
        properties: {
          age: 25,
          city: 'New York'
        }
      };

      const validation = validateContract(validRequest, CreateNodeRequestContract);
      expect(validation.valid).toBe(true);
    });

    test('should return valid create node response', () => {
      const mockResponse = {
        id: 'generated-id-123',
        name: 'New Node',
        type: 'Person',
        properties: {
          age: 25,
          city: 'New York'
        },
        created: '2023-12-01T10:00:00Z'
      };

      const validation = validateContract(mockResponse, CreateNodeResponseContract);
      expect(validation.valid).toBe(true);
    });

    test('should reject invalid create node request', () => {
      const invalidRequest = {
        // Missing required 'name' field
        type: 'Person',
        properties: {
          age: 25
        }
      };

      const validation = validateContract(invalidRequest, CreateNodeRequestContract);
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Missing required field: name');
    });
  });

  describe('GET /api/graph/search Contract', () => {
    const SearchRequestContract = {
      required: [],
      optional: ['query', 'nodeTypes', 'edgeTypes', 'properties', 'limit', 'offset'],
      types: {
        query: 'string',
        nodeTypes: 'array',
        edgeTypes: 'array',
        properties: 'object',
        limit: 'number',
        offset: 'number'
      }
    };

    const SearchResponseContract = {
      required: ['nodes', 'edges', 'total', 'query', 'executionTime'],
      types: {
        nodes: 'array',
        edges: 'array',
        total: 'number',
        query: 'string',
        executionTime: 'number'
      }
    };

    test('should accept valid search parameters', () => {
      const validParams = {
        query: 'John',
        nodeTypes: ['Person', 'Company'],
        limit: 10,
        offset: 0
      };

      const validation = validateContract(validParams, SearchRequestContract);
      expect(validation.valid).toBe(true);
    });

    test('should return valid search response', () => {
      const mockResponse = {
        nodes: [
          { id: '1', name: 'John Doe', type: 'Person' }
        ],
        edges: [],
        total: 1,
        query: 'John',
        executionTime: 45
      };

      const validation = validateContract(mockResponse, SearchResponseContract);
      expect(validation.valid).toBe(true);
    });

    test('should handle empty search results', () => {
      const emptyResponse = {
        nodes: [],
        edges: [],
        total: 0,
        query: 'nonexistent',
        executionTime: 12
      };

      const validation = validateContract(emptyResponse, SearchResponseContract);
      expect(validation.valid).toBe(true);
    });
  });

  describe('Error Response Contract', () => {
    const ErrorResponseContract = {
      required: ['error', 'message', 'statusCode'],
      optional: ['details', 'timestamp', 'requestId'],
      types: {
        error: 'string',
        message: 'string',
        statusCode: 'number',
        details: 'array',
        timestamp: 'string',
        requestId: 'string'
      }
    };

    test('should return valid error response for 400 errors', () => {
      const validationError = {
        error: 'VALIDATION_ERROR',
        message: 'Invalid input data',
        statusCode: 400,
        details: [
          { field: 'name', message: 'Name is required' },
          { field: 'type', message: 'Type must be one of: Person, Company' }
        ],
        timestamp: '2023-12-01T10:00:00Z',
        requestId: 'req-123'
      };

      const validation = validateContract(validationError, ErrorResponseContract);
      expect(validation.valid).toBe(true);
    });

    test('should return valid error response for 500 errors', () => {
      const serverError = {
        error: 'INTERNAL_SERVER_ERROR',
        message: 'An internal server error occurred',
        statusCode: 500,
        timestamp: '2023-12-01T10:00:00Z',
        requestId: 'req-456'
      };

      const validation = validateContract(serverError, ErrorResponseContract);
      expect(validation.valid).toBe(true);
    });
  });

  describe('Backward Compatibility', () => {
    test('should maintain compatibility with legacy node format', () => {
      // Legacy format that should still be supported
      const legacyNode = {
        id: '1',
        name: 'Legacy Node',
        type: 'Person',
        // Legacy field that might be deprecated but still supported
        label: 'Person',
        // Properties might be flattened in legacy format
        age: 30,
        city: 'New York'
      };

      // Should still validate against core contract
      const coreValidation = validateContract(legacyNode, GraphNodeContract);
      expect(coreValidation.valid).toBe(true);

      // Additional legacy fields should not break validation
      expect(legacyNode.label).toBe('Person');
      expect(legacyNode.age).toBe(30);
    });

    test('should handle version-specific response formats', () => {
      const v1Response = {
        nodes: [],
        edges: [],
        metadata: {
          nodeCount: 0,
          edgeCount: 0,
          nodeTypes: [],
          edgeTypes: [],
          lastUpdated: '2023-12-01T10:00:00Z'
        }
      };

      const v2Response = {
        ...v1Response,
        // V2 might add additional metadata
        metadata: {
          ...v1Response.metadata,
          version: '2.0',
          performance: {
            queryTime: 45,
            renderTime: 12
          }
        }
      };

      // Both versions should validate against base contract
      expect(validateContract(v1Response, GraphDataContract).valid).toBe(true);
      expect(validateContract(v2Response, GraphDataContract).valid).toBe(true);

      // V2 specific fields should be present
      expect(v2Response.metadata.version).toBe('2.0');
      expect(v2Response.metadata.performance).toBeDefined();
    });
  });

  describe('API Versioning Contract', () => {
    test('should support API version headers', () => {
      const apiVersions = ['1.0', '1.1', '2.0'];
      
      for (const version of apiVersions) {
        const requestHeaders = {
          'Accept': 'application/json',
          'API-Version': version
        };

        // All versions should be supported
        expect(requestHeaders['API-Version']).toMatch(/^\d+\.\d+$/);
      }
    });

    test('should handle version-specific feature availability', () => {
      const featureMatrix = {
        '1.0': ['basic_graph', 'search'],
        '1.1': ['basic_graph', 'search', 'analysis'],
        '2.0': ['basic_graph', 'search', 'analysis', 'real_time_updates']
      };

      // Each version should support at least the features of previous versions
      expect(featureMatrix['1.1']).toEqual(expect.arrayContaining(featureMatrix['1.0']));
      expect(featureMatrix['2.0']).toEqual(expect.arrayContaining(featureMatrix['1.1']));
    });
  });
});
