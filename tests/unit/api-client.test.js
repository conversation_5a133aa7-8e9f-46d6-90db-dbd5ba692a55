/**
 * API Client Unit Tests
 * 
 * Tests for the unified API client system
 */

describe('Unified API Client', () => {
  // Mock the API client since we don't have TypeScript compilation
  const mockApiClient = {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
    healthCheck: jest.fn()
  };

  const mockApiError = class {
    constructor(message, code, statusCode) {
      this.message = message;
      this.code = code;
      this.statusCode = statusCode;
      this.timestamp = new Date().toISOString();
    }

    isRetryable() {
      const retryableCodes = ['NETWORK_ERROR', 'TIMEOUT_ERROR', 'TOO_MANY_REQUESTS', 'INTERNAL_SERVER_ERROR'];
      return retryableCodes.includes(this.code);
    }

    isClientError() {
      return this.statusCode >= 400 && this.statusCode < 500;
    }

    isServerError() {
      return this.statusCode >= 500 && this.statusCode < 600;
    }

    getUserMessage() {
      switch (this.code) {
        case 'NETWORK_ERROR':
          return 'Unable to connect to the server. Please check your internet connection.';
        case 'TIMEOUT_ERROR':
          return 'The request took too long to complete. Please try again.';
        case 'UNAUTHORIZED':
          return 'You are not authorized to perform this action. Please log in.';
        default:
          return this.message || 'An unexpected error occurred. Please try again.';
      }
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic HTTP Methods', () => {
    test('should make GET requests', async () => {
      const mockResponse = { data: 'test' };
      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await mockApiClient.get('/test');
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/test');
      expect(result).toEqual(mockResponse);
    });

    test('should make POST requests with data', async () => {
      const mockData = { name: 'test' };
      const mockResponse = { id: 1, ...mockData };
      mockApiClient.post.mockResolvedValue(mockResponse);

      const result = await mockApiClient.post('/test', mockData);
      
      expect(mockApiClient.post).toHaveBeenCalledWith('/test', mockData);
      expect(result).toEqual(mockResponse);
    });

    test('should make PUT requests', async () => {
      const mockData = { id: 1, name: 'updated' };
      mockApiClient.put.mockResolvedValue(mockData);

      const result = await mockApiClient.put('/test/1', mockData);
      
      expect(mockApiClient.put).toHaveBeenCalledWith('/test/1', mockData);
      expect(result).toEqual(mockData);
    });

    test('should make PATCH requests', async () => {
      const mockData = { name: 'patched' };
      mockApiClient.patch.mockResolvedValue(mockData);

      const result = await mockApiClient.patch('/test/1', mockData);
      
      expect(mockApiClient.patch).toHaveBeenCalledWith('/test/1', mockData);
      expect(result).toEqual(mockData);
    });

    test('should make DELETE requests', async () => {
      mockApiClient.delete.mockResolvedValue(undefined);

      await mockApiClient.delete('/test/1');
      
      expect(mockApiClient.delete).toHaveBeenCalledWith('/test/1');
    });
  });

  describe('Error Handling', () => {
    test('should handle network errors', () => {
      const error = new mockApiError('Network error', 'NETWORK_ERROR', 0);
      
      expect(error.isRetryable()).toBe(true);
      expect(error.getUserMessage()).toBe('Unable to connect to the server. Please check your internet connection.');
    });

    test('should handle timeout errors', () => {
      const error = new mockApiError('Timeout', 'TIMEOUT_ERROR', 0);
      
      expect(error.isRetryable()).toBe(true);
      expect(error.getUserMessage()).toBe('The request took too long to complete. Please try again.');
    });

    test('should handle client errors (4xx)', () => {
      const error = new mockApiError('Unauthorized', 'UNAUTHORIZED', 401);
      
      expect(error.isClientError()).toBe(true);
      expect(error.isServerError()).toBe(false);
      expect(error.isRetryable()).toBe(false);
    });

    test('should handle server errors (5xx)', () => {
      const error = new mockApiError('Internal Server Error', 'INTERNAL_SERVER_ERROR', 500);
      
      expect(error.isServerError()).toBe(true);
      expect(error.isClientError()).toBe(false);
      expect(error.isRetryable()).toBe(true);
    });

    test('should provide user-friendly error messages', () => {
      const unauthorizedError = new mockApiError('Unauthorized', 'UNAUTHORIZED', 401);
      expect(unauthorizedError.getUserMessage()).toBe('You are not authorized to perform this action. Please log in.');

      const networkError = new mockApiError('Network error', 'NETWORK_ERROR', 0);
      expect(networkError.getUserMessage()).toBe('Unable to connect to the server. Please check your internet connection.');
    });
  });

  describe('Health Check', () => {
    test('should perform health check successfully', async () => {
      const mockHealthResponse = { status: 'healthy', timestamp: new Date().toISOString() };
      mockApiClient.healthCheck.mockResolvedValue(mockHealthResponse);

      const result = await mockApiClient.healthCheck();
      
      expect(result.status).toBe('healthy');
      expect(result.timestamp).toBeDefined();
    });

    test('should handle health check failure', async () => {
      const error = new mockApiError('Service unavailable', 'SERVICE_UNAVAILABLE', 503);
      mockApiClient.healthCheck.mockRejectedValue(error);

      try {
        await mockApiClient.healthCheck();
        fail('Should have thrown an error');
      } catch (err) {
        expect(err.code).toBe('SERVICE_UNAVAILABLE');
        expect(err.statusCode).toBe(503);
      }
    });
  });

  describe('Request Configuration', () => {
    test('should handle custom headers', async () => {
      const customHeaders = { 'X-Custom-Header': 'test-value' };
      mockApiClient.get.mockResolvedValue({ data: 'test' });

      await mockApiClient.get('/test', {}, { headers: customHeaders });
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/test', {}, { headers: customHeaders });
    });

    test('should handle timeout configuration', async () => {
      const options = { timeout: 5000 };
      mockApiClient.get.mockResolvedValue({ data: 'test' });

      await mockApiClient.get('/test', {}, options);
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/test', {}, options);
    });

    test('should handle retry configuration', async () => {
      const options = { retries: 5 };
      mockApiClient.get.mockResolvedValue({ data: 'test' });

      await mockApiClient.get('/test', {}, options);
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/test', {}, options);
    });
  });

  describe('Service-Specific APIs', () => {
    const mockGraphApi = {
      getInitialGraph: jest.fn(),
      searchGraph: jest.fn(),
      getNode: jest.fn(),
      getNeighbors: jest.fn()
    };

    const mockAnalysisApi = {
      getClusters: jest.fn(),
      getHiddenLinks: jest.fn(),
      getCentrality: jest.fn(),
      findPaths: jest.fn()
    };

    test('should handle graph API operations', async () => {
      const mockGraphData = {
        nodes: [{ id: '1', name: 'Node 1', type: 'test' }],
        edges: [{ id: '1', source: '1', target: '2', type: 'test' }],
        metadata: { nodeCount: 1, edgeCount: 1 }
      };

      mockGraphApi.getInitialGraph.mockResolvedValue(mockGraphData);

      const result = await mockGraphApi.getInitialGraph();
      
      expect(result.nodes).toHaveLength(1);
      expect(result.edges).toHaveLength(1);
      expect(result.metadata.nodeCount).toBe(1);
    });

    test('should handle analysis API operations', async () => {
      const mockClusters = {
        clusters: [
          { id: 'cluster1', name: 'Cluster 1', size: 10, nodes: ['1', '2', '3'] }
        ],
        modularity: 0.8,
        totalClusters: 1
      };

      mockAnalysisApi.getClusters.mockResolvedValue(mockClusters);

      const result = await mockAnalysisApi.getClusters();
      
      expect(result.clusters).toHaveLength(1);
      expect(result.modularity).toBe(0.8);
      expect(result.totalClusters).toBe(1);
    });

    test('should handle search operations', async () => {
      const mockSearchResult = {
        nodes: [{ id: '1', name: 'Test Node', type: 'test' }],
        edges: [],
        total: 1,
        query: 'test',
        executionTime: 50
      };

      mockGraphApi.searchGraph.mockResolvedValue(mockSearchResult);

      const result = await mockGraphApi.searchGraph({ query: 'test' });
      
      expect(result.total).toBe(1);
      expect(result.query).toBe('test');
      expect(result.executionTime).toBeDefined();
    });
  });

  describe('Interceptors', () => {
    test('should handle logging interceptor', () => {
      const mockLoggingInterceptor = {
        name: 'logging',
        priority: 100,
        onRequest: jest.fn((config) => {
          console.log(`Request: ${config.method} ${config.url}`);
          return config;
        }),
        onResponse: jest.fn((response, data) => {
          console.log(`Response: ${response.status}`);
          return { response, data };
        })
      };

      const config = { method: 'GET', url: '/test', headers: {} };
      const processedConfig = mockLoggingInterceptor.onRequest(config);
      
      expect(mockLoggingInterceptor.onRequest).toHaveBeenCalledWith(config);
      expect(processedConfig).toEqual(config);
    });

    test('should handle retry interceptor', () => {
      const mockRetryInterceptor = {
        name: 'retry',
        priority: 80,
        onError: jest.fn((error) => {
          if (error.isRetryable && error.isRetryable()) {
            // Add retry metadata
            return { ...error, retryAttempt: 1 };
          }
          return error;
        })
      };

      const retryableError = new mockApiError('Network error', 'NETWORK_ERROR', 0);
      const processedError = mockRetryInterceptor.onError(retryableError);
      
      expect(mockRetryInterceptor.onError).toHaveBeenCalledWith(retryableError);
      expect(processedError.retryAttempt).toBe(1);
    });
  });
});
