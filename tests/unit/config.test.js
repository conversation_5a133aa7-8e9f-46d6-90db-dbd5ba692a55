/**
 * Configuration System Unit Tests
 * 
 * Tests for the legacy configuration adapter and validation system
 */

describe('Configuration System', () => {
  let originalEnv;
  
  beforeEach(() => {
    // Save original environment
    originalEnv = { ...process.env };
    
    // Clear environment variables
    delete process.env.NEO4J_USER;
    delete process.env.NEO4J_USERNAME;
    delete process.env.PORT;
    delete process.env.API_PORT;
    delete process.env.PROXY_PORT;
    delete process.env.PROXY_SERVER_PORT;
  });
  
  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
  });
  
  describe('Legacy Configuration Adapter', () => {
    // Mock the legacy adapter since we don't have TypeScript compilation
    const mockLegacyAdapter = {
      adapt: () => {
        const mappings = {
          'NEO4J_USER': 'NEO4J_USERNAME',
          'PORT': 'API_PORT',
          'PROXY_PORT': 'PROXY_SERVER_PORT'
        };

        Object.entries(mappings).forEach(([oldKey, newKey]) => {
          if (process.env[oldKey] && !process.env[newKey]) {
            process.env[newKey] = process.env[oldKey];
          }
        });
      },

      validate: () => {
        const errors = [];
        const requiredVars = ['NEO4J_URI', 'NEO4J_USERNAME', 'NEO4J_PASSWORD'];
        
        requiredVars.forEach(varName => {
          if (!process.env[varName]) {
            errors.push(`Missing required environment variable: ${varName}`);
          }
        });
        
        return errors;
      },

      getMigrationStatus: () => {
        const legacyVars = ['NEO4J_USER', 'PORT', 'PROXY_PORT'];
        const newVars = ['NEO4J_USERNAME', 'API_PORT', 'PROXY_SERVER_PORT'];
        
        const legacyVariablesFound = legacyVars.filter(v => process.env[v]);
        const newVariablesFound = newVars.filter(v => process.env[v]);
        
        return {
          legacyVariablesFound,
          newVariablesFound,
          migrationComplete: legacyVariablesFound.length === 0
        };
      }
    };
    
    test('should adapt legacy environment variables', () => {
      // Set legacy variables
      process.env.NEO4J_USER = 'test_user';
      process.env.PORT = '3333';
      process.env.PROXY_PORT = '3001';
      
      // Run adapter
      mockLegacyAdapter.adapt();
      
      // Check that new variables are set
      expect(process.env.NEO4J_USERNAME).toBe('test_user');
      expect(process.env.API_PORT).toBe('3333');
      expect(process.env.PROXY_SERVER_PORT).toBe('3001');
      
      // Check that legacy variables are still present
      expect(process.env.NEO4J_USER).toBe('test_user');
      expect(process.env.PORT).toBe('3333');
      expect(process.env.PROXY_PORT).toBe('3001');
    });
    
    test('should not override existing new variables', () => {
      // Set both legacy and new variables
      process.env.NEO4J_USER = 'legacy_user';
      process.env.NEO4J_USERNAME = 'new_user';
      
      // Run adapter
      mockLegacyAdapter.adapt();
      
      // New variable should not be overridden
      expect(process.env.NEO4J_USERNAME).toBe('new_user');
    });
    
    test('should validate required environment variables', () => {
      // Set required variables
      process.env.NEO4J_URI = 'neo4j://localhost:7687';
      process.env.NEO4J_USERNAME = 'neo4j';
      process.env.NEO4J_PASSWORD = 'password';
      
      const errors = mockLegacyAdapter.validate();
      expect(errors).toHaveLength(0);
    });
    
    test('should return errors for missing required variables', () => {
      // Don't set required variables
      const errors = mockLegacyAdapter.validate();
      
      expect(errors).toHaveLength(3);
      expect(errors).toContain('Missing required environment variable: NEO4J_URI');
      expect(errors).toContain('Missing required environment variable: NEO4J_USERNAME');
      expect(errors).toContain('Missing required environment variable: NEO4J_PASSWORD');
    });
    
    test('should detect migration status correctly', () => {
      // Set legacy variables
      process.env.NEO4J_USER = 'test_user';
      process.env.PORT = '3333';
      
      const status = mockLegacyAdapter.getMigrationStatus();
      
      expect(status.legacyVariablesFound).toContain('NEO4J_USER');
      expect(status.legacyVariablesFound).toContain('PORT');
      expect(status.migrationComplete).toBe(false);
    });
    
    test('should detect completed migration', () => {
      // Set only new variables
      process.env.NEO4J_USERNAME = 'test_user';
      process.env.API_PORT = '3333';
      
      const status = mockLegacyAdapter.getMigrationStatus();
      
      expect(status.legacyVariablesFound).toHaveLength(0);
      expect(status.newVariablesFound).toContain('NEO4J_USERNAME');
      expect(status.newVariablesFound).toContain('API_PORT');
      expect(status.migrationComplete).toBe(true);
    });
  });
  
  describe('Configuration Validation', () => {
    test('should validate complete configuration', () => {
      const config = testUtils.createTestConfig();
      expect(config).toHaveValidConfig();
    });
    
    test('should detect invalid configuration', () => {
      const config = { environment: 'test' }; // Missing required fields
      expect(config).not.toHaveValidConfig();
    });
  });
  
  describe('Environment Variable Mappings', () => {
    test('should handle database configuration', () => {
      process.env.NEO4J_URI = 'neo4j://localhost:7687';
      process.env.NEO4J_USERNAME = 'neo4j';
      process.env.NEO4J_PASSWORD = 'password';
      process.env.NEO4J_DATABASE = 'test';
      
      expect(process.env.NEO4J_URI).toBe('neo4j://localhost:7687');
      expect(process.env.NEO4J_USERNAME).toBe('neo4j');
      expect(process.env.NEO4J_PASSWORD).toBe('password');
      expect(process.env.NEO4J_DATABASE).toBe('test');
    });
    
    test('should handle API configuration', () => {
      process.env.API_PORT = '3002';
      process.env.CORS_ORIGINS = 'http://localhost:3000,http://localhost:5173';
      
      expect(process.env.API_PORT).toBe('3002');
      expect(process.env.CORS_ORIGINS).toBe('http://localhost:3000,http://localhost:5173');
    });
    
    test('should handle LLM configuration', () => {
      process.env.LLM_PRIMARY_PROVIDER = 'ollama';
      process.env.LLM_OLLAMA_BASE_URL = 'http://localhost:11434';
      process.env.LLM_ANTHROPIC_API_KEY = 'test_key';
      
      expect(process.env.LLM_PRIMARY_PROVIDER).toBe('ollama');
      expect(process.env.LLM_OLLAMA_BASE_URL).toBe('http://localhost:11434');
      expect(process.env.LLM_ANTHROPIC_API_KEY).toBe('test_key');
    });
  });
  
  describe('Configuration Loading', () => {
    test('should load configuration with defaults', () => {
      // Set minimal required variables
      process.env.NEO4J_URI = 'neo4j://localhost:7687';
      process.env.NEO4J_USERNAME = 'neo4j';
      process.env.NEO4J_PASSWORD = 'password';
      
      const config = testUtils.createTestConfig();
      
      expect(config.environment).toBe('test');
      expect(config.database.uri).toBe('neo4j://localhost:7687');
      expect(config.database.username).toBe('neo4j');
      expect(config.database.password).toBe('test_password'); // From test setup
    });
    
    test('should override defaults with environment variables', () => {
      process.env.NODE_ENV = 'production';
      process.env.API_PORT = '8080';
      
      // In a real implementation, this would use the actual config loader
      expect(process.env.NODE_ENV).toBe('production');
      expect(process.env.API_PORT).toBe('8080');
    });
  });
});
