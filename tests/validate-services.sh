#!/bin/bash
set -e

# Service Validation Test Suite
# Comprehensive validation of all services for refactoring safety

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_RUN=$((TESTS_RUN + 1))
    
    log_info "Running test: $test_name"
    
    if eval "$test_command" >/dev/null 2>&1; then
        log_success "PASSED: $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "FAILED: $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Function to test API endpoints
test_api_endpoints() {
    log_info "Testing API endpoints..."
    
    # Test health endpoint
    run_test "API Health Endpoint" "curl -f http://localhost:3002/api/health"
    
    # Test graph endpoints
    run_test "Graph Initial Endpoint" "curl -f http://localhost:3002/api/graph/initial"
    
    # Test analysis endpoints
    run_test "Analysis Clusters Endpoint" "curl -f http://localhost:3002/api/analysis/clusters"
    
    # Test settings endpoints
    run_test "Settings Endpoint" "curl -f http://localhost:3002/api/settings"
    
    # Test chat endpoints (may fail if not configured)
    if curl -f http://localhost:3002/api/chat >/dev/null 2>&1; then
        run_test "Chat Endpoint" "curl -f http://localhost:3002/api/chat"
    else
        log_warning "Chat endpoint not available (expected if not configured)"
    fi
}

# Function to test database connectivity
test_database() {
    log_info "Testing database connectivity..."
    
    # Test Neo4j HTTP interface
    run_test "Neo4j HTTP Interface" "curl -f http://localhost:7474"
    
    # Test Neo4j Cypher connectivity
    run_test "Neo4j Cypher Connection" "docker exec kg_neo4j cypher-shell -u neo4j -p development_password 'RETURN 1'"
    
    # Test basic query
    run_test "Neo4j Basic Query" "docker exec kg_neo4j cypher-shell -u neo4j -p development_password 'MATCH (n) RETURN count(n) LIMIT 1'"
    
    # Test GDS procedures (if available)
    if docker exec kg_neo4j cypher-shell -u neo4j -p development_password "CALL dbms.procedures() YIELD name WHERE name STARTS WITH 'gds' RETURN count(name) as gds_count" 2>/dev/null | grep -q "gds_count"; then
        run_test "Neo4j GDS Procedures" "docker exec kg_neo4j cypher-shell -u neo4j -p development_password 'CALL dbms.procedures() YIELD name WHERE name STARTS WITH \"gds\" RETURN count(name) as gds_count'"
    else
        log_warning "GDS procedures not available (expected in community edition)"
    fi
}

# Function to test frontend
test_frontend() {
    log_info "Testing frontend service..."
    
    # Test frontend accessibility
    run_test "Frontend Accessibility" "curl -f http://localhost:5173"
    
    # Test if it's serving HTML
    if curl -s http://localhost:5173 | grep -q "<html"; then
        log_success "Frontend serving HTML content"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_error "Frontend not serving HTML content"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
    
    # Test static assets (if available)
    if curl -f http://localhost:5173/assets >/dev/null 2>&1; then
        run_test "Frontend Static Assets" "curl -f http://localhost:5173/assets"
    else
        log_warning "Frontend static assets not available (expected in dev mode)"
    fi
}

# Function to test proxy service
test_proxy() {
    log_info "Testing proxy service..."
    
    # Test proxy health
    if curl -f http://localhost:3001/health >/dev/null 2>&1; then
        run_test "Proxy Health Endpoint" "curl -f http://localhost:3001/health"
    else
        log_warning "Proxy service not running (optional service)"
    fi
    
    # Test proxy routing (if available)
    if curl -f http://localhost:3001/api >/dev/null 2>&1; then
        run_test "Proxy API Routing" "curl -f http://localhost:3001/api"
    else
        log_warning "Proxy API routing not available"
    fi
}

# Function to test Docker containers
test_docker_containers() {
    log_info "Testing Docker containers..."
    
    # Test Neo4j container
    run_test "Neo4j Container Running" "docker ps | grep -q kg_neo4j"
    
    # Test API container (if running)
    if docker ps | grep -q kg_api; then
        run_test "API Container Running" "docker ps | grep -q kg_api"
    else
        log_warning "API container not running (may be running in dev mode)"
    fi
    
    # Test container health
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep kg_neo4j | grep -q "healthy"; then
        log_success "Neo4j container is healthy"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_warning "Neo4j container health check not available"
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Function to test configuration
test_configuration() {
    log_info "Testing configuration..."
    
    # Test environment variables
    run_test "NEO4J_URI Environment Variable" "[ ! -z \"$NEO4J_URI\" ]"
    run_test "NEO4J_USERNAME Environment Variable" "[ ! -z \"$NEO4J_USERNAME\" ]"
    run_test "NEO4J_PASSWORD Environment Variable" "[ ! -z \"$NEO4J_PASSWORD\" ]"
    
    # Test configuration files
    run_test "Docker Compose File" "[ -f docker-compose.yml ]"
    run_test "Package.json File" "[ -f package.json ]"
    run_test "Requirements.txt File" "[ -f requirements.txt ]"
    
    # Test service configurations
    if [ -d "360t-kg-api" ]; then
        run_test "API Service Directory" "[ -d 360t-kg-api ]"
        if [ -f "360t-kg-api/.env" ] || [ -f "360t-kg-api/.env.example" ]; then
            log_success "API service configuration found"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            log_warning "API service configuration not found"
        fi
        TESTS_RUN=$((TESTS_RUN + 1))
    fi
    
    if [ -d "360t-kg-ui" ]; then
        run_test "UI Service Directory" "[ -d 360t-kg-ui ]"
        if [ -f "360t-kg-ui/.env" ] || [ -f "360t-kg-ui/.env.example" ]; then
            log_success "UI service configuration found"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            log_warning "UI service configuration not found"
        fi
        TESTS_RUN=$((TESTS_RUN + 1))
    fi
}

# Function to test LLM configuration
test_llm_configuration() {
    log_info "Testing LLM configuration..."
    
    # Check if any LLM provider is configured
    local providers_configured=0
    
    if [ ! -z "$LLM_OLLAMA_BASE_URL" ] || [ ! -z "$OLLAMA_BASE_URL" ]; then
        log_success "Ollama provider configured"
        providers_configured=$((providers_configured + 1))
    fi
    
    if [ ! -z "$LLM_ANTHROPIC_API_KEY" ] || [ ! -z "$ANTHROPIC_API_KEY" ]; then
        log_success "Anthropic provider configured"
        providers_configured=$((providers_configured + 1))
    fi
    
    if [ ! -z "$LLM_GOOGLE_API_KEY" ] || [ ! -z "$GOOGLE_API_KEY" ]; then
        log_success "Google provider configured"
        providers_configured=$((providers_configured + 1))
    fi
    
    if [ ! -z "$LLM_AZURE_API_KEY" ] || [ ! -z "$AZURE_OPENAI_API_KEY" ]; then
        log_success "Azure provider configured"
        providers_configured=$((providers_configured + 1))
    fi
    
    if [ $providers_configured -gt 0 ]; then
        log_success "$providers_configured LLM provider(s) configured"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_warning "No LLM providers configured"
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Function to test backup system
test_backup_system() {
    log_info "Testing backup system..."
    
    # Test backup script exists
    run_test "Backup Script Exists" "[ -f scripts/backup-system.sh ]"
    run_test "Backup Script Executable" "[ -x scripts/backup-system.sh ]"
    
    # Test backup directory
    run_test "Backup Directory Exists" "[ -d backups ]"
    
    # Test if backups exist
    if ls backups/ | grep -E '^[0-9]{8}_[0-9]{6}$' >/dev/null 2>&1; then
        log_success "Backup files found"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_warning "No backup files found"
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
    
    # Test latest backup symlink
    if [ -L "backups/latest" ]; then
        log_success "Latest backup symlink exists"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_warning "Latest backup symlink not found"
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Function to test rollback system
test_rollback_system() {
    log_info "Testing rollback system..."
    
    # Test rollback scripts exist
    run_test "Emergency Rollback Script Exists" "[ -f scripts/emergency-rollback.sh ]"
    run_test "Emergency Rollback Script Executable" "[ -x scripts/emergency-rollback.sh ]"
    run_test "Service Rollback Script Exists" "[ -f scripts/rollback-service.sh ]"
    run_test "Service Rollback Script Executable" "[ -x scripts/rollback-service.sh ]"
    run_test "Rollback Validation Script Exists" "[ -f scripts/validate-rollback.sh ]"
    run_test "Rollback Validation Script Executable" "[ -x scripts/validate-rollback.sh ]"
}

# Function to test health monitoring
test_health_monitoring() {
    log_info "Testing health monitoring system..."
    
    # Test health check script
    run_test "Health Check Script Exists" "[ -f scripts/health-check.cjs ]"
    run_test "Health Check Script Executable" "[ -x scripts/health-check.cjs ]"
    
    # Test health monitoring files
    run_test "Health Monitor TypeScript File" "[ -f shared/monitoring/health-monitor.ts ]"
    run_test "Health Dashboard HTML File" "[ -f tools/health-dashboard.html ]"
    
    # Test health check execution
    if node scripts/health-check.cjs >/dev/null 2>&1; then
        log_success "Health check script executes successfully"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_warning "Health check script execution failed (expected if services are down)"
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Function to show test summary
show_test_summary() {
    echo
    log_info "🧪 Test Summary"
    log_info "==============="
    log_info "Tests run: $TESTS_RUN"
    log_success "Tests passed: $TESTS_PASSED"
    
    if [ $TESTS_FAILED -gt 0 ]; then
        log_error "Tests failed: $TESTS_FAILED"
    else
        log_success "Tests failed: $TESTS_FAILED"
    fi
    
    local success_rate=$((TESTS_PASSED * 100 / TESTS_RUN))
    log_info "Success rate: ${success_rate}%"
    
    echo
    if [ $TESTS_FAILED -eq 0 ]; then
        log_success "🎉 All tests passed! System is ready for refactoring."
        return 0
    elif [ $success_rate -ge 80 ]; then
        log_warning "⚠️  Most tests passed. System is mostly ready for refactoring."
        return 0
    else
        log_error "💥 Many tests failed. Please fix issues before proceeding with refactoring."
        return 1
    fi
}

# Main function
main() {
    cd "$PROJECT_ROOT"
    
    echo
    log_info "🧪 Starting comprehensive service validation..."
    log_info "Timestamp: $(date)"
    echo
    
    # Load environment variables if available
    if [ -f ".env" ]; then
        set -a
        source .env
        set +a
        log_info "Loaded environment variables from .env"
    fi
    
    # Run all test suites
    test_configuration
    test_docker_containers
    test_database
    test_api_endpoints
    test_frontend
    test_proxy
    test_llm_configuration
    test_backup_system
    test_rollback_system
    test_health_monitoring
    
    # Show summary
    show_test_summary
}

# Run main function
main "$@"
