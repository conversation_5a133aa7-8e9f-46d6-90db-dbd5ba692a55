/**
 * Jest Test Setup
 * 
 * Global test configuration and utilities
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.NEO4J_URI = 'neo4j://localhost:7687';
process.env.NEO4J_USERNAME = 'neo4j';
process.env.NEO4J_PASSWORD = 'test_password';
process.env.NEO4J_DATABASE = 'test';

// Mock console methods in tests to reduce noise
const originalConsole = { ...console };

beforeEach(() => {
  // Reset console mocks before each test
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
  console.info = jest.fn();
});

afterEach(() => {
  // Restore console after each test
  Object.assign(console, originalConsole);
});

// Global test utilities
global.testUtils = {
  // Wait for a condition to be true
  waitFor: async (condition, timeout = 5000, interval = 100) => {
    const start = Date.now();
    while (Date.now() - start < timeout) {
      if (await condition()) {
        return true;
      }
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    throw new Error(`Condition not met within ${timeout}ms`);
  },
  
  // Create mock environment variables
  mockEnv: (envVars) => {
    const originalEnv = { ...process.env };
    Object.assign(process.env, envVars);
    return () => {
      process.env = originalEnv;
    };
  },
  
  // Create mock fetch responses
  mockFetch: (responses) => {
    const originalFetch = global.fetch;
    global.fetch = jest.fn((url) => {
      const response = responses[url] || responses.default;
      if (!response) {
        return Promise.reject(new Error(`No mock response for ${url}`));
      }
      return Promise.resolve({
        ok: response.ok !== false,
        status: response.status || 200,
        json: () => Promise.resolve(response.data || {}),
        text: () => Promise.resolve(response.text || ''),
        headers: new Map(Object.entries(response.headers || {}))
      });
    });
    return () => {
      global.fetch = originalFetch;
    };
  },
  
  // Create test configuration
  createTestConfig: () => ({
    environment: 'test',
    database: {
      uri: 'neo4j://localhost:7687',
      username: 'neo4j',
      password: 'test_password',
      database: 'test'
    },
    api: {
      port: 3002,
      cors: { origins: ['*'] }
    },
    llm: {
      primaryProvider: 'ollama',
      providers: {
        ollama: { baseUrl: 'http://localhost:11434' }
      }
    }
  })
};

// Global test matchers
expect.extend({
  toBeHealthy(received) {
    const pass = received && received.status === 'healthy';
    if (pass) {
      return {
        message: () => `expected ${received} not to be healthy`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be healthy`,
        pass: false
      };
    }
  },
  
  toHaveValidConfig(received) {
    const requiredFields = ['environment', 'database', 'api'];
    const missingFields = requiredFields.filter(field => !received[field]);
    
    if (missingFields.length === 0) {
      return {
        message: () => `expected config not to be valid`,
        pass: true
      };
    } else {
      return {
        message: () => `expected config to be valid, missing fields: ${missingFields.join(', ')}`,
        pass: false
      };
    }
  }
});

// Increase timeout for integration tests
jest.setTimeout(30000);
