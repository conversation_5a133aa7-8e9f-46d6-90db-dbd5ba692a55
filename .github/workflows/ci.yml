name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'packages/**'
      - '.github/workflows/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'packages/**'
      - '.github/workflows/**'

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Code Quality and Security Checks
  quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'packages/package-lock.json'

      - name: Install dependencies
        working-directory: packages
        run: npm ci

      - name: Run ESLint
        working-directory: packages
        run: npm run lint

      - name: Check code formatting
        working-directory: packages
        run: npm run format:check

      - name: TypeScript type checking
        working-directory: packages
        run: npm run type-check

      - name: Security audit
        working-directory: packages
        run: npm audit --audit-level=moderate

      - name: Check for outdated dependencies
        working-directory: packages
        run: npm outdated || true

  # Unit and Integration Tests
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    strategy:
      matrix:
        package: [shared, graph-api, web-ui, chat-service]
    
    services:
      neo4j:
        image: neo4j:5.13-community
        env:
          NEO4J_AUTH: neo4j/test_password
          NEO4J_PLUGINS: '["graph-data-science"]'
        ports:
          - 7687:7687
          - 7474:7474
        options: >-
          --health-cmd "cypher-shell -u neo4j -p test_password 'RETURN 1'"
          --health-interval 30s
          --health-timeout 10s
          --health-retries 5

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 30s
          --health-timeout 10s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'packages/package-lock.json'

      - name: Install dependencies
        working-directory: packages
        run: npm ci

      - name: Build shared package
        working-directory: packages
        run: npm run build:shared

      - name: Run tests for ${{ matrix.package }}
        working-directory: packages
        run: npm run test --workspace=${{ matrix.package }}
        env:
          NODE_ENV: test
          DATABASE_URI: neo4j://localhost:7687
          DATABASE_USERNAME: neo4j
          DATABASE_PASSWORD: test_password
          REDIS_URL: redis://localhost:6379

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: packages/${{ matrix.package }}/coverage/lcov.info
          flags: ${{ matrix.package }}
          name: ${{ matrix.package }}-coverage

  # Build and Docker Tests
  build:
    name: Build & Docker
    runs-on: ubuntu-latest
    needs: [quality, test]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'packages/package-lock.json'

      - name: Install dependencies
        working-directory: packages
        run: npm ci

      - name: Build all packages
        working-directory: packages
        run: npm run build

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker images
        working-directory: packages
        run: |
          docker build --target graph-api-production -t kg-graph-api:test .
          docker build --target chat-service-production -t kg-chat-service:test .
          docker build --target web-ui-production -t kg-web-ui:test .

      - name: Test Docker images
        run: |
          # Test that images can start
          docker run --rm -d --name test-api kg-graph-api:test || true
          docker run --rm -d --name test-chat kg-chat-service:test || true
          docker run --rm -d --name test-ui kg-web-ui:test || true
          
          # Clean up
          docker stop test-api test-chat test-ui || true

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            packages/*/dist/
            packages/shared/dist/
          retention-days: 7

  # End-to-End Tests
  e2e:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'packages/package-lock.json'

      - name: Install dependencies
        working-directory: packages
        run: npm ci

      - name: Install Playwright
        working-directory: packages
        run: npx playwright install --with-deps

      - name: Start test environment
        working-directory: packages
        run: |
          chmod +x scripts/docker-dev.sh
          ./scripts/docker-dev.sh start
        env:
          COMPOSE_FILE: docker-compose.development.yml

      - name: Wait for services
        run: |
          timeout 300 bash -c 'until curl -f http://localhost:3000/health; do sleep 5; done'
          timeout 300 bash -c 'until curl -f http://localhost:3002/api/health; do sleep 5; done'

      - name: Run E2E tests
        working-directory: packages
        run: npm run test:e2e
        env:
          BASE_URL: http://localhost:3000
          API_URL: http://localhost:3002

      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-results
          path: |
            packages/test-results/
            packages/playwright-report/
          retention-days: 7

      - name: Stop test environment
        if: always()
        working-directory: packages
        run: ./scripts/docker-dev.sh stop

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: [quality]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: 'packages/'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          languages: javascript

  # Performance Tests
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Start test environment
        working-directory: packages
        run: |
          chmod +x scripts/docker-dev.sh
          ./scripts/docker-dev.sh start

      - name: Wait for services
        run: |
          timeout 300 bash -c 'until curl -f http://localhost:3002/api/health; do sleep 5; done'

      - name: Run performance tests
        run: |
          # Install k6
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
          
          # Run load tests (placeholder - would need actual test file)
          echo "Performance tests would run here"

      - name: Stop test environment
        if: always()
        working-directory: packages
        run: ./scripts/docker-dev.sh stop
