name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string

env:
  NODE_VERSION: '18'

jobs:
  # Create Release
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: read
    outputs:
      release-id: ${{ steps.create-release.outputs.id }}
      release-tag: ${{ steps.get-tag.outputs.tag }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get tag name
        id: get-tag
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "tag=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
          else
            echo "tag=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          fi

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'packages/package-lock.json'

      - name: Install dependencies
        working-directory: packages
        run: npm ci

      - name: Build packages
        working-directory: packages
        run: npm run build

      - name: Generate changelog
        id: changelog
        run: |
          # Generate changelog from git commits
          PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
          if [ -n "$PREVIOUS_TAG" ]; then
            CHANGELOG=$(git log --pretty=format:"- %s (%h)" $PREVIOUS_TAG..HEAD)
          else
            CHANGELOG=$(git log --pretty=format:"- %s (%h)" HEAD)
          fi
          
          # Save changelog to file
          echo "## Changes" > CHANGELOG.md
          echo "$CHANGELOG" >> CHANGELOG.md
          
          # Set output for use in release
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          cat CHANGELOG.md >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Create release archive
        run: |
          # Create release archive
          tar -czf kg-visualizer-${{ steps.get-tag.outputs.tag }}.tar.gz \
            --exclude=node_modules \
            --exclude=.git \
            --exclude=coverage \
            --exclude=dist \
            packages/

      - name: Create GitHub Release
        id: create-release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.get-tag.outputs.tag }}
          release_name: Knowledge Graph Visualizer ${{ steps.get-tag.outputs.tag }}
          body: |
            # Knowledge Graph Visualizer ${{ steps.get-tag.outputs.tag }}
            
            ${{ steps.changelog.outputs.changelog }}
            
            ## Installation
            
            ### Docker (Recommended)
            ```bash
            docker-compose up -d
            ```
            
            ### Manual Installation
            ```bash
            tar -xzf kg-visualizer-${{ steps.get-tag.outputs.tag }}.tar.gz
            cd packages
            npm install
            npm run build
            npm start
            ```
            
            ## Documentation
            - [Installation Guide](https://github.com/${{ github.repository }}/blob/${{ steps.get-tag.outputs.tag }}/README.md)
            - [Docker Guide](https://github.com/${{ github.repository }}/blob/${{ steps.get-tag.outputs.tag }}/packages/DOCKER.md)
            - [Development Guide](https://github.com/${{ github.repository }}/blob/${{ steps.get-tag.outputs.tag }}/packages/DEVELOPMENT.md)
            
            ## Migration Notes
            This release includes the new microservices architecture. Please review the migration guide before upgrading.
          draft: false
          prerelease: ${{ contains(steps.get-tag.outputs.tag, '-') }}

      - name: Upload release archive
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create-release.outputs.upload_url }}
          asset_path: kg-visualizer-${{ steps.get-tag.outputs.tag }}.tar.gz
          asset_name: kg-visualizer-${{ steps.get-tag.outputs.tag }}.tar.gz
          asset_content_type: application/gzip

  # Build and Push Release Images
  build-release-images:
    name: Build Release Images
    runs-on: ubuntu-latest
    needs: create-release
    permissions:
      contents: read
      packages: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'packages/package-lock.json'

      - name: Install dependencies
        working-directory: packages
        run: npm ci

      - name: Build packages
        working-directory: packages
        run: npm run build

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Graph API release image
        uses: docker/build-push-action@v5
        with:
          context: packages
          target: graph-api-production
          push: true
          tags: |
            ghcr.io/${{ github.repository }}/graph-api:${{ needs.create-release.outputs.release-tag }}
            ghcr.io/${{ github.repository }}/graph-api:latest
          labels: |
            org.opencontainers.image.title=Knowledge Graph Visualizer API
            org.opencontainers.image.description=Backend API service for Knowledge Graph Visualizer
            org.opencontainers.image.version=${{ needs.create-release.outputs.release-tag }}
            org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
            org.opencontainers.image.revision=${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push Chat Service release image
        uses: docker/build-push-action@v5
        with:
          context: packages
          target: chat-service-production
          push: true
          tags: |
            ghcr.io/${{ github.repository }}/chat-service:${{ needs.create-release.outputs.release-tag }}
            ghcr.io/${{ github.repository }}/chat-service:latest
          labels: |
            org.opencontainers.image.title=Knowledge Graph Visualizer Chat Service
            org.opencontainers.image.description=Chat microservice for Knowledge Graph Visualizer
            org.opencontainers.image.version=${{ needs.create-release.outputs.release-tag }}
            org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
            org.opencontainers.image.revision=${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push Web UI release image
        uses: docker/build-push-action@v5
        with:
          context: packages
          target: web-ui-production
          push: true
          tags: |
            ghcr.io/${{ github.repository }}/web-ui:${{ needs.create-release.outputs.release-tag }}
            ghcr.io/${{ github.repository }}/web-ui:latest
          labels: |
            org.opencontainers.image.title=Knowledge Graph Visualizer Web UI
            org.opencontainers.image.description=Frontend application for Knowledge Graph Visualizer
            org.opencontainers.image.version=${{ needs.create-release.outputs.release-tag }}
            org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
            org.opencontainers.image.revision=${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Update Documentation
  update-docs:
    name: Update Documentation
    runs-on: ubuntu-latest
    needs: create-release
    permissions:
      contents: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Update version in package.json files
        run: |
          VERSION=${{ needs.create-release.outputs.release-tag }}
          VERSION_NUMBER=${VERSION#v}
          
          # Update main package.json
          sed -i "s/\"version\": \".*\"/\"version\": \"$VERSION_NUMBER\"/" packages/package.json
          
          # Update individual package.json files
          for package in shared graph-api web-ui chat-service; do
            sed -i "s/\"version\": \".*\"/\"version\": \"$VERSION_NUMBER\"/" packages/$package/package.json
          done

      - name: Update README with latest version
        run: |
          VERSION=${{ needs.create-release.outputs.release-tag }}
          sed -i "s/Version: .*/Version: $VERSION/" README.md
          sed -i "s/kg-visualizer:latest/kg-visualizer:$VERSION/g" README.md

      - name: Commit version updates
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add packages/*/package.json README.md
          git commit -m "chore: update version to ${{ needs.create-release.outputs.release-tag }}" || exit 0
          git push

  # Notify Release
  notify-release:
    name: Notify Release
    runs-on: ubuntu-latest
    needs: [create-release, build-release-images, update-docs]
    if: always()
    
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#releases'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          custom_payload: |
            {
              "text": "🎉 New Release Published",
              "attachments": [{
                "color": "good",
                "fields": [{
                  "title": "Version",
                  "value": "${{ needs.create-release.outputs.release-tag }}",
                  "short": true
                }, {
                  "title": "Repository",
                  "value": "${{ github.repository }}",
                  "short": true
                }, {
                  "title": "Release Notes",
                  "value": "https://github.com/${{ github.repository }}/releases/tag/${{ needs.create-release.outputs.release-tag }}",
                  "short": false
                }]
              }]
            }

      - name: Create deployment issue
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `Deploy ${{ needs.create-release.outputs.release-tag }} to Production`,
              body: `## Production Deployment Checklist
              
              - [ ] Staging deployment successful
              - [ ] All tests passing
              - [ ] Performance tests completed
              - [ ] Security scan completed
              - [ ] Database migrations reviewed
              - [ ] Rollback plan confirmed
              - [ ] Monitoring alerts configured
              - [ ] Team notified
              
              **Release**: ${{ needs.create-release.outputs.release-tag }}
              **Images**: Available in GitHub Container Registry
              **Documentation**: Updated
              
              /cc @team-leads`,
              labels: ['deployment', 'production', 'release']
            })
