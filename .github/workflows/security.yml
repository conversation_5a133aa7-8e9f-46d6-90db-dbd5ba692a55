name: Security Scan

on:
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'
  push:
    branches: [ main ]
    paths:
      - 'packages/**'
      - '.github/workflows/security.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'packages/**'
  workflow_dispatch:

env:
  NODE_VERSION: '18'

jobs:
  # Dependency Vulnerability Scan
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'packages/package-lock.json'

      - name: Install dependencies
        working-directory: packages
        run: npm ci

      - name: Run npm audit
        working-directory: packages
        run: |
          npm audit --audit-level=moderate --json > npm-audit.json || true
          npm audit --audit-level=moderate

      - name: Upload npm audit results
        uses: actions/upload-artifact@v3
        with:
          name: npm-audit-results
          path: packages/npm-audit.json

      - name: Run Snyk vulnerability scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=medium --file=packages/package.json

      - name: Upload Snyk results to GitHub Code Scanning
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: snyk.sarif

  # Container Security Scan
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build Docker images for scanning
        working-directory: packages
        run: |
          docker build --target graph-api-production -t kg-graph-api:scan .
          docker build --target chat-service-production -t kg-chat-service:scan .
          docker build --target web-ui-production -t kg-web-ui:scan .

      - name: Run Trivy vulnerability scanner on Graph API
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'kg-graph-api:scan'
          format: 'sarif'
          output: 'trivy-graph-api.sarif'

      - name: Run Trivy vulnerability scanner on Chat Service
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'kg-chat-service:scan'
          format: 'sarif'
          output: 'trivy-chat-service.sarif'

      - name: Run Trivy vulnerability scanner on Web UI
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'kg-web-ui:scan'
          format: 'sarif'
          output: 'trivy-web-ui.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-graph-api.sarif'

      - name: Upload Chat Service Trivy results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-chat-service.sarif'

      - name: Upload Web UI Trivy results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-web-ui.sarif'

  # Code Security Analysis
  code-analysis:
    name: Code Security Analysis
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
    
    strategy:
      matrix:
        language: [ 'javascript' ]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: ${{ matrix.language }}
          queries: security-extended,security-and-quality

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'packages/package-lock.json'

      - name: Install dependencies
        working-directory: packages
        run: npm ci

      - name: Build packages
        working-directory: packages
        run: npm run build

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          category: "/language:${{matrix.language}}"

  # Secret Scanning
  secret-scan:
    name: Secret Scanning
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run TruffleHog OSS
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

      - name: Run GitLeaks
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # License Compliance
  license-scan:
    name: License Compliance
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'packages/package-lock.json'

      - name: Install dependencies
        working-directory: packages
        run: npm ci

      - name: Install license-checker
        run: npm install -g license-checker

      - name: Check licenses
        working-directory: packages
        run: |
          license-checker --onlyAllow 'MIT;Apache-2.0;BSD-2-Clause;BSD-3-Clause;ISC;0BSD' --excludePrivatePackages > license-report.txt
          cat license-report.txt

      - name: Upload license report
        uses: actions/upload-artifact@v3
        with:
          name: license-report
          path: packages/license-report.txt

  # Security Report
  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [dependency-scan, container-scan, code-analysis, secret-scan, license-scan]
    if: always()
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download artifacts
        uses: actions/download-artifact@v3

      - name: Generate security report
        run: |
          echo "# Security Scan Report" > security-report.md
          echo "Generated on: $(date)" >> security-report.md
          echo "" >> security-report.md
          
          echo "## Scan Results" >> security-report.md
          echo "- Dependency Scan: ${{ needs.dependency-scan.result }}" >> security-report.md
          echo "- Container Scan: ${{ needs.container-scan.result }}" >> security-report.md
          echo "- Code Analysis: ${{ needs.code-analysis.result }}" >> security-report.md
          echo "- Secret Scan: ${{ needs.secret-scan.result }}" >> security-report.md
          echo "- License Scan: ${{ needs.license-scan.result }}" >> security-report.md
          echo "" >> security-report.md
          
          echo "## Recommendations" >> security-report.md
          echo "1. Review any high or critical vulnerabilities in the Security tab" >> security-report.md
          echo "2. Update dependencies with known vulnerabilities" >> security-report.md
          echo "3. Address any secrets detected in the codebase" >> security-report.md
          echo "4. Ensure all dependencies have compatible licenses" >> security-report.md

      - name: Upload security report
        uses: actions/upload-artifact@v3
        with:
          name: security-report
          path: security-report.md

      - name: Create security issue on failure
        if: failure()
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `Security Scan Failed - ${new Date().toISOString().split('T')[0]}`,
              body: `## Security Scan Failure
              
              One or more security scans have failed. Please review the results and take appropriate action.
              
              **Failed Jobs:**
              - Dependency Scan: ${{ needs.dependency-scan.result }}
              - Container Scan: ${{ needs.container-scan.result }}
              - Code Analysis: ${{ needs.code-analysis.result }}
              - Secret Scan: ${{ needs.secret-scan.result }}
              - License Scan: ${{ needs.license-scan.result }}
              
              **Action Required:**
              1. Review the Security tab for detailed findings
              2. Address any critical or high severity issues
              3. Update this issue when resolved
              
              **Workflow Run:** ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}`,
              labels: ['security', 'bug', 'high-priority']
            })

      - name: Notify security team
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: 'failure'
          channel: '#security'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          custom_payload: |
            {
              "text": "🚨 Security Scan Failed",
              "attachments": [{
                "color": "danger",
                "fields": [{
                  "title": "Repository",
                  "value": "${{ github.repository }}",
                  "short": true
                }, {
                  "title": "Branch",
                  "value": "${{ github.ref_name }}",
                  "short": true
                }, {
                  "title": "Workflow",
                  "value": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
                  "short": false
                }]
              }]
            }
