name: Deploy

on:
  push:
    branches: [ main ]
    paths:
      - 'packages/**'
  release:
    types: [ published ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Build and Push Docker Images
  build-and-push:
    name: Build & Push Images
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'packages/package-lock.json'

      - name: Install dependencies
        working-directory: packages
        run: npm ci

      - name: Build packages
        working-directory: packages
        run: npm run build

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-

      - name: Build and push Graph API image
        uses: docker/build-push-action@v5
        with:
          context: packages
          target: graph-api-production
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/graph-api:${{ github.sha }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push Chat Service image
        uses: docker/build-push-action@v5
        with:
          context: packages
          target: chat-service-production
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/chat-service:${{ github.sha }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push Web UI image
        uses: docker/build-push-action@v5
        with:
          context: packages
          target: web-ui-production
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/web-ui:${{ github.sha }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment:
      name: staging
      url: https://staging.kg-visualizer.com
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to staging server
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            cd /opt/kg-visualizer
            
            # Pull latest code
            git pull origin main
            
            # Update environment variables
            echo "IMAGE_TAG=${{ github.sha }}" > .env.staging
            echo "REGISTRY=${{ env.REGISTRY }}" >> .env.staging
            echo "IMAGE_NAME=${{ env.IMAGE_NAME }}" >> .env.staging
            cat .env.staging.template >> .env.staging
            
            # Deploy with docker-compose
            docker-compose -f docker-compose.staging.yml pull
            docker-compose -f docker-compose.staging.yml up -d
            
            # Health check
            sleep 30
            curl -f http://localhost:3002/api/health
            curl -f http://localhost:8000/health
            curl -f http://localhost:3000/health

      - name: Run staging tests
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            cd /opt/kg-visualizer
            
            # Run smoke tests
            npm run test:staging

      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-and-push, deploy-staging]
    if: github.event_name == 'release' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment:
      name: production
      url: https://kg-visualizer.com
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to production server
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USER }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          script: |
            cd /opt/kg-visualizer
            
            # Create backup
            ./scripts/backup.sh
            
            # Pull latest code
            git pull origin main
            
            # Update environment variables
            echo "IMAGE_TAG=${{ github.sha }}" > .env.production
            echo "REGISTRY=${{ env.REGISTRY }}" >> .env.production
            echo "IMAGE_NAME=${{ env.IMAGE_NAME }}" >> .env.production
            cat .env.production.template >> .env.production
            
            # Deploy with zero-downtime strategy
            docker-compose -f docker-compose.yml pull
            docker-compose -f docker-compose.yml up -d --no-deps graph-api
            sleep 30
            docker-compose -f docker-compose.yml up -d --no-deps chat-service
            sleep 30
            docker-compose -f docker-compose.yml up -d --no-deps web-ui
            
            # Health check
            sleep 60
            curl -f https://kg-visualizer.com/api/health
            curl -f https://kg-visualizer.com/chat/health
            curl -f https://kg-visualizer.com/health

      - name: Run production smoke tests
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USER }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          script: |
            cd /opt/kg-visualizer
            
            # Run production smoke tests
            npm run test:production

      - name: Update monitoring
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USER }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          script: |
            # Update Grafana dashboards
            curl -X POST "${{ secrets.GRAFANA_WEBHOOK }}" \
              -H "Content-Type: application/json" \
              -d '{"deployment": "production", "version": "${{ github.sha }}", "timestamp": "'$(date -Iseconds)'"}'

      - name: Notify production deployment
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          channel: '#production'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          custom_payload: |
            {
              "text": "🚀 Production Deployment",
              "attachments": [{
                "color": "${{ job.status == 'success' && 'good' || 'danger' }}",
                "fields": [{
                  "title": "Status",
                  "value": "${{ job.status }}",
                  "short": true
                }, {
                  "title": "Version",
                  "value": "${{ github.sha }}",
                  "short": true
                }, {
                  "title": "Environment",
                  "value": "Production",
                  "short": true
                }]
              }]
            }

  # Rollback on Failure
  rollback:
    name: Rollback on Failure
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: failure() && github.event_name == 'release'
    environment:
      name: production
    
    steps:
      - name: Rollback production deployment
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USER }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          script: |
            cd /opt/kg-visualizer
            
            # Rollback to previous version
            ./scripts/rollback.sh
            
            # Verify rollback
            sleep 30
            curl -f https://kg-visualizer.com/api/health

      - name: Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: 'warning'
          channel: '#production'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          custom_payload: |
            {
              "text": "⚠️ Production Rollback Executed",
              "attachments": [{
                "color": "warning",
                "fields": [{
                  "title": "Reason",
                  "value": "Deployment failure detected",
                  "short": false
                }, {
                  "title": "Action",
                  "value": "Automatic rollback completed",
                  "short": false
                }]
              }]
            }
