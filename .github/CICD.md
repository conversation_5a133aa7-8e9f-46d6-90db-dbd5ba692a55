# CI/CD Pipeline Documentation

This document describes the continuous integration and deployment pipeline for the Knowledge Graph Visualizer project.

## 🔄 Overview

The CI/CD pipeline consists of four main workflows:

1. **Continuous Integration** (`ci.yml`) - Code quality, testing, and validation
2. **Deployment** (`deploy.yml`) - Automated deployment to staging and production
3. **Release** (`release.yml`) - Release management and versioning
4. **Security** (`security.yml`) - Security scanning and compliance

## 🧪 Continuous Integration (CI)

### Triggers
- Push to `main` or `develop` branches
- Pull requests to `main` or `develop`
- Changes to `packages/**` or workflow files

### Jobs

#### 1. Code Quality & Security
- ESLint code linting
- Prettier code formatting check
- TypeScript type checking
- Security audit with `npm audit`
- Dependency vulnerability check

#### 2. Test Suite
- Unit tests for all packages
- Integration tests with Neo4j and Redis
- Code coverage reporting to Codecov
- Matrix strategy for parallel testing

#### 3. Build & Docker
- Build all packages
- Create Docker images for all services
- Test Docker image startup
- Upload build artifacts

#### 4. End-to-End Tests (PR only)
- Full application testing with Playwright
- Real browser testing scenarios
- API integration testing
- Performance validation

#### 5. Security Scanning
- Trivy vulnerability scanning
- CodeQL security analysis
- SARIF report generation

#### 6. Performance Tests (PR only)
- Load testing with k6
- API performance benchmarks
- Resource usage monitoring

### Required Secrets
- `CODECOV_TOKEN` - Code coverage reporting

## 🚀 Deployment

### Triggers
- Push to `main` branch (staging deployment)
- Release publication (production deployment)
- Manual workflow dispatch

### Environments

#### Staging
- **URL**: https://staging.kg-visualizer.com
- **Auto-deploy**: On push to `main`
- **Purpose**: Pre-production testing

#### Production
- **URL**: https://kg-visualizer.com
- **Deploy**: Manual approval required
- **Purpose**: Live application

### Deployment Strategy

#### Staging Deployment
1. Build and push Docker images
2. Deploy to staging server via SSH
3. Run health checks
4. Execute smoke tests
5. Notify team via Slack

#### Production Deployment
1. Requires staging deployment success
2. Zero-downtime rolling deployment
3. Service-by-service updates with health checks
4. Automatic rollback on failure
5. Monitoring integration updates

### Required Secrets
- `STAGING_HOST` - Staging server hostname
- `STAGING_USER` - SSH username for staging
- `STAGING_SSH_KEY` - SSH private key for staging
- `PRODUCTION_HOST` - Production server hostname
- `PRODUCTION_USER` - SSH username for production
- `PRODUCTION_SSH_KEY` - SSH private key for production
- `SLACK_WEBHOOK` - Slack notifications
- `GRAFANA_WEBHOOK` - Monitoring updates

## 📦 Release Management

### Triggers
- Git tags matching `v*` pattern
- Manual workflow dispatch with version input

### Process
1. **Create Release**
   - Generate changelog from git commits
   - Create GitHub release with notes
   - Upload release archive

2. **Build Release Images**
   - Build production Docker images
   - Tag with release version and `latest`
   - Push to GitHub Container Registry

3. **Update Documentation**
   - Update version in package.json files
   - Update README with latest version
   - Commit version changes

4. **Notifications**
   - Slack notification to #releases channel
   - Create deployment issue for tracking

### Versioning
- Semantic versioning (e.g., v1.2.3)
- Pre-release versions supported (e.g., v1.2.3-beta.1)
- Automatic latest tag for stable releases

## 🔒 Security Scanning

### Schedule
- Daily at 2 AM UTC
- On push to main branch
- On pull requests
- Manual trigger available

### Scans

#### 1. Dependency Vulnerability Scan
- npm audit for Node.js dependencies
- Snyk vulnerability database
- SARIF report generation

#### 2. Container Security Scan
- Trivy scanner for Docker images
- Base image vulnerability assessment
- Multi-service scanning

#### 3. Code Security Analysis
- GitHub CodeQL analysis
- Security-focused queries
- Extended security ruleset

#### 4. Secret Scanning
- TruffleHog for secret detection
- GitLeaks for credential scanning
- Historical commit analysis

#### 5. License Compliance
- License compatibility checking
- Approved license validation
- Compliance reporting

### Required Secrets
- `SNYK_TOKEN` - Snyk vulnerability scanning

## 🛠️ Setup Instructions

### 1. Repository Secrets
Configure the following secrets in GitHub repository settings:

```bash
# Deployment
STAGING_HOST=staging.example.com
STAGING_USER=deploy
STAGING_SSH_KEY=<private-key>
PRODUCTION_HOST=production.example.com
PRODUCTION_USER=deploy
PRODUCTION_SSH_KEY=<private-key>

# Notifications
SLACK_WEBHOOK=https://hooks.slack.com/...
GRAFANA_WEBHOOK=https://grafana.example.com/...

# Security
SNYK_TOKEN=<snyk-token>

# Coverage
CODECOV_TOKEN=<codecov-token>
```

### 2. Environment Setup
Ensure target servers have:
- Docker and Docker Compose installed
- Application directory at `/opt/kg-visualizer`
- SSH access configured
- Environment files prepared

### 3. Branch Protection
Configure branch protection rules:
- Require status checks to pass
- Require up-to-date branches
- Require review from code owners
- Restrict pushes to main branch

## 📊 Monitoring & Alerts

### Workflow Notifications
- **Slack Integration**: Real-time notifications to relevant channels
- **GitHub Issues**: Automatic issue creation on failures
- **Email Alerts**: GitHub workflow failure notifications

### Monitoring Channels
- `#deployments` - Deployment notifications
- `#releases` - Release announcements
- `#security` - Security scan alerts
- `#production` - Production deployment updates

### Metrics Tracking
- Build success/failure rates
- Deployment frequency
- Lead time for changes
- Mean time to recovery

## 🚨 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check Node.js version compatibility
# Verify package.json dependencies
# Review TypeScript compilation errors
```

#### Test Failures
```bash
# Check service dependencies (Neo4j, Redis)
# Verify environment variables
# Review test database setup
```

#### Deployment Failures
```bash
# Verify SSH connectivity
# Check server disk space
# Validate environment configuration
# Review Docker image availability
```

#### Security Scan Failures
```bash
# Update vulnerable dependencies
# Address code security issues
# Remove detected secrets
# Update license compliance
```

### Emergency Procedures

#### Production Rollback
1. Manual workflow dispatch of rollback
2. SSH to production server
3. Execute rollback script
4. Verify service health
5. Notify team of rollback

#### Security Incident Response
1. Review security scan results
2. Assess vulnerability impact
3. Apply immediate fixes
4. Deploy emergency patch
5. Document incident response

## 📚 Best Practices

### Development Workflow
1. Create feature branch from `develop`
2. Make changes with proper tests
3. Run local CI checks before push
4. Create pull request with description
5. Address review feedback
6. Merge after CI passes

### Release Process
1. Merge features to `develop`
2. Create release branch
3. Update version and changelog
4. Test release candidate
5. Merge to `main` and tag
6. Monitor deployment

### Security Practices
1. Regular dependency updates
2. Security scan review
3. Secret rotation
4. Access control review
5. Incident response testing

## 🔗 Related Documentation

- [Docker Configuration](../packages/DOCKER.md)
- [Development Guide](../packages/DEVELOPMENT.md)
- [Security Guidelines](SECURITY.md)
- [Contributing Guidelines](CONTRIBUTING.md)
