/**
 * Simple Jest Configuration for KnowledgeGraphVisualizer
 */

export default {
  testEnvironment: 'node',
  testMatch: [
    '**/__tests__/**/*.(js|jsx|ts|tsx)',
    '**/*.(test|spec).(js|jsx|ts|tsx)'
  ],
  setupFilesAfterEnv: [
    '<rootDir>/tests/setup.js'
  ],
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/',
    '/fastapi_env/',
    '/google-cloud-sdk/'
  ],
  verbose: true,
  testTimeout: 30000,
  clearMocks: true,
  restoreMocks: true
};
