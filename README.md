# 360T Knowledge Graph Visualizer

A comprehensive tool for exploring and analyzing relationships between 360T platform components using a graph database, with an interactive UI and a conversational AI for natural language queries.

## 🏗️ Architecture Overview & Code Quality Assessment

### Current Project Structure
```
.
├── packages/            # Backend Microservices Architecture
│   ├── graph-api/       # Graph API service
│   ├── chat-service/    # Chat service
│   └── web-ui/          # Modern web UI components
│
├── 360t-kg-ui/          # Frontend React Application
│   ├── docs/            # Detailed frontend documentation
│   └── src/             # Main application source code
│
└── scripts/             # Development and deployment scripts
```

### 🔍 Detailed Code Analysis Summary

**Critical Issues Identified:**

#### 1. **Naming Conventions & Standards**
- ❌ **Inconsistent naming**: `360t-kg-api` (kebab-case) vs `proxy-server` vs potential camelCase files
- ❌ **Non-semantic naming**: `server.js` doesn't indicate its purpose
- ❌ **Mixed case patterns**: Directory names don't follow consistent convention
- ❌ **Legacy prefixes**: `360t-` prefix creates vendor lock-in
- ✅ **Solution**: Implement strict naming conventions with linting rules

#### 2. **Architecture Anti-patterns**
- 🚨 **Monolithic server entry point**: Single `server.js` handling all concerns
- 🚨 **Tight coupling**: Direct Python script execution from Node.js
- 🚨 **Missing abstraction layers**: No separation between routes, business logic, and data access
- 🚨 **Cross-language dependency**: Node.js → Python execution creates fragility
- 🚨 **No dependency injection**: Hard to test and maintain

#### 3. **Code Quality & Maintainability**
- ⚠️ **Missing type safety**: No TypeScript configuration visible
- ⚠️ **No testing strategy**: Test files location/structure unclear
- ⚠️ **Missing error handling**: No centralized error management
- ⚠️ **No logging strategy**: Observability limited to basic metrics
- ⚠️ **Security concerns**: No evident security middleware or validation

#### 4. **Development Experience Issues**
- ❌ **Manual process dependencies**: Python script execution
- ❌ **Port conflicts**: Hardcoded ports (3001, 5173)
- ❌ **Missing containerization**: No Docker for consistent environments
- ❌ **No hot reload for backend**: Development efficiency issues

## 📋 Comprehensive Refactoring Plan & Implementation Guide

### Phase 1: Foundation & Structure (Weeks 1-3)

#### 1.1 Directory Structure Modernization
```
knowledge-graph-visualizer/                    # Root rename
├── .github/                                   # GitHub workflows
│   ├── workflows/
│   │   ├── ci.yml                            # Continuous integration
│   │   ├── cd.yml                            # Continuous deployment
│   │   └── security.yml                     # Security scanning
│   └── ISSUE_TEMPLATE/                       # Issue templates
│
├── packages/                                  # Monorepo structure
│   ├── shared/                               # Shared utilities & types
│   │   ├── src/
│   │   │   ├── types/                        # TypeScript definitions
│   │   │   │   ├── graph.types.ts           # Graph-related types
│   │   │   │   ├── api.types.ts             # API contract types
│   │   │   │   └── chat.types.ts            # Chat interface types
│   │   │   ├── utils/                        # Cross-package utilities
│   │   │   │   ├── validation.ts            # Input validation
│   │   │   │   ├── logging.ts               # Logging utilities
│   │   │   │   └── constants.ts             # Shared constants
│   │   │   └── errors/                       # Custom error classes
│   │   ├── package.json
│   │   └── tsconfig.json
│   │
│   ├── graph-api/                            # Renamed backend
│   │   ├── src/
│   │   │   ├── controllers/                  # HTTP route handlers
│   │   │   │   ├── graph.controller.ts      # Graph operations
│   │   │   │   ├── analysis.controller.ts   # Analysis endpoints
│   │   │   │   ├── search.controller.ts     # Search functionality
│   │   │   │   └── health.controller.ts     # Health checks
│   │   │   ├── services/                     # Business logic layer
│   │   │   │   ├── graph.service.ts         # Core graph operations
│   │   │   │   ├── neo4j.service.ts         # Database operations
│   │   │   │   ├── gds.service.ts           # Graph Data Science
│   │   │   │   ├── metrics.service.ts       # Prometheus metrics
│   │   │   │   └── cache.service.ts         # Caching strategy
│   │   │   ├── repositories/                 # Data access layer
│   │   │   │   ├── base.repository.ts       # Base repository pattern
│   │   │   │   ├── node.repository.ts       # Node operations
│   │   │   │   └── relationship.repository.ts
│   │   │   ├── middleware/                   # Express middleware
│   │   │   │   ├── auth.middleware.ts       # Authentication
│   │   │   │   ├── validation.middleware.ts # Request validation
│   │   │   │   ├── error.middleware.ts      # Error handling
│   │   │   │   ├── logging.middleware.ts    # Request logging
│   │   │   │   └── cors.middleware.ts       # CORS configuration
│   │   │   ├── config/                       # Configuration management
│   │   │   │   ├── database.config.ts       # Neo4j configuration
│   │   │   │   ├── server.config.ts         # Server settings
│   │   │   │   └── environment.config.ts    # Environment variables
│   │   │   ├── routes/                       # Route definitions
│   │   │   │   ├── api/                     # API routes
│   │   │   │   │   ├── v1/                  # API versioning
│   │   │   │   │   │   ├── graph.routes.ts
│   │   │   │   │   │   ├── analysis.routes.ts
│   │   │   │   │   │   └── search.routes.ts
│   │   │   │   │   └── index.ts
│   │   │   │   └── health.routes.ts
│   │   │   ├── validators/                   # Input validation schemas
│   │   │   │   ├── graph.validators.ts
│   │   │   │   └── common.validators.ts
│   │   │   ├── app.ts                       # Express app setup
│   │   │   └── server.ts                    # Server entry point
│   │   ├── tests/                           # Test files
│   │   │   ├── unit/                        # Unit tests
│   │   │   ├── integration/                 # Integration tests
│   │   │   ├── e2e/                         # End-to-end tests
│   │   │   └── fixtures/                    # Test data
│   │   ├── docs/                            # API documentation
│   │   ├── Dockerfile
│   │   ├── package.json
│   │   └── tsconfig.json
│   │
│   ├── web-ui/                              # Frontend application
│   │   ├── src/
│   │   │   ├── app/                         # App-level components
│   │   │   │   ├── App.tsx
│   │   │   │   ├── App.test.tsx
│   │   │   │   └── store.ts                 # Global state
│   │   │   ├── features/                    # Feature-based organization
│   │   │   │   ├── graph-visualization/     # Graph display feature
│   │   │   │   │   ├── components/
│   │   │   │   │   │   ├── GraphCanvas.tsx
│   │   │   │   │   │   ├── NodeDetails.tsx
│   │   │   │   │   │   └── EdgeDetails.tsx
│   │   │   │   │   ├── hooks/
│   │   │   │   │   │   ├── useGraphData.ts
│   │   │   │   │   │   └── useGraphLayout.ts
│   │   │   │   │   ├── services/
│   │   │   │   │   │   └── graph-api.service.ts
│   │   │   │   │   ├── types/
│   │   │   │   │   │   └── graph.types.ts
│   │   │   │   │   └── index.ts
│   │   │   │   ├── chat-interface/          # Chat feature
│   │   │   │   │   ├── components/
│   │   │   │   │   │   ├── ChatWindow.tsx
│   │   │   │   │   │   ├── MessageList.tsx
│   │   │   │   │   │   └── MessageInput.tsx
│   │   │   │   │   ├── hooks/
│   │   │   │   │   │   ├── useChat.ts
│   │   │   │   │   │   └── useChatHistory.ts
│   │   │   │   │   └── services/
│   │   │   │   │       └── chat-api.service.ts
│   │   │   │   ├── analysis-tools/          # Analysis feature
│   │   │   │   │   ├── components/
│   │   │   │   │   │   ├── AnalysisPanel.tsx
│   │   │   │   │   │   ├── ImpactAnalysis.tsx
│   │   │   │   │   │   └── HiddenLinks.tsx
│   │   │   │   │   └── hooks/
│   │   │   │   │       └── useAnalysis.ts
│   │   │   │   └── search-filter/           # Search & filter
│   │   │   │       ├── components/
│   │   │   │       │   ├── SearchBar.tsx
│   │   │   │       │   └── FilterPanel.tsx
│   │   │   │       └── hooks/
│   │   │   │           └── useSearch.ts
│   │   │   ├── shared/                      # Shared UI components
│   │   │   │   ├── components/              # Reusable components
│   │   │   │   │   ├── ui/                  # Basic UI components
│   │   │   │   │   │   ├── Button.tsx
│   │   │   │   │   │   ├── Input.tsx
│   │   │   │   │   │   ├── Modal.tsx
│   │   │   │   │   │   └── LoadingSpinner.tsx
│   │   │   │   │   ├── layout/              # Layout components
│   │   │   │   │   │   ├── Header.tsx
│   │   │   │   │   │   ├── Sidebar.tsx
│   │   │   │   │   │   └── Footer.tsx
│   │   │   │   │   └── error-boundary/      # Error handling
│   │   │   │   │       └── ErrorBoundary.tsx
│   │   │   │   ├── hooks/                   # Shared hooks
│   │   │   │   │   ├── useApi.ts
│   │   │   │   │   ├── useLocalStorage.ts
│   │   │   │   │   └── useDebounce.ts
│   │   │   │   ├── utils/                   # Utility functions
│   │   │   │   │   ├── api.utils.ts
│   │   │   │   │   ├── format.utils.ts
│   │   │   │   │   └── validation.utils.ts
│   │   │   │   └── constants/               # UI constants
│   │   │   │       ├── colors.ts
│   │   │   │       └── breakpoints.ts
│   │   │   ├── styles/                      # Global styles
│   │   │   │   ├── globals.css
│   │   │   │   ├── variables.css
│   │   │   │   └── components.css
│   │   │   ├── types/                       # TypeScript definitions
│   │   │   │   ├── api.types.ts
│   │   │   │   └── ui.types.ts
│   │   │   ├── main.tsx                     # App entry point
│   │   │   └── vite-env.d.ts
│   │   ├── public/                          # Static assets
│   │   ├── tests/                           # Frontend tests
│   │   │   ├── components/                  # Component tests
│   │   │   ├── features/                    # Feature tests
│   │   │   ├── utils/                       # Utility tests
│   │   │   └── setup.ts                     # Test setup
│   │   ├── docs/                            # Frontend documentation
│   │   ├── Dockerfile
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   ├── vite.config.ts
│   │   └── tailwind.config.js
│   │
│   └── chat-service/                        # Microservice for AI chat
│       ├── src/
│       │   ├── api/                         # FastAPI application
│       │   │   ├── routes/
│       │   │   │   ├── chat.py
│       │   │   │   └── health.py
│       │   │   ├── middleware/
│       │   │   │   ├── auth.py
│       │   │   │   └── cors.py
│       │   │   └── main.py                  # FastAPI app
│       │   ├── core/                        # Core business logic
│       │   │   ├── llm/                     # LLM integration
│       │   │   │   ├── openai_client.py
│       │   │   │   ├── prompt_templates.py
│       │   │   │   └── response_parser.py
│       │   │   ├── knowledge/               # Knowledge processing
│       │   │   │   ├── graph_query.py
│       │   │   │   ├── context_builder.py
│       │   │   │   └── rag_pipeline.py
│       │   │   └── models/                  # Pydantic models
│       │   │       ├── chat.py
│       │   │       └── graph.py
│       │   ├── config/                      # Configuration
│       │   │   ├── settings.py
│       │   │   └── database.py
│       │   └── utils/                       # Utilities
│       │       ├── logging.py
│       │       └── validation.py
│       ├── tests/                           # Python tests
│       │   ├── unit/
│       │   ├── integration/
│       │   └── fixtures/
│       ├── docs/                            # Service documentation
│       ├── Dockerfile
│       ├── requirements.txt
│       ├── pyproject.toml
│       └── .env.example
│
├── tools/                                   # Development & deployment tools
│   ├── dev-proxy/                          # Development proxy (renamed)
│   │   ├── src/
│   │   │   └── proxy-server.js
│   │   ├── package.json
│   │   └── Dockerfile
│   ├── scripts/                            # Automation scripts
│   │   ├── setup/                          # Environment setup
│   │   │   ├── install-dependencies.sh
│   │   │   ├── setup-database.sh
│   │   │   └── generate-env-files.sh
│   │   ├── build/                          # Build scripts
│   │   │   ├── build-all.sh
│   │   │   ├── build-docker-images.sh
│   │   │   └── optimize-builds.sh
│   │   ├── deployment/                     # Deployment scripts
│   │   │   ├── deploy-staging.sh
│   │   │   ├── deploy-production.sh
│   │   │   └── rollback.sh
│   │   └── maintenance/                    # Maintenance scripts
│   │       ├── backup-database.sh
│   │       ├── cleanup-docker.sh
│   │       └── update-dependencies.sh
│   ├── docker/                             # Docker configurations
│   │   ├── development/
│   │   │   └── docker-compose.dev.yml
│   │   ├── staging/
│   │   │   └── docker-compose.staging.yml
│   │   └── production/
│   │       └── docker-compose.prod.yml
│   └── monitoring/                         # Monitoring & observability
│       ├── prometheus/
│       │   └── prometheus.yml
│       ├── grafana/
│       │   └── dashboards/
│       └── logs/
│           └── logrotate.conf
│
├── docs/                                   # Centralized documentation
│   ├── architecture/                       # Architecture documentation
│   │   ├── README.md                       # Architecture overview
│   │   ├── design-decisions.md             # ADRs
│   │   ├── data-flow.md                    # Data flow diagrams
│   │   ├── security.md                     # Security considerations
│   │   └── performance.md                  # Performance guidelines
│   ├── api/                               # API documentation
│   │   ├── openapi.yml                    # OpenAPI specification
│   │   ├── endpoints.md                   # Endpoint documentation
│   │   └── authentication.md              # Auth documentation
│   ├── development/                       # Development guides
│   │   ├── getting-started.md             # Quick start guide
│   │   ├── coding-standards.md            # Code style guide
│   │   ├── testing-guide.md               # Testing strategies
│   │   └── debugging.md                   # Debugging guide
│   ├── deployment/                        # Deployment documentation
│   │   ├── environments.md                # Environment setup
│   │   ├── ci-cd.md                      # CI/CD pipeline
│   │   ├── monitoring.md                  # Monitoring setup
│   │   └── troubleshooting.md             # Common issues
│   └── user-guides/                       # End-user documentation
│       ├── features.md                    # Feature documentation
│       ├── tutorials.md                   # Step-by-step tutorials
│       └── faq.md                         # Frequently asked questions
│
├── .github/                               # GitHub configuration
├── .vscode/                               # VS Code settings
│   ├── settings.json                      # Workspace settings
│   ├── tasks.json                         # Build tasks
│   └── launch.json                        # Debug configuration
├── docker-compose.yml                     # Main compose file
├── .env.example                           # Environment template
├── .gitignore                             # Git ignore rules
├── .eslintrc.js                           # ESLint configuration
├── .prettierrc                            # Prettier configuration
├── lerna.json                             # Monorepo configuration
├── package.json                           # Root package.json
├── tsconfig.json                          # Root TypeScript config
└── README.md                              # This file
```

## 🚨 Risk Assessment & Migration Safety Plan

### Critical Risks Identified

#### 1. **Monolithic Server Breakage Risk** 🔴 HIGH
**Problem**: Moving from single `server.js` to layered architecture could break existing API endpoints and integrations.

**Breaking Changes**:
- Route path changes during controller extraction
- Middleware execution order changes
- Database connection handling modifications
- Error response format changes

**Mitigation Strategy**:
```typescript
// Phase 1: Facade Pattern - Keep existing server.js as proxy
// packages/graph-api/src/legacy-server.js (temporary)
const express = require('express');
const { GraphController } = require('./controllers/graph.controller');
const { createLegacyRoutes } = require('./routes/legacy.routes');

// Maintain exact same API surface during transition
const app = express();
app.use('/api/v1', createLegacyRoutes()); // Proxy to new controllers
app.use('*', (req, res) => {
  // Fallback to old behavior for unmatched routes
  require('./old-server-backup')(req, res);
});
```

#### 2. **Python Script Execution Breakage** 🔴 HIGH
**Problem**: Extracting chat functionality to FastAPI service breaks the current Node.js → Python execution pattern.

**Breaking Changes**:
- `real_llm_kg_script.py` execution path changes
- Inter-process communication failures
- Environment variable access issues
- File system path dependencies

**Mitigation Strategy**:
```typescript
// packages/graph-api/src/services/chat-bridge.service.ts
export class ChatBridgeService {
  private useNewService: boolean;
  
  constructor() {
    // Feature flag to gradually migrate
    this.useNewService = process.env.USE_NEW_CHAT_SERVICE === 'true';
  }
  
  async processChat(message: string): Promise<ChatResponse> {
    if (this.useNewService) {
      try {
        return await this.callNewChatService(message);
      } catch (error) {
        console.warn('New chat service failed, falling back to legacy:', error);
        return await this.callLegacyPythonScript(message);
      }
    }
    return await this.callLegacyPythonScript(message);
  }
  
  private async callLegacyPythonScript(message: string): Promise<ChatResponse> {
    // Keep existing Python execution logic intact
    const { spawn } = require('child_process');
    // ...existing implementation...
  }
}
```

#### 3. **Neo4j Query Compatibility** 🟡 MEDIUM
**Problem**: Repository pattern abstraction might change query behavior or performance characteristics.

**Breaking Changes**:
- Connection pooling changes affecting performance
- Transaction boundary modifications
- Query parameter binding differences
- GDS pipeline execution changes

**Mitigation Strategy**:
```typescript
// packages/graph-api/src/repositories/neo4j-migration.repository.ts
export class Neo4jMigrationRepository {
  private useNewRepository: boolean;
  private legacyDriver: Driver;
  private newRepository: NodeRepository;
  
  constructor() {
    this.useNewRepository = process.env.USE_NEW_REPOSITORY === 'true';
    // Maintain both old and new connections during transition
  }
  
  async findNodes(query: string, params: any): Promise<Node[]> {
    // Run queries in parallel for comparison during migration
    const [legacyResult, newResult] = await Promise.allSettled([
      this.executeLegacyQuery(query, params),
      this.useNewRepository ? this.newRepository.findAll(params) : null
    ]);
    
    // Log differences for validation
    if (this.useNewRepository && legacyResult.status === 'fulfilled' && newResult.status === 'fulfilled') {
      this.validateResultsMatch(legacyResult.value, newResult.value);
    }
    
    return this.useNewRepository && newResult.status === 'fulfilled' 
      ? newResult.value 
      : legacyResult.status === 'fulfilled' ? legacyResult.value : [];
  }
}
```

#### 4. **Frontend State Management Breakage** 🟡 MEDIUM
**Problem**: Moving to feature-slice design could break existing component communication and state flow.

**Breaking Changes**:
- Component prop interfaces changes
- State structure modifications
- Event handling pattern changes
- Routing configuration breakage

**Mitigation Strategy**:
```typescript
// packages/web-ui/src/migration/legacy-adapter.tsx
export const LegacyStateAdapter: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [migrationMode, setMigrationMode] = useState(
    localStorage.getItem('ui-migration-mode') === 'new'
  );
  
  // Provide both old and new state structures during transition
  const legacyState = useLegacyState();
  const newState = useNewFeatureState();
  
  return (
    <MigrationContext.Provider value={{ migrationMode, legacyState, newState }}>
      {children}
    </MigrationContext.Provider>
  );
};

// Custom hook for gradual migration
export const useAdaptiveState = () => {
  const { migrationMode, legacyState, newState } = useContext(MigrationContext);
  return migrationMode ? newState : legacyState;
};
```

### 🛡️ Comprehensive Mitigation Framework

#### 1. **Strangler Fig Pattern Implementation**
```typescript
// Progressive migration without big-bang changes
interface MigrationConfig {
  features: {
    newChatService: boolean;
    newRepository: boolean;
    newFrontendArchitecture: boolean;
  };
  rollbackThresholds: {
    errorRate: number;
    responseTime: number;
  };
}

class MigrationController {
  async migrateFeature(feature: string, percentage: number): Promise<void> {
    // Gradually route percentage of traffic to new implementation
    await this.updateFeatureFlag(feature, percentage);
    await this.monitorMetrics(feature);
    
    if (this.detectProblems(feature)) {
      await this.rollbackFeature(feature);
      throw new Error(`Migration rollback triggered for ${feature}`);
    }
  }
}
```

#### 2. **Database Migration Safety**
```sql
-- Create backup procedures before schema changes
CREATE PROCEDURE backup_graph_data()
BEGIN
    -- Export current graph state
    CALL apoc.export.cypher.all("backup_" + datetime() + ".cypher", {});
    
    -- Create rollback scripts
    CALL apoc.export.cypher.schema("schema_rollback.cypher", {});
END;

-- Validate data integrity after migrations
CREATE PROCEDURE validate_migration()
BEGIN
    -- Check node counts
    MATCH (n) RETURN count(n) as node_count;
    
    -- Check relationship integrity
    MATCH ()-[r]->() RETURN count(r) as rel_count;
    
    -- Validate GDS projections still work
    CALL gds.graph.exists('knowledge-graph') YIELD exists;
END;
```

#### 3. **API Backward Compatibility Layer**
```typescript
// packages/graph-api/src/middleware/backward-compatibility.middleware.ts
export const backwardCompatibilityMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Transform old API calls to new format
  if (req.path.startsWith('/api/legacy/')) {
    req.path = req.path.replace('/api/legacy/', '/api/v1/');
    req.body = transformLegacyRequestBody(req.body);
  }
  
  // Intercept responses to maintain old format
  const originalSend = res.send;
  res.send = function(data) {
    if (req.headers['x-legacy-format'] === 'true') {
      data = transformToLegacyResponse(data);
    }
    return originalSend.call(this, data);
  };
  
  next();
};
```

### 📊 Migration Monitoring & Rollback Strategy

#### 1. **Real-time Migration Dashboard**
```typescript
// packages/graph-api/src/monitoring/migration-metrics.ts
export class MigrationMetrics {
  private prometheus = require('prom-client');
  
  private migrationSuccessRate = new this.prometheus.Histogram({
    name: 'migration_success_rate',
    help: 'Success rate of migrated features',
    labelNames: ['feature', 'version']
  });
  
  private performanceComparison = new this.prometheus.Histogram({
    name: 'migration_performance_comparison',
    help: 'Performance comparison between old and new implementations',
    labelNames: ['feature', 'implementation']
  });
  
  recordMigrationAttempt(feature: string, success: boolean, responseTime: number): void {
    this.migrationSuccessRate
      .labels(feature, 'new')
      .observe(success ? 1 : 0);
      
    this.performanceComparison
      .labels(feature, 'new')
      .observe(responseTime);
  }
}
```

#### 2. **Automated Rollback Triggers**
```typescript
// packages/graph-api/src/monitoring/rollback-controller.ts
export class RollbackController {
  private thresholds = {
    errorRate: 0.05, // 5% error rate triggers rollback
    responseTimeIncrease: 2.0, // 2x response time increase
    userComplaintThreshold: 10
  };
  
  async monitorAndRollback(): Promise<void> {
    const metrics = await this.collectMetrics();
    
    if (this.shouldRollback(metrics)) {
      await this.executeRollback();
      await this.notifyTeam('Automatic rollback executed');
    }
  }
  
  private async executeRollback(): Promise<void> {
    // Revert feature flags
    await this.revertFeatureFlags();
    
    // Restore backup configurations
    await this.restoreBackupConfigs();
    
    // Restart services with old configuration
    await this.restartServicesWithOldConfig();
  }
}
```

### 🧪 Testing Strategy for Safe Migration

#### 1. **Parallel Testing Framework**
```typescript
// packages/shared/src/testing/parallel-test.framework.ts
export class ParallelTestFramework {
  async runParallelTests(testSuite: TestSuite): Promise<ComparisonResult> {
    const [legacyResults, newResults] = await Promise.all([
      this.runTestsOnLegacySystem(testSuite),
      this.runTestsOnNewSystem(testSuite)
    ]);
    
    return this.compareResults(legacyResults, newResults);
  }
  
  private compareResults(legacy: TestResult[], modern: TestResult[]): ComparisonResult {
    const differences = [];
    
    for (let i = 0; i < legacy.length; i++) {
      if (!this.resultsMatch(legacy[i], modern[i])) {
        differences.push({
          test: legacy[i].name,
          legacyResult: legacy[i].output,
          modernResult: modern[i].output,
          severity: this.assessSeverity(legacy[i], modern[i])
        });
      }
    }
    
    return { differences, riskLevel: this.calculateRiskLevel(differences) };
  }
}
```

#### 2. **Contract Testing for API Compatibility**
```typescript
// packages/graph-api/tests/contract/api-compatibility.test.ts
describe('API Backward Compatibility', () => {
  test('legacy graph query format still works', async () => {
    const legacyRequest = {
      query: 'MATCH (n) RETURN n LIMIT 10',
      parameters: {}
    };
    
    const response = await request(app)
      .post('/api/legacy/graph/query')
      .send(legacyRequest)
      .expect(200);
    
    expect(response.body).toHaveProperty('nodes');
    expect(response.body).toHaveProperty('relationships');
    // Ensure exact same structure as before
  });
  
  test('chat endpoint maintains response format', async () => {
    const chatRequest = { message: 'Show me all modules' };
    
    const [legacyResponse, newResponse] = await Promise.all([
      testLegacyChatEndpoint(chatRequest),
      testNewChatEndpoint(chatRequest)
    ]);
    
    expect(normalizeResponse(legacyResponse)).toEqual(
      normalizeResponse(newResponse)
    );
  });
});
```

### 📋 Revised Migration Phases with Risk Mitigation

#### Phase 0: Pre-Migration Safety Setup (Week 0)
```bash
# Create comprehensive backups
npm run create-full-backup
npm run validate-current-system
npm run setup-monitoring-dashboard

# Implement feature flags
npm run setup-feature-flags
npm run create-rollback-procedures
```

#### Phase 1: Foundation with Safety Rails (Weeks 1-2)
1. **Implement Strangler Fig Pattern**
   - Keep existing `server.js` running
   - Add new architecture alongside old code
   - Route 10% of traffic to new endpoints

2. **Database Migration Safety**
   - Run dual-write to old and new query patterns
   - Compare results in parallel
   - Maintain connection to original database structure

#### Phase 2: Gradual Service Extraction (Weeks 3-6)
1. **Chat Service Migration**
   - Keep Python script execution as fallback
   - Implement circuit breaker pattern
   - Monitor error rates continuously

2. **Frontend Migration Safety**
   - Maintain old components alongside new ones
   - Use feature flags for UI switching
   - Implement user preference for UI version

#### Phase 3: Validation & Optimization (Weeks 7-10)
1. **Performance Validation**
   - Compare performance metrics continuously
   - Automated rollback on performance degradation
   - User experience monitoring

2. **Full Migration Completion**
   - Remove old code only after 99.9% confidence
   - Keep rollback capabilities for 30 days post-migration

## 🚀 Detailed Migration Strategy

### Pre-Migration Checklist ✅
- [ ] **Full System Backup**: Database, code, and configuration
- [ ] **Monitoring Setup**: Comprehensive metrics and alerting
- [ ] **Feature Flag Infrastructure**: Gradual rollout capability
- [ ] **Rollback Procedures**: Automated and manual rollback plans
- [ ] **Testing Framework**: Parallel testing and validation
- [ ] **Team Training**: Migration procedures and emergency protocols

### Migration Safety Gates 🚪
Each phase requires passing these gates before proceeding:

1. **Functionality Gate**: All existing features work identically
2. **Performance Gate**: No degradation in response times
3. **Reliability Gate**: Error rates remain below baseline
4. **User Experience Gate**: No increase in user complaints
5. **Rollback Gate**: Ability to rollback within 5 minutes

### Emergency Procedures 🚨
```bash
# Immediate rollback script
./tools/scripts/emergency-rollback.sh

# Health check validation
./tools/scripts/validate-system-health.sh

# Restore from backup
./tools/scripts/restore-from-backup.sh [timestamp]
```

## Key Features

-   **Interactive Graph Visualization**: Explore nodes and relationships visually.
-   **Conversational AI Chat**: Ask natural language questions about the graph.
-   **Hidden Links Prediction**: Discover potential relationships using Neo4j GDS Node2Vec + Link Prediction pipeline (toggle in Analysis view).
-   **Advanced Analysis**: Perform impact, dependency, and test coverage analysis.
-   **Search and Filtering**: Quickly find nodes and filter the graph view.
-   **Persistent UI Settings**: Customize and save your UI configuration.

## Environment Variables (Hidden Links)

The hidden-links feature respects two optional variables:

```env
# Comma-separated list of node labels to include (default '*')
GDS_GRAPH_NODES=Module,Product,Workflow

# Relationship types to include (default '*')
GDS_GRAPH_RELATIONSHIPS=USES,CONTAINS
```

If unset, the entire graph is projected.

## Observability

A Prometheus-compatible metrics endpoint is now available on the backend:

```
GET /metrics
```

It exposes a `hidden_links_latency_ms` histogram capturing execution time of the GDS pipeline so you can monitor performance trends.

## Development

-   Backend API runs on port 3001.
-   Frontend development server runs on port 5173.
-   The chat functionality relies on a Python script (`real_llm_kg_script.py`) which is called by the backend.

## Enhanced Markdown Formatting 📝

The knowledge graph chat responses now feature rich Markdown formatting for better readability and user experience:

### Key Features

- **Structured Responses**: Clean sections with headers (## and ###)
- **Visual Elements**: Emojis, bullet points, and formatting for better scanning
- **Technical Content**: Code blocks for field names, configuration examples
- **Important Notes**: Blockquotes for warnings and key information  
- **Interactive Elements**: Suggested follow-up questions
- **Source Attribution**: Metadata footer showing document sources

### Example Response Structure

```markdown
## Topic Overview

**Key concept** description with important terms in bold.

### Features

- **Feature 1**: Description with `technical_terms` highlighted
- **Feature 2**: More details
  - Nested bullet points ✅
  - Status indicators ❌

### Important Notes

> ⚠️ **Warning**: Critical information in callout boxes

### Example Code

```yaml
configuration:
  type: hybrid
  value: 2.5
```

### 💡 Related Questions

- How do I configure this feature?
- What are the alternatives?

---
*📊 Response based on **X documents** from the knowledge graph.*
```

### Testing Markdown Formatting

Run the test script to see the formatting in action:

```bash
python test_markdown_formatting.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the ISC License.