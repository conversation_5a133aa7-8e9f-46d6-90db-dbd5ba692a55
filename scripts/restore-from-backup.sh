#!/bin/bash
set -e

BACKUP_DIR=${1:-"backups/latest"}
TEST_MODE=${2:-""}

if [ ! -d "$BACKUP_DIR" ]; then
  echo "❌ Backup directory not found: $BACKUP_DIR"
  exit 1
fi

echo "🔄 Restoring from backup: $BACKUP_DIR"

if [ "$TEST_MODE" = "--test-mode" ]; then
  echo "🧪 Running in test mode (no actual restore)"
  echo "📋 Would restore the following files:"
  find "$BACKUP_DIR" -type f -not -name "backup-manifest.txt" -not -name "checksums.md5" | sort
  echo "✅ Test mode completed successfully"
  exit 0
fi

# Validate backup first
echo "🔍 Validating backup before restore..."
./scripts/validate-backup.sh "$BACKUP_DIR"

# Create restore backup of current state
RESTORE_BACKUP_DIR="backups/pre-restore-$(date +%Y%m%d_%H%M%S)"
echo "💾 Creating pre-restore backup: $RESTORE_BACKUP_DIR"
./scripts/backup-system.sh >/dev/null
mv "backups/$(ls -t backups/ | grep -E '^[0-9]{8}_[0-9]{6}$' | head -n1)" "$RESTORE_BACKUP_DIR"

# Stop services if running
echo "🛑 Stopping services..."
docker-compose down 2>/dev/null || echo "⚠️  Docker compose not running"

# Restore configuration files
echo "📁 Restoring configuration files..."
cp "$BACKUP_DIR"/.env* . 2>/dev/null || echo "⚠️  No .env files to restore"
cp "$BACKUP_DIR"/docker-compose.yml . 2>/dev/null || echo "⚠️  No docker-compose.yml to restore"
cp "$BACKUP_DIR"/package.json . 2>/dev/null || echo "⚠️  No package.json to restore"
cp "$BACKUP_DIR"/requirements.txt . 2>/dev/null || echo "⚠️  No requirements.txt to restore"

# Restore service configurations
echo "🔧 Restoring service configurations..."
if [ -d "360t-kg-api" ]; then
  cp "$BACKUP_DIR"/.env* 360t-kg-api/ 2>/dev/null || echo "⚠️  No API .env files to restore"
fi
if [ -d "360t-kg-ui" ]; then
  cp "$BACKUP_DIR"/.env* 360t-kg-ui/ 2>/dev/null || echo "⚠️  No UI .env files to restore"
fi
if [ -d "proxy-server" ]; then
  cp "$BACKUP_DIR"/.env* proxy-server/ 2>/dev/null || echo "⚠️  No proxy .env files to restore"
fi

# Restore config directory if it exists in backup
if [ -d "$BACKUP_DIR/config" ]; then
  echo "⚙️  Restoring config directory..."
  rm -rf config/
  cp -r "$BACKUP_DIR/config" .
fi

# Restore database if dump exists
if [ -f "$BACKUP_DIR/neo4j.dump" ]; then
  echo "🗄️  Database dump found, starting Neo4j for restore..."
  docker-compose up -d neo4j
  
  # Wait for Neo4j to be ready
  echo "⏳ Waiting for Neo4j to be ready..."
  sleep 30
  
  # Copy dump to container and restore
  docker cp "$BACKUP_DIR/neo4j.dump" kg_neo4j:/var/lib/neo4j/
  docker exec kg_neo4j neo4j-admin database load neo4j --from-path=/var/lib/neo4j/ --overwrite-destination=true 2>/dev/null || echo "⚠️  Database restore failed"
  
  # Restart Neo4j
  docker-compose restart neo4j
else
  echo "⚠️  No database dump found in backup"
fi

# Start services
echo "🚀 Starting services..."
docker-compose up -d

echo "✅ Restore completed successfully"
echo "💾 Pre-restore backup saved to: $RESTORE_BACKUP_DIR"
echo "⏳ Waiting for services to start..."
sleep 30

# Validate restore
echo "🔍 Validating restored services..."
if curl -f http://localhost:3002/api/health >/dev/null 2>&1; then
  echo "✅ API service healthy"
else
  echo "⚠️  API service not responding"
fi

if curl -f http://localhost:5173 >/dev/null 2>&1; then
  echo "✅ Frontend service healthy"
else
  echo "⚠️  Frontend service not responding"
fi

echo "🎉 Restore process completed!"
