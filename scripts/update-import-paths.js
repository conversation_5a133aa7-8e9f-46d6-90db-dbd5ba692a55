#!/usr/bin/env node

/**
 * Import Path Update Script
 * 
 * Updates import paths to use the new directory structure
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.resolve(__dirname, '..');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

// Import path mappings
const importMappings = [
  // Relative to absolute imports
  {
    pattern: /from\s+['"]\.\.\/components\/([^'"]+)['"]/g,
    replacement: "from '@/components/$1'",
    description: 'Convert relative component imports to absolute'
  },
  {
    pattern: /from\s+['"]\.\.\/services\/([^'"]+)['"]/g,
    replacement: "from '@/services/$1'",
    description: 'Convert relative service imports to absolute'
  },
  {
    pattern: /from\s+['"]\.\.\/utils\/([^'"]+)['"]/g,
    replacement: "from '@/utils/$1'",
    description: 'Convert relative utility imports to absolute'
  },
  {
    pattern: /from\s+['"]\.\.\/hooks\/([^'"]+)['"]/g,
    replacement: "from '@/hooks/$1'",
    description: 'Convert relative hook imports to absolute'
  },
  {
    pattern: /from\s+['"]\.\.\/types\/([^'"]+)['"]/g,
    replacement: "from '@/types/$1'",
    description: 'Convert relative type imports to absolute'
  },
  {
    pattern: /from\s+['"]\.\.\/contexts\/([^'"]+)['"]/g,
    replacement: "from '@/contexts/$1'",
    description: 'Convert relative context imports to absolute'
  },
  
  // Shared library imports
  {
    pattern: /from\s+['"]shared\/([^'"]+)['"]/g,
    replacement: "from '@shared/$1'",
    description: 'Convert shared imports to use alias'
  },
  {
    pattern: /from\s+['"]\.\.\/shared\/([^'"]+)['"]/g,
    replacement: "from '@shared/$1'",
    description: 'Convert relative shared imports to alias'
  },
  
  // API imports
  {
    pattern: /from\s+['"]\.\.\/api\/([^'"]+)['"]/g,
    replacement: "from '@api/$1'",
    description: 'Convert relative API imports to alias'
  },
  
  // Config imports
  {
    pattern: /from\s+['"]\.\.\/config\/([^'"]+)['"]/g,
    replacement: "from '@config/$1'",
    description: 'Convert relative config imports to alias'
  },
  
  // Deep relative imports (multiple levels)
  {
    pattern: /from\s+['"]\.\.\/\.\.\/components\/([^'"]+)['"]/g,
    replacement: "from '@/components/$1'",
    description: 'Convert deep relative component imports'
  },
  {
    pattern: /from\s+['"]\.\.\/\.\.\/services\/([^'"]+)['"]/g,
    replacement: "from '@/services/$1'",
    description: 'Convert deep relative service imports'
  },
  
  // Import statements (not from)
  {
    pattern: /import\s+(['"])\.\.\/components\/([^'"]+)\1/g,
    replacement: "import '@/components/$2'",
    description: 'Convert relative component import statements'
  },
  {
    pattern: /import\s+(['"])shared\/([^'"]+)\1/g,
    replacement: "import '@shared/$2'",
    description: 'Convert shared import statements'
  }
];

// File extensions to process
const fileExtensions = ['.js', '.jsx', '.ts', '.tsx', '.vue'];

// Directories to process
const processDirectories = [
  'src',
  '360t-kg-ui/src',
  '360t-kg-api/src',
  'shared',
  'lib'
];

/**
 * Get all files to process
 */
function getFilesToProcess() {
  const files = [];
  
  for (const dir of processDirectories) {
    const fullPath = path.join(PROJECT_ROOT, dir);
    if (fs.existsSync(fullPath)) {
      const dirFiles = getFilesRecursively(fullPath);
      files.push(...dirFiles);
    }
  }
  
  return files.filter(file => 
    fileExtensions.some(ext => file.endsWith(ext))
  );
}

/**
 * Get files recursively from directory
 */
function getFilesRecursively(dir) {
  const files = [];
  
  try {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', 'dist', 'build', 'coverage'].includes(entry.name)) {
          files.push(...getFilesRecursively(fullPath));
        }
      } else if (entry.isFile()) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    logWarning(`Could not read directory: ${dir}`);
  }
  
  return files;
}

/**
 * Update imports in a single file
 */
function updateFileImports(filePath, dryRun = false) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let updatedContent = content;
    let changesMade = 0;
    const changes = [];
    
    for (const mapping of importMappings) {
      const matches = [...content.matchAll(mapping.pattern)];
      
      if (matches.length > 0) {
        updatedContent = updatedContent.replace(mapping.pattern, mapping.replacement);
        changesMade += matches.length;
        
        changes.push({
          description: mapping.description,
          count: matches.length,
          examples: matches.slice(0, 3).map(match => match[0])
        });
      }
    }
    
    if (changesMade > 0) {
      if (!dryRun) {
        fs.writeFileSync(filePath, updatedContent, 'utf8');
      }
      
      return {
        file: path.relative(PROJECT_ROOT, filePath),
        changesMade,
        changes
      };
    }
    
    return null;
  } catch (error) {
    logError(`Error processing file ${filePath}: ${error.message}`);
    return null;
  }
}

/**
 * Update all import paths
 */
function updateAllImports(dryRun = false) {
  logInfo('🔄 Starting import path updates...');
  
  if (dryRun) {
    logInfo('Running in dry-run mode - no files will be modified');
  }
  
  const files = getFilesToProcess();
  logInfo(`Found ${files.length} files to process`);
  
  const results = [];
  let totalChanges = 0;
  
  for (const file of files) {
    const result = updateFileImports(file, dryRun);
    if (result) {
      results.push(result);
      totalChanges += result.changesMade;
    }
  }
  
  // Report results
  console.log('\n📊 Import Update Results:');
  console.log('========================');
  
  if (results.length === 0) {
    logInfo('No import paths needed updating');
    return;
  }
  
  logSuccess(`Updated ${totalChanges} import statements in ${results.length} files`);
  
  // Show detailed results
  for (const result of results) {
    console.log(`\n📄 ${result.file} (${result.changesMade} changes):`);
    
    for (const change of result.changes) {
      console.log(`  • ${change.description} (${change.count} occurrences)`);
      
      if (change.examples.length > 0) {
        console.log(`    Examples:`);
        for (const example of change.examples) {
          console.log(`      - ${example}`);
        }
      }
    }
  }
  
  if (dryRun) {
    console.log('\n💡 Run without --dry-run to apply these changes');
  } else {
    logSuccess('\n🎉 Import path updates completed successfully!');
  }
}

/**
 * Validate import paths
 */
function validateImports() {
  logInfo('🔍 Validating import paths...');
  
  const files = getFilesToProcess();
  const issues = [];
  
  for (const file of files) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const relativePath = path.relative(PROJECT_ROOT, file);
      
      // Check for remaining relative imports that should be absolute
      const relativeImports = content.match(/from\s+['"]\.\.\/[^'"]+['"]/g);
      if (relativeImports) {
        issues.push({
          file: relativePath,
          type: 'relative-imports',
          count: relativeImports.length,
          examples: relativeImports.slice(0, 3)
        });
      }
      
      // Check for imports that might not exist
      const importPaths = content.match(/from\s+['"]@[^'"]+['"]/g);
      if (importPaths) {
        for (const importPath of importPaths) {
          const match = importPath.match(/from\s+['"](@[^'"]+)['"]/);
          if (match) {
            const aliasPath = match[1];
            // This would need more sophisticated checking in a real implementation
            // For now, just log that we found alias imports
          }
        }
      }
      
    } catch (error) {
      logError(`Error validating file ${file}: ${error.message}`);
    }
  }
  
  if (issues.length === 0) {
    logSuccess('✅ All import paths look good!');
  } else {
    logWarning(`Found ${issues.length} files with potential import issues:`);
    
    for (const issue of issues) {
      console.log(`\n📄 ${issue.file}:`);
      console.log(`  • ${issue.type}: ${issue.count} occurrences`);
      
      if (issue.examples) {
        console.log(`    Examples:`);
        for (const example of issue.examples) {
          console.log(`      - ${example}`);
        }
      }
    }
  }
}

/**
 * Show usage information
 */
function showUsage() {
  console.log(`
🔧 Import Path Update Script

Usage: node scripts/update-import-paths.js [OPTIONS]

Options:
  --dry-run     Show what would be changed without modifying files
  --validate    Validate import paths and report issues
  --help        Show this help message

Examples:
  node scripts/update-import-paths.js                # Update all import paths
  node scripts/update-import-paths.js --dry-run      # Preview changes
  node scripts/update-import-paths.js --validate     # Check for issues

This script updates import paths to use the new directory structure and path aliases.
`);
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    showUsage();
    return;
  }
  
  const dryRun = args.includes('--dry-run');
  const validate = args.includes('--validate');
  
  if (validate) {
    validateImports();
  } else {
    updateAllImports(dryRun);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
