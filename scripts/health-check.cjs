#!/usr/bin/env node

/**
 * Health Check Script
 * 
 * Command-line health monitoring for all services
 */

const https = require('https');
const http = require('http');

class HealthChecker {
  constructor() {
    this.timeout = 5000;
    this.endpoints = {
      database: 'http://localhost:7474/db/neo4j/tx/commit',
      api: 'http://localhost:3002/api/health',
      frontend: 'http://localhost:5173',
      proxy: 'http://localhost:3001/health'
    };
  }

  async checkEndpoint(name, url, options = {}) {
    const startTime = Date.now();
    
    return new Promise((resolve) => {
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;
      
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        timeout: this.timeout,
        headers: options.headers || {}
      };

      const req = client.request(requestOptions, (res) => {
        const responseTime = Date.now() - startTime;
        
        if (res.statusCode >= 200 && res.statusCode < 400) {
          resolve({
            name,
            status: 'healthy',
            responseTime,
            statusCode: res.statusCode
          });
        } else {
          resolve({
            name,
            status: 'unhealthy',
            responseTime,
            statusCode: res.statusCode,
            error: `HTTP ${res.statusCode}`
          });
        }
      });

      req.on('error', (error) => {
        resolve({
          name,
          status: 'unhealthy',
          responseTime: Date.now() - startTime,
          error: error.message
        });
      });

      req.on('timeout', () => {
        req.destroy();
        resolve({
          name,
          status: 'unhealthy',
          responseTime: Date.now() - startTime,
          error: 'Request timeout'
        });
      });

      if (options.body) {
        req.write(options.body);
      }
      
      req.end();
    });
  }

  async checkDatabase() {
    return this.checkEndpoint('database', this.endpoints.database, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        statements: [{ statement: 'RETURN 1 as health_check' }]
      })
    });
  }

  async checkAPI() {
    return this.checkEndpoint('api', this.endpoints.api);
  }

  async checkFrontend() {
    return this.checkEndpoint('frontend', this.endpoints.frontend);
  }

  async checkProxy() {
    return this.checkEndpoint('proxy', this.endpoints.proxy);
  }

  async checkLLM() {
    // Check if LLM providers are configured
    const providers = {
      ollama: process.env.LLM_OLLAMA_BASE_URL,
      anthropic: process.env.LLM_ANTHROPIC_API_KEY,
      google: process.env.LLM_GOOGLE_API_KEY,
      azure: process.env.LLM_AZURE_API_KEY
    };

    const configuredProviders = Object.entries(providers)
      .filter(([_, value]) => !!value)
      .map(([key, _]) => key);

    if (configuredProviders.length === 0) {
      return {
        name: 'llm',
        status: 'degraded',
        responseTime: 0,
        error: 'No LLM providers configured',
        metadata: { configuredProviders: [] }
      };
    }

    return {
      name: 'llm',
      status: 'healthy',
      responseTime: 0,
      metadata: { 
        configuredProviders,
        primaryProvider: process.env.LLM_PRIMARY_PROVIDER || 'ollama'
      }
    };
  }

  async checkAll() {
    console.log('🏥 Running health checks...\n');
    
    const checks = await Promise.all([
      this.checkDatabase(),
      this.checkAPI(),
      this.checkFrontend(),
      this.checkProxy(),
      this.checkLLM()
    ]);

    const overall = checks.every(check => check.status === 'healthy');
    
    return {
      timestamp: new Date().toISOString(),
      overall,
      checks
    };
  }

  formatReport(healthData) {
    const { overall, checks, timestamp } = healthData;
    
    let report = `🏥 Health Check Report\n`;
    report += `===================\n`;
    report += `Timestamp: ${new Date(timestamp).toLocaleString()}\n`;
    report += `Overall Status: ${overall ? '✅ All services healthy' : '❌ Some services unhealthy'}\n\n`;
    
    report += `Service Details:\n`;
    report += `================\n`;
    
    checks.forEach(check => {
      const icon = check.status === 'healthy' ? '✅' : 
                   check.status === 'degraded' ? '⚠️' : '❌';
      
      report += `${icon} ${check.name.toUpperCase()}: ${check.status.toUpperCase()}`;
      
      if (check.responseTime > 0) {
        report += ` (${check.responseTime}ms)`;
      }
      
      if (check.statusCode) {
        report += ` [HTTP ${check.statusCode}]`;
      }
      
      report += '\n';
      
      if (check.error) {
        report += `   Error: ${check.error}\n`;
      }
      
      if (check.metadata) {
        report += `   Metadata: ${JSON.stringify(check.metadata)}\n`;
      }
      
      report += '\n';
    });
    
    return report;
  }

  async runContinuous(interval = 30000) {
    console.log(`🔄 Starting continuous health monitoring (${interval/1000}s intervals)`);
    console.log('Press Ctrl+C to stop\n');
    
    const runCheck = async () => {
      try {
        const healthData = await this.checkAll();
        console.clear();
        console.log(this.formatReport(healthData));
        
        if (!healthData.overall) {
          console.log('🚨 ALERT: Some services are unhealthy!');
        }
        
        console.log(`Next check in ${interval/1000} seconds...`);
      } catch (error) {
        console.error('❌ Health check failed:', error.message);
      }
    };
    
    // Run initial check
    await runCheck();
    
    // Set up interval
    const intervalId = setInterval(runCheck, interval);
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n👋 Stopping health monitoring...');
      clearInterval(intervalId);
      process.exit(0);
    });
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const checker = new HealthChecker();
  
  if (args.includes('--continuous') || args.includes('-c')) {
    const intervalArg = args.find(arg => arg.startsWith('--interval='));
    const interval = intervalArg ? parseInt(intervalArg.split('=')[1]) * 1000 : 30000;
    await checker.runContinuous(interval);
  } else if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🏥 Health Check Script

Usage:
  node scripts/health-check.cjs [options]

Options:
  --continuous, -c     Run continuous monitoring
  --interval=N         Set check interval in seconds (default: 30)
  --help, -h          Show this help message

Examples:
  node scripts/health-check.cjs                    # Single health check
  node scripts/health-check.cjs --continuous       # Continuous monitoring
  node scripts/health-check.cjs -c --interval=60   # Check every 60 seconds
`);
  } else {
    // Single health check
    try {
      const healthData = await checker.checkAll();
      console.log(checker.formatReport(healthData));
      
      // Exit with error code if unhealthy
      process.exit(healthData.overall ? 0 : 1);
    } catch (error) {
      console.error('❌ Health check failed:', error.message);
      process.exit(1);
    }
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Unexpected error:', error.message);
    process.exit(1);
  });
}

module.exports = { HealthChecker };
