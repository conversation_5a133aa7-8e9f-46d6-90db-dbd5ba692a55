#!/bin/bash
set -e

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backups/$TIMESTAMP"
mkdir -p "$BACKUP_DIR"

echo "🔄 Creating backup: $BACKUP_DIR"

# Backup configurations
echo "📁 Backing up configuration files..."
find . -name ".env*" -not -path "./node_modules/*" -not -path "./fastapi_env/*" -not -path "./google-cloud-sdk/*" -exec cp {} "$BACKUP_DIR/" \; 2>/dev/null || true
cp docker-compose.yml "$BACKUP_DIR/" 2>/dev/null || true
cp -r config/ "$BACKUP_DIR/" 2>/dev/null || true

# Backup package files
echo "📦 Backing up package files..."
cp package.json "$BACKUP_DIR/" 2>/dev/null || true
cp package-lock.json "$BACKUP_DIR/" 2>/dev/null || true
cp requirements.txt "$BACKUP_DIR/" 2>/dev/null || true

# Backup service configurations
echo "🔧 Backing up service configurations..."
cp -r 360t-kg-api/.env* "$BACKUP_DIR/" 2>/dev/null || true
cp -r 360t-kg-ui/.env* "$BACKUP_DIR/" 2>/dev/null || true
cp -r proxy-server/.env* "$BACKUP_DIR/" 2>/dev/null || true

# Backup Docker files
echo "🐳 Backing up Docker configurations..."
cp Dockerfile* "$BACKUP_DIR/" 2>/dev/null || true

# Backup database (if Neo4j container is running)
echo "🗄️  Backing up database..."
if docker ps | grep -q kg_neo4j; then
  echo "Neo4j container found, creating database dump..."
  docker exec kg_neo4j neo4j-admin database dump neo4j --to-path=/backups 2>/dev/null || echo "⚠️  Database dump failed (container may not be ready)"
  docker cp kg_neo4j:/backups/neo4j.dump "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  Could not copy database dump"
else
  echo "⚠️  Neo4j container not running, skipping database backup"
fi

# Create backup manifest
echo "📋 Creating backup manifest..."
cat > "$BACKUP_DIR/backup-manifest.txt" << EOF
Backup created: $(date)
Backup directory: $BACKUP_DIR
Files backed up:
$(find "$BACKUP_DIR" -type f | sort)
EOF

# Calculate checksums for integrity
echo "🔐 Calculating checksums..."
find "$BACKUP_DIR" -type f -exec md5sum {} \; > "$BACKUP_DIR/checksums.md5" 2>/dev/null || true

echo "✅ Backup completed successfully: $BACKUP_DIR"
echo "📊 Backup size: $(du -sh "$BACKUP_DIR" | cut -f1)"
echo "📁 Files backed up: $(find "$BACKUP_DIR" -type f | wc -l)"

# Create symlink to latest backup
rm -f backups/latest
ln -sf "$TIMESTAMP" backups/latest

echo "🔗 Latest backup symlink created: backups/latest -> $TIMESTAMP"
