#!/bin/bash
set -e

BACKUP_DIR=${1:-"backups/latest"}

if [ ! -d "$BACKUP_DIR" ]; then
  echo "❌ Backup directory not found: $BACKUP_DIR"
  exit 1
fi

echo "🔍 Validating backup: $BACKUP_DIR"

# Check if backup manifest exists
if [ ! -f "$BACKUP_DIR/backup-manifest.txt" ]; then
  echo "❌ Backup manifest not found"
  exit 1
fi

echo "📋 Backup manifest found"

# Validate checksums if available
if [ -f "$BACKUP_DIR/checksums.md5" ]; then
  echo "🔐 Validating checksums..."
  cd "$BACKUP_DIR"
  if md5sum -c checksums.md5 >/dev/null 2>&1; then
    echo "✅ Checksums validated successfully"
  else
    echo "❌ Checksum validation failed"
    exit 1
  fi
  cd - >/dev/null
else
  echo "⚠️  No checksums file found, skipping integrity check"
fi

# Check for critical files
echo "📁 Checking for critical files..."
CRITICAL_FILES=(
  "docker-compose.yml"
  "package.json"
  "requirements.txt"
)

for file in "${CRITICAL_FILES[@]}"; do
  if [ -f "$BACKUP_DIR/$file" ]; then
    echo "✅ Found: $file"
  else
    echo "⚠️  Missing: $file"
  fi
done

# Check backup size
BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
echo "📊 Backup size: $BACKUP_SIZE"

# Count files
FILE_COUNT=$(find "$BACKUP_DIR" -type f | wc -l)
echo "📁 Total files: $FILE_COUNT"

if [ "$FILE_COUNT" -lt 5 ]; then
  echo "⚠️  Backup seems incomplete (less than 5 files)"
  exit 1
fi

echo "✅ Backup validation completed successfully"
