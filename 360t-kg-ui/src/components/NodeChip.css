/* NodeChip Component Styles */

.node-chip {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background-color: rgba(0, 151, 58, 0.08);
  border: 1px solid rgba(0, 151, 58, 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  font-size: 10px;
  max-width: 120px;
  min-width: 40px;
  overflow: hidden;
}

.node-chip:hover {
  background-color: rgba(0, 151, 58, 0.12);
  border-color: rgba(0, 151, 58, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.node-chip:focus {
  outline: 2px solid rgba(0, 151, 58, 0.5);
  outline-offset: 2px;
}

.node-chip:active {
  transform: translateY(0);
  background-color: rgba(0, 151, 58, 0.15);
}

/* Icon */
.node-chip-icon {
  flex-shrink: 0;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: hue-rotate(0deg) saturate(1.2);
  transition: filter 0.2s ease;
}

.node-chip:hover .node-icon {
  filter: hue-rotate(5deg) saturate(1.4) brightness(1.1);
}

/* ID Display */
.node-chip-id {
  font-size: 9px;
  color: var(--360t-primary, #00973a);
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.1;
  flex: 1;
  min-width: 0;
}

/* Tooltip */
.node-chip-tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 8px;
  padding: 8px 10px;
  background-color: var(--360t-dark, #1a1a1a);
  color: white;
  border-radius: 6px;
  font-size: 11px;
  line-height: 1.3;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  min-width: 160px;
  max-width: 250px;
  word-wrap: break-word;
  
  /* Tooltip arrow */
  &::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-bottom-color: var(--360t-dark, #1a1a1a);
  }
}

.tooltip-header {
  margin-bottom: 4px;
  color: var(--360t-primary, #00973a);
}

.tooltip-id {
  margin-bottom: 4px;
  font-size: 10px;
  color: #ccc;
}

.tooltip-labels {
  margin-bottom: 4px;
  font-size: 10px;
}

.tooltip-action {
  font-size: 9px;
  color: #999;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .node-chip {
    gap: 3px;
    padding: 2px 4px;
    font-size: 9px;
    max-width: 100px;
  }
  
  .node-chip-icon {
    width: 12px;
    height: 12px;
  }
  
  .node-chip-id {
    font-size: 8px;
  }
  
  .node-chip-tooltip {
    min-width: 140px;
    max-width: 200px;
    font-size: 10px;
    padding: 6px 8px;
  }
  
  .tooltip-header {
    font-size: 10px;
  }
  
  .tooltip-id,
  .tooltip-labels {
    font-size: 9px;
  }
  
  .tooltip-action {
    font-size: 8px;
  }
}

@media (max-width: 480px) {
  .node-chip {
    gap: 2px;
    padding: 1px 3px;
    font-size: 8px;
    max-width: 80px;
  }
  
  .node-chip-icon {
    width: 10px;
    height: 10px;
  }
  
  .node-chip-id {
    font-size: 7px;
  }
  
  .node-chip-tooltip {
    min-width: 120px;
    max-width: 180px;
    font-size: 9px;
    padding: 4px 6px;
    
    &::before {
      border-width: 3px;
    }
  }
}

@media (prefers-contrast: high) {
  .node-chip {
    border-color: rgba(0, 151, 58, 0.6);
    background-color: rgba(0, 151, 58, 0.15);
  }
  
  .node-chip:hover {
    border-color: rgba(0, 151, 58, 0.8);
    background-color: rgba(0, 151, 58, 0.25);
  }
  
  .node-chip:hover .node-chip-id {
    color: var(--360t-dark, #1a1a1a);
    font-weight: 600;
  }
  
  .node-chip-tooltip {
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
}

@media (prefers-reduced-motion: reduce) {
  .node-chip {
    transition: none;
  }
  
  .node-chip:hover {
    transform: none;
  }
  
  .node-icon {
    transition: none;
  }
}

.node-chip:focus-visible {
  outline: 2px solid rgba(0, 151, 58, 0.7);
  outline-offset: 2px;
} 