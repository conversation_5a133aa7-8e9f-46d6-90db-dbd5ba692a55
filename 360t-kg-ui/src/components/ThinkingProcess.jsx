import React, { useState, useEffect, useRef } from 'react';
import '../styles/ThinkingProcess.css';

/**
 * ThinkingProcess component for displaying the LLM's thinking process
 * Shows debug information and search results in a collapsible format
 */
function ThinkingProcess({ 
  thinkingData = null, 
  isStreaming = false, 
  isCollapsed = true,
  onToggle = null 
}) {
  const [collapsed, setCollapsed] = useState(isCollapsed);
  const [displayedContent, setDisplayedContent] = useState('');
  const contentRef = useRef(null);

  // Auto-scroll to bottom when content updates during streaming
  useEffect(() => {
    if (contentRef.current && isStreaming) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  }, [displayedContent, isStreaming]);

  // Update displayed content when thinking data changes
  useEffect(() => {
    if (thinkingData) {
      const sections = [];

      // Add search debug information
      if (thinkingData.searchDebug) {
        sections.push(formatSearchDebug(thinkingData.searchDebug));
      }

      // Add context information
      if (thinkingData.context) {
        sections.push(formatContextInfo(thinkingData.context));
      }

      // Add citations
      if (thinkingData.citations) {
        sections.push(formatCitations(thinkingData.citations));
      }

      // Add LLM provider information
      if (thinkingData.llmProvider) {
        sections.push(formatLLMProviderInfo(thinkingData.llmProvider));
      }

      // Add prompt information
      if (thinkingData.prompt) {
        sections.push(formatPromptInfo(thinkingData.prompt));
      }

      setDisplayedContent(sections);
    }
  }, [thinkingData]);

  const formatSearchDebug = (searchDebug) => {
    return (
      <div className="search-debug-section">
        <h4>🔍 Search Information</h4>
        <div className="search-metadata">
          <div className="metadata-item">
            <span className="metadata-label">Query:</span>
            <span className="metadata-value">{searchDebug.query || 'N/A'}</span>
          </div>
          <div className="metadata-item">
            <span className="metadata-label">Search Recipe:</span>
            <span className="metadata-value">{searchDebug.searchRecipe || 'N/A'}</span>
          </div>
          <div className="metadata-item">
            <span className="metadata-label">Results Limit:</span>
            <span className="metadata-value">{searchDebug.resultsLimit || 'N/A'}</span>
          </div>
          <div className="metadata-item">
            <span className="metadata-label">Total Results Found:</span>
            <span className="metadata-value">{searchDebug.totalResultsFound || 'N/A'}</span>
          </div>
          {searchDebug.groupIds && (
            <div className="metadata-item">
              <span className="metadata-label">Group ID Filter:</span>
              <span className="metadata-value">
                Found {searchDebug.groupIds.length} group_ids containing '{searchDebug.groupIdFilter}':
                {searchDebug.groupIds.join(', ')}
              </span>
            </div>
          )}
        </div>

        {searchDebug.searchResults && searchDebug.searchResults.length > 0 && (
          <div className="search-results-section">
            <h5>📊 Detailed Search Results</h5>
            <div className="search-results-list">
              {searchDebug.searchResults.map((result, index) => (
                <div key={index} className={`search-result-item ${result.type.toLowerCase()}`}>
                  <div className="result-header">
                    <span className={`result-type-badge ${result.type.toLowerCase()}`}>
                      {result.type === 'Edge' ? '🔗' : result.type === 'Node' ? '🔵' : '❓'} {result.type}
                    </span>
                    <span className="result-index">#{result.index}</span>
                  </div>
                  <div className="result-details">
                    <div className="result-detail">
                      <span className="detail-label">UUID:</span>
                      <span className="detail-value">{result.uuid}</span>
                    </div>
                    <div className="result-detail">
                      <span className="detail-label">Group ID:</span>
                      <span className="detail-value">{result.group_id}</span>
                    </div>
                    {result.name !== 'N/A' && (
                      <div className="result-detail">
                        <span className="detail-label">Name:</span>
                        <span className="detail-value">{result.name}</span>
                      </div>
                    )}
                    {result.fact && (
                      <div className="result-detail">
                        <span className="detail-label">Fact:</span>
                        <span className="detail-value">{result.fact}</span>
                      </div>
                    )}
                    {result.summary && (
                      <div className="result-detail">
                        <span className="detail-label">Summary:</span>
                        <span className="detail-value">{result.summary}</span>
                      </div>
                    )}
                    {result.source_node_uuid && (
                      <div className="result-detail">
                        <span className="detail-label">Source Node:</span>
                        <span className="detail-value">{result.source_node_uuid}</span>
                      </div>
                    )}
                    {result.target_node_uuid && (
                      <div className="result-detail">
                        <span className="detail-label">Target Node:</span>
                        <span className="detail-value">{result.target_node_uuid}</span>
                      </div>
                    )}
                    <div className="result-detail">
                      <span className="detail-label">Created:</span>
                      <span className="detail-value">{result.created_at}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const formatContextInfo = (context) => {
    return (
      <div className="context-section">
        <h4>📄 Context Information</h4>
        <div className="context-metadata">
          <div className="metadata-item">
            <span className="metadata-label">Context Length:</span>
            <span className="metadata-value">{context.length || 0} characters</span>
          </div>
        </div>
        <div className="context-content">
          <h5>Context Content:</h5>
          <pre className="context-text">{context.content || 'No content available'}</pre>
        </div>
      </div>
    );
  };

  const formatCitations = (citations) => {
    return (
      <div className="citations-section">
        <h4>📚 Citations</h4>
        <div className="citations-list">
          {Array.isArray(citations) && citations.length > 0 ? (
            citations.map((citation, index) => (
              <span key={index} className="citation-item">{citation}</span>
            ))
          ) : (
            <span className="no-citations">No citations available</span>
          )}
        </div>
      </div>
    );
  };

  const formatPromptInfo = (prompt) => {
    return (
      <div className="prompt-section">
        <h4>📝 Prompt Information</h4>
        <div className="prompt-metadata">
          <div className="metadata-item">
            <span className="metadata-label">Prompt Length:</span>
            <span className="metadata-value">{prompt.length || 0} characters</span>
          </div>
        </div>
        <div className="prompt-content">
          <h5>Prompt Content:</h5>
          <pre className="prompt-text">{prompt.content || 'No content available'}</pre>
        </div>
      </div>
    );
  };

  const formatLLMProviderInfo = (llmProvider) => {
    return (
      <div className="llm-provider-section">
        <h4>🤖 LLM Provider Information</h4>
        <div className="provider-metadata">
          <div className="metadata-item">
            <span className="metadata-label">Provider:</span>
            <span className="metadata-value">{llmProvider.provider || 'N/A'}</span>
          </div>
          <div className="metadata-item">
            <span className="metadata-label">Model:</span>
            <span className="metadata-value">{llmProvider.model || 'N/A'}</span>
          </div>
          <div className="metadata-item">
            <span className="metadata-label">URL:</span>
            <span className="metadata-value">{llmProvider.url || 'N/A'}</span>
          </div>
        </div>
        <div className="provider-summary">
          <strong>Generated by:</strong> {llmProvider.provider || 'Unknown'} - {llmProvider.model || 'Unknown Model'}
        </div>
      </div>
    );
  };

  const handleToggle = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    if (onToggle) {
      onToggle(newCollapsed);
    }
  };

  // Don't render if no thinking data
  if (!thinkingData && !isStreaming) {
    return null;
  }

  return (
    <div className={`thinking-process ${collapsed ? 'collapsed' : 'expanded'} ${isStreaming ? 'streaming' : 'complete'}`}>
      {/* Toggle Header */}
      <div className="thinking-header" onClick={handleToggle}>
        <div className="thinking-title">
          <span className="thinking-icon">💭</span>
          <span className="thinking-label">
            {isStreaming ? 'Thinking process (live)' : 'View thinking process'}
          </span>
          {isStreaming && (
            <div className="thinking-indicator">
              <div className="thinking-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          )}
        </div>
        <div className={`thinking-toggle ${collapsed ? 'collapsed' : 'expanded'}`}>
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path 
              d="M3 4.5L6 7.5L9 4.5" 
              stroke="currentColor" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>

      {/* Collapsible Content */}
      {!collapsed && (
        <div className="thinking-content">
          <div className="thinking-scroll-container" ref={contentRef}>
            {Array.isArray(displayedContent) && displayedContent.length > 0 ? (
              <div className="thinking-sections">
                {displayedContent.map((section, index) => (
                  <div key={index} className="thinking-section">
                    {section}
                  </div>
                ))}
              </div>
            ) : (
              <div className="thinking-placeholder">
                {isStreaming ? 'Preparing search...' : 'No thinking data available'}
              </div>
            )}
          </div>
          
          {/* Streaming Stats */}
          {isStreaming && thinkingData?.stats && (
            <div className="thinking-stats">
              <div className="stat-item">
                <span className="stat-label">Processing:</span>
                <span className="stat-value">{thinkingData.stats.stage || 'Initializing'}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Elapsed:</span>
                <span className="stat-value">{thinkingData.stats.elapsed || '0'}ms</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default ThinkingProcess;
