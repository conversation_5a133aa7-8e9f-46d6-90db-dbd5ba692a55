import React, { useState, useEffect, useRef } from 'react';
import '../styles/StreamingMessage.css';

/**
 * StreamingMessage component for displaying real-time AI responses
 * Shows tokens as they arrive from the server with typing animation
 */
function StreamingMessage({ 
  message, 
  isStreaming = false, 
  onStreamComplete = null,
  showThinking = true 
}) {
  const [displayedContent, setDisplayedContent] = useState('');
  const [streamingStats, setStreamingStats] = useState(null);
  const [isThinking, setIsThinking] = useState(false);
  const contentRef = useRef(null);
  const streamStartTime = useRef(null);
  const tokenCount = useRef(0);

  // Auto-scroll to bottom when content updates
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  }, [displayedContent]);

  // Handle streaming updates
  useEffect(() => {
    if (isStreaming && message.content) {
      // If we're starting to stream
      if (!streamStartTime.current) {
        streamStartTime.current = Date.now();
        setIsThinking(true);
        tokenCount.current = 0;
      }

      // Update displayed content
      setDisplayedContent(message.content);
      tokenCount.current = message.content.length;

      // Update streaming stats
      const elapsed = Date.now() - streamStartTime.current;
      setStreamingStats({
        tokensPerSecond: tokenCount.current / (elapsed / 1000),
        elapsed: elapsed,
        tokenCount: tokenCount.current
      });
    } else if (!isStreaming && message.content) {
      // Streaming is complete
      setDisplayedContent(message.content);
      setIsThinking(false);
      
      if (onStreamComplete && streamStartTime.current) {
        const totalTime = Date.now() - streamStartTime.current;
        onStreamComplete({
          totalTime,
          tokenCount: tokenCount.current,
          tokensPerSecond: tokenCount.current / (totalTime / 1000)
        });
      }
      
      // Reset for next stream
      streamStartTime.current = null;
    }
  }, [message.content, isStreaming, onStreamComplete]);

  const formatStreamingStats = () => {
    if (!streamingStats) return null;
    
    return (
      <div className="streaming-stats">
        <span className="stat">
          {streamingStats.tokensPerSecond.toFixed(1)} tokens/s
        </span>
        <span className="stat">
          {streamingStats.tokenCount} tokens
        </span>
        <span className="stat">
          {(streamingStats.elapsed / 1000).toFixed(1)}s
        </span>
      </div>
    );
  };

  return (
    <div className={`streaming-message ${message.role} ${isStreaming ? 'streaming' : 'complete'}`}>
      {/* Message Header */}
      <div className="message-header">
        <span className="role-label">
          {message.role === 'assistant' ? '🤖 Assistant' : '👤 You'}
        </span>
        
        {isStreaming && (
          <div className="streaming-indicator">
            <div className="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <span className="streaming-text">Thinking...</span>
          </div>
        )}
        
        {streamingStats && isStreaming && (
          formatStreamingStats()
        )}
      </div>

      {/* Message Content */}
      <div 
        ref={contentRef}
        className="message-content"
        dangerouslySetInnerHTML={{
          __html: formatMessageContent(displayedContent)
        }}
      />

      {/* Thinking Indicator for DeepSeek-style reasoning */}
      {isThinking && showThinking && displayedContent.includes('<think>') && (
        <div className="thinking-indicator">
          <span className="thinking-label">🧠 Reasoning:</span>
          <div className="thinking-content">
            {extractThinkingContent(displayedContent)}
          </div>
        </div>
      )}

      {/* Source Documents */}
      {message.sourceDocuments && message.sourceDocuments.length > 0 && (
        <div className="source-documents">
          <h4>📚 Sources:</h4>
          <ul>
            {message.sourceDocuments.map((doc, index) => (
              <li key={index}>
                <strong>{doc.title || `Document ${index + 1}`}</strong>
                {doc.content && (
                  <p>{doc.content.substring(0, 150)}...</p>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Performance Info */}
      {message.timing && !isStreaming && (
        <div className="performance-info">
          <details>
            <summary>⏱️ Performance Details</summary>
            <div className="timing-details">
              <div>Total Time: {message.timing.backend?.totalDuration || 0}ms</div>
              <div>Streaming: {message.timing.streaming?.total_duration_ms || 0}ms</div>
              <div>Tokens/sec: {message.timing.streaming?.tokens_per_second?.toFixed(1) || 'N/A'}</div>
            </div>
          </details>
        </div>
      )}

      {/* Timestamp */}
      <div className="message-timestamp">
        {new Date(message.timestamp).toLocaleTimeString()}
      </div>
    </div>
  );
}

/**
 * Format message content with markdown-like styling
 */
function formatMessageContent(content) {
  if (!content) return '';
  
  // Basic markdown formatting
  let formatted = content
    // Bold text
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Headers
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
    // Line breaks
    .replace(/\n/g, '<br>')
    // Citations
    .replace(/\[(\d+)\]/g, '<span class="citation">[$1]</span>');
  
  return formatted;
}

/**
 * Extract thinking content from DeepSeek-style reasoning tags
 */
function extractThinkingContent(content) {
  const thinkMatch = content.match(/<think>(.*?)<\/think>/s);
  if (thinkMatch) {
    return thinkMatch[1].trim();
  }
  return '';
}

export default StreamingMessage;
