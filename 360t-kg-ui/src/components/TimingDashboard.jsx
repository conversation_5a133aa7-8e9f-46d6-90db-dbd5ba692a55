import React, { useState, useEffect } from 'react';
import { useChatState } from '../contexts/ChatContext';
import '../styles/TimingDashboard.css';

/**
 * TimingDashboard component for displaying real-time performance metrics
 * Shows detailed timing breakdown for chat responses to help identify bottlenecks
 */
function TimingDashboard({ isVisible = false, onToggle }) {
  const state = useChatState();
  const [timingHistory, setTimingHistory] = useState([]);
  const [selectedOperation, setSelectedOperation] = useState(null);

  // Extract timing data from chat history
  useEffect(() => {
    const timingData = state.history
      .filter(message => message.role === 'assistant' && message.timing)
      .map(message => ({
        timestamp: message.timestamp,
        operationId: message.timing?.backend?.operationId || message.operationId,
        frontend: message.timing?.frontend,
        backend: message.timing?.backend,
        messageLength: message.content?.length || 0,
        error: message.timing?.error || false
      }))
      .slice(-10); // Keep last 10 operations

    setTimingHistory(timingData);
  }, [state.history]);

  const formatDuration = (ms) => {
    if (ms === null || ms === undefined) return 'N/A';
    if (ms < 1000) return `${ms.toFixed(1)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const getPerformanceColor = (duration) => {
    if (!duration) return '#666';
    if (duration < 500) return '#4CAF50'; // Green - Fast
    if (duration < 2000) return '#FF9800'; // Orange - Moderate
    return '#F44336'; // Red - Slow
  };

  const calculateAverages = () => {
    if (timingHistory.length === 0) return null;

    const validEntries = timingHistory.filter(entry => !entry.error);
    if (validEntries.length === 0) return null;

    const totals = validEntries.reduce((acc, entry) => {
      acc.frontend += entry.frontend?.totalFrontendTime || 0;
      acc.backend += entry.backend?.totalDuration || 0;
      acc.apiCall += entry.frontend?.apiCallToResponse || 0;
      return acc;
    }, { frontend: 0, backend: 0, apiCall: 0 });

    return {
      avgFrontend: totals.frontend / validEntries.length,
      avgBackend: totals.backend / validEntries.length,
      avgApiCall: totals.apiCall / validEntries.length,
      totalOperations: validEntries.length
    };
  };

  const averages = calculateAverages();

  if (!isVisible) {
    return (
      <div className="timing-dashboard-toggle">
        <button 
          onClick={onToggle}
          className="timing-toggle-btn"
          title="Show Performance Dashboard"
        >
          ⏱️ Performance
        </button>
      </div>
    );
  }

  return (
    <div className="timing-dashboard">
      <div className="timing-header">
        <h3>⏱️ Performance Dashboard</h3>
        <button onClick={onToggle} className="timing-close-btn">×</button>
      </div>

      {/* Performance Summary */}
      {averages && (
        <div className="timing-summary">
          <h4>Average Response Times</h4>
          <div className="timing-metrics">
            <div className="metric">
              <span className="metric-label">Frontend:</span>
              <span 
                className="metric-value"
                style={{ color: getPerformanceColor(averages.avgFrontend) }}
              >
                {formatDuration(averages.avgFrontend)}
              </span>
            </div>
            <div className="metric">
              <span className="metric-label">Backend:</span>
              <span 
                className="metric-value"
                style={{ color: getPerformanceColor(averages.avgBackend) }}
              >
                {formatDuration(averages.avgBackend)}
              </span>
            </div>
            <div className="metric">
              <span className="metric-label">API Call:</span>
              <span 
                className="metric-value"
                style={{ color: getPerformanceColor(averages.avgApiCall) }}
              >
                {formatDuration(averages.avgApiCall)}
              </span>
            </div>
            <div className="metric">
              <span className="metric-label">Operations:</span>
              <span className="metric-value">{averages.totalOperations}</span>
            </div>
          </div>
        </div>
      )}

      {/* Recent Operations */}
      <div className="timing-history">
        <h4>Recent Operations</h4>
        {timingHistory.length === 0 ? (
          <p className="no-data">No timing data available yet. Send a message to see performance metrics.</p>
        ) : (
          <div className="timing-list">
            {timingHistory.map((entry, index) => (
              <div 
                key={entry.operationId || index}
                className={`timing-entry ${entry.error ? 'error' : ''} ${selectedOperation === entry.operationId ? 'selected' : ''}`}
                onClick={() => setSelectedOperation(selectedOperation === entry.operationId ? null : entry.operationId)}
              >
                <div className="timing-entry-header">
                  <span className="timing-timestamp">
                    {new Date(entry.timestamp).toLocaleTimeString()}
                  </span>
                  <span className="timing-total">
                    {formatDuration((entry.frontend?.totalFrontendTime || 0) + (entry.backend?.totalDuration || 0))}
                  </span>
                  {entry.error && <span className="error-indicator">❌</span>}
                </div>
                
                {selectedOperation === entry.operationId && (
                  <div className="timing-details">
                    <div className="timing-breakdown">
                      <h5>Frontend Breakdown</h5>
                      <div className="breakdown-item">
                        <span>User → API Call:</span>
                        <span>{formatDuration(entry.frontend?.userInteractionToApiCall)}</span>
                      </div>
                      <div className="breakdown-item">
                        <span>API Call Duration:</span>
                        <span>{formatDuration(entry.frontend?.apiCallToResponse)}</span>
                      </div>
                      <div className="breakdown-item">
                        <span>Response → Display:</span>
                        <span>{formatDuration(entry.frontend?.responseToDisplay)}</span>
                      </div>
                      
                      {entry.backend && (
                        <>
                          <h5>Backend Breakdown</h5>
                          {entry.backend.stages?.map(stage => (
                            <div key={stage.name} className="breakdown-item">
                              <span>{stage.name}:</span>
                              <span>{formatDuration(stage.duration)}</span>
                              <span className="percentage">({stage.percentage}%)</span>
                            </div>
                          ))}

                          {/* Neo4j Query Details */}
                          {entry.backend.metadata?.neo4j_queries && (
                            <>
                              <h5>Neo4j Queries</h5>
                              <div className="breakdown-item">
                                <span>Total Queries:</span>
                                <span>{entry.backend.metadata.neo4j_queries.total_queries}</span>
                              </div>
                              <div className="breakdown-item">
                                <span>Query Time:</span>
                                <span>{formatDuration(entry.backend.metadata.neo4j_queries.total_duration_ms)}</span>
                              </div>
                              <div className="breakdown-item">
                                <span>Avg Query:</span>
                                <span>{formatDuration(entry.backend.metadata.neo4j_queries.average_duration_ms)}</span>
                              </div>
                              {entry.backend.metadata.neo4j_queries.slowest_query_ms > 0 && (
                                <div className="breakdown-item">
                                  <span>Slowest Query:</span>
                                  <span>{formatDuration(entry.backend.metadata.neo4j_queries.slowest_query_ms)}</span>
                                </div>
                              )}
                            </>
                          )}
                        </>
                      )}
                    </div>
                    
                    <div className="timing-metadata">
                      <div className="metadata-item">
                        <span>Operation ID:</span>
                        <span className="operation-id">{entry.operationId}</span>
                      </div>
                      <div className="metadata-item">
                        <span>Response Length:</span>
                        <span>{entry.messageLength} chars</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Performance Tips */}
      <div className="timing-tips">
        <h4>💡 Performance Tips</h4>
        <ul>
          <li><strong>Frontend &gt; 100ms:</strong> Check network connection or browser performance</li>
          <li><strong>Backend &gt; 5s:</strong> Knowledge graph search or LLM processing may be slow</li>
          <li><strong>API Call &gt; 10s:</strong> Consider optimizing Python script or model</li>
          <li><strong>Neo4j queries &gt; 1s:</strong> Add indexes on Entity.name, Entity.summary, or relationship.fact</li>
          <li><strong>Many Neo4j queries:</strong> Consider query batching or result caching</li>
          <li><strong>Frequent errors:</strong> Check server logs and connection stability</li>
        </ul>
      </div>
    </div>
  );
}

export default TimingDashboard;
