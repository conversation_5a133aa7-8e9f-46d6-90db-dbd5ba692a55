import React, { useState, useEffect } from 'react';
import '../styles/AdminConfigPanel.css';

/**
 * AdminConfigPanel component for managing knowledge graph configuration
 * Integrates all functionality from the standalone admin page into the chat interface
 */
function AdminConfigPanel({ 
  isCollapsed = true, 
  onToggle = null 
}) {
  const [collapsed, setCollapsed] = useState(isCollapsed);
  const [loading, setLoading] = useState(false);
  const [alerts, setAlerts] = useState([]);
  
  // Configuration state
  const [searchRecipes, setSearchRecipes] = useState([]);
  const [llmProviders, setLlmProviders] = useState([]);
  const [currentConfig, setCurrentConfig] = useState({});
  const [metrics, setMetrics] = useState({});
  
  // Form state
  const [searchRecipe, setSearchRecipe] = useState('');
  const [resultsLimit, setResultsLimit] = useState(10);
  const [llmProvider, setLlmProvider] = useState('');
  const [llmModel, setLlmModel] = useState('');
  const [customPrompt, setCustomPrompt] = useState('');

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadSearchRecipes(),
        loadLLMProviders(),
        loadCurrentConfig(),
        loadMetrics(),
        loadPromptConfig()
      ]);
    } catch (error) {
      showAlert('Failed to load configuration data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadSearchRecipes = async () => {
    try {
      console.log('🔍 Loading search recipes...');
      const response = await fetch('/api/config/search-recipes');
      if (response.ok) {
        const recipes = await response.json();
        console.log('✅ Recipes loaded from API:', recipes);

        // Flatten the categorized structure into a single array
        const flatRecipes = [];
        if (recipes && typeof recipes === 'object') {
          Object.values(recipes).forEach(categoryRecipes => {
            if (Array.isArray(categoryRecipes)) {
              flatRecipes.push(...categoryRecipes);
            }
          });
        }

        console.log('📋 Flattened recipes:', flatRecipes);
        setSearchRecipes(flatRecipes);
      } else {
        console.log('⚠️ API not available, using fallback data');
        // Fallback to mock data if API not available
        const fallbackRecipes = [
          { key: 'COMBINED_HYBRID_SEARCH_RRF', name: 'Combined Hybrid Search (RRF)' },
          { key: 'COMBINED_HYBRID_SEARCH_MMR', name: 'Combined Hybrid Search (MMR)' },
          { key: 'EDGE_SEARCH', name: 'Edge Search' },
          { key: 'NODE_SEARCH', name: 'Node Search' }
        ];
        console.log('📋 Setting fallback recipes:', fallbackRecipes);
        setSearchRecipes(fallbackRecipes);
      }
    } catch (error) {
      console.error('❌ Failed to load search recipes:', error);
      console.log('📋 Setting fallback recipes due to error');
      const fallbackRecipes = [
        { key: 'COMBINED_HYBRID_SEARCH_RRF', name: 'Combined Hybrid Search (RRF)' },
        { key: 'COMBINED_HYBRID_SEARCH_MMR', name: 'Combined Hybrid Search (MMR)' },
        { key: 'EDGE_SEARCH', name: 'Edge Search' },
        { key: 'NODE_SEARCH', name: 'Node Search' }
      ];
      setSearchRecipes(fallbackRecipes);
    }
  };

  const loadLLMProviders = async () => {
    try {
      const response = await fetch('/api/config/llm-providers');
      if (response.ok) {
        const providers = await response.json();
        setLlmProviders(Array.isArray(providers) ? providers : []);
      } else {
        // Fallback to mock data if API not available
        setLlmProviders([
          { key: 'ollama', name: 'Ollama', hasApiKey: true, models: ['gemma:2b', 'gemma:7b', 'llama3:8b'] },
          { key: 'google', name: 'Google Gemini', hasApiKey: false, models: ['gemini-pro', 'gemini-pro-vision'] },
          { key: 'openai', name: 'OpenAI', hasApiKey: false, models: ['gpt-4', 'gpt-3.5-turbo'] }
        ]);
      }
    } catch (error) {
      console.error('Failed to load LLM providers:', error);
      setLlmProviders([]);
    }
  };

  const loadCurrentConfig = async () => {
    try {
      const response = await fetch('/api/config/current');
      if (response.ok) {
        const config = await response.json();
        setCurrentConfig(config);
        setSearchRecipe(config.searchRecipe || '');
        setResultsLimit(config.resultsLimit || 10);
        setLlmProvider(config.llmProvider || '');
        setLlmModel(config.llmModel || '');
      } else {
        // Fallback to default config
        const defaultConfig = {
          searchRecipe: 'COMBINED_HYBRID_SEARCH_RRF',
          resultsLimit: 10,
          llmProvider: 'ollama',
          llmModel: 'gemma:2b'
        };
        setCurrentConfig(defaultConfig);
        setSearchRecipe(defaultConfig.searchRecipe);
        setResultsLimit(defaultConfig.resultsLimit);
        setLlmProvider(defaultConfig.llmProvider);
        setLlmModel(defaultConfig.llmModel);
      }
    } catch (error) {
      console.error('Failed to load current config:', error);
    }
  };

  const loadMetrics = async () => {
    try {
      const response = await fetch('/api/config/metrics');
      if (response.ok) {
        const metricsData = await response.json();
        setMetrics(metricsData || {});
      } else {
        setMetrics({});
      }
    } catch (error) {
      console.error('Failed to load metrics:', error);
      setMetrics({});
    }
  };

  const loadPromptConfig = async () => {
    try {
      const response = await fetch('/api/config/prompt');
      if (response.ok) {
        const promptData = await response.json();
        setCustomPrompt(promptData.customPrompt || '');
      } else {
        setCustomPrompt('');
      }
    } catch (error) {
      console.error('Failed to load prompt config:', error);
      setCustomPrompt('');
    }
  };

  const applyPromptConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/config/prompt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ customPrompt })
      });

      if (response.ok) {
        showAlert('Prompt configuration updated successfully!', 'success');
      } else {
        showAlert('Prompt configuration API not available - changes saved locally', 'error');
      }
    } catch (error) {
      showAlert('Prompt configuration API not available - changes saved locally', 'error');
    } finally {
      setLoading(false);
    }
  };

  const resetPromptToDefault = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/config/prompt/reset', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const defaultPromptData = await response.json();
        setCustomPrompt(defaultPromptData.defaultPrompt || '');
        showAlert('Prompt reset to default successfully!', 'success');
      } else {
        showAlert('Prompt reset API not available', 'error');
      }
    } catch (error) {
      showAlert('Prompt reset API not available', 'error');
    } finally {
      setLoading(false);
    }
  };

  const applySearchConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/config/search-recipe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ recipe: searchRecipe, resultsLimit })
      });

      if (response.ok) {
        const updatedConfig = await response.json();
        setCurrentConfig(updatedConfig);
        showAlert('Search configuration updated successfully!', 'success');
        await loadMetrics();
      } else {
        showAlert('Search configuration API not available - changes saved locally', 'error');
      }
    } catch (error) {
      showAlert('Search configuration API not available - changes saved locally', 'error');
    } finally {
      setLoading(false);
    }
  };

  const applyLLMConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/config/llm-provider', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider: llmProvider, model: llmModel })
      });

      if (response.ok) {
        const updatedConfig = await response.json();
        setCurrentConfig(updatedConfig);
        showAlert('LLM configuration updated successfully!', 'success');
      } else {
        showAlert('LLM configuration API not available - changes saved locally', 'error');
      }
    } catch (error) {
      showAlert('LLM configuration API not available - changes saved locally', 'error');
    } finally {
      setLoading(false);
    }
  };

  const testProvider = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/config/test-provider', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider: llmProvider, model: llmModel })
      });

      if (response.ok) {
        const result = await response.json();
        showAlert(`Provider test successful! Response: ${result.response}`, 'success');
      } else {
        showAlert('Provider test API not available', 'error');
      }
    } catch (error) {
      showAlert('Provider test API not available', 'error');
    } finally {
      setLoading(false);
    }
  };

  const refreshMetrics = async () => {
    try {
      setLoading(true);
      await loadMetrics();
      showAlert('Metrics refreshed successfully!', 'success');
    } catch (error) {
      showAlert('Failed to refresh metrics', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showAlert = (message, type) => {
    const alert = { id: Date.now(), message, type };
    setAlerts(prev => [...prev, alert]);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      setAlerts(prev => prev.filter(a => a.id !== alert.id));
    }, 5000);
  };

  const handleToggle = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    if (onToggle) {
      onToggle(newCollapsed);
    }
  };

  const getProviderModels = () => {
    const provider = llmProviders.find(p => p.key === llmProvider);
    return provider?.models || [];
  };

  return (
    <div className={`admin-config-panel ${collapsed ? 'collapsed' : 'expanded'}`}>
      {/* Toggle Header */}
      <div className="admin-header" onClick={handleToggle}>
        <div className="admin-title">
          <span className="admin-icon">⚙️</span>
          <span className="admin-label">Configuration Panel</span>
        </div>
        <div className={`admin-toggle ${collapsed ? 'collapsed' : 'expanded'}`}>
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path 
              d="M3 4.5L6 7.5L9 4.5" 
              stroke="currentColor" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>

      {/* Collapsible Content */}
      {!collapsed && (
        <div className="admin-content">
          {/* Alerts */}
          {alerts.length > 0 && (
            <div className="admin-alerts">
              {alerts.map(alert => (
                <div key={alert.id} className={`admin-alert alert-${alert.type}`}>
                  {alert.message}
                </div>
              ))}
            </div>
          )}

          <div className="admin-sections">
            {/* Search Configuration */}
            <div className="admin-section">
              <h3>Search Configuration</h3>
              
              <div className="form-group">
                <label htmlFor="searchRecipe">Search Recipe:</label>
                <select 
                  id="searchRecipe" 
                  value={searchRecipe} 
                  onChange={(e) => setSearchRecipe(e.target.value)}
                  disabled={loading}
                >
                  <option value="">Select a recipe...</option>
                  {searchRecipes.map(recipe => (
                    <option key={recipe.key} value={recipe.key}>
                      {recipe.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="resultsLimit">Results Limit:</label>
                <input 
                  type="number" 
                  id="resultsLimit" 
                  value={resultsLimit} 
                  onChange={(e) => setResultsLimit(parseInt(e.target.value))}
                  min="1" 
                  max="50"
                  disabled={loading}
                />
              </div>

              <button 
                onClick={applySearchConfig} 
                disabled={loading || !searchRecipe}
                className="btn btn-primary"
              >
                {loading ? 'Applying...' : 'Apply Search Configuration'}
              </button>
            </div>

            {/* LLM Configuration */}
            <div className="admin-section">
              <h3>LLM Configuration</h3>
              
              <div className="form-group">
                <label htmlFor="llmProvider">LLM Provider:</label>
                <select 
                  id="llmProvider" 
                  value={llmProvider} 
                  onChange={(e) => setLlmProvider(e.target.value)}
                  disabled={loading}
                >
                  <option value="">Select a provider...</option>
                  {llmProviders.map(provider => (
                    <option 
                      key={provider.key} 
                      value={provider.key}
                      disabled={!provider.hasApiKey}
                    >
                      {provider.name} {!provider.hasApiKey ? '(Missing API Key)' : ''}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="llmModel">Model:</label>
                <select 
                  id="llmModel" 
                  value={llmModel} 
                  onChange={(e) => setLlmModel(e.target.value)}
                  disabled={loading || !llmProvider}
                >
                  <option value="">Select a model...</option>
                  {getProviderModels().map(model => (
                    <option key={model} value={model}>
                      {model}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-actions">
                <button 
                  onClick={applyLLMConfig} 
                  disabled={loading || !llmProvider || !llmModel}
                  className="btn btn-primary"
                >
                  {loading ? 'Applying...' : 'Apply LLM Configuration'}
                </button>
                
                <button 
                  onClick={testProvider} 
                  disabled={loading || !llmProvider || !llmModel}
                  className="btn btn-secondary"
                >
                  {loading ? 'Testing...' : 'Test Provider'}
                </button>
              </div>
            </div>

            {/* Prompt Configuration */}
            <div className="admin-section">
              <h3>Prompt Configuration</h3>

              <div className="form-group">
                <label htmlFor="customPrompt">Custom Prompt Instructions:</label>
                <textarea
                  id="customPrompt"
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  placeholder="Enter custom prompt instructions to modify how the AI responds to questions..."
                  rows="6"
                  disabled={loading}
                  className="prompt-textarea"
                />
                <div className="form-help">
                  Customize how the AI responds to questions. Leave empty to use default prompt.
                </div>
              </div>

              <div className="form-actions">
                <button
                  onClick={applyPromptConfig}
                  disabled={loading}
                  className="btn btn-primary"
                >
                  {loading ? 'Applying...' : 'Apply Prompt Configuration'}
                </button>

                <button
                  onClick={resetPromptToDefault}
                  disabled={loading}
                  className="btn btn-secondary"
                >
                  {loading ? 'Resetting...' : 'Reset to Default'}
                </button>
              </div>
            </div>
          </div>

          {/* Metrics */}
          {Object.keys(metrics).length > 0 && (
            <div className="admin-metrics">
              <div className="metrics-header">
                <h3>Current Metrics</h3>
                <button 
                  onClick={refreshMetrics} 
                  disabled={loading}
                  className="btn btn-small"
                >
                  {loading ? 'Refreshing...' : 'Refresh'}
                </button>
              </div>
              
              <div className="metrics-grid">
                {Object.entries(metrics).map(([key, value]) => (
                  <div key={key} className="metric-item">
                    <span className="metric-label">{key}:</span>
                    <span className="metric-value">{JSON.stringify(value)}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default AdminConfigPanel;
