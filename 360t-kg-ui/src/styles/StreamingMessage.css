/* Streaming Message Styles */

.streaming-message {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 12px;
  position: relative;
  transition: all 0.3s ease;
}

.streaming-message.user {
  background: #e3f2fd;
  border-left: 4px solid #2196F3;
  margin-left: 20%;
}

.streaming-message.assistant {
  background: #f5f5f5;
  border-left: 4px solid #4CAF50;
  margin-right: 20%;
}

.streaming-message.streaming {
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
  border-left-color: #4CAF50;
}

.streaming-message.streaming::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #4CAF50, #81C784, #4CAF50);
  background-size: 200% 100%;
  animation: streamingProgress 2s linear infinite;
}

@keyframes streamingProgress {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Message Header */
.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 8px;
}

.role-label {
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.streaming-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4CAF50;
  font-size: 12px;
}

.typing-dots {
  display: flex;
  gap: 2px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background: #4CAF50;
  border-radius: 50%;
  animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }
.typing-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes typingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.streaming-text {
  font-style: italic;
  color: #666;
}

/* Streaming Statistics */
.streaming-stats {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #666;
  background: rgba(76, 175, 80, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.streaming-stats .stat {
  display: flex;
  align-items: center;
  gap: 2px;
}

/* Message Content */
.message-content {
  line-height: 1.6;
  color: #333;
  max-height: 400px;
  overflow-y: auto;
  word-wrap: break-word;
}

.message-content h1,
.message-content h2,
.message-content h3 {
  margin: 16px 0 8px 0;
  color: #2c3e50;
}

.message-content h1 { font-size: 20px; }
.message-content h2 { font-size: 18px; }
.message-content h3 { font-size: 16px; }

.message-content strong {
  font-weight: 600;
  color: #2c3e50;
}

.citation {
  background: #2196F3;
  color: white;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  margin: 0 1px;
}

/* Thinking Indicator */
.thinking-indicator {
  margin-top: 12px;
  padding: 12px;
  background: #fff3e0;
  border-left: 3px solid #ff9800;
  border-radius: 6px;
}

.thinking-label {
  font-weight: 600;
  color: #e65100;
  font-size: 12px;
  display: block;
  margin-bottom: 6px;
}

.thinking-content {
  font-size: 12px;
  color: #bf360c;
  font-style: italic;
  line-height: 1.4;
  max-height: 100px;
  overflow-y: auto;
}

/* Source Documents */
.source-documents {
  margin-top: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.source-documents h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #495057;
}

.source-documents ul {
  margin: 0;
  padding-left: 16px;
  list-style-type: none;
}

.source-documents li {
  margin-bottom: 8px;
  font-size: 12px;
  position: relative;
}

.source-documents li::before {
  content: '📄';
  position: absolute;
  left: -16px;
}

.source-documents li strong {
  color: #2c3e50;
}

.source-documents li p {
  margin: 4px 0 0 0;
  color: #6c757d;
  font-size: 11px;
}

/* Performance Info */
.performance-info {
  margin-top: 12px;
}

.performance-info details {
  font-size: 11px;
  color: #666;
}

.performance-info summary {
  cursor: pointer;
  padding: 4px 0;
  font-weight: 500;
}

.performance-info summary:hover {
  color: #2196F3;
}

.timing-details {
  padding: 8px 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 4px;
  font-family: monospace;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  margin-top: 4px;
}

/* Message Timestamp */
.message-timestamp {
  margin-top: 12px;
  font-size: 10px;
  color: #999;
  text-align: right;
}

/* Responsive Design */
@media (max-width: 768px) {
  .streaming-message.user {
    margin-left: 10%;
  }
  
  .streaming-message.assistant {
    margin-right: 10%;
  }
  
  .message-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .streaming-stats {
    flex-direction: column;
    gap: 4px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .streaming-message.user {
    background: #1e3a8a;
    color: #e0e7ff;
  }
  
  .streaming-message.assistant {
    background: #1f2937;
    color: #f3f4f6;
  }
  
  .message-content {
    color: #f3f4f6;
  }
  
  .message-content h1,
  .message-content h2,
  .message-content h3 {
    color: #e5e7eb;
  }
  
  .thinking-indicator {
    background: #451a03;
    border-left-color: #f59e0b;
  }
  
  .thinking-label {
    color: #fbbf24;
  }
  
  .thinking-content {
    color: #fed7aa;
  }
  
  .source-documents {
    background: #374151;
    border-color: #4b5563;
  }
  
  .source-documents h4 {
    color: #d1d5db;
  }
  
  .timing-details {
    background: #374151;
    color: #d1d5db;
  }
}
