/* ThinkingProcess Component Styles */

.thinking-process {
  margin: 0.75rem 0;
  border: 1px solid var(--360t-border);
  border-radius: 8px;
  background-color: var(--360t-white);
  box-shadow: 0 1px 3px var(--360t-shadow);
  overflow: hidden;
  transition: all 0.3s ease;
}

.thinking-process.streaming {
  border-color: var(--360t-primary);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
}

/* Header */
.thinking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid var(--360t-border);
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.thinking-header:hover {
  background-color: #e9ecef;
}

.thinking-process.streaming .thinking-header {
  background-color: #e3f2fd;
}

.thinking-process.streaming .thinking-header:hover {
  background-color: #bbdefb;
}

.thinking-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--360t-text);
}

.thinking-icon {
  font-size: 1rem;
}

.thinking-label {
  color: var(--360t-dark-gray);
}

.thinking-process.streaming .thinking-label {
  color: var(--360t-primary);
}

/* Thinking indicator animation */
.thinking-indicator {
  display: flex;
  align-items: center;
  margin-left: 0.5rem;
}

.thinking-dots {
  display: flex;
  gap: 2px;
}

.thinking-dots span {
  width: 4px;
  height: 4px;
  background-color: var(--360t-primary);
  border-radius: 50%;
  animation: thinking-pulse 1.4s ease-in-out infinite both;
}

.thinking-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.thinking-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes thinking-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Toggle Arrow */
.thinking-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: var(--360t-dark-gray);
}

.thinking-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.thinking-toggle.collapsed {
  transform: rotate(-90deg);
}

.thinking-toggle.expanded {
  transform: rotate(0deg);
}

/* Content */
.thinking-content {
  border-top: 1px solid var(--360t-border);
  background-color: #fafbfc;
}

.thinking-scroll-container {
  max-height: 600px;
  overflow-y: auto;
  padding: 1rem;
}

.thinking-sections {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.thinking-section {
  border: 1px solid var(--360t-border);
  border-radius: 8px;
  padding: 1rem;
  background-color: var(--360t-white);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.thinking-placeholder {
  text-align: center;
  color: var(--360t-dark-gray);
  font-style: italic;
  padding: 2rem;
}

/* Section Headers */
.thinking-section h4 {
  margin: 0 0 1rem 0;
  color: var(--360t-primary);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.thinking-section h5 {
  margin: 1rem 0 0.5rem 0;
  color: var(--360t-text);
  font-size: 0.9rem;
  font-weight: 500;
}

/* Metadata Items */
.search-metadata,
.context-metadata,
.provider-metadata {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.metadata-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.metadata-label {
  font-weight: 600;
  color: var(--360t-dark-gray);
  min-width: 120px;
  flex-shrink: 0;
}

.metadata-value {
  color: var(--360t-text);
  word-break: break-word;
}

/* Streaming Stats */
.thinking-stats {
  display: flex;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background-color: #f1f3f4;
  border-top: 1px solid var(--360t-border);
  font-size: 0.75rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-label {
  color: var(--360t-dark-gray);
  font-weight: 500;
}

.stat-value {
  color: var(--360t-text);
  font-weight: 600;
}

/* Collapsed State */
.thinking-process.collapsed .thinking-content {
  display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .thinking-header {
    padding: 0.6rem 0.8rem;
  }
  
  .thinking-title {
    font-size: 0.8rem;
  }
  
  .thinking-scroll-container {
    max-height: 300px;
    padding: 0.8rem;
  }
  
  .thinking-text {
    font-size: 0.7rem;
  }
  
  .thinking-stats {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.6rem 0.8rem;
  }
}

@media (max-width: 480px) {
  .thinking-header {
    padding: 0.5rem 0.6rem;
  }
  
  .thinking-scroll-container {
    max-height: 250px;
    padding: 0.6rem;
  }
  
  .thinking-text {
    font-size: 0.65rem;
  }
}

/* Search Results Styles */
.search-results-section {
  margin-top: 1rem;
}

.search-results-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.search-result-item {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 0.75rem;
  background-color: #f8f9fa;
}

.search-result-item.edge {
  border-left: 4px solid #3b82f6;
}

.search-result-item.node {
  border-left: 4px solid #10b981;
}

.search-result-item.error {
  border-left: 4px solid #ef4444;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.result-type-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.result-type-badge.edge {
  background-color: #dbeafe;
  color: #1e40af;
}

.result-type-badge.node {
  background-color: #d1fae5;
  color: #065f46;
}

.result-type-badge.error {
  background-color: #fee2e2;
  color: #991b1b;
}

.result-index {
  font-size: 0.75rem;
  color: var(--360t-dark-gray);
  font-weight: 500;
}

.result-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.result-detail {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.detail-label {
  font-weight: 600;
  color: var(--360t-dark-gray);
  min-width: 100px;
  flex-shrink: 0;
}

.detail-value {
  color: var(--360t-text);
  word-break: break-word;
}

/* Content Display Styles */
.context-content,
.prompt-content {
  margin-top: 0.5rem;
}

.context-text,
.prompt-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
  color: var(--360t-text);
  background-color: #f1f3f4;
  border: 1px solid var(--360t-border);
  border-radius: 4px;
  padding: 0.75rem;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
}

/* Citations Styles */
.citations-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.citation-item {
  background-color: var(--360t-primary);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.no-citations {
  color: var(--360t-dark-gray);
  font-style: italic;
}

/* Provider Summary */
.provider-summary {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #e8f4fd;
  border-radius: 4px;
  font-size: 0.9rem;
  color: var(--360t-primary);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .thinking-process {
    background-color: #2d2d2d;
    border-color: #444;
  }

  .thinking-header {
    background-color: #3d3d3d;
    border-bottom-color: #444;
  }

  .thinking-header:hover {
    background-color: #4d4d4d;
  }

  .thinking-process.streaming .thinking-header {
    background-color: #1e3a5f;
  }

  .thinking-process.streaming .thinking-header:hover {
    background-color: #2a4a6f;
  }

  .thinking-content {
    background-color: #2a2a2a;
    border-top-color: #444;
  }

  .thinking-section {
    background-color: #3a3a3a;
    border-color: #555;
  }

  .search-result-item {
    background-color: #3a3a3a;
    border-color: #555;
  }

  .context-text,
  .prompt-text {
    background-color: #2a2a2a;
    border-color: #555;
    color: #e0e0e0;
  }

  .provider-summary {
    background-color: #1e3a5f;
    color: #93c5fd;
  }

  .thinking-stats {
    background-color: #3a3a3a;
    border-top-color: #444;
  }

  .stat-label {
    color: #ccc;
  }

  .stat-value {
    color: #e0e0e0;
  }
}
