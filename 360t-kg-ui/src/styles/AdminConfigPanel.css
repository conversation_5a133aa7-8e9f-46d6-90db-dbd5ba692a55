/* AdminConfigPanel Component Styles */

.admin-config-panel {
  margin: 0.75rem 0;
  border: 1px solid var(--360t-border);
  border-radius: 8px;
  background-color: var(--360t-white);
  box-shadow: 0 1px 3px var(--360t-shadow);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Header */
.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid var(--360t-border);
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.admin-header:hover {
  background-color: #e9ecef;
}

.admin-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--360t-text);
}

.admin-icon {
  font-size: 1rem;
}

.admin-label {
  color: var(--360t-dark-gray);
}

/* Toggle Arrow */
.admin-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: var(--360t-dark-gray);
}

.admin-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.admin-toggle.collapsed {
  transform: rotate(-90deg);
}

.admin-toggle.expanded {
  transform: rotate(0deg);
}

/* Content */
.admin-content {
  padding: 1rem;
  background-color: #fafbfc;
  border-top: 1px solid var(--360t-border);
}

/* Alerts */
.admin-alerts {
  margin-bottom: 1rem;
}

.admin-alert {
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.admin-alert.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.admin-alert.alert-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Sections */
.admin-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.admin-section {
  background-color: var(--360t-white);
  border: 1px solid var(--360t-border);
  border-radius: 8px;
  padding: 1rem;
}

.admin-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--360t-text);
  border-bottom: 1px solid var(--360t-border);
  padding-bottom: 0.5rem;
}

/* Form Elements */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--360t-text);
}

.form-group select,
.form-group input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--360t-border);
  border-radius: 4px;
  font-size: 0.875rem;
  background-color: var(--360t-white);
  color: var(--360t-text);
  transition: border-color 0.2s ease;
}

.form-group select:focus,
.form-group input:focus {
  outline: none;
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.form-group select:disabled,
.form-group input:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

/* Buttons */
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  display: inline-block;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--360t-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--360t-primary-dark);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #5a6268;
}

.btn-small {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.form-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

/* Metrics */
.admin-metrics {
  background-color: var(--360t-white);
  border: 1px solid var(--360t-border);
  border-radius: 8px;
  padding: 1rem;
}

.metrics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--360t-border);
  padding-bottom: 0.5rem;
}

.metrics-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--360t-text);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 0.8rem;
}

.metric-label {
  font-weight: 500;
  color: var(--360t-dark-gray);
}

.metric-value {
  font-weight: 600;
  color: var(--360t-text);
  word-break: break-all;
}

/* Collapsed State */
.admin-config-panel.collapsed .admin-content {
  display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-sections {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .admin-header {
    padding: 0.6rem 0.8rem;
  }
  
  .admin-content {
    padding: 0.8rem;
  }
  
  .admin-section {
    padding: 0.8rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .admin-header {
    padding: 0.5rem 0.6rem;
  }
  
  .admin-content {
    padding: 0.6rem;
  }
  
  .admin-section {
    padding: 0.6rem;
  }
  
  .admin-title {
    font-size: 0.8rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .admin-config-panel {
    background-color: #2d2d2d;
    border-color: #444;
  }
  
  .admin-header {
    background-color: #3d3d3d;
    border-bottom-color: #444;
  }
  
  .admin-header:hover {
    background-color: #4d4d4d;
  }
  
  .admin-content {
    background-color: #2a2a2a;
    border-top-color: #444;
  }
  
  .admin-section,
  .admin-metrics {
    background-color: #2d2d2d;
    border-color: #444;
  }
  
  .form-group select,
  .form-group input {
    background-color: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
  }
  
  .form-group select:disabled,
  .form-group input:disabled {
    background-color: #3a3a3a;
    color: #999;
  }
  
  .metric-item {
    background-color: #3a3a3a;
  }
  
  .admin-alert.alert-success {
    background-color: #1e4620;
    color: #4caf50;
    border-color: #2e7d32;
  }
  
  .admin-alert.alert-error {
    background-color: #4a1e1e;
    color: #f44336;
    border-color: #d32f2f;
  }
}

/* Prompt Configuration Styles */
.prompt-textarea {
  width: 100%;
  min-height: 120px;
  padding: 0.75rem;
  border: 1px solid var(--360t-border);
  border-radius: 4px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 0.875rem;
  line-height: 1.4;
  resize: vertical;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.prompt-textarea:focus {
  outline: none;
  border-color: var(--360t-green);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.prompt-textarea:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.form-help {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--360t-gray);
  font-style: italic;
}
