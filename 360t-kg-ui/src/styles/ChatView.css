/* <PERSON><PERSON><PERSON><PERSON>w Component Styles */
.chat-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--360t-light-gray);
  border-radius: 8px;
  overflow: hidden;
}

/* Chat Header */
.chat-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem 1.5rem;
  background-color: var(--360t-white);
  border-bottom: 1px solid var(--360t-border);
  box-shadow: 0 1px 3px var(--360t-shadow);
}

.chat-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--360t-text);
}

.chat-controls {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

/* SOLUTION 1: Circular Icon Buttons - Floating Action Style */
.conversation-selector {
  min-width: 200px;
  max-width: 300px;
  padding: 0.625rem 2.5rem 0.625rem 1rem;
  background-color: var(--360t-white);
  border: 2px solid var(--360t-mid-gray);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--360t-text);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23374151' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1rem;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-selector:hover:not(:disabled) {
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1);
  transform: translateY(-1px);
}

.conversation-selector:focus {
  outline: none;
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.15);
}

.conversation-selector:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--360t-light-gray);
}

.conversation-selector option {
  padding: 0.5rem;
  background-color: var(--360t-white);
  color: var(--360t-text);
}

/* Circular Icon Button Styling */
.new-chat-button,
.delete-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.new-chat-button {
  background-color: var(--360t-primary);
  color: white;
  box-shadow: 0 3px 12px rgba(0, 151, 58, 0.4);
}

.new-chat-button:hover:not(:disabled) {
  background-color: var(--360t-primary-dark);
  box-shadow: 0 6px 20px rgba(0, 151, 58, 0.5);
  transform: translateY(-2px) scale(1.05);
}

.delete-button {
  background-color: #ef4444;
  color: white;
  box-shadow: 0 3px 12px rgba(239, 68, 68, 0.4);
}

.delete-button:hover:not(:disabled) {
  background-color: #dc2626;
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.5);
  transform: translateY(-2px) scale(1.05);
}

.new-chat-button:disabled,
.delete-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

.new-chat-button:active:not(:disabled),
.delete-button:active:not(:disabled) {
  transform: translateY(0px) scale(1);
}

/* Ripple effect */
.new-chat-button:before,
.delete-button:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.new-chat-button:active:before,
.delete-button:active:before {
  width: 60px;
  height: 60px;
}

/* Chat Error Display */
.chat-error {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  background-color: #fef2f2;
  border-bottom: 1px solid #fecaca;
  color: #dc2626;
  font-size: 0.875rem;
  animation: slideDown 0.3s ease-out;
}

.chat-error .error-icon {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.chat-error .error-message {
  flex: 1;
  line-height: 1.4;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background-color: var(--360t-light-gray);
}

/* Admin Panel Integration */
.admin-config-panel {
  margin: 0;
  border-radius: 0;
  border-left: none;
  border-right: none;
  border-top: none;
}

.admin-config-panel:not(.collapsed) {
  border-bottom: 2px solid var(--360t-primary);
}

/* Thinking Process Integration */
.message .thinking-process {
  margin: 0.5rem 0 1rem 0;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--360t-dark-gray);
  padding: 2rem;
}

.empty-state-icon {
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-state h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--360t-text);
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
  max-width: 400px;
}

/* Messages List */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Individual Message */
.message {
  display: flex;
  margin-bottom: 1rem;
}

.message.user {
  justify-content: flex-end;
}

.message.assistant {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  background-color: var(--360t-white);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  box-shadow: 0 1px 3px var(--360t-shadow);
  border: 1px solid var(--360t-border);
}

.message.user .message-content {
  background-color: var(--360t-primary);
  color: white;
  border-color: var(--360t-primary-dark);
}

.message.assistant .message-content {
  background-color: var(--360t-white);
  color: var(--360t-text);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.message.user .message-header {
  color: rgba(255, 255, 255, 0.8);
}

.message.assistant .message-header {
  color: var(--360t-dark-gray);
}

.message-role {
  font-weight: 600;
}

.message-timestamp {
  opacity: 0.7;
  font-size: 0.7rem;
}

.message-text {
  font-size: 1rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Loading Message */
.message.loading .message-content {
  background-color: var(--360t-white);
  border-color: var(--360t-mid-gray);
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0.5rem 0;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: var(--360t-dark-gray);
  opacity: 0.4;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Chat Input Container */
.chat-input-container {
  padding: 1rem 1.5rem;
  background-color: var(--360t-white);
  border-top: 1px solid var(--360t-border);
  box-shadow: 0 -1px 3px var(--360t-shadow);
}

/* Chat controls */
.chat-controls {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 12px;
}

/* Streaming toggle */
.streaming-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  user-select: none;
}

.streaming-toggle input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 40px;
  height: 20px;
  background: #ccc;
  border-radius: 20px;
  transition: background 0.3s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.streaming-toggle input:checked + .toggle-slider {
  background: #4CAF50;
}

.streaming-toggle input:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.toggle-label {
  font-weight: 500;
  transition: color 0.3s ease;
}

.streaming-toggle input:checked ~ .toggle-label {
  color: #4CAF50;
}

.chat-form {
  width: 100%;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 0.75rem;
  position: relative;
}

.chat-input {
  flex: 1;
  min-height: 40px;
  max-height: 120px;
  padding: 0.75rem 1rem;
  border: 1px solid var(--360t-mid-gray);
  border-radius: 20px;
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.4;
  resize: none;
  background-color: var(--360t-white);
  color: var(--360t-text);
  transition: border-color 0.2s, box-shadow 0.2s;
}

.chat-input:focus {
  outline: none;
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1);
}

.chat-input:disabled {
  background-color: var(--360t-light-gray);
  color: var(--360t-dark-gray);
  cursor: not-allowed;
}

.chat-input::placeholder {
  color: var(--360t-dark-gray);
  opacity: 0.7;
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--360t-primary);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background-color: var(--360t-primary-dark);
  transform: scale(1.05);
}

.send-button:disabled {
  background-color: var(--360t-mid-gray);
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-header {
    padding: 0.75rem 1rem;
  }
  
  .chat-header h3 {
    font-size: 1.125rem;
  }
  
  .messages-container {
    padding: 0.75rem;
  }
  
  .message-content {
    max-width: 85%;
    padding: 0.6rem 0.8rem;
  }
  
  .chat-input-container {
    padding: 0.75rem 1rem;
  }
  
  .input-wrapper {
    gap: 0.5rem;
  }
  
  .chat-input {
    padding: 0.6rem 0.8rem;
    font-size: 0.9rem;
  }
  
  .send-button {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .message-content {
    max-width: 95%;
  }
  
  .clear-button {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }
  
  .clear-button svg {
    width: 14px;
    height: 14px;
  }
}

/* SOLUTION 2: Square Icon Buttons - Toolbar Style */
/*
.new-chat-button,
.delete-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1.5px solid var(--360t-mid-gray);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  background-color: var(--360t-white);
}

.new-chat-button {
  color: var(--360t-primary);
  border-color: var(--360t-primary);
}

.new-chat-button:hover:not(:disabled) {
  background-color: var(--360t-primary);
  color: white;
  border-color: var(--360t-primary);
  box-shadow: 0 2px 8px rgba(0, 151, 58, 0.3);
  transform: translateY(-1px);
}

.delete-button {
  color: #ef4444;
  border-color: #ef4444;
}

.delete-button:hover:not(:disabled) {
  background-color: #ef4444;
  color: white;
  border-color: #ef4444;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  transform: translateY(-1px);
}

.new-chat-button:disabled,
.delete-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
  background-color: var(--360t-light-gray) !important;
  color: var(--360t-dark-gray) !important;
  border-color: var(--360t-mid-gray) !important;
}

.new-chat-button:active:not(:disabled),
.delete-button:active:not(:disabled) {
  transform: translateY(0px);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}
*/

/* SOLUTION 3: Minimal Ghost Buttons - Ultra-clean */
/*
.new-chat-button,
.delete-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
  background-color: transparent;
  color: var(--360t-dark-gray);
  position: relative;
}

.new-chat-button:hover:not(:disabled) {
  background-color: rgba(0, 151, 58, 0.08);
  color: var(--360t-primary);
  transform: scale(1.1);
}

.delete-button:hover:not(:disabled) {
  background-color: rgba(239, 68, 68, 0.08);
  color: #ef4444;
  transform: scale(1.1);
}

.new-chat-button:disabled,
.delete-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  transform: none !important;
}

.new-chat-button:active:not(:disabled),
.delete-button:active:not(:disabled) {
  transform: scale(0.95);
}

.new-chat-button svg,
.delete-button svg {
  transition: all 0.15s ease;
}

.new-chat-button:hover:not(:disabled) svg,
.delete-button:hover:not(:disabled) svg {
  stroke-width: 2.5;
}
*/

/* 
TO SWITCH BETWEEN ICON BUTTON SOLUTIONS:
1. Currently Active: Solution 1 (Circular Icon Buttons - Floating Action Style)
2. To activate Solution 2: Comment out Solution 1 styles and uncomment Solution 2 block
3. To activate Solution 3: Comment out Solution 1 styles and uncomment Solution 3 block

Each solution maintains the same functionality while providing different visual styles:
- Solution 1: Modern floating action buttons with shadows and ripple effects
- Solution 2: Professional toolbar-style square buttons with borders
- Solution 3: Minimal ghost buttons for ultra-clean interfaces
*/

.clear-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: transparent;
  color: var(--360t-dark-gray);
  border: 1px solid var(--360t-mid-gray);
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-button:hover:not(:disabled) {
  background-color: var(--360t-light-gray);
  border-color: var(--360t-border);
}

.clear-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
} 