/* Timing Dashboard Styles */

.timing-dashboard-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.timing-toggle-btn {
  background: #2196F3;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
  transition: all 0.2s ease;
}

.timing-toggle-btn:hover {
  background: #1976D2;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

.timing-dashboard {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.timing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.timing-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.timing-close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.timing-close-btn:hover {
  background: #e0e0e0;
}

.timing-summary {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.timing-summary h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
}

.timing-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f9f9f9;
  border-radius: 4px;
}

.metric-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.metric-value {
  font-size: 12px;
  font-weight: 600;
}

.timing-history {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.timing-history h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
}

.no-data {
  color: #666;
  font-size: 12px;
  text-align: center;
  padding: 20px;
  font-style: italic;
}

.timing-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timing-entry {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.timing-entry:hover {
  border-color: #2196F3;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
}

.timing-entry.selected {
  border-color: #2196F3;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

.timing-entry.error {
  border-color: #F44336;
  background: #ffebee;
}

.timing-entry-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
}

.timing-timestamp {
  font-size: 11px;
  color: #666;
}

.timing-total {
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.error-indicator {
  font-size: 12px;
}

.timing-details {
  border-top: 1px solid #e0e0e0;
  padding: 12px;
  background: #fafafa;
}

.timing-breakdown h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #333;
  font-weight: 600;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 11px;
}

.breakdown-item span:first-child {
  color: #666;
}

.breakdown-item span:nth-child(2) {
  font-weight: 500;
  color: #333;
}

.percentage {
  color: #999 !important;
  font-size: 10px !important;
}

.timing-metadata {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #e0e0e0;
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
  font-size: 10px;
}

.metadata-item span:first-child {
  color: #666;
}

.operation-id {
  font-family: monospace;
  background: #f0f0f0;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 9px !important;
}

.timing-tips {
  padding: 16px;
  background: #f9f9f9;
  border-top: 1px solid #e0e0e0;
}

.timing-tips h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #333;
}

.timing-tips ul {
  margin: 0;
  padding-left: 16px;
  font-size: 10px;
  color: #666;
  line-height: 1.4;
}

.timing-tips li {
  margin-bottom: 4px;
}

.timing-tips strong {
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .timing-dashboard {
    width: calc(100vw - 40px);
    max-width: 400px;
  }
  
  .timing-metrics {
    grid-template-columns: 1fr;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .timing-dashboard {
    background: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
  }
  
  .timing-header {
    background: #3d3d3d;
    border-bottom-color: #444;
  }
  
  .timing-header h3 {
    color: #e0e0e0;
  }
  
  .timing-close-btn {
    color: #ccc;
  }
  
  .timing-close-btn:hover {
    background: #555;
  }
  
  .metric {
    background: #3d3d3d;
  }
  
  .timing-entry {
    border-color: #444;
    background: #2d2d2d;
  }
  
  .timing-entry.error {
    border-color: #d32f2f;
    background: #4a2c2a;
  }
  
  .timing-details {
    background: #3d3d3d;
    border-top-color: #444;
  }
  
  .timing-tips {
    background: #3d3d3d;
    border-top-color: #444;
  }
  
  .operation-id {
    background: #555;
    color: #e0e0e0;
  }
}
