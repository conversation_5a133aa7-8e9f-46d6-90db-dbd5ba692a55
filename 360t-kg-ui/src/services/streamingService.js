/**
 * Streaming Service for real-time chat responses
 * Handles Server-Sent Events (SSE) from the backend
 */

import { useState, useCallback } from 'react';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3003/api';

class StreamingService {
  constructor() {
    this.eventSource = null;
    this.isStreaming = false;
    this.currentOperationId = null;
  }

  /**
   * Start streaming a chat response
   * @param {string} message - The user message
   * @param {Array} history - Chat history
   * @param {Object} callbacks - Event callbacks
   * @param {Function} callbacks.onStart - Called when streaming starts
   * @param {Function} callbacks.onToken - Called for each token (token, isDone)
   * @param {Function} callbacks.onResult - Called with final result
   * @param {Function} callbacks.onComplete - Called when streaming completes
   * @param {Function} callbacks.onError - Called on error
   */
  async startStreaming(message, history = [], callbacks = {}) {
    if (this.isStreaming) {
      throw new Error('Already streaming a response');
    }

    this.isStreaming = true;
    
    try {
      // Send the initial request to start streaming
      const response = await fetch(`${API_BASE_URL}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          history
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Set up Server-Sent Events
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      let buffer = '';
      let streamingContent = '';
      
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        // Decode the chunk and add to buffer
        buffer += decoder.decode(value, { stream: true });
        
        // Process complete lines
        const lines = buffer.split('\n');
        buffer = lines.pop(); // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              await this.handleStreamEvent(data, callbacks, streamingContent);
              
              // Update streaming content for tokens
              if (data.type === 'token') {
                streamingContent += data.token;
              }
              
            } catch (error) {
              console.warn('Failed to parse SSE data:', line, error);
            }
          }
        }
      }

    } catch (error) {
      console.error('Streaming error:', error);
      if (callbacks.onError) {
        callbacks.onError(error);
      }
    } finally {
      this.isStreaming = false;
      this.currentOperationId = null;
    }
  }

  /**
   * Handle individual stream events
   */
  async handleStreamEvent(data, callbacks, currentContent) {
    switch (data.type) {
      case 'start':
        this.currentOperationId = data.operationId;
        console.log('🚀 Streaming started:', data.operationId);
        if (callbacks.onStart) {
          callbacks.onStart(data);
        }
        break;

      case 'stream_start':
        console.log('📡 Token streaming started');
        break;

      case 'token':
        // Call token callback with the new token
        if (callbacks.onToken) {
          callbacks.onToken(data.token, false, currentContent + data.token);
        }
        break;

      case 'result':
        console.log('📋 Final result received');
        if (callbacks.onResult) {
          callbacks.onResult(data.result);
        }
        break;

      case 'thinking':
        console.log('💭 Thinking data received');
        if (callbacks.onThinking) {
          callbacks.onThinking(data.stage, data.data);
        }
        break;

      case 'complete':
        console.log('✅ Streaming completed:', data.operationId);
        if (callbacks.onComplete) {
          callbacks.onComplete(data);
        }
        break;

      case 'error':
        console.error('❌ Streaming error:', data.error);
        if (callbacks.onError) {
          callbacks.onError(new Error(data.error));
        }
        break;

      default:
        console.log('📨 Unknown stream event:', data.type, data);
    }
  }

  /**
   * Parse debug output from Python script to extract thinking data
   * @param {string} output - Raw output from Python script
   * @returns {Object} Parsed thinking data
   */
  parseThinkingData(output) {
    const thinkingData = {};

    // Extract search debug information
    const searchDebugMatch = output.match(/=== GRAPHITI SEARCH DEBUG ===([\s\S]*?)=== END GRAPHITI SEARCH DEBUG ===/);
    if (searchDebugMatch) {
      const searchContent = searchDebugMatch[1];
      thinkingData.searchDebug = this.parseSearchDebug(searchContent);
    }

    // Extract context information
    const contextMatch = output.match(/=== CONTEXT RECEIVED ===([\s\S]*?)=== CITATIONS ===/);
    if (contextMatch) {
      const contextContent = contextMatch[1];
      thinkingData.context = this.parseContextInfo(contextContent);
    }

    // Extract citations
    const citationsMatch = output.match(/=== CITATIONS ===([\s\S]*?)=== FULL PROMPT SENT TO LLM ===/);
    if (citationsMatch) {
      const citationsContent = citationsMatch[1];
      thinkingData.citations = this.parseCitations(citationsContent);
    }

    // Extract prompt information
    const promptMatch = output.match(/=== FULL PROMPT SENT TO LLM ===([\s\S]*?)=== END PROMPT ===/);
    if (promptMatch) {
      const promptContent = promptMatch[1];
      thinkingData.prompt = this.parsePromptInfo(promptContent);
    }

    return thinkingData;
  }

  parseSearchDebug(content) {
    const debug = {};

    const queryMatch = content.match(/Query: (.+)/);
    if (queryMatch) debug.query = queryMatch[1];

    const recipeMatch = content.match(/Search Recipe: (.+)/);
    if (recipeMatch) debug.searchRecipe = recipeMatch[1];

    const limitMatch = content.match(/Results Limit: (.+)/);
    if (limitMatch) debug.resultsLimit = parseInt(limitMatch[1]);

    const groupIdsMatch = content.match(/Found (\d+) group_ids containing '([^']+)': (\[.*?\])/);
    if (groupIdsMatch) {
      debug.groupIdFilter = groupIdsMatch[2];
      try {
        debug.groupIds = JSON.parse(groupIdsMatch[3]);
      } catch (e) {
        debug.groupIds = [];
      }
    }

    return debug;
  }

  parseContextInfo(content) {
    const context = {};

    const lengthMatch = content.match(/Context length: (\d+)/);
    if (lengthMatch) context.length = parseInt(lengthMatch[1]);

    const contentMatch = content.match(/Context content:\n([\s\S]*)/);
    if (contentMatch) context.content = contentMatch[1].trim();

    return context;
  }

  parseCitations(content) {
    const citationsMatch = content.match(/Citations: (\[.*?\])/);
    if (citationsMatch) {
      try {
        return JSON.parse(citationsMatch[1]);
      } catch (e) {
        return [];
      }
    }
    return [];
  }

  parsePromptInfo(content) {
    const prompt = {};

    const lengthMatch = content.match(/Prompt length: (\d+)/);
    if (lengthMatch) prompt.length = parseInt(lengthMatch[1]);

    const contentMatch = content.match(/Prompt content:\n([\s\S]*)/);
    if (contentMatch) prompt.content = contentMatch[1].trim();

    return prompt;
  }

  /**
   * Stop current streaming
   */
  stopStreaming() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.isStreaming = false;
    this.currentOperationId = null;
  }

  /**
   * Check if currently streaming
   */
  getStreamingStatus() {
    return {
      isStreaming: this.isStreaming,
      operationId: this.currentOperationId
    };
  }
}

// Create singleton instance
const streamingService = new StreamingService();

/**
 * Hook for using streaming in React components
 */
export function useStreaming() {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingContent, setStreamingContent] = useState('');
  const [streamingStats, setStreamingStats] = useState(null);

  const startStreaming = useCallback(async (message, history, callbacks = {}) => {
    setIsStreaming(true);
    setStreamingContent('');
    setStreamingStats(null);

    const startTime = Date.now();
    let tokenCount = 0;

    const enhancedCallbacks = {
      ...callbacks,
      onStart: (data) => {
        console.log('Streaming started:', data);
        if (callbacks.onStart) callbacks.onStart(data);
      },
      onToken: (token, isDone, fullContent) => {
        tokenCount++;
        setStreamingContent(fullContent);
        
        // Update streaming stats
        const elapsed = Date.now() - startTime;
        setStreamingStats({
          tokenCount,
          tokensPerSecond: tokenCount / (elapsed / 1000),
          elapsed
        });

        if (callbacks.onToken) callbacks.onToken(token, isDone, fullContent);
      },
      onComplete: (data) => {
        setIsStreaming(false);
        if (callbacks.onComplete) callbacks.onComplete(data);
      },
      onError: (error) => {
        setIsStreaming(false);
        if (callbacks.onError) callbacks.onError(error);
      }
    };

    try {
      await streamingService.startStreaming(message, history, enhancedCallbacks);
    } catch (error) {
      setIsStreaming(false);
      throw error;
    }
  }, []);

  const stopStreaming = useCallback(() => {
    streamingService.stopStreaming();
    setIsStreaming(false);
  }, []);

  return {
    isStreaming,
    streamingContent,
    streamingStats,
    startStreaming,
    stopStreaming
  };
}

export default streamingService;
