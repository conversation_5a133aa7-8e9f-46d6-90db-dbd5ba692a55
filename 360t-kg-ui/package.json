{"name": "360t-kg-ui", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "lint": "npm run icon-check && eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:ui": "playwright test --ui", "test:e2e:report": "playwright show-report", "test:e2e:install": "playwright install", "icon-check": "node ../scripts/check-icons.js"}, "dependencies": {"axios": "^1.10.0", "d3": "^7.8.5", "dompurify": "^3.2.4", "lodash.debounce": "^4.0.8", "marked": "^15.0.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0"}, "devDependencies": {"@axe-core/react": "^4.10.2", "@babel/preset-env": "^7.0.0", "@babel/preset-react": "^7.0.0", "@playwright/test": "^1.53.1", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "babel-jest": "^29.0.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jest": "^29.0.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^29.7.0", "vite": "^4.4.5"}}