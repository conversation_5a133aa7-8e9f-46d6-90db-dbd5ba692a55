import { test, expect } from '@playwright/test';
import { MainPage } from './pages/MainPage';

test.describe('Basic Functionality Tests', () => {
  let mainPage: MainPage;

  test.beforeEach(async ({ page }) => {
    mainPage = new MainPage(page);
    await mainPage.goto();
  });

  test.describe('Application Loading', () => {
    test('should load the application successfully', async ({ page }) => {
      // Verify basic page structure
      await expect(mainPage.header).toBeVisible();
      
      // Check that we have navigation buttons
      await expect(mainPage.explorerTab).toBeVisible();
      await expect(mainPage.chatTab).toBeVisible();
      await expect(mainPage.analysisTab).toBeVisible();
      await expect(mainPage.documentationTab).toBeVisible();
      
      console.log('✅ Application loaded with all navigation tabs');
    });

    test('should have working navigation tabs', async ({ page }) => {
      // Test each tab click without strict content verification
      await mainPage.explorerTab.click();
      await page.waitForTimeout(1000);
      console.log('✅ Explorer tab clicked');

      await mainPage.chatTab.click();
      await page.waitForTimeout(1000);
      console.log('✅ Chat tab clicked');

      await mainPage.analysisTab.click();
      await page.waitForTimeout(1000);
      console.log('✅ Analysis tab clicked');

      await mainPage.documentationTab.click();
      await page.waitForTimeout(1000);
      console.log('✅ Documentation tab clicked');

      // Return to explorer
      await mainPage.explorerTab.click();
      await page.waitForTimeout(1000);
      console.log('✅ Returned to Explorer tab');
    });
  });

  test.describe('Explorer View', () => {
    test('should display explorer interface', async ({ page }) => {
      await mainPage.navigateToExplorer();
      
      // Check for search functionality
      const searchInput = page.locator('input[type="text"], input[placeholder*="search"], input[placeholder*="Search"]');
      if (await searchInput.count() > 0) {
        await expect(searchInput.first()).toBeVisible();
        console.log('✅ Search input found');
      }

      // Check for graph container or content area
      const graphArea = page.locator('.graph-container, .graph-view, svg, .content-wrapper');
      await expect(graphArea.first()).toBeVisible();
      console.log('✅ Graph area found');
    });

    test('should handle search input', async ({ page }) => {
      await mainPage.navigateToExplorer();
      
      const searchInput = page.locator('input[type="text"], input[placeholder*="search"], input[placeholder*="Search"]');
      if (await searchInput.count() > 0) {
        await searchInput.first().fill('test search');
        await searchInput.first().press('Enter');
        await page.waitForTimeout(2000);
        
        console.log('✅ Search input handled successfully');
      } else {
        console.log('⚠️ No search input found');
      }
    });

    test('should load graph data if available', async ({ page }) => {
      await mainPage.navigateToExplorer();
      await page.waitForTimeout(3000); // Wait for potential graph loading
      
      // Check for any SVG elements (graph visualization)
      const svgElements = page.locator('svg');
      const svgCount = await svgElements.count();
      
      if (svgCount > 0) {
        console.log(`✅ Found ${svgCount} SVG elements (potential graph)`);
        
        // Check for graph nodes
        const nodes = page.locator('svg circle, svg .node, svg g');
        const nodeCount = await nodes.count();
        console.log(`📊 Found ${nodeCount} potential graph nodes`);
      } else {
        console.log('⚠️ No SVG elements found - graph may not be loaded');
      }
    });
  });

  test.describe('Chat View', () => {
    test('should display chat interface', async ({ page }) => {
      await mainPage.navigateToChat();
      
      // Look for chat-related elements
      const chatElements = page.locator('textarea, input[placeholder*="Ask"], .chat-container, .chat-input');
      if (await chatElements.count() > 0) {
        console.log('✅ Chat interface elements found');
      } else {
        console.log('⚠️ No chat interface elements found');
      }
    });

    test('should handle chat input if available', async ({ page }) => {
      await mainPage.navigateToChat();
      
      const chatInput = page.locator('textarea, input[placeholder*="Ask"], input[placeholder*="ask"]');
      if (await chatInput.count() > 0) {
        await chatInput.first().fill('Hello, this is a test message');
        console.log('✅ Chat input handled successfully');
        
        // Look for send button
        const sendButton = page.locator('button:has-text("Send"), button[type="submit"], .send-button');
        if (await sendButton.count() > 0) {
          console.log('✅ Send button found');
        }
      } else {
        console.log('⚠️ No chat input found');
      }
    });
  });

  test.describe('Analysis View', () => {
    test('should display analysis interface', async ({ page }) => {
      await mainPage.navigateToAnalysis();
      
      // Check for analysis-related content
      const analysisElements = page.locator('.analysis, .advanced-analysis, .content-wrapper');
      await expect(analysisElements.first()).toBeVisible();
      console.log('✅ Analysis interface displayed');
    });
  });

  test.describe('Documentation View', () => {
    test('should display documentation interface', async ({ page }) => {
      await mainPage.navigateToDocumentation();
      
      // Check for documentation content
      const docElements = page.locator('.documentation, .content-wrapper, .doc-content');
      await expect(docElements.first()).toBeVisible();
      console.log('✅ Documentation interface displayed');
    });
  });

  test.describe('Error Handling', () => {
    test('should handle navigation without crashing', async ({ page }) => {
      // Rapid navigation test
      for (let i = 0; i < 3; i++) {
        await mainPage.explorerTab.click();
        await page.waitForTimeout(500);
        await mainPage.chatTab.click();
        await page.waitForTimeout(500);
        await mainPage.analysisTab.click();
        await page.waitForTimeout(500);
        await mainPage.documentationTab.click();
        await page.waitForTimeout(500);
      }
      
      // Verify app is still responsive
      await expect(mainPage.header).toBeVisible();
      console.log('✅ Application survived rapid navigation');
    });

    test('should not show critical JavaScript errors', async ({ page }) => {
      const errors: string[] = [];
      
      page.on('console', msg => {
        if (msg.type() === 'error') {
          errors.push(msg.text());
        }
      });
      
      // Navigate through all views
      await mainPage.navigateToExplorer();
      await mainPage.navigateToChat();
      await mainPage.navigateToAnalysis();
      await mainPage.navigateToDocumentation();
      
      // Check for critical errors
      const criticalErrors = errors.filter(error => 
        error.includes('TypeError') || 
        error.includes('ReferenceError') ||
        error.includes('Cannot read property')
      );
      
      if (criticalErrors.length > 0) {
        console.log('⚠️ Critical JavaScript errors detected:', criticalErrors);
      } else {
        console.log('✅ No critical JavaScript errors detected');
      }
      
      // Don't fail the test for non-critical errors
      expect(criticalErrors.length).toBeLessThan(5); // Allow some minor errors
    });
  });

  test.describe('Performance', () => {
    test('should load within reasonable time', async ({ page }) => {
      const startTime = Date.now();
      await mainPage.goto();
      const loadTime = Date.now() - startTime;
      
      console.log(`📊 Page load time: ${loadTime}ms`);
      expect(loadTime).toBeLessThan(10000); // 10 second max
    });

    test('should handle tab switching efficiently', async ({ page }) => {
      const switchTimes: number[] = [];
      
      for (let i = 0; i < 3; i++) {
        const startTime = Date.now();
        await mainPage.explorerTab.click();
        await page.waitForTimeout(100);
        const switchTime = Date.now() - startTime;
        switchTimes.push(switchTime);
      }
      
      const avgSwitchTime = switchTimes.reduce((a, b) => a + b, 0) / switchTimes.length;
      console.log(`📊 Average tab switch time: ${avgSwitchTime.toFixed(2)}ms`);
      
      expect(avgSwitchTime).toBeLessThan(2000); // 2 second max average
    });
  });
});
