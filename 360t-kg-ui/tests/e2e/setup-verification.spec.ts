import { test, expect } from '@playwright/test';

test.describe('E2E Setup Verification', () => {
  test('should verify Playwright setup is working', async ({ page }) => {
    // Simple test to verify <PERSON><PERSON> is working
    await page.goto('https://example.com');
    await expect(page).toHaveTitle(/Example/);
    console.log('✅ Playwright setup verified');
  });

  test('should verify backend services are available', async ({ page }) => {
    // Check if backend services are running
    try {
      const newApiResponse = await page.request.get('http://localhost:3003/api/health');
      if (newApiResponse.ok()) {
        console.log('✅ New microservices system (port 3003) is available');
      } else {
        console.log('⚠️ New microservices system not responding correctly');
      }
    } catch (error) {
      console.log('⚠️ New microservices system not available');
    }

    // Legacy system (port 3002) has been decommissioned
    console.log('ℹ️ Legacy system (port 3002) has been successfully removed');
  });

  test('should check if frontend is accessible', async ({ page }) => {
    try {
      await page.goto('http://localhost:5173', { timeout: 10000 });
      console.log('✅ Frontend application is accessible on port 5173');
      
      // Check if we can see basic elements
      const body = page.locator('body');
      await expect(body).toBeVisible();
      
    } catch (error) {
      console.log('⚠️ Frontend application not accessible on port 5173');
      console.log('This is expected if the frontend is not running');
    }
  });
});
