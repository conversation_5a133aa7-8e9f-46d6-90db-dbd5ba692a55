import { test, expect } from '@playwright/test';
import { MainPage } from './pages/MainPage';

test.describe('Error Scenario Testing', () => {
  let mainPage: MainPage;

  test.beforeEach(async ({ page }) => {
    mainPage = new MainPage(page);
    await mainPage.goto();
  });

  test.describe('Network Failure Scenarios', () => {
    test('should handle complete API unavailability', async ({ page }) => {
      // Block all API requests
      await page.route('**/api/**', route => route.abort());

      await mainPage.navigateToExplorer();
      await page.waitForTimeout(5000);

      // Application should not crash
      await expect(mainPage.header).toBeVisible();
      
      // Should show appropriate error message or fallback
      const pageContent = await page.textContent('body');
      expect(pageContent).not.toContain('TypeError');
      expect(pageContent).not.toContain('undefined');
    });

    test('should handle intermittent network failures', async ({ page }) => {
      let requestCount = 0;
      
      // Fail every other request
      await page.route('**/api/**', route => {
        requestCount++;
        if (requestCount % 2 === 0) {
          route.abort();
        } else {
          route.continue();
        }
      });

      await mainPage.navigateToExplorer();
      await page.waitForTimeout(5000);

      // Should handle partial failures gracefully
      await expect(mainPage.header).toBeVisible();
      await mainPage.verifyNoErrors();
    });

    test('should handle slow API responses', async ({ page }) => {
      // Add significant delay to API responses
      await page.route('**/api/**', async route => {
        await new Promise(resolve => setTimeout(resolve, 3000));
        route.continue();
      });

      await mainPage.navigateToExplorer();
      
      // Should show loading state or handle timeout gracefully
      await page.waitForTimeout(8000);
      await expect(mainPage.header).toBeVisible();
    });

    test('should handle malformed API responses', async ({ page }) => {
      // Return invalid JSON
      await page.route('**/api/graph/**', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: 'invalid json response'
        });
      });

      await mainPage.navigateToExplorer();
      await page.waitForTimeout(3000);

      // Should handle parsing errors gracefully
      await expect(mainPage.header).toBeVisible();
      await mainPage.verifyNoErrors();
    });
  });

  test.describe('Server Error Scenarios', () => {
    test('should handle 500 Internal Server Error', async ({ page }) => {
      await page.route('**/api/**', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal Server Error' })
        });
      });

      await mainPage.navigateToExplorer();
      await page.waitForTimeout(3000);

      // Should show user-friendly error message
      const pageContent = await page.textContent('body');
      expect(pageContent).not.toContain('500');
      expect(pageContent).not.toContain('Internal Server Error');
      
      // Application should remain functional
      await expect(mainPage.header).toBeVisible();
    });

    test('should handle 404 Not Found errors', async ({ page }) => {
      await page.route('**/api/graph/**', route => {
        route.fulfill({
          status: 404,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Not Found' })
        });
      });

      await mainPage.navigateToExplorer();
      await page.waitForTimeout(3000);

      // Should handle missing resources gracefully
      await expect(mainPage.header).toBeVisible();
      await mainPage.verifyNoErrors();
    });

    test('should handle timeout errors', async ({ page }) => {
      // Simulate request timeout by never responding
      await page.route('**/api/**', route => {
        // Never respond to simulate timeout
      });

      await mainPage.navigateToExplorer();
      await page.waitForTimeout(10000);

      // Should handle timeouts gracefully
      await expect(mainPage.header).toBeVisible();
    });
  });

  test.describe('Data Corruption Scenarios', () => {
    test('should handle empty graph data', async ({ page }) => {
      await page.route('**/api/graph/**', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ nodes: [], edges: [] })
        });
      });

      await mainPage.navigateToExplorer();
      await page.waitForTimeout(3000);

      // Should handle empty data gracefully
      await expect(mainPage.explorerContent).toBeVisible();
      await mainPage.verifyNoErrors();
    });

    test('should handle malformed graph data', async ({ page }) => {
      await page.route('**/api/graph/**', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ 
            nodes: [{ invalid: 'structure' }], 
            edges: [{ missing: 'required_fields' }] 
          })
        });
      });

      await mainPage.navigateToExplorer();
      await page.waitForTimeout(3000);

      // Should handle malformed data without crashing
      await expect(mainPage.header).toBeVisible();
      await mainPage.verifyNoErrors();
    });

    test('should handle missing required fields', async ({ page }) => {
      await page.route('**/api/graph/**', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ 
            nodes: [{}], // Empty node object
            edges: [{}]  // Empty edge object
          })
        });
      });

      await mainPage.navigateToExplorer();
      await page.waitForTimeout(3000);

      // Should handle missing fields gracefully
      await expect(mainPage.explorerContent).toBeVisible();
    });
  });

  test.describe('User Input Error Scenarios', () => {
    test('should handle invalid search queries', async ({ page }) => {
      await mainPage.navigateToExplorer();
      
      // Wait for initial load
      try {
        await mainPage.waitForGraphLoad();
      } catch {
        // If graph doesn't load, continue with search tests
      }

      // Test various invalid inputs
      const invalidQueries = [
        '', // Empty string
        '   ', // Whitespace only
        '<script>alert("xss")</script>', // XSS attempt
        'a'.repeat(1000), // Very long string
        '!@#$%^&*()', // Special characters
      ];

      for (const query of invalidQueries) {
        await mainPage.searchInGraph(query);
        await page.waitForTimeout(1000);
        
        // Should not crash or show errors
        await mainPage.verifyNoErrors();
        await expect(mainPage.explorerContent).toBeVisible();
      }
    });

    test('should handle rapid user interactions', async ({ page }) => {
      await mainPage.navigateToExplorer();
      
      // Rapidly switch tabs
      for (let i = 0; i < 10; i++) {
        await mainPage.explorerTab.click();
        await mainPage.chatTab.click();
        await mainPage.analysisTab.click();
      }

      // Should remain stable
      await expect(mainPage.header).toBeVisible();
      await mainPage.verifyNoErrors();
    });

    test('should handle concurrent search operations', async ({ page }) => {
      await mainPage.navigateToExplorer();
      
      // Try to load graph, but continue even if it fails
      try {
        await mainPage.waitForGraphLoad();
      } catch {
        // Continue with test even if graph doesn't load
      }

      // Perform multiple searches rapidly
      const searches = ['test1', 'test2', 'test3', 'test4', 'test5'];
      
      for (const query of searches) {
        await mainPage.searchBar.fill(query);
        // Don't wait for completion, simulate rapid typing
      }
      
      await mainPage.searchBar.press('Enter');
      await page.waitForTimeout(2000);

      // Should handle concurrent operations gracefully
      await mainPage.verifyNoErrors();
    });
  });

  test.describe('Browser Compatibility Issues', () => {
    test('should handle JavaScript errors gracefully', async ({ page }) => {
      // Inject a JavaScript error
      await page.addInitScript(() => {
        // Override console.error to catch errors
        const originalError = console.error;
        window.jsErrors = [];
        console.error = (...args) => {
          window.jsErrors.push(args.join(' '));
          originalError.apply(console, args);
        };
      });

      await mainPage.navigateToExplorer();
      await page.waitForTimeout(3000);

      // Check for JavaScript errors
      const jsErrors = await page.evaluate(() => window.jsErrors || []);
      
      // Should not have critical errors that break functionality
      await expect(mainPage.header).toBeVisible();
      
      // Log errors for debugging but don't fail test unless critical
      if (jsErrors.length > 0) {
        console.log('JavaScript errors detected:', jsErrors);
      }
    });
  });

  test.describe('Recovery and Graceful Degradation', () => {
    test('should recover from temporary failures', async ({ page }) => {
      let failureCount = 0;
      
      // Fail first few requests, then succeed
      await page.route('**/api/**', route => {
        failureCount++;
        if (failureCount <= 3) {
          route.abort();
        } else {
          route.continue();
        }
      });

      await mainPage.navigateToExplorer();
      await page.waitForTimeout(5000);

      // Should eventually recover and work
      await expect(mainPage.explorerContent).toBeVisible();
    });

    test('should provide fallback functionality', async ({ page }) => {
      // Block graph API but allow other APIs
      await page.route('**/api/graph/**', route => route.abort());

      await mainPage.navigateToExplorer();
      await page.waitForTimeout(3000);

      // Should still show basic interface even without graph data
      await expect(mainPage.explorerContent).toBeVisible();
      await expect(mainPage.searchBar).toBeVisible();
    });

    test('should maintain core functionality during partial failures', async ({ page }) => {
      // Block only specific endpoints
      await page.route('**/api/analysis/**', route => route.abort());

      await mainPage.navigateToExplorer();
      
      // Explorer should still work
      try {
        await mainPage.waitForGraphLoad();
      } catch {
        // Continue even if graph doesn't load
      }
      
      // Chat should still work
      await mainPage.navigateToChat();
      await expect(mainPage.chatContent).toBeVisible();
    });
  });
});
