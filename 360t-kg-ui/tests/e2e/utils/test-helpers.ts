import { Page, expect } from '@playwright/test';

export class TestHelpers {
  constructor(private page: Page) {}

  /**
   * Wait for network to be idle and all animations to complete
   */
  async waitForStableState(timeout = 10000) {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(1000); // Allow for animations
  }

  /**
   * Take a screenshot with timestamp
   */
  async takeTimestampedScreenshot(name: string) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    await this.page.screenshot({ 
      path: `test-results/screenshots/${name}-${timestamp}.png`,
      fullPage: true 
    });
  }

  /**
   * Wait for API response and validate
   */
  async waitForApiResponse(urlPattern: string | RegExp, timeout = 30000) {
    const response = await this.page.waitForResponse(urlPattern, { timeout });
    expect(response.ok()).toBeTruthy();
    return response;
  }

  /**
   * Measure page load performance
   */
  async measurePageLoadTime(): Promise<number> {
    const startTime = Date.now();
    await this.page.waitForLoadState('networkidle');
    return Date.now() - startTime;
  }

  /**
   * Verify responsive design at different viewport sizes
   */
  async testResponsiveDesign(viewports: Array<{width: number, height: number}>) {
    for (const viewport of viewports) {
      await this.page.setViewportSize(viewport);
      await this.waitForStableState();
      
      // Check that critical elements are still visible
      await expect(this.page.locator('header')).toBeVisible();
      await expect(this.page.locator('.tab-button')).toBeVisible();
    }
  }

  /**
   * Test keyboard navigation
   */
  async testKeyboardNavigation() {
    // Tab through interactive elements
    await this.page.keyboard.press('Tab');
    await this.page.keyboard.press('Tab');
    await this.page.keyboard.press('Enter');
    
    // Verify focus is visible
    const focusedElement = await this.page.locator(':focus');
    await expect(focusedElement).toBeVisible();
  }

  /**
   * Wait for graph to load with nodes
   */
  async waitForGraphLoad(timeout = 30000) {
    await this.page.waitForSelector('.graph-svg', { timeout });
    await this.page.waitForSelector('.graph-svg .node-group', { timeout });
    
    // Ensure we have actual nodes, not just the container
    const nodeCount = await this.page.locator('.graph-svg .node-group').count();
    expect(nodeCount).toBeGreaterThan(0);
    
    return nodeCount;
  }

  /**
   * Simulate network conditions
   */
  async simulateSlowNetwork() {
    await this.page.route('**/*', route => {
      setTimeout(() => route.continue(), 1000); // Add 1s delay
    });
  }

  /**
   * Simulate API errors
   */
  async simulateApiError(urlPattern: string | RegExp, statusCode = 500) {
    await this.page.route(urlPattern, route => {
      route.fulfill({
        status: statusCode,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Simulated API error' })
      });
    });
  }

  /**
   * Check for memory leaks (basic check)
   */
  async checkMemoryUsage(): Promise<number> {
    const metrics = await this.page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0;
    });
    return metrics;
  }

  /**
   * Verify 360T design system compliance
   */
  async verify360TDesignSystem() {
    // Check for 360T green color usage
    const headerBg = await this.page.locator('header').evaluate(el => 
      getComputedStyle(el).backgroundColor
    );
    
    // Check typography consistency
    const headings = await this.page.locator('h1, h2, h3').all();
    for (const heading of headings) {
      const fontFamily = await heading.evaluate(el => 
        getComputedStyle(el).fontFamily
      );
      expect(fontFamily).toContain('Arial'); // Adjust based on actual font
    }
  }

  /**
   * Verify contrast ratios for accessibility
   */
  async checkContrastRatios() {
    const elements = await this.page.locator('button, a, .text-content').all();
    
    for (const element of elements) {
      const styles = await element.evaluate(el => {
        const computed = getComputedStyle(el);
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor
        };
      });
      
      // Verify contrast ratio meets WCAG AA standards (4.5:1)
      // Implementation would calculate actual contrast ratio
    }
  }

  /**
   * Check for console errors
   */
  async getConsoleErrors(): Promise<string[]> {
    const errors: string[] = [];
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    return errors;
  }

  /**
   * Wait for element to be stable (not moving)
   */
  async waitForElementStable(selector: string, timeout = 5000) {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout });
    
    // Wait for element to stop moving
    let previousPosition = await element.boundingBox();
    await this.page.waitForTimeout(100);
    
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      const currentPosition = await element.boundingBox();
      if (previousPosition && currentPosition &&
          previousPosition.x === currentPosition.x &&
          previousPosition.y === currentPosition.y) {
        break;
      }
      previousPosition = currentPosition;
      await this.page.waitForTimeout(100);
    }
  }

  /**
   * Verify API health before tests
   */
  async verifyApiHealth() {
    try {
      // Check new microservices system
      const newApiResponse = await this.page.request.get('http://localhost:3003/api/health');
      if (!newApiResponse.ok()) {
        console.warn('⚠️ New microservices system health check failed');
      }
      
      // Legacy system has been removed - no longer checking port 3002
    } catch (error) {
      console.warn('⚠️ API health check failed:', error.message);
    }
  }
}
