import { test, expect } from '@playwright/test';
import { MainPage } from './pages/MainPage';

test.describe('Performance and Accessibility Tests', () => {
  let mainPage: MainPage;

  test.beforeEach(async ({ page }) => {
    mainPage = new MainPage(page);
    await mainPage.goto();
  });

  test.describe('Performance Benchmarking', () => {
    test('should meet sub-2-second response time targets', async ({ page }) => {
      // Test initial page load
      const startTime = Date.now();
      await mainPage.goto();
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(2000);

      // Test tab switching performance
      const tabSwitchStart = Date.now();
      await mainPage.navigateToExplorer();
      const tabSwitchTime = Date.now() - tabSwitchStart;
      expect(tabSwitchTime).toBeLessThan(1000);

      // Test graph loading performance
      const graphLoadStart = Date.now();
      await mainPage.waitForGraphLoad();
      const graphLoadTime = Date.now() - graphLoadStart;
      expect(graphLoadTime).toBeLessThan(2000);
    });

    test('should handle concurrent user interactions efficiently', async ({ page }) => {
      await mainPage.navigateToExplorer();
      await mainPage.waitForGraphLoad();

      // Simulate rapid tab switching
      const startTime = Date.now();
      
      for (let i = 0; i < 3; i++) {
        await mainPage.navigateToChat();
        await mainPage.navigateToExplorer();
        await mainPage.navigateToAnalysis();
      }

      const totalTime = Date.now() - startTime;
      expect(totalTime).toBeLessThan(8000); // 8 seconds for all operations
    });

    test('should maintain performance under search load', async ({ page }) => {
      await mainPage.navigateToExplorer();
      await mainPage.waitForGraphLoad();

      // Perform multiple search operations
      const searches = ['test', 'node', 'graph', 'data'];
      const searchTimes = [];

      for (const query of searches) {
        const startTime = Date.now();
        await mainPage.searchInGraph(query);
        await page.waitForTimeout(1000);
        const searchTime = Date.now() - startTime;
        searchTimes.push(searchTime);
      }

      // All searches should complete within reasonable time
      const avgSearchTime = searchTimes.reduce((a, b) => a + b, 0) / searchTimes.length;
      expect(avgSearchTime).toBeLessThan(2000);
    });

    test('should optimize memory usage', async ({ page }) => {
      await mainPage.navigateToExplorer();
      const initialMemory = await mainPage.page.evaluate(() => {
        return (performance as any).memory?.usedJSHeapSize || 0;
      });

      // Perform memory-intensive operations
      await mainPage.waitForGraphLoad();
      await mainPage.navigateToChat();
      await mainPage.navigateToAnalysis();
      await mainPage.navigateToExplorer();

      const finalMemory = await mainPage.page.evaluate(() => {
        return (performance as any).memory?.usedJSHeapSize || 0;
      });
      
      const memoryIncrease = finalMemory - initialMemory;
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // 50MB limit
    });
  });

  test.describe('Cross-Browser and Viewport Testing', () => {
    test('should work across different viewport sizes', async ({ page }) => {
      const viewports = [
        { width: 1920, height: 1080, name: 'Desktop Large' },
        { width: 1366, height: 768, name: 'Desktop Standard' },
        { width: 1440, height: 900, name: 'Desktop Medium' },
        { width: 768, height: 1024, name: 'Tablet' },
      ];

      for (const viewport of viewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await page.waitForTimeout(1000);

        // Verify critical elements are visible
        await expect(mainPage.header).toBeVisible();
        await expect(mainPage.explorerTab).toBeVisible();

        // Test navigation works at this viewport
        await mainPage.navigateToExplorer();
        await expect(mainPage.explorerContent).toBeVisible();

        console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}) - OK`);
      }
    });

    test('should maintain functionality on mobile viewport', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Test basic functionality
      await mainPage.navigateToExplorer();
      await expect(mainPage.explorerContent).toBeVisible();
      
      // Test mobile navigation
      await mainPage.navigateToChat();
      await expect(mainPage.chatContent).toBeVisible();
    });
  });

  test.describe('Accessibility Compliance', () => {
    test('should support keyboard navigation', async ({ page }) => {
      // Test tab navigation
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      
      // Verify focus is visible
      const focusedElement = await page.locator(':focus');
      if (await focusedElement.count() > 0) {
        await expect(focusedElement).toBeVisible();
      }

      // Test Enter key activation
      await page.keyboard.press('Enter');
      await page.waitForTimeout(1000);

      // Should not cause errors
      await mainPage.verifyNoErrors();
    });

    test('should have proper ARIA labels and roles', async ({ page }) => {
      // Check for ARIA landmarks
      const main = page.locator('[role="main"], main');
      const navigation = page.locator('[role="navigation"], nav');
      
      // These are optional but good practice
      if (await main.count() > 0) {
        await expect(main).toBeVisible();
      }
      
      if (await navigation.count() > 0) {
        await expect(navigation).toBeVisible();
      }

      // Check for button accessibility
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i);
        const ariaLabel = await button.getAttribute('aria-label');
        const textContent = await button.textContent();
        
        // Button should have either aria-label or text content
        expect(ariaLabel || textContent).toBeTruthy();
      }
    });

    test('should meet color contrast requirements', async ({ page }) => {
      await mainPage.navigateToExplorer();
      
      // Check header contrast
      const headerStyles = await mainPage.header.evaluate(el => {
        const computed = getComputedStyle(el);
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor
        };
      });

      // Verify colors are defined
      expect(headerStyles.color).toBeTruthy();
      expect(headerStyles.backgroundColor).toBeTruthy();

      // Check button contrast
      const buttonStyles = await mainPage.explorerTab.evaluate(el => {
        const computed = getComputedStyle(el);
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor
        };
      });

      expect(buttonStyles.color).toBeTruthy();
    });

    test('should provide alternative text for images', async ({ page }) => {
      const images = page.locator('img');
      const imageCount = await images.count();

      for (let i = 0; i < imageCount; i++) {
        const img = images.nth(i);
        const alt = await img.getAttribute('alt');
        const ariaLabel = await img.getAttribute('aria-label');
        
        // Image should have alt text or aria-label
        expect(alt || ariaLabel).toBeTruthy();
      }
    });

    test('should support screen reader navigation', async ({ page }) => {
      // Check for proper heading hierarchy
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      const headingCount = await headings.count();

      if (headingCount > 0) {
        // Should have at least one main heading
        const mainHeadings = page.locator('h1, h2');
        const mainHeadingCount = await mainHeadings.count();
        expect(mainHeadingCount).toBeGreaterThan(0);
      }
    });
  });

  test.describe('360T Design System Compliance', () => {
    test('should use consistent brand colors', async ({ page }) => {
      await mainPage.navigateToExplorer();

      // Check header for brand colors
      const headerBg = await mainPage.header.evaluate(el => 
        getComputedStyle(el).backgroundColor
      );

      // Verify brand color usage
      expect(headerBg).toBeTruthy();
    });

    test('should maintain consistent typography', async ({ page }) => {
      await mainPage.navigateToExplorer();

      // Check font consistency across elements
      const elements = [
        mainPage.header,
        mainPage.explorerTab,
        page.locator('h1, h2, h3').first()
      ];

      for (const element of elements) {
        if (await element.count() > 0) {
          const fontFamily = await element.evaluate(el => 
            getComputedStyle(el).fontFamily
          );
          expect(fontFamily).toBeTruthy();
        }
      }
    });

    test('should maintain proper spacing and layout', async ({ page }) => {
      await mainPage.navigateToExplorer();

      // Check for consistent header height
      const headerHeight = await mainPage.header.evaluate(el => 
        el.getBoundingClientRect().height
      );

      expect(headerHeight).toBeGreaterThan(40); // Minimum usable header height
      expect(headerHeight).toBeLessThan(120); // Maximum reasonable header height
    });

    test('should maintain visual consistency across tabs', async ({ page }) => {
      const tabs = [
        { tab: mainPage.explorerTab, content: mainPage.explorerContent },
        { tab: mainPage.chatTab, content: mainPage.chatContent },
        { tab: mainPage.analysisTab, content: mainPage.analysisContent },
        { tab: mainPage.documentationTab, content: mainPage.documentationContent },
      ];

      for (const { tab, content } of tabs) {
        await tab.click();
        await expect(content).toBeVisible();
        
        // Check that header remains consistent
        await expect(mainPage.header).toBeVisible();
        
        // Check that tab styling is consistent
        const tabStyles = await tab.evaluate(el => {
          const computed = getComputedStyle(el);
          return {
            fontSize: computed.fontSize,
            fontWeight: computed.fontWeight,
            padding: computed.padding
          };
        });
        
        expect(tabStyles.fontSize).toBeTruthy();
        expect(tabStyles.fontWeight).toBeTruthy();
      }
    });
  });
});
