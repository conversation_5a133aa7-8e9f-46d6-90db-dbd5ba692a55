import { test, expect } from '@playwright/test';
import { MainPage } from './pages/MainPage';

test.describe('Core Functionality Tests', () => {
  let mainPage: MainPage;

  test.beforeEach(async ({ page }) => {
    mainPage = new MainPage(page);
    await mainPage.goto();
    await mainPage.verifyApiHealth();
  });

  test.describe('Navigation and Tab Switching', () => {
    test('should navigate between all tabs successfully', async ({ page }) => {
      // Test Explorer tab
      await mainPage.navigateToExplorer();
      await expect(mainPage.explorerContent).toBeVisible();
      
      // Test Chat tab
      await mainPage.navigateToChat();
      await expect(mainPage.chatContent).toBeVisible();
      
      // Test Analysis tab
      await mainPage.navigateToAnalysis();
      await expect(mainPage.analysisContent).toBeVisible();
      
      // Test Documentation tab
      await mainPage.navigateToDocumentation();
      await expect(mainPage.documentationContent).toBeVisible();
      
      // Return to Explorer
      await mainPage.navigateToExplorer();
      await expect(mainPage.explorerContent).toBeVisible();
    });

    test('should maintain state when switching tabs', async ({ page }) => {
      // Start in Explorer and load graph
      await mainPage.navigateToExplorer();
      const initialNodeCount = await mainPage.waitForGraphLoad();
      
      // Switch to Chat and back
      await mainPage.navigateToChat();
      await mainPage.navigateToExplorer();
      
      // Verify graph is still loaded
      await mainPage.verifyGraphIsLoaded();
      const finalNodeCount = await mainPage.getNodeCount();
      expect(Math.abs(finalNodeCount - initialNodeCount)).toBeLessThan(5);
    });

    test('should handle rapid tab switching', async ({ page }) => {
      // Rapidly switch between tabs
      for (let i = 0; i < 5; i++) {
        await mainPage.navigateToExplorer();
        await mainPage.navigateToChat();
        await mainPage.navigateToAnalysis();
      }
      
      // Should remain stable
      await expect(mainPage.header).toBeVisible();
      await mainPage.verifyNoErrors();
    });
  });

  test.describe('Graph Visualization', () => {
    test('should load initial graph on Explorer tab', async ({ page }) => {
      await mainPage.navigateToExplorer();
      await mainPage.verifyGraphIsLoaded();
      
      // Verify graph elements
      await expect(mainPage.graphSvg).toBeVisible();
      await expect(mainPage.nodeGroups.first()).toBeVisible();
      
      const nodeCount = await mainPage.getNodeCount();
      expect(nodeCount).toBeGreaterThan(3);
    });

    test('should handle node interactions', async ({ page }) => {
      await mainPage.navigateToExplorer();
      await mainPage.waitForGraphLoad();
      
      // Click on a node
      await mainPage.clickNode(0);
      
      // Verify interaction doesn't cause errors
      await mainPage.verifyNoErrors();
    });

    test('should support graph search functionality', async ({ page }) => {
      await mainPage.navigateToExplorer();
      await mainPage.waitForGraphLoad();
      
      // Perform search
      await mainPage.searchInGraph('test');
      
      // Verify search results or graph update
      await expect(mainPage.graphSvg).toBeVisible();
      await mainPage.verifyNoErrors();
    });

    test('should handle empty search results gracefully', async ({ page }) => {
      await mainPage.navigateToExplorer();
      await mainPage.waitForGraphLoad();
      
      // Search for something that likely doesn't exist
      await mainPage.searchInGraph('nonexistentnode12345');
      
      // Should not crash or show errors
      await mainPage.verifyNoErrors();
      await expect(mainPage.graphSvg).toBeVisible();
    });

    test('should clear search and return to full graph', async ({ page }) => {
      await mainPage.navigateToExplorer();
      const initialNodeCount = await mainPage.waitForGraphLoad();
      
      // Perform search
      await mainPage.searchInGraph('test');
      await page.waitForTimeout(2000);
      
      // Clear search
      await mainPage.searchBar.clear();
      await mainPage.searchBar.press('Enter');
      await page.waitForTimeout(2000);
      
      // Should return to full graph
      const finalNodeCount = await mainPage.getNodeCount();
      expect(Math.abs(finalNodeCount - initialNodeCount)).toBeLessThan(5);
    });
  });

  test.describe('Chat Interface', () => {
    test('should display chat interface correctly', async ({ page }) => {
      await mainPage.navigateToChat();
      
      // Verify chat elements are present
      await expect(mainPage.chatContent).toBeVisible();
      await expect(mainPage.chatInput).toBeVisible();
    });

    test('should handle chat message input', async ({ page }) => {
      await mainPage.navigateToChat();
      
      // Type a message
      await mainPage.chatInput.fill('Hello, can you help me understand this graph?');
      
      // Verify input was accepted
      const inputValue = await mainPage.chatInput.inputValue();
      expect(inputValue).toContain('Hello');
    });

    test('should handle chat submission if available', async ({ page }) => {
      await mainPage.navigateToChat();
      
      // Send a message if send functionality is available
      if (await mainPage.sendButton.count() > 0) {
        await mainPage.sendChatMessage('Test message');
        await page.waitForTimeout(2000);
        await mainPage.verifyNoErrors();
      } else {
        // Just verify input works
        await mainPage.chatInput.fill('Test message');
        await mainPage.chatInput.press('Enter');
        await mainPage.verifyNoErrors();
      }
    });
  });

  test.describe('Analysis Interface', () => {
    test('should display analysis interface correctly', async ({ page }) => {
      await mainPage.navigateToAnalysis();
      
      // Verify analysis elements are present
      await expect(mainPage.analysisContent).toBeVisible();
    });

    test('should handle analysis panel interactions', async ({ page }) => {
      await mainPage.navigateToAnalysis();
      
      // Check if analysis panel exists and interact with it
      if (await mainPage.analysisPanel.count() > 0) {
        await expect(mainPage.analysisPanel).toBeVisible();
      }
      
      await mainPage.verifyNoErrors();
    });
  });

  test.describe('Documentation Interface', () => {
    test('should display documentation interface correctly', async ({ page }) => {
      await mainPage.navigateToDocumentation();
      
      // Verify documentation elements are present
      await expect(mainPage.documentationContent).toBeVisible();
    });

    test('should handle documentation navigation', async ({ page }) => {
      await mainPage.navigateToDocumentation();
      
      // Verify documentation loads without errors
      await page.waitForTimeout(2000);
      await mainPage.verifyNoErrors();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Navigate to explorer first
      await mainPage.navigateToExplorer();
      
      // Simulate network failure for future requests
      await page.route('**/api/**', route => route.abort());
      
      // Try to perform search (which might trigger API call)
      await mainPage.searchInGraph('network test');
      
      // Should not crash completely
      await expect(mainPage.header).toBeVisible();
    });

    test('should display user-friendly error messages', async ({ page }) => {
      // Simulate API error
      await page.route('**/api/graph/**', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Server error' })
        });
      });
      
      await mainPage.navigateToExplorer();
      await page.waitForTimeout(3000);
      
      // Should not show raw error details to user
      const pageContent = await page.textContent('body');
      expect(pageContent).not.toContain('500');
      expect(pageContent).not.toContain('Internal Server Error');
    });
  });

  test.describe('Data Integrity', () => {
    test('should display consistent data across page refreshes', async ({ page }) => {
      await mainPage.navigateToExplorer();
      const initialNodeCount = await mainPage.waitForGraphLoad();
      
      // Refresh page
      await page.reload();
      await mainPage.navigateToExplorer();
      const refreshedNodeCount = await mainPage.waitForGraphLoad();
      
      // Should have similar node count (allowing for minor variations)
      expect(Math.abs(refreshedNodeCount - initialNodeCount)).toBeLessThan(10);
    });

    test('should maintain data consistency during interactions', async ({ page }) => {
      await mainPage.navigateToExplorer();
      await mainPage.waitForGraphLoad();
      
      // Perform search
      await mainPage.searchInGraph('test');
      await page.waitForTimeout(2000);
      
      // Clear search and verify original data returns
      await mainPage.searchBar.clear();
      await mainPage.searchBar.press('Enter');
      await page.waitForTimeout(2000);
      
      // Should show full graph again
      const nodeCount = await mainPage.getNodeCount();
      expect(nodeCount).toBeGreaterThan(3);
    });
  });
});
