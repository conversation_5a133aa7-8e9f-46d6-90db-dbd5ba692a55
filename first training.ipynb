{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'langchain_community'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[10], line 4\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01mdotenv\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m load_dotenv\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mos\u001b[39;00m\n\u001b[0;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mlangchain_community\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mgraphs\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Neo4jGraph\n\u001b[1;32m      6\u001b[0m \u001b[38;5;66;03m# Warning control\u001b[39;00m\n\u001b[1;32m      7\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mwarnings\u001b[39;00m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'langchain_community'"]}], "source": ["from dotenv import load_dotenv\n", "import os\n", "\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "# Warning control\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}