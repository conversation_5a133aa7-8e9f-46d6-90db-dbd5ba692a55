# Detailed Mitigation Strategies & Testing Protocols

## Critical Risk Mitigation Plans

### 1. Configuration Consolidation Mitigation (BC-001)

#### **Pre-Migration Setup**
```bash
#!/bin/bash
# setup-config-migration.sh

echo "🔧 Setting up configuration migration..."

# 1. Create backup directory
BACKUP_DIR="./config-backup/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# 2. Backup all existing configurations
find . -name ".env*" -not -path "./node_modules/*" -exec cp {} "$BACKUP_DIR/" \;
cp docker-compose.yml "$BACKUP_DIR/"
cp -r config/ "$BACKUP_DIR/" 2>/dev/null || true

# 3. Create compatibility layer
mkdir -p shared/config/legacy
```

#### **Compatibility Layer Implementation**
```typescript
// shared/config/legacy/adapter.ts
export class LegacyConfigAdapter {
  private static readonly VARIABLE_MAPPINGS = {
    // API Service mappings
    'NEO4J_USER': 'NEO4J_USERNAME',
    'NEO4J_URI': 'DATABASE_URI',
    'PORT': 'API_PORT',
    
    // Proxy Service mappings
    'PROXY_PORT': 'PROXY_SERVER_PORT',
    'FASTAPI_URL': 'CHAT_API_URL',
    
    // Python Service mappings
    'ANTHROPIC_API_KEY': 'LLM_ANTHROPIC_API_KEY',
    'GOOGLE_API_KEY': 'LLM_GOOGLE_API_KEY'
  };

  static adaptEnvironment(): void {
    Object.entries(this.VARIABLE_MAPPINGS).forEach(([oldKey, newKey]) => {
      if (process.env[oldKey] && !process.env[newKey]) {
        process.env[newKey] = process.env[oldKey];
        console.warn(`⚠️  Using legacy environment variable ${oldKey}. Please update to ${newKey}`);
      }
    });
  }

  static validateMigration(): string[] {
    const errors: string[] = [];
    const requiredVars = ['NEO4J_URI', 'NEO4J_USERNAME', 'NEO4J_PASSWORD'];
    
    requiredVars.forEach(varName => {
      if (!process.env[varName]) {
        errors.push(`Missing required environment variable: ${varName}`);
      }
    });
    
    return errors;
  }
}
```

#### **Gradual Migration Script**
```bash
#!/bin/bash
# migrate-config-step-by-step.sh

set -e

echo "🚀 Starting gradual configuration migration..."

# Step 1: Validate current state
echo "📋 Step 1: Validating current configuration..."
npm run validate-config || {
  echo "❌ Current configuration is invalid. Aborting migration."
  exit 1
}

# Step 2: Create new unified config
echo "🔧 Step 2: Creating unified configuration..."
node scripts/create-unified-config.js

# Step 3: Test with compatibility layer
echo "🧪 Step 3: Testing with compatibility layer..."
export CONFIG_MODE="hybrid"
docker-compose up -d --build

# Wait for services to start
sleep 30

# Validate all services are healthy
./scripts/validate-services.sh || {
  echo "❌ Services failed health check. Rolling back..."
  ./scripts/emergency-rollback.sh
  exit 1
}

echo "✅ Configuration migration completed successfully!"
```

### 2. Directory Restructuring Mitigation (BC-002)

#### **Phased Directory Migration**
```bash
#!/bin/bash
# migrate-directories-safely.sh

echo "📁 Starting safe directory migration..."

# Phase 1: Create new structure alongside old
mkdir -p packages/kg-api packages/kg-ui packages/kg-proxy
mkdir -p shared/config shared/types shared/utils
mkdir -p libs/llm-abstraction libs/graph-services

# Phase 2: Copy files (don't move yet)
echo "📋 Copying files to new structure..."
cp -r 360t-kg-api/* packages/kg-api/
cp -r 360t-kg-ui/* packages/kg-ui/
cp -r proxy-server/* packages/kg-proxy/
cp -r llm_abstraction/* libs/llm-abstraction/
cp -r services/* libs/graph-services/

# Phase 3: Update import paths in copied files
echo "🔧 Updating import paths..."
find packages/ -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx" | \
  xargs sed -i.bak 's|../360t-kg-api|../kg-api|g'

# Phase 4: Update Docker configurations
echo "🐳 Updating Docker configurations..."
cp docker-compose.yml docker-compose.yml.backup
envsubst < docker-compose.template.yml > docker-compose.yml

# Phase 5: Test new structure
echo "🧪 Testing new directory structure..."
export USE_NEW_STRUCTURE=true
docker-compose -f docker-compose.new.yml up -d --build

# Validate services
./scripts/validate-services.sh || {
  echo "❌ New structure failed validation. Keeping old structure."
  export USE_NEW_STRUCTURE=false
  exit 1
}

echo "✅ Directory migration successful!"
```

#### **Import Path Update Script**
```javascript
// scripts/update-import-paths.js
const fs = require('fs');
const path = require('path');
const glob = require('glob');

const IMPORT_MAPPINGS = {
  // JavaScript/TypeScript imports
  'from \'../360t-kg-api': 'from \'../packages/kg-api',
  'from \'./360t-kg-api': 'from \'./packages/kg-api',
  'require(\'../360t-kg-api': 'require(\'../packages/kg-api',
  
  // Python imports
  'from llm_abstraction': 'from libs.llm_abstraction',
  'from services': 'from libs.graph_services',
  'from config.environment': 'from shared.config.environment'
};

function updateImportsInFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  Object.entries(IMPORT_MAPPINGS).forEach(([oldImport, newImport]) => {
    if (content.includes(oldImport)) {
      content = content.replace(new RegExp(oldImport, 'g'), newImport);
      updated = true;
    }
  });
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Updated imports in ${filePath}`);
  }
}

// Update all relevant files
const patterns = [
  'packages/**/*.{js,ts,jsx,tsx,py}',
  'shared/**/*.{js,ts,jsx,tsx,py}',
  'libs/**/*.{js,ts,jsx,tsx,py}'
];

patterns.forEach(pattern => {
  glob.sync(pattern).forEach(updateImportsInFile);
});
```

### 3. API Endpoint Consolidation Mitigation (BC-003)

#### **API Versioning Strategy**
```typescript
// shared/api/versioning.ts
export class APIVersionManager {
  private static readonly ENDPOINT_MAPPINGS = {
    // Legacy endpoints → New versioned endpoints
    '/api/analysis/clusters': '/api/v1/analysis/clusters',
    '/api/analysis/hidden-links': '/api/v1/analysis/hidden-links',
    '/api/chat/message': '/api/v1/chat/message',
    '/api/graph/initial': '/api/v1/graph/initial'
  };

  static createLegacyRoutes(app: Express): void {
    Object.entries(this.ENDPOINT_MAPPINGS).forEach(([legacy, versioned]) => {
      app.use(legacy, (req, res, next) => {
        console.warn(`⚠️  Legacy endpoint ${legacy} used. Please update to ${versioned}`);
        req.url = versioned;
        next();
      });
    });
  }

  static validateEndpointCompatibility(): Promise<boolean> {
    // Test all legacy endpoints still work
    return Promise.all([
      this.testEndpoint('/api/analysis/clusters'),
      this.testEndpoint('/api/chat/message'),
      this.testEndpoint('/api/graph/initial')
    ]).then(results => results.every(Boolean));
  }

  private static async testEndpoint(endpoint: string): Promise<boolean> {
    try {
      const response = await fetch(`http://localhost:3002${endpoint}`);
      return response.status < 500;
    } catch {
      return false;
    }
  }
}
```

#### **Contract Testing Implementation**
```typescript
// tests/api-contracts.test.ts
import { describe, test, expect } from '@jest/globals';

describe('API Contract Tests', () => {
  const API_BASE = 'http://localhost:3002';
  
  test('Analysis endpoints maintain contract', async () => {
    const endpoints = [
      '/api/analysis/clusters?resolution=1.0',
      '/api/analysis/hidden-links?topN=20',
      '/api/analysis/centrality?type=pagerank'
    ];
    
    for (const endpoint of endpoints) {
      const response = await fetch(`${API_BASE}${endpoint}`);
      expect(response.status).toBeLessThan(500);
      
      if (response.ok) {
        const data = await response.json();
        expect(data).toBeDefined();
        // Validate response structure
        expect(typeof data).toBe('object');
      }
    }
  });
  
  test('Chat endpoints maintain contract', async () => {
    const response = await fetch(`${API_BASE}/api/chat/message`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ message: 'test' })
    });
    
    expect(response.status).toBeLessThan(500);
  });
});
```

## Testing Protocols

### Pre-Migration Testing Checklist

```bash
#!/bin/bash
# pre-migration-tests.sh

echo "🧪 Running pre-migration validation tests..."

# 1. Service Health Tests
echo "📋 Testing service health..."
curl -f http://localhost:3002/api/health || exit 1
curl -f http://localhost:5173 || exit 1
curl -f http://localhost:7474 || exit 1

# 2. Database Connectivity
echo "🗄️  Testing database connectivity..."
docker exec kg_neo4j cypher-shell -u neo4j -p development_password "RETURN 1" || exit 1

# 3. API Endpoint Tests
echo "🔌 Testing API endpoints..."
curl -f http://localhost:3002/api/graph/initial || exit 1
curl -f http://localhost:3002/api/analysis/clusters || exit 1

# 4. Frontend Functionality
echo "🖥️  Testing frontend functionality..."
npm run test:e2e || exit 1

echo "✅ All pre-migration tests passed!"
```

### Post-Migration Validation

```bash
#!/bin/bash
# post-migration-validation.sh

echo "🔍 Running post-migration validation..."

# 1. Configuration Validation
echo "⚙️  Validating configuration..."
node -e "
  const config = require('./shared/config');
  console.log('Config loaded successfully:', !!config);
  process.exit(config ? 0 : 1);
"

# 2. Service Integration Tests
echo "🔗 Testing service integration..."
npm run test:integration

# 3. End-to-End Workflow Tests
echo "🎯 Testing end-to-end workflows..."
npm run test:e2e:critical

# 4. Performance Baseline
echo "⚡ Running performance tests..."
npm run test:performance

echo "✅ Post-migration validation completed!"
```

### Continuous Monitoring Setup

```typescript
// shared/monitoring/health-monitor.ts
export class HealthMonitor {
  private checks = new Map<string, () => Promise<boolean>>();
  
  constructor() {
    this.registerChecks();
  }
  
  private registerChecks(): void {
    this.checks.set('database', this.checkDatabase);
    this.checks.set('api', this.checkAPI);
    this.checks.set('llm', this.checkLLMProviders);
    this.checks.set('frontend', this.checkFrontend);
  }
  
  async runAllChecks(): Promise<HealthReport> {
    const results = new Map<string, boolean>();
    
    for (const [name, check] of this.checks) {
      try {
        results.set(name, await check());
      } catch (error) {
        console.error(`Health check failed for ${name}:`, error);
        results.set(name, false);
      }
    }
    
    return {
      timestamp: new Date().toISOString(),
      overall: Array.from(results.values()).every(Boolean),
      details: Object.fromEntries(results)
    };
  }
  
  private async checkDatabase(): Promise<boolean> {
    // Neo4j connection test
    return true; // Implementation details
  }
  
  private async checkAPI(): Promise<boolean> {
    // API endpoint tests
    return true; // Implementation details
  }
  
  private async checkLLMProviders(): Promise<boolean> {
    // LLM provider health checks
    return true; // Implementation details
  }
  
  private async checkFrontend(): Promise<boolean> {
    // Frontend asset and functionality checks
    return true; // Implementation details
  }
}

interface HealthReport {
  timestamp: string;
  overall: boolean;
  details: Record<string, boolean>;
}
```

This comprehensive mitigation strategy provides specific, actionable steps to minimize risks during the refactoring process while maintaining system stability and functionality.
