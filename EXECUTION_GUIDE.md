# KnowledgeGraphVisualizer Refactoring: Execution Guide

## Quick Start Commands

### Phase 1: Foundation & Safety (Week 1)
```bash
# Start with Phase 1 foundation tasks
git checkout -b refactor/phase-1-foundation

# Task 1.1: Create Comprehensive Backup System
mkdir -p scripts backups/{config,database,docker,scripts}

# Create backup script
cat > scripts/backup-system.sh << 'EOF'
#!/bin/bash
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backups/$TIMESTAMP"
mkdir -p "$BACKUP_DIR"

echo "🔄 Creating backup: $BACKUP_DIR"

# Backup configurations
find . -name ".env*" -not -path "./node_modules/*" -exec cp {} "$BACKUP_DIR/" \;
cp docker-compose.yml "$BACKUP_DIR/"
cp -r config/ "$BACKUP_DIR/" 2>/dev/null || true

# Backup database
if docker ps | grep -q kg_neo4j; then
  docker exec kg_neo4j neo4j-admin database dump neo4j --to-path=/backups 2>/dev/null || true
  docker cp kg_neo4j:/backups/neo4j.dump "$BACKUP_DIR/" 2>/dev/null || true
fi

echo "✅ Backup completed: $BACKUP_DIR"
EOF

chmod +x scripts/backup-system.sh

# Execute backup
./scripts/backup-system.sh

# Task 1.2: Create Configuration Compatibility Layer
mkdir -p shared/config/legacy

cat > shared/config/legacy/adapter.ts << 'EOF'
export class LegacyConfigAdapter {
  private static readonly VARIABLE_MAPPINGS = {
    'NEO4J_USER': 'NEO4J_USERNAME',
    'PORT': 'API_PORT',
    'PROXY_PORT': 'PROXY_SERVER_PORT',
    'FASTAPI_URL': 'CHAT_API_URL'
  };

  static adapt(): void {
    Object.entries(this.VARIABLE_MAPPINGS).forEach(([oldKey, newKey]) => {
      if (process.env[oldKey] && !process.env[newKey]) {
        process.env[newKey] = process.env[oldKey];
        console.warn(`⚠️  Using legacy environment variable ${oldKey}. Please update to ${newKey}`);
      }
    });
  }

  static validate(): string[] {
    const errors: string[] = [];
    const requiredVars = ['NEO4J_URI', 'NEO4J_USERNAME', 'NEO4J_PASSWORD'];
    
    requiredVars.forEach(varName => {
      if (!process.env[varName]) {
        errors.push(`Missing required environment variable: ${varName}`);
      }
    });
    
    return errors;
  }
}
EOF

# Task 1.3: Create Health Monitoring System
mkdir -p shared/monitoring

cat > shared/monitoring/health-monitor.ts << 'EOF'
export interface HealthReport {
  timestamp: string;
  overall: boolean;
  services: {
    database: boolean;
    api: boolean;
    llm: boolean;
    frontend: boolean;
  };
}

export class HealthMonitor {
  async checkAllServices(): Promise<HealthReport> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkAPI(),
      this.checkLLM(),
      this.checkFrontend()
    ]);

    return {
      timestamp: new Date().toISOString(),
      overall: checks.every(c => c.status === 'fulfilled' && c.value),
      services: {
        database: checks[0].status === 'fulfilled' ? checks[0].value : false,
        api: checks[1].status === 'fulfilled' ? checks[1].value : false,
        llm: checks[2].status === 'fulfilled' ? checks[2].value : false,
        frontend: checks[3].status === 'fulfilled' ? checks[3].value : false
      }
    };
  }

  private async checkDatabase(): Promise<boolean> {
    try {
      const response = await fetch('http://localhost:7474/db/neo4j/tx/commit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ statements: [{ statement: 'RETURN 1' }] })
      });
      return response.ok;
    } catch { return false; }
  }

  private async checkAPI(): Promise<boolean> {
    try {
      const response = await fetch('http://localhost:3002/api/health');
      return response.ok;
    } catch { return false; }
  }

  private async checkLLM(): Promise<boolean> {
    // Add LLM provider health checks
    return true;
  }

  private async checkFrontend(): Promise<boolean> {
    try {
      const response = await fetch('http://localhost:5173');
      return response.ok;
    } catch { return false; }
  }
}
EOF

# Task 1.4: Create Emergency Rollback Procedures
cat > scripts/emergency-rollback.sh << 'EOF'
#!/bin/bash
set -e

echo "🚨 EMERGENCY ROLLBACK INITIATED"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 1. Stop all services
echo "🛑 Stopping all services..."
docker-compose down

# 2. Restore from latest backup
LATEST_BACKUP=$(ls -t backups/ | head -n1)
if [ -z "$LATEST_BACKUP" ]; then
  echo "❌ No backup found!"
  exit 1
fi

echo "📁 Restoring from backup: $LATEST_BACKUP"
cp -r "backups/$LATEST_BACKUP"/* ./

# 3. Restart services
echo "🚀 Restarting services..."
docker-compose up -d

# 4. Wait for services to start
sleep 30

# 5. Validate rollback
echo "🔍 Validating rollback..."
if curl -f http://localhost:3002/api/health > /dev/null 2>&1; then
  echo "✅ API service healthy"
else
  echo "❌ API service failed"
fi

if curl -f http://localhost:5173 > /dev/null 2>&1; then
  echo "✅ Frontend service healthy"
else
  echo "❌ Frontend service failed"
fi

echo "✅ ROLLBACK COMPLETED"
EOF

chmod +x scripts/emergency-rollback.sh

# Task 1.5: Setup Testing Infrastructure
mkdir -p tests/{unit,integration,e2e,performance}

cat > tests/validate-services.sh << 'EOF'
#!/bin/bash
echo "🧪 Validating all services..."

# Check API health
if curl -f http://localhost:3002/api/health > /dev/null 2>&1; then
  echo "✅ API service healthy"
else
  echo "❌ API service failed"
  exit 1
fi

# Check Frontend
if curl -f http://localhost:5173 > /dev/null 2>&1; then
  echo "✅ Frontend service healthy"
else
  echo "❌ Frontend service failed"
  exit 1
fi

# Check Neo4j
if curl -f http://localhost:7474 > /dev/null 2>&1; then
  echo "✅ Neo4j service healthy"
else
  echo "❌ Neo4j service failed"
  exit 1
fi

echo "✅ All services validated successfully"
EOF

chmod +x tests/validate-services.sh

# Validate Phase 1 completion
echo "🔍 Validating Phase 1 completion..."
./tests/validate-services.sh

echo "✅ Phase 1 Foundation completed successfully!"
```

### Phase 2: Gradual Migration (Weeks 2-3)
```bash
# Start Phase 2
git checkout -b refactor/phase-2-migration

# Task 2.1: Create Unified Configuration System
mkdir -p shared/config/{types,environments}

# Create configuration types
cat > shared/config/types.ts << 'EOF'
export interface DatabaseConfig {
  uri: string;
  username: string;
  password: string;
  database: string;
}

export interface LLMConfig {
  primaryProvider: 'ollama' | 'azure' | 'google';
  providers: {
    ollama?: { baseUrl: string; };
    azure?: { apiKey: string; endpoint: string; };
    google?: { apiKey: string; };
  };
}

export interface AppConfig {
  environment: 'development' | 'staging' | 'production';
  database: DatabaseConfig;
  api: {
    port: number;
    cors: { origins: string[]; };
  };
  llm: LLMConfig;
}
EOF

# Create configuration loader
cat > shared/config/config-loader.ts << 'EOF'
import { z } from 'zod';
import { AppConfig } from './types';
import { LegacyConfigAdapter } from './legacy/adapter';

const DatabaseConfigSchema = z.object({
  uri: z.string().url(),
  username: z.string().min(1),
  password: z.string().min(1),
  database: z.string().min(1)
});

const AppConfigSchema = z.object({
  environment: z.enum(['development', 'staging', 'production']),
  database: DatabaseConfigSchema,
  api: z.object({
    port: z.number().min(1000).max(65535),
    cors: z.object({
      origins: z.array(z.string())
    })
  }),
  llm: z.object({
    primaryProvider: z.enum(['ollama', 'azure', 'google']),
    providers: z.object({
      ollama: z.object({ baseUrl: z.string().url() }).optional(),
      azure: z.object({ 
        apiKey: z.string().min(1), 
        endpoint: z.string().url() 
      }).optional(),
      google: z.object({ apiKey: z.string().min(1) }).optional()
    })
  })
});

export class ConfigLoader {
  static load(): AppConfig {
    // Apply legacy adapter first
    LegacyConfigAdapter.adapt();
    
    const config = {
      environment: (process.env.NODE_ENV as any) || 'development',
      database: {
        uri: process.env.NEO4J_URI!,
        username: process.env.NEO4J_USERNAME!,
        password: process.env.NEO4J_PASSWORD!,
        database: process.env.NEO4J_DATABASE || 'neo4j'
      },
      api: {
        port: parseInt(process.env.API_PORT || process.env.PORT || '3002'),
        cors: {
          origins: process.env.CORS_ORIGINS?.split(',') || ['*']
        }
      },
      llm: {
        primaryProvider: (process.env.LLM_PRIMARY_PROVIDER as any) || 'ollama',
        providers: {
          ollama: process.env.LLM_OLLAMA_BASE_URL ? {
            baseUrl: process.env.LLM_OLLAMA_BASE_URL
          } : undefined,
          azure: process.env.LLM_AZURE_API_KEY ? {
            apiKey: process.env.LLM_AZURE_API_KEY,
            endpoint: process.env.LLM_AZURE_ENDPOINT!
          } : undefined,
          google: process.env.LLM_GOOGLE_API_KEY ? {
            apiKey: process.env.LLM_GOOGLE_API_KEY
          } : undefined
        }
      }
    };

    const result = AppConfigSchema.safeParse(config);
    if (!result.success) {
      throw new Error(`Invalid configuration: ${result.error.message}`);
    }

    return result.data;
  }
}
EOF

# Task 2.2: Implement API Service Consolidation
mkdir -p shared/api

cat > shared/api/errors.ts << 'EOF'
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public code: string
  ) {
    super(message);
    this.name = 'ApiError';
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      statusCode: this.statusCode,
      code: this.code
    };
  }
}
EOF

# Test Phase 2 configuration
echo "🧪 Testing unified configuration..."
npm install zod
node -e "
  require('ts-node/register');
  const { ConfigLoader } = require('./shared/config/config-loader');
  try {
    const config = ConfigLoader.load();
    console.log('✅ Configuration loaded successfully');
    console.log('Database URI:', config.database.uri);
    console.log('API Port:', config.api.port);
  } catch (error) {
    console.error('❌ Configuration failed:', error.message);
    process.exit(1);
  }
"

echo "✅ Phase 2 Migration completed successfully!"
```

This execution guide provides specific, actionable commands that can be run directly to implement the refactoring plan safely and systematically.
