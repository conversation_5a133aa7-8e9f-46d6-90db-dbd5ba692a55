# Implementation Examples for Critical Refactoring

## 1. Unified Configuration System

### Current Problems:
- 5+ different configuration files with overlapping concerns
- Inconsistent environment variable naming
- No type safety or validation

### Solution: Centralized Configuration

```typescript
// shared/config/types.ts
export interface DatabaseConfig {
  uri: string;
  username: string;
  password: string;
  database: string;
}

export interface LLMConfig {
  primaryProvider: 'ollama' | 'azure' | 'google';
  fallbackProviders: string[];
  retryAttempts: number;
}

export interface AppConfig {
  environment: 'development' | 'staging' | 'production';
  database: DatabaseConfig;
  llm: LLMConfig;
  api: {
    port: number;
    cors: {
      origins: string[];
    };
  };
}
```

```typescript
// shared/config/config-loader.ts
import { z } from 'zod';

const DatabaseConfigSchema = z.object({
  uri: z.string().url(),
  username: z.string().min(1),
  password: z.string().min(1),
  database: z.string().min(1),
});

const AppConfigSchema = z.object({
  environment: z.enum(['development', 'staging', 'production']),
  database: DatabaseConfigSchema,
  // ... other schemas
});

export class ConfigLoader {
  static load(): AppConfig {
    const config = {
      environment: process.env.NODE_ENV || 'development',
      database: {
        uri: process.env.NEO4J_URI,
        username: process.env.NEO4J_USERNAME,
        password: process.env.NEO4J_PASSWORD,
        database: process.env.NEO4J_DATABASE || 'neo4j',
      },
      // ... load other config sections
    };

    // Validate configuration
    const result = AppConfigSchema.safeParse(config);
    if (!result.success) {
      throw new Error(`Invalid configuration: ${result.error.message}`);
    }

    return result.data;
  }
}
```

## 2. API Service Consolidation

### Current Problems:
- Repeated axios setup in multiple files
- Inconsistent error handling
- No request/response interceptors

### Solution: Unified API Client

```typescript
// shared/api/api-client.ts
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

export interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

export class ApiClient {
  private client: AxiosInstance;

  constructor(config: ApiClientConfig) {
    this.client = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 10000,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error(`API Error: ${error.response?.status} ${error.message}`);
        return Promise.reject(this.normalizeError(error));
      }
    );
  }

  private normalizeError(error: any): ApiError {
    return new ApiError(
      error.response?.data?.message || error.message,
      error.response?.status || 500,
      error.response?.data?.code || 'UNKNOWN_ERROR'
    );
  }

  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const response = await this.client.get(endpoint, { params });
    return response.data;
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response = await this.client.post(endpoint, data);
    return response.data;
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response = await this.client.put(endpoint, data);
    return response.data;
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await this.client.delete(endpoint);
    return response.data;
  }
}

export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public code: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}
```

```typescript
// packages/kg-ui/src/services/analysis-api.ts
import { ApiClient } from '../../../shared/api/api-client';

interface ClusterParams {
  resolution?: number;
  subGraph?: string;
}

interface HiddenLinksParams {
  topN?: number;
  threshold?: number;
}

export class AnalysisApiService {
  private client: ApiClient;

  constructor() {
    this.client = new ApiClient({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3002/api',
    });
  }

  async fetchClusters(params: ClusterParams = {}) {
    return this.client.get('/analysis/clusters', params);
  }

  async fetchHiddenLinks(params: HiddenLinksParams = {}) {
    return this.client.get('/analysis/hidden-links', params);
  }

  async fetchCriticality(params: { topN?: number } = {}) {
    return this.client.get('/analysis/centrality', {
      type: 'pagerank',
      ...params,
    });
  }
}
```

## 3. Component Decomposition (App.jsx)

### Current Problem:
- 906-line monolithic component
- Mixed concerns (routing, state, rendering)
- Hard to test and maintain

### Solution: Component Decomposition

```typescript
// packages/kg-ui/src/components/layout/AppLayout.tsx
import React from 'react';
import { Header } from './Header';
import { MainContent } from './MainContent';
import { NodeDetails } from '../node/NodeDetails';

interface AppLayoutProps {
  currentView: string;
  onViewChange: (view: string) => void;
  selectedNode?: any;
  onNodeSelect: (node: any) => void;
  onCloseDetails: () => void;
}

export const AppLayout: React.FC<AppLayoutProps> = ({
  currentView,
  onViewChange,
  selectedNode,
  onNodeSelect,
  onCloseDetails,
}) => {
  return (
    <div className="app">
      <Header 
        currentView={currentView}
        onSwitchView={onViewChange}
      />
      <div className="app-container">
        <MainContent 
          currentView={currentView}
          onNodeSelect={onNodeSelect}
        />
        {selectedNode && (
          <NodeDetails
            selectedNode={selectedNode}
            onClose={onCloseDetails}
          />
        )}
      </div>
    </div>
  );
};
```

```typescript
// packages/kg-ui/src/components/layout/MainContent.tsx
import React from 'react';
import { ExplorerView } from '../views/ExplorerView';
import { AnalysisView } from '../views/AnalysisView';
import { ChatView } from '../views/ChatView';
import { DocumentationView } from '../views/DocumentationView';

interface MainContentProps {
  currentView: string;
  onNodeSelect: (node: any) => void;
}

export const MainContent: React.FC<MainContentProps> = ({
  currentView,
  onNodeSelect,
}) => {
  const renderView = () => {
    switch (currentView) {
      case 'analysis':
        return <AnalysisView />;
      case 'chat':
        return <ChatView onNodeSelect={onNodeSelect} />;
      case 'documentation':
        return <DocumentationView />;
      default:
        return <ExplorerView onNodeSelect={onNodeSelect} />;
    }
  };

  return (
    <div className="main-content">
      {renderView()}
    </div>
  );
};
```

```typescript
// packages/kg-ui/src/hooks/useAppState.ts
import { useState, useCallback } from 'react';

export const useAppState = () => {
  const [currentView, setCurrentView] = useState('explorer');
  const [selectedNode, setSelectedNode] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleViewChange = useCallback((view: string) => {
    setCurrentView(view);
    setSelectedNode(null); // Clear selection when changing views
  }, []);

  const handleNodeSelect = useCallback((node: any) => {
    setSelectedNode(node);
  }, []);

  const handleCloseDetails = useCallback(() => {
    setSelectedNode(null);
  }, []);

  return {
    currentView,
    selectedNode,
    loading,
    error,
    handleViewChange,
    handleNodeSelect,
    handleCloseDetails,
    setLoading,
    setError,
  };
};
```

```typescript
// packages/kg-ui/src/App.tsx (Refactored)
import React from 'react';
import { ErrorBoundary } from './components/ErrorBoundary';
import { ChatProvider } from './contexts/ChatContext';
import { AppLayout } from './components/layout/AppLayout';
import { useAppState } from './hooks/useAppState';

const App: React.FC = () => {
  const {
    currentView,
    selectedNode,
    handleViewChange,
    handleNodeSelect,
    handleCloseDetails,
  } = useAppState();

  return (
    <ErrorBoundary>
      <ChatProvider>
        <AppLayout
          currentView={currentView}
          onViewChange={handleViewChange}
          selectedNode={selectedNode}
          onNodeSelect={handleNodeSelect}
          onCloseDetails={handleCloseDetails}
        />
      </ChatProvider>
    </ErrorBoundary>
  );
};

export default App;
```

## 4. Standardized Error Handling

### Current Problem:
- Inconsistent error handling across services
- No centralized error logging
- Poor error user experience

### Solution: Unified Error System

```typescript
// shared/errors/app-error.ts
export enum ErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
}

export class AppError extends Error {
  constructor(
    message: string,
    public code: ErrorCode,
    public statusCode: number = 500,
    public details?: any
  ) {
    super(message);
    this.name = 'AppError';
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      details: this.details,
    };
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, ErrorCode.VALIDATION_ERROR, 400, details);
  }
}

export class NetworkError extends AppError {
  constructor(message: string, details?: any) {
    super(message, ErrorCode.NETWORK_ERROR, 503, details);
  }
}
```

```typescript
// shared/errors/error-handler.ts
import { AppError, ErrorCode } from './app-error';

export class ErrorHandler {
  static handle(error: unknown): AppError {
    if (error instanceof AppError) {
      return error;
    }

    if (error instanceof Error) {
      return new AppError(
        error.message,
        ErrorCode.INTERNAL_ERROR,
        500
      );
    }

    return new AppError(
      'An unknown error occurred',
      ErrorCode.INTERNAL_ERROR,
      500
    );
  }

  static async logError(error: AppError, context?: any): Promise<void> {
    const logEntry = {
      timestamp: new Date().toISOString(),
      error: error.toJSON(),
      context,
    };

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error:', logEntry);
    }

    // In production, send to logging service
    // await loggingService.log(logEntry);
  }
}
```

This refactoring plan addresses the most critical issues in your codebase and provides a clear path forward for improving maintainability, consistency, and developer experience.
