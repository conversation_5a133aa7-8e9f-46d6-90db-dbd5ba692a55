/**
 * Jest Configuration for KnowledgeGraphVisualizer
 *
 * Comprehensive testing configuration for unit, integration, and contract tests
 */

export default {
  // Test environment
  testEnvironment: 'node',

  // ES modules support
  preset: 'ts-jest/presets/default-esm',
  extensionsToTreatAsEsm: ['.ts'],
  
  // Root directories for tests
  roots: [
    '<rootDir>/tests',
    '<rootDir>/shared',
    '<rootDir>/360t-kg-api',
    '<rootDir>/360t-kg-ui',
    '<rootDir>/proxy-server'
  ],
  
  // Test file patterns
  testMatch: [
    '**/__tests__/**/*.(js|jsx|ts|tsx)',
    '**/*.(test|spec).(js|jsx|ts|tsx)'
  ],
  
  // File extensions to consider
  moduleFileExtensions: [
    'js',
    'jsx',
    'ts',
    'tsx',
    'json',
    'node'
  ],
  
  // Transform files
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
    '^.+\\.(js|jsx)$': 'babel-jest'
  },
  
  // Module name mapping
  moduleNameMapper: {
    '^@shared/(.*)$': '<rootDir>/shared/$1',
    '^@api/(.*)$': '<rootDir>/360t-kg-api/src/$1',
    '^@ui/(.*)$': '<rootDir>/360t-kg-ui/src/$1',
    '^@proxy/(.*)$': '<rootDir>/proxy-server/src/$1',
    '^@tests/(.*)$': '<rootDir>/tests/$1'
  },
  
  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/tests/setup.js'
  ],
  
  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: '<rootDir>/coverage',
  coverageReporters: [
    'text',
    'lcov',
    'html',
    'json'
  ],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // Files to collect coverage from
  collectCoverageFrom: [
    'shared/**/*.{js,jsx,ts,tsx}',
    '360t-kg-api/src/**/*.{js,jsx,ts,tsx}',
    '360t-kg-ui/src/**/*.{js,jsx,ts,tsx}',
    'proxy-server/src/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/coverage/**',
    '!**/dist/**',
    '!**/build/**'
  ],
  
  // Test suites
  projects: [
    {
      displayName: 'unit',
      testMatch: ['<rootDir>/tests/unit/**/*.(test|spec).(js|jsx|ts|tsx)'],
      testEnvironment: 'node'
    },
    {
      displayName: 'integration',
      testMatch: ['<rootDir>/tests/integration/**/*.(test|spec).(js|jsx|ts|tsx)'],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/tests/integration/setup.js']
    },
    {
      displayName: 'contract',
      testMatch: ['<rootDir>/tests/contract/**/*.(test|spec).(js|jsx|ts|tsx)'],
      testEnvironment: 'node'
    },
    {
      displayName: 'ui',
      testMatch: ['<rootDir>/360t-kg-ui/src/**/*.(test|spec).(js|jsx|ts|tsx)'],
      testEnvironment: 'jsdom',
      setupFilesAfterEnv: ['<rootDir>/tests/ui-setup.js']
    }
  ],
  
  // Global variables
  globals: {
    'ts-jest': {
      useESM: true
    }
  },
  
  // Module directories
  moduleDirectories: [
    'node_modules',
    '<rootDir>/shared',
    '<rootDir>/tests'
  ],
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/',
    '/fastapi_env/',
    '/google-cloud-sdk/'
  ],
  
  // Watch plugins (commented out as not installed)
  // watchPlugins: [
  //   'jest-watch-typeahead/filename',
  //   'jest-watch-typeahead/testname'
  // ],
  
  // Verbose output
  verbose: true,
  
  // Timeout for tests
  testTimeout: 30000,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true,
  
  // Error on deprecated features
  errorOnDeprecated: true
};
