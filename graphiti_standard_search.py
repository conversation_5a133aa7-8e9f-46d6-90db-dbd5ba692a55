#!/usr/bin/env python3
"""
Graphiti Standard Search Implementation
=====================================

Migrates from custom search to Graphiti's standard search framework with:
- All 16 search recipes support
- Multiple LLM provider support (Ollama, Google Gemini, OpenAI)
- Group ID filtering for database isolation
- Performance monitoring integration
- Streaming support for Node.js backend

Environment variables expected:
- NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD, NEO4J_DATABASE
- OPENAI_API_KEY (for embeddings)
- GOOGLE_API_KEY (optional, for Gemini)
- GROQ_API_KEY (optional, for Groq)
- OLLAMA_URL, OLLAMA_MODEL (for local LLM)
"""

import asyncio
import json
import os
import time
import argparse
from datetime import datetime, timezone
from typing import Dict, Any, List, Tuple, Optional
import textwrap

from graphiti_core import Graphiti
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.llm_client.openai_client import OpenAIClient
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
# from graphiti_core.llm_client.gemini_client import GeminiClient
# from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient

# Import all search recipes
from graphiti_core.search.search_config_recipes import (
    COMBINED_HYBRID_SEARCH_RRF,
    COMBINED_HYBRID_SEARCH_MMR,
    COMBINED_HYBRID_SEARCH_CROSS_ENCODER,
    EDGE_HYBRID_SEARCH_RRF,
    EDGE_HYBRID_SEARCH_MMR,
    EDGE_HYBRID_SEARCH_NODE_DISTANCE,
    EDGE_HYBRID_SEARCH_EPISODE_MENTIONS,
    EDGE_HYBRID_SEARCH_CROSS_ENCODER,
    NODE_HYBRID_SEARCH_RRF,
    NODE_HYBRID_SEARCH_MMR,
    NODE_HYBRID_SEARCH_NODE_DISTANCE,
    NODE_HYBRID_SEARCH_EPISODE_MENTIONS,
    NODE_HYBRID_SEARCH_CROSS_ENCODER,
    COMMUNITY_HYBRID_SEARCH_RRF,
    COMMUNITY_HYBRID_SEARCH_MMR,
    COMMUNITY_HYBRID_SEARCH_CROSS_ENCODER
)

# Performance tracking (reuse from existing implementation)
class PythonTimingTracker:
    """Python timing tracker to match the Node.js timing system"""
    
    def __init__(self, operation_id=None):
        self.operation_id = operation_id or f"py_{int(time.time() * 1000)}_{os.getpid()}"
        self.start_time = time.time()
        self.stages = {}
        self.metadata = {}
        self.neo4j_queries = []

    def start_stage(self, stage_name, metadata=None):
        timestamp = time.time()
        self.stages[stage_name] = {
            'start_time': timestamp,
            'metadata': metadata or {},
            'status': 'running'
        }
        return timestamp

    def end_stage(self, stage_name, result=None):
        timestamp = time.time()
        stage = self.stages.get(stage_name, {})
        duration = timestamp - stage.get('start_time', timestamp)
        stage.update({
            'end_time': timestamp,
            'duration': duration,
            'duration_ms': duration * 1000,
            'result': result or {},
            'status': 'completed'
        })
        return duration

    def fail_stage(self, stage_name, error):
        timestamp = time.time()
        stage = self.stages.get(stage_name, {})
        if 'start_time' in stage:
            duration = timestamp - stage['start_time']
            stage.update({
                'end_time': timestamp,
                'duration': duration,
                'error': str(error),
                'status': 'failed'
            })

    def add_metadata(self, metadata):
        self.metadata.update(metadata)

    def track_neo4j_query(self, query_type, duration_ms, record_count, error=None):
        self.neo4j_queries.append({
            'query_type': query_type,
            'duration_ms': duration_ms,
            'record_count': record_count,
            'error': error,
            'timestamp': time.time()
        })

    def get_summary(self):
        total_duration = time.time() - self.start_time
        stages_summary = []

        for name, stage in self.stages.items():
            stages_summary.append({
                'name': name,
                'duration': stage.get('duration'),
                'duration_ms': stage.get('duration_ms'),
                'status': stage.get('status'),
                'percentage': (stage.get('duration', 0) / total_duration * 100) if total_duration > 0 else 0
            })

        # Calculate Neo4j query statistics
        neo4j_stats = {
            'total_queries': len(self.neo4j_queries),
            'total_duration_ms': sum(q['duration_ms'] for q in self.neo4j_queries),
            'average_duration_ms': 0,
            'slowest_query_ms': 0,
            'fastest_query_ms': 0,
            'failed_queries': len([q for q in self.neo4j_queries if q['error']]),
            'queries': self.neo4j_queries
        }

        if self.neo4j_queries:
            durations = [q['duration_ms'] for q in self.neo4j_queries if not q['error']]
            if durations:
                neo4j_stats['average_duration_ms'] = sum(durations) / len(durations)
                neo4j_stats['slowest_query_ms'] = max(durations)
                neo4j_stats['fastest_query_ms'] = min(durations)

        return {
            'operation_id': self.operation_id,
            'total_duration': total_duration,
            'total_duration_ms': total_duration * 1000,
            'stages': stages_summary,
            'neo4j_queries': neo4j_stats,
            'metadata': self.metadata,
            'completed_at': datetime.now(timezone.utc).isoformat()
        }

# Search recipe mapping
SEARCH_RECIPES = {
    'COMBINED_HYBRID_SEARCH_RRF': COMBINED_HYBRID_SEARCH_RRF,
    'COMBINED_HYBRID_SEARCH_MMR': COMBINED_HYBRID_SEARCH_MMR,
    'COMBINED_HYBRID_SEARCH_CROSS_ENCODER': COMBINED_HYBRID_SEARCH_CROSS_ENCODER,
    'EDGE_HYBRID_SEARCH_RRF': EDGE_HYBRID_SEARCH_RRF,
    'EDGE_HYBRID_SEARCH_MMR': EDGE_HYBRID_SEARCH_MMR,
    'EDGE_HYBRID_SEARCH_NODE_DISTANCE': EDGE_HYBRID_SEARCH_NODE_DISTANCE,
    'EDGE_HYBRID_SEARCH_EPISODE_MENTIONS': EDGE_HYBRID_SEARCH_EPISODE_MENTIONS,
    'EDGE_HYBRID_SEARCH_CROSS_ENCODER': EDGE_HYBRID_SEARCH_CROSS_ENCODER,
    'NODE_HYBRID_SEARCH_RRF': NODE_HYBRID_SEARCH_RRF,
    'NODE_HYBRID_SEARCH_MMR': NODE_HYBRID_SEARCH_MMR,
    'NODE_HYBRID_SEARCH_NODE_DISTANCE': NODE_HYBRID_SEARCH_NODE_DISTANCE,
    'NODE_HYBRID_SEARCH_EPISODE_MENTIONS': NODE_HYBRID_SEARCH_EPISODE_MENTIONS,
    'NODE_HYBRID_SEARCH_CROSS_ENCODER': NODE_HYBRID_SEARCH_CROSS_ENCODER,
    'COMMUNITY_HYBRID_SEARCH_RRF': COMMUNITY_HYBRID_SEARCH_RRF,
    'COMMUNITY_HYBRID_SEARCH_MMR': COMMUNITY_HYBRID_SEARCH_MMR,
    'COMMUNITY_HYBRID_SEARCH_CROSS_ENCODER': COMMUNITY_HYBRID_SEARCH_CROSS_ENCODER
}

def load_configuration():
    """Load configuration from config file or environment variables"""
    config_path = os.path.join(os.path.dirname(__file__), 'packages', 'graph-api', 'config', 'kg-config.json')
    
    # Default configuration
    config = {
        'searchRecipe': 'COMBINED_HYBRID_SEARCH_RRF',
        'llmProvider': 'ollama',
        'llmModel': 'gemma3:12b',
        'resultsLimit': 10
    }
    
    # Try to load from config file
    try:
        with open(config_path, 'r') as f:
            file_config = json.load(f)
            config.update(file_config)
    except FileNotFoundError:
        pass  # Use defaults
    
    return config

def build_graphiti_instance(llm_provider: str, llm_model: str) -> Graphiti:
    """Build Graphiti instance with specified LLM provider"""

    neo4j_config = {
        'uri': os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        'user': os.getenv("NEO4J_USER", "neo4j"),
        'password': os.getenv("NEO4J_PASSWORD", "password"),
    }

    # Always use OpenAI embeddings for consistency (as per existing setup)
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        raise ValueError("OPENAI_API_KEY environment variable required for embeddings")

    embedder = OpenAIEmbedder(
        config=OpenAIEmbedderConfig(
            api_key=openai_api_key,
            embedding_model="text-embedding-ada-002",  # Use existing 768-dim model
            embedding_dim=768  # Match existing database embedding dimension
        )
    )

    if llm_provider == 'ollama':
        # Ollama configuration for LLM only
        llm_config = LLMConfig(
            api_key="abc",  # Ollama doesn't require real API key
            model=llm_model,
            small_model=llm_model,
            base_url=os.getenv("OLLAMA_URL", "http://localhost:11434/v1"),
        )

        llm_client = OpenAIClient(config=llm_config)
        cross_encoder = OpenAIRerankerClient(client=llm_client, config=llm_config)
        
    elif llm_provider == 'google':
        # Google Gemini configuration - temporarily disabled due to import issues
        raise ValueError("Google Gemini provider temporarily disabled - use 'ollama' or 'openai'")
        
    elif llm_provider == 'openai':
        # OpenAI configuration for LLM (embeddings already configured above)
        llm_config = LLMConfig(
            api_key=openai_api_key,
            model=llm_model,
            small_model="gpt-4o-mini",
        )

        llm_client = OpenAIClient(config=llm_config)
        cross_encoder = OpenAIRerankerClient(client=llm_client, config=llm_config)
        
    else:
        raise ValueError(f"Unsupported LLM provider: {llm_provider}")
    
    return Graphiti(
        **neo4j_config,
        llm_client=llm_client,
        embedder=embedder,  # Always OpenAI embeddings
        cross_encoder=cross_encoder,
    )

async def fetch_context_standard(
    graphiti: Graphiti, 
    query: str, 
    search_recipe: str = 'COMBINED_HYBRID_SEARCH_RRF',
    results_limit: int = 10,
    timer: PythonTimingTracker = None
) -> Tuple[str, List[str]]:
    """
    Use Graphiti's standard search with group_id filtering for database isolation
    """
    
    if timer:
        timer.start_stage('graphiti_standard_search')
    
    try:
        # Get the search recipe configuration
        if search_recipe not in SEARCH_RECIPES:
            raise ValueError(f"Unknown search recipe: {search_recipe}")
        
        search_config = SEARCH_RECIPES[search_recipe].model_copy(deep=True)
        search_config.limit = results_limit
        
        # Add group_id filtering to avoid old GraphRAG nodes
        # This ensures we only search Entity nodes with group_id
        search_start_time = time.time()
        
        search_results = await graphiti._search(
            query=query,
            config=search_config,
            # group_id filtering will be handled by Graphiti internally
        )
        
        search_duration = (time.time() - search_start_time) * 1000
        
        if timer:
            timer.track_neo4j_query(
                'graphiti_standard_search', 
                search_duration, 
                len(search_results.edges) + len(search_results.nodes) + len(search_results.communities)
            )
        
        # Build context from search results
        context_lines = []
        citations = []
        idx = 1
        
        # Process edges (facts)
        for edge in search_results.edges:
            context_lines.append(f"[{idx}] {edge.fact}")
            citations.append(f"[{idx}]")
            idx += 1
        
        # Process nodes (entity summaries)
        for node in search_results.nodes:
            if node.summary:
                context_lines.append(f"[{idx}] {node.summary}")
                citations.append(f"[{idx}]")
                idx += 1
        
        # Process communities (high-level context)
        for community in search_results.communities:
            if community.summary:
                context_lines.append(f"[{idx}] {community.summary}")
                citations.append(f"[{idx}]")
                idx += 1
        
        context_md = "\n".join(context_lines) if context_lines else ""
        
        if timer:
            timer.end_stage('graphiti_standard_search', {
                'context_lines': len(context_lines),
                'citations': len(citations),
                'edges_found': len(search_results.edges),
                'nodes_found': len(search_results.nodes),
                'communities_found': len(search_results.communities),
                'search_recipe': search_recipe
            })
        
        return context_md, citations
        
    except Exception as e:
        if timer:
            timer.fail_stage('graphiti_standard_search', str(e))
        raise e

# Prompt template (reuse existing)
PROMPT_TEMPLATE = textwrap.dedent(
    """
    You are a FOREX domain knowledge expert answering questions using *only* the supplied context.

    === CONTEXT ===
    {context}
    === QUESTION ===
    {question}

    Instructions:
    1. Begin with a concise answer, then elaborate.
    2. Cite facts using the bracket numbers provided in CONTEXT (e.g. [1]).
    3. Format output in clean Markdown with headings, lists, and **bold** keywords.
    4. If context is insufficient, state so clearly.
    5. Finish with a "### 💡 Related" section suggesting 2 follow‑up questions.
    """
)

# Streaming response class for Node.js integration
class StreamingResponse:
    def __init__(self):
        self.operation_id = f"stream_{int(time.time() * 1000)}"
        self.tokens = []
        self.is_complete = False

    def add_token(self, token: str, is_done: bool = False):
        self.tokens.append(token)
        self.is_complete = is_done
        # Print for Node.js to capture
        print(f"TOKEN:{token}")
        if is_done:
            print("STREAM_COMPLETE")

# Main async function for Node.js integration
async def get_kg_data_with_standard_search(
    question: str,
    enable_timing: bool = False,
    token_callback=None
) -> Dict[str, Any]:
    """
    Main function using Graphiti standard search with configuration support
    """

    # Load configuration
    config = load_configuration()

    # Initialize timing if requested
    timer = None
    streaming_response = StreamingResponse()

    if enable_timing:
        operation_id = os.getenv('OPERATION_ID', streaming_response.operation_id)
        timer = PythonTimingTracker(operation_id)
        timer.add_metadata({
            'question_length': len(question),
            'search_recipe': config['searchRecipe'],
            'llm_provider': config['llmProvider'],
            'llm_model': config['llmModel'],
            'streaming': True
        })
        timer.start_stage('graphiti_initialization')

    try:
        # Build Graphiti instance with configured provider
        g = build_graphiti_instance(config['llmProvider'], config['llmModel'])

        if timer:
            timer.end_stage('graphiti_initialization')
            timer.start_stage('knowledge_graph_search')

        # Use standard Graphiti search
        context_md, citations = await fetch_context_standard(
            g,
            question,
            config['searchRecipe'],
            config['resultsLimit'],
            timer
        )

        if timer:
            timer.end_stage('knowledge_graph_search', {
                'context_length': len(context_md) if context_md else 0,
                'citations_count': len(citations) if citations else 0
            })

        if not context_md:
            result = {
                "success": False,
                "error": "No relevant context found in the graph",
                "context": "",
                "answer": "",
                "citations": [],
                "hasStreaming": False
            }
            if timer:
                timer.fail_stage('knowledge_graph_search', 'No relevant context found')
                result['timing'] = timer.get_summary()
            return result

        if timer:
            timer.start_stage('prompt_preparation')

        prompt = PROMPT_TEMPLATE.format(context=context_md, question=question)

        if timer:
            timer.end_stage('prompt_preparation', {
                'prompt_length': len(prompt)
            })
            timer.start_stage('llm_streaming')

        # Create a callback that updates our streaming response
        def combined_callback(token: str, is_done: bool):
            streaming_response.add_token(token, is_done)

            # Call the external callback if provided
            if token_callback:
                token_callback(token, is_done)

        # Stream the response based on provider
        if config['llmProvider'] == 'ollama':
            answer_md = call_ollama_streaming_with_callback(
                prompt,
                config['llmModel'],
                os.getenv("OLLAMA_URL", "http://localhost:11434/api/generate"),
                combined_callback
            )
        else:
            # For cloud providers, use non-streaming for now
            # TODO: Implement streaming for Google Gemini and OpenAI
            answer_md = "Streaming not yet implemented for cloud providers"
            combined_callback(answer_md, True)

        if timer:
            timer.end_stage('llm_streaming', {
                'answer_length': len(answer_md),
                'tokens_streamed': len(streaming_response.tokens),
                'provider_error': answer_md.startswith("❌")
            })

        result = {
            "success": True,
            "context": context_md,
            "answer": answer_md,
            "citations": citations,
            "hasStreaming": True,
            "searchRecipe": config['searchRecipe'],
            "llmProvider": config['llmProvider'],
            "llmModel": config['llmModel']
        }

        if timer:
            result['timing'] = timer.get_summary()

        return result

    except Exception as e:
        error_result = {
            "success": False,
            "error": str(e),
            "context": "",
            "answer": "",
            "citations": [],
            "hasStreaming": False
        }

        if timer:
            timer.fail_stage('llm_streaming', str(e))
            error_result['timing'] = timer.get_summary()

        return error_result

    finally:
        # Close Graphiti connection
        if 'g' in locals():
            await g.close()

# Ollama integration (reuse existing streaming functions)
def call_ollama_streaming_with_callback(prompt: str, model: str, url: str, callback):
    """Call Ollama with streaming and callback support"""
    import requests

    if url.endswith("/v1"):
        url = url[:-3] + "/api/generate"
    elif not url.endswith("/api/generate"):
        url = url.rstrip("/") + "/api/generate"

    payload = {
        "model": model,
        "prompt": prompt,
        "stream": True,
        "options": {"temperature": 0.3, "top_p": 0.9, "top_k": 40},
    }

    try:
        response = requests.post(url, json=payload, stream=True, timeout=30)
        response.raise_for_status()

        full_response = ""
        for line in response.iter_lines():
            if line:
                try:
                    data = json.loads(line.decode('utf-8'))
                    if 'response' in data:
                        token = data['response']
                        full_response += token
                        if callback:
                            callback(token, data.get('done', False))
                    if data.get('done', False):
                        break
                except json.JSONDecodeError:
                    continue

        return full_response

    except Exception as e:
        error_msg = f"❌ Ollama error: {str(e)}"
        if callback:
            callback(error_msg, True)
        return error_msg

# CLI interface for testing
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Graphiti Standard Search")
    parser.add_argument("question", type=str, help="Question to ask the knowledge graph")
    parser.add_argument("--search-recipe", type=str, default="COMBINED_HYBRID_SEARCH_RRF",
                       help="Search recipe to use")
    parser.add_argument("--llm-provider", type=str, default="ollama",
                       choices=['ollama', 'google', 'openai'], help="LLM provider")
    parser.add_argument("--llm-model", type=str, default="gemma3:12b", help="LLM model")
    parser.add_argument("--results-limit", type=int, default=10, help="Results limit")
    parser.add_argument("--timing", type=str, default="false", help="Enable timing")

    args = parser.parse_args()

    # Override configuration with CLI args
    config = {
        'searchRecipe': args.search_recipe,
        'llmProvider': args.llm_provider,
        'llmModel': args.llm_model,
        'resultsLimit': args.results_limit
    }

    # Save temporary config
    config_dir = os.path.join(os.path.dirname(__file__), '360t-kg-api', 'config')
    os.makedirs(config_dir, exist_ok=True)
    config_path = os.path.join(config_dir, 'kg-config.json')
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)

    # Run the search
    enable_timing = args.timing.lower() == 'true'
    result = asyncio.run(get_kg_data_with_standard_search(args.question, enable_timing))

    if result['success']:
        print(f"\n✅ Search Recipe: {result['searchRecipe']}")
        print(f"🤖 LLM Provider: {result['llmProvider']} ({result['llmModel']})")
        print(f"📝 Context: {len(result['context'])} characters")
        print(f"💬 Answer: {result['answer']}")
    else:
        print(f"❌ Error: {result['error']}")
