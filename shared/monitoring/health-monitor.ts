/**
 * Health Monitoring System
 * 
 * Provides comprehensive health monitoring for all services
 * with automated alerts and validation endpoints.
 */

export interface HealthReport {
  timestamp: string;
  overall: boolean;
  services: {
    database: boolean;
    api: boolean;
    llm: boolean;
    frontend: boolean;
    proxy?: boolean;
  };
  details: {
    database?: HealthDetail;
    api?: HealthDetail;
    llm?: HealthDetail;
    frontend?: HealthDetail;
    proxy?: HealthDetail;
  };
  responseTime: number;
}

export interface HealthDetail {
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  error?: string;
  metadata?: Record<string, any>;
}

export class HealthMonitor {
  private static readonly TIMEOUT = 5000; // 5 seconds
  private static readonly ENDPOINTS = {
    database: 'http://localhost:7474/db/neo4j/tx/commit',
    api: 'http://localhost:3002/api/health',
    frontend: 'http://localhost:5173',
    proxy: 'http://localhost:3001/health'
  };

  /**
   * Check health of all services
   */
  async checkAllServices(): Promise<HealthReport> {
    const startTime = Date.now();
    
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkAPI(),
      this.checkLLM(),
      this.checkFrontend(),
      this.checkProxy()
    ]);

    const services = {
      database: checks[0].status === 'fulfilled' ? checks[0].value.status === 'healthy' : false,
      api: checks[1].status === 'fulfilled' ? checks[1].value.status === 'healthy' : false,
      llm: checks[2].status === 'fulfilled' ? checks[2].value.status === 'healthy' : false,
      frontend: checks[3].status === 'fulfilled' ? checks[3].value.status === 'healthy' : false,
      proxy: checks[4].status === 'fulfilled' ? checks[4].value.status === 'healthy' : false
    };

    const details = {
      database: checks[0].status === 'fulfilled' ? checks[0].value : undefined,
      api: checks[1].status === 'fulfilled' ? checks[1].value : undefined,
      llm: checks[2].status === 'fulfilled' ? checks[2].value : undefined,
      frontend: checks[3].status === 'fulfilled' ? checks[3].value : undefined,
      proxy: checks[4].status === 'fulfilled' ? checks[4].value : undefined
    };

    return {
      timestamp: new Date().toISOString(),
      overall: Object.values(services).every(Boolean),
      services,
      details,
      responseTime: Date.now() - startTime
    };
  }

  /**
   * Check database health (Neo4j)
   */
  private async checkDatabase(): Promise<HealthDetail> {
    const startTime = Date.now();
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT);

      const response = await fetch(this.ENDPOINTS.database, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          statements: [{ statement: 'RETURN 1 as health_check' }] 
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        return {
          status: 'healthy',
          responseTime: Date.now() - startTime,
          metadata: {
            neo4j_version: data.neo4j_version || 'unknown',
            database: 'neo4j'
          }
        };
      } else {
        return {
          status: 'unhealthy',
          responseTime: Date.now() - startTime,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error: any) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error.message || 'Database connection failed'
      };
    }
  }

  /**
   * Check API service health
   */
  private async checkAPI(): Promise<HealthDetail> {
    const startTime = Date.now();
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT);

      const response = await fetch(this.ENDPOINTS.api, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        return {
          status: 'healthy',
          responseTime: Date.now() - startTime,
          metadata: data
        };
      } else {
        return {
          status: 'unhealthy',
          responseTime: Date.now() - startTime,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error: any) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error.message || 'API service connection failed'
      };
    }
  }

  /**
   * Check LLM providers health
   */
  private async checkLLM(): Promise<HealthDetail> {
    const startTime = Date.now();
    
    try {
      // Check if any LLM provider is configured
      const providers = {
        ollama: process.env.LLM_OLLAMA_BASE_URL,
        anthropic: process.env.LLM_ANTHROPIC_API_KEY,
        google: process.env.LLM_GOOGLE_API_KEY,
        azure: process.env.LLM_AZURE_API_KEY
      };

      const configuredProviders = Object.entries(providers)
        .filter(([_, value]) => !!value)
        .map(([key, _]) => key);

      if (configuredProviders.length === 0) {
        return {
          status: 'degraded',
          responseTime: Date.now() - startTime,
          error: 'No LLM providers configured',
          metadata: { configuredProviders: [] }
        };
      }

      // For now, just check if providers are configured
      // In a real implementation, you'd ping each provider
      return {
        status: 'healthy',
        responseTime: Date.now() - startTime,
        metadata: { 
          configuredProviders,
          primaryProvider: process.env.LLM_PRIMARY_PROVIDER || 'ollama'
        }
      };
    } catch (error: any) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error.message || 'LLM provider check failed'
      };
    }
  }

  /**
   * Check frontend health
   */
  private async checkFrontend(): Promise<HealthDetail> {
    const startTime = Date.now();
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT);

      const response = await fetch(this.ENDPOINTS.frontend, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        return {
          status: 'healthy',
          responseTime: Date.now() - startTime,
          metadata: {
            contentType: response.headers.get('content-type'),
            server: 'vite-dev-server'
          }
        };
      } else {
        return {
          status: 'unhealthy',
          responseTime: Date.now() - startTime,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error: any) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error.message || 'Frontend service connection failed'
      };
    }
  }

  /**
   * Check proxy service health
   */
  private async checkProxy(): Promise<HealthDetail> {
    const startTime = Date.now();
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT);

      const response = await fetch(this.ENDPOINTS.proxy, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        return {
          status: 'healthy',
          responseTime: Date.now() - startTime,
          metadata: data
        };
      } else {
        return {
          status: 'unhealthy',
          responseTime: Date.now() - startTime,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error: any) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error.message || 'Proxy service connection failed'
      };
    }
  }

  /**
   * Generate health summary
   */
  static generateHealthSummary(report: HealthReport): string {
    const { services, overall, responseTime } = report;
    
    let summary = `🏥 Health Check Report (${new Date(report.timestamp).toLocaleString()})\n`;
    summary += `Overall Status: ${overall ? '✅ Healthy' : '❌ Unhealthy'}\n`;
    summary += `Response Time: ${responseTime}ms\n\n`;
    
    summary += 'Service Status:\n';
    Object.entries(services).forEach(([service, healthy]) => {
      const icon = healthy ? '✅' : '❌';
      const detail = report.details[service as keyof typeof report.details];
      const responseTime = detail?.responseTime || 0;
      summary += `  ${icon} ${service}: ${healthy ? 'Healthy' : 'Unhealthy'} (${responseTime}ms)\n`;
      
      if (!healthy && detail?.error) {
        summary += `    Error: ${detail.error}\n`;
      }
    });
    
    return summary;
  }
}
