/**
 * React Hook for Feature Flags
 * Provides frontend access to feature flags with real-time updates
 */

import { useState, useEffect, useContext, createContext } from 'react';

// Feature Flags Context
const FeatureFlagsContext = createContext();

/**
 * Feature Flags Provider Component
 */
export function FeatureFlagsProvider({ children, apiUrl = '/api' }) {
    const [flags, setFlags] = useState({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [migrationPhase, setMigrationPhase] = useState(null);

    // Fetch feature flags from backend
    const fetchFlags = async () => {
        try {
            setLoading(true);
            const response = await fetch(`${apiUrl}/feature-flags`);
            
            if (!response.ok) {
                throw new Error(`Failed to fetch feature flags: ${response.status}`);
            }
            
            const data = await response.json();
            setFlags(data.flags || {});
            setMigrationPhase(data.migrationPhase || null);
            setError(null);
        } catch (err) {
            console.error('Error fetching feature flags:', err);
            setError(err.message);
            
            // Fallback to environment variables or defaults
            setFlags(getClientSideFlags());
        } finally {
            setLoading(false);
        }
    };

    // Get client-side feature flags from environment
    const getClientSideFlags = () => {
        return {
            NEW_FEATURE_SLICE_ARCHITECTURE: process.env.REACT_APP_FEATURE_FLAG_NEW_FEATURE_SLICE_ARCHITECTURE === 'true',
            NEW_GRAPH_COMPONENTS: process.env.REACT_APP_FEATURE_FLAG_NEW_GRAPH_COMPONENTS === 'true',
            NEW_CHAT_INTERFACE: process.env.REACT_APP_FEATURE_FLAG_NEW_CHAT_INTERFACE === 'true',
            NEW_STATE_MANAGEMENT: process.env.REACT_APP_FEATURE_FLAG_NEW_STATE_MANAGEMENT === 'true',
            DEBUG_MODE: process.env.REACT_APP_FEATURE_FLAG_DEBUG_MODE === 'true',
            VERBOSE_LOGGING: process.env.REACT_APP_FEATURE_FLAG_VERBOSE_LOGGING === 'true'
        };
    };

    // Fetch flags on mount and set up polling
    useEffect(() => {
        fetchFlags();
        
        // Poll for updates every 30 seconds
        const interval = setInterval(fetchFlags, 30000);
        
        return () => clearInterval(interval);
    }, [apiUrl]);

    const contextValue = {
        flags,
        loading,
        error,
        migrationPhase,
        refetch: fetchFlags,
        isEnabled: (flagName) => Boolean(flags[flagName]),
        shouldUseNewFeature: (flagName, userId = null) => {
            // Simple client-side traffic splitting
            const percentage = flags[`TRAFFIC_PERCENTAGE_${flagName}`] || 0;
            if (percentage === 0) return false;
            if (percentage === 100) return true;
            
            // Use localStorage for consistent user experience
            const storageKey = `feature_flag_${flagName}_${userId || 'anonymous'}`;
            let shouldUse = localStorage.getItem(storageKey);
            
            if (shouldUse === null) {
                shouldUse = Math.random() * 100 < percentage;
                localStorage.setItem(storageKey, shouldUse.toString());
            } else {
                shouldUse = shouldUse === 'true';
            }
            
            return shouldUse;
        }
    };

    return (
        <FeatureFlagsContext.Provider value={contextValue}>
            {children}
        </FeatureFlagsContext.Provider>
    );
}

/**
 * Hook to use feature flags
 */
export function useFeatureFlags() {
    const context = useContext(FeatureFlagsContext);
    
    if (!context) {
        throw new Error('useFeatureFlags must be used within a FeatureFlagsProvider');
    }
    
    return context;
}

/**
 * Hook for specific feature flag
 */
export function useFeatureFlag(flagName) {
    const { isEnabled, loading, error } = useFeatureFlags();
    
    return {
        enabled: isEnabled(flagName),
        loading,
        error
    };
}

/**
 * Component wrapper for feature-gated content
 */
export function FeatureGate({ flag, fallback = null, children }) {
    const { isEnabled, loading } = useFeatureFlags();
    
    if (loading) {
        return fallback;
    }
    
    return isEnabled(flag) ? children : fallback;
}

/**
 * Component for A/B testing between old and new implementations
 */
export function MigrationGate({ 
    flag, 
    legacyComponent: LegacyComponent, 
    newComponent: NewComponent,
    userId = null 
}) {
    const { shouldUseNewFeature, loading } = useFeatureFlags();
    
    if (loading) {
        return <LegacyComponent />;
    }
    
    const useNew = shouldUseNewFeature(flag, userId);
    
    return useNew ? <NewComponent /> : <LegacyComponent />;
}

/**
 * Hook for migration phase information
 */
export function useMigrationPhase() {
    const { migrationPhase, loading, error } = useFeatureFlags();
    
    return {
        phase: migrationPhase,
        loading,
        error,
        isBackendMigrated: migrationPhase?.backend?.percentage === 100,
        isFrontendMigrated: migrationPhase?.frontend?.percentage === 100,
        isFullyMigrated: migrationPhase?.backend?.percentage === 100 && 
                         migrationPhase?.frontend?.percentage === 100
    };
}

/**
 * Debug component to show current feature flags
 */
export function FeatureFlagsDebug() {
    const { flags, migrationPhase, loading, error } = useFeatureFlags();
    
    if (!flags.DEBUG_MODE) {
        return null;
    }
    
    return (
        <div style={{
            position: 'fixed',
            top: 10,
            right: 10,
            background: 'rgba(0,0,0,0.8)',
            color: 'white',
            padding: '10px',
            borderRadius: '5px',
            fontSize: '12px',
            zIndex: 9999,
            maxWidth: '300px'
        }}>
            <h4>Feature Flags Debug</h4>
            {loading && <p>Loading...</p>}
            {error && <p style={{color: 'red'}}>Error: {error}</p>}
            
            <h5>Flags:</h5>
            <pre>{JSON.stringify(flags, null, 2)}</pre>
            
            {migrationPhase && (
                <>
                    <h5>Migration Phase:</h5>
                    <pre>{JSON.stringify(migrationPhase, null, 2)}</pre>
                </>
            )}
        </div>
    );
}
