/**
 * Feature Flag Middleware for Express.js
 * Adds feature flag context to requests and enables gradual rollout
 */

const featureFlags = require('../config/feature-flags');

/**
 * Middleware to add feature flag context to requests
 */
function featureFlagMiddleware(req, res, next) {
    // Add feature flag helper to request
    req.featureFlags = {
        isEnabled: (flagName) => featureFlags.isEnabled(flagName),
        shouldUseNewFeature: (flagName, userId) => {
            const userIdToUse = userId || req.headers['x-user-id'] || req.ip;
            return featureFlags.shouldUseNewFeature(flagName, userIdToUse);
        },
        getTrafficPercentage: (flagName) => featureFlags.getTrafficPercentage(flagName),
        getMigrationPhase: () => featureFlags.getMigrationPhase()
    };
    
    // Add migration context headers to response
    res.setHeader('X-Migration-Phase', JSON.stringify(featureFlags.getMigrationPhase()));
    res.setHeader('X-Feature-Flags-Version', '1.0.0');
    
    next();
}

/**
 * Route-specific feature flag guard
 */
function requireFeatureFlag(flagName) {
    return (req, res, next) => {
        if (!req.featureFlags.isEnabled(flagName)) {
            return res.status(404).json({
                error: 'Feature not available',
                flag: flagName,
                enabled: false
            });
        }
        next();
    };
}

/**
 * Traffic splitting middleware
 */
function trafficSplitter(flagName, legacyHandler, newHandler) {
    return (req, res, next) => {
        const userId = req.headers['x-user-id'] || req.ip;
        const shouldUseNew = featureFlags.shouldUseNewFeature(flagName, userId);
        
        // Add routing info to request
        req.routingInfo = {
            flag: flagName,
            useNew: shouldUseNew,
            trafficPercentage: featureFlags.getTrafficPercentage(flagName)
        };
        
        // Route to appropriate handler
        if (shouldUseNew && newHandler) {
            return newHandler(req, res, next);
        } else if (legacyHandler) {
            return legacyHandler(req, res, next);
        } else {
            next();
        }
    };
}

/**
 * Dual execution middleware for validation
 */
function dualExecutionMiddleware(flagName, legacyHandler, newHandler) {
    return async (req, res, next) => {
        if (!featureFlags.isEnabled('DUAL_EXECUTION_MODE')) {
            // If dual execution is disabled, use traffic splitter
            return trafficSplitter(flagName, legacyHandler, newHandler)(req, res, next);
        }
        
        const startTime = Date.now();
        let legacyResult, newResult, legacyError, newError;
        
        try {
            // Execute both handlers in parallel
            const [legacyResponse, newResponse] = await Promise.allSettled([
                executeHandler(legacyHandler, req, res),
                executeHandler(newHandler, req, res)
            ]);
            
            legacyResult = legacyResponse.status === 'fulfilled' ? legacyResponse.value : null;
            legacyError = legacyResponse.status === 'rejected' ? legacyResponse.reason : null;
            
            newResult = newResponse.status === 'fulfilled' ? newResponse.value : null;
            newError = newResponse.status === 'rejected' ? newResponse.reason : null;
            
            // Log comparison results
            const executionTime = Date.now() - startTime;
            logDualExecutionResults(flagName, {
                legacyResult,
                newResult,
                legacyError,
                newError,
                executionTime,
                requestPath: req.path
            });
            
            // Decide which result to return
            const userId = req.headers['x-user-id'] || req.ip;
            const shouldUseNew = featureFlags.shouldUseNewFeature(flagName, userId);
            
            if (shouldUseNew && newResult && !newError) {
                return res.json(newResult);
            } else if (legacyResult && !legacyError) {
                return res.json(legacyResult);
            } else {
                throw legacyError || newError || new Error('Both handlers failed');
            }
            
        } catch (error) {
            console.error('Dual execution failed:', error);
            next(error);
        }
    };
}

/**
 * Execute handler and capture result
 */
async function executeHandler(handler, req, res) {
    return new Promise((resolve, reject) => {
        // Create mock response to capture result
        const mockRes = {
            json: (data) => resolve(data),
            status: (code) => mockRes,
            send: (data) => resolve(data),
            end: () => resolve(null)
        };
        
        try {
            handler(req, mockRes, (error) => {
                if (error) reject(error);
                else resolve(null);
            });
        } catch (error) {
            reject(error);
        }
    });
}

/**
 * Log dual execution results for analysis
 */
function logDualExecutionResults(flagName, results) {
    const logEntry = {
        timestamp: new Date().toISOString(),
        flag: flagName,
        path: results.requestPath,
        executionTime: results.executionTime,
        legacySuccess: !results.legacyError,
        newSuccess: !results.newError,
        resultsMatch: compareResults(results.legacyResult, results.newResult)
    };
    
    if (featureFlags.isEnabled('VERBOSE_LOGGING')) {
        console.log('Dual Execution Results:', JSON.stringify(logEntry, null, 2));
    }
    
    // Store metrics for monitoring
    if (featureFlags.isEnabled('MIGRATION_METRICS')) {
        // This would integrate with your metrics system
        recordMigrationMetric(logEntry);
    }
}

/**
 * Compare results for validation
 */
function compareResults(legacy, modern) {
    try {
        return JSON.stringify(legacy) === JSON.stringify(modern);
    } catch {
        return false;
    }
}

/**
 * Record migration metrics (placeholder)
 */
function recordMigrationMetric(logEntry) {
    // This would integrate with Prometheus or your metrics system
    // For now, just log to console if debug mode is enabled
    if (featureFlags.isEnabled('DEBUG_MODE')) {
        console.log('Migration Metric:', logEntry);
    }
}

module.exports = {
    featureFlagMiddleware,
    requireFeatureFlag,
    trafficSplitter,
    dualExecutionMiddleware
};
