/**
 * API Request/Response Interceptors
 * 
 * Unified interceptors for logging, authentication, error handling, and monitoring
 */

import { ApiError, ApiErrorCode } from './errors';

export interface RequestInterceptor {
  name: string;
  priority: number;
  onRequest(config: RequestConfig): Promise<RequestConfig> | RequestConfig;
  onError?(error: Error): Promise<Error> | Error;
}

export interface ResponseInterceptor {
  name: string;
  priority: number;
  onResponse(response: Response, data: any): Promise<{ response: Response; data: any }> | { response: Response; data: any };
  onError?(error: ApiError): Promise<ApiError> | ApiError;
}

export interface RequestConfig {
  url: string;
  method: string;
  headers: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
  metadata?: Record<string, any>;
}

/**
 * Logging Interceptor - logs all requests and responses
 */
export class LoggingInterceptor implements RequestInterceptor, ResponseInterceptor {
  name = 'logging';
  priority = 100;

  private startTimes = new Map<string, number>();

  onRequest(config: RequestConfig): RequestConfig {
    const requestId = this.generateRequestId();
    const startTime = Date.now();
    
    this.startTimes.set(requestId, startTime);
    
    console.log(`🔌 API Request [${requestId}]: ${config.method.toUpperCase()} ${config.url}`, {
      headers: this.sanitizeHeaders(config.headers),
      body: config.body ? this.sanitizeBody(config.body) : undefined,
      timestamp: new Date().toISOString()
    });

    return {
      ...config,
      metadata: {
        ...config.metadata,
        requestId,
        startTime
      }
    };
  }

  onResponse(response: Response, data: any): { response: Response; data: any } {
    const requestId = response.headers.get('x-request-id') || 'unknown';
    const startTime = this.startTimes.get(requestId) || Date.now();
    const duration = Date.now() - startTime;
    
    this.startTimes.delete(requestId);

    if (response.ok) {
      console.log(`✅ API Response [${requestId}]: ${response.status} ${response.statusText}`, {
        duration: `${duration}ms`,
        size: this.getResponseSize(data),
        timestamp: new Date().toISOString()
      });
    } else {
      console.warn(`⚠️  API Response [${requestId}]: ${response.status} ${response.statusText}`, {
        duration: `${duration}ms`,
        error: data,
        timestamp: new Date().toISOString()
      });
    }

    return { response, data };
  }

  onError(error: ApiError): ApiError {
    const requestId = error.requestId || 'unknown';
    const startTime = this.startTimes.get(requestId) || Date.now();
    const duration = Date.now() - startTime;
    
    this.startTimes.delete(requestId);

    console.error(`❌ API Error [${requestId}]:`, {
      code: error.code,
      message: error.message,
      statusCode: error.statusCode,
      duration: `${duration}ms`,
      timestamp: error.timestamp
    });

    return error;
  }

  private generateRequestId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private sanitizeHeaders(headers: Record<string, string>): Record<string, string> {
    const sanitized = { ...headers };
    const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
    
    for (const header of sensitiveHeaders) {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }

  private sanitizeBody(body: any): any {
    if (typeof body === 'string') {
      try {
        const parsed = JSON.parse(body);
        return this.sanitizeObject(parsed);
      } catch {
        return '[BINARY_DATA]';
      }
    }
    
    return this.sanitizeObject(body);
  }

  private sanitizeObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    const sanitized = { ...obj };
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'apiKey'];
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }

  private getResponseSize(data: any): string {
    try {
      const size = new Blob([JSON.stringify(data)]).size;
      if (size < 1024) return `${size}B`;
      if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
      return `${(size / (1024 * 1024)).toFixed(1)}MB`;
    } catch {
      return 'unknown';
    }
  }
}

/**
 * Authentication Interceptor - adds authentication headers
 */
export class AuthenticationInterceptor implements RequestInterceptor {
  name = 'authentication';
  priority = 90;

  constructor(private getToken: () => string | null) {}

  onRequest(config: RequestConfig): RequestConfig {
    const token = this.getToken();
    
    if (token) {
      return {
        ...config,
        headers: {
          ...config.headers,
          'Authorization': `Bearer ${token}`
        }
      };
    }
    
    return config;
  }
}

/**
 * Retry Interceptor - handles automatic retries for failed requests
 */
export class RetryInterceptor implements ResponseInterceptor {
  name = 'retry';
  priority = 80;

  constructor(
    private maxRetries: number = 3,
    private retryDelay: number = 1000,
    private backoffMultiplier: number = 2
  ) {}

  onError(error: ApiError): ApiError {
    const retries = error.details?.find(d => d.field === 'retries')?.value || 0;
    
    if (retries < this.maxRetries && error.isRetryable()) {
      // Add retry metadata
      const newDetails = [
        ...(error.details || []),
        { field: 'retries', value: retries + 1 },
        { field: 'retryDelay', value: this.retryDelay * Math.pow(this.backoffMultiplier, retries) }
      ];

      return new ApiError(
        error.message,
        error.code,
        error.statusCode,
        newDetails,
        error.originalError
      );
    }
    
    return error;
  }

  onResponse(response: Response, data: any): { response: Response; data: any } {
    return { response, data };
  }
}

/**
 * Rate Limiting Interceptor - handles rate limiting
 */
export class RateLimitInterceptor implements ResponseInterceptor {
  name = 'rateLimit';
  priority = 70;

  private requestCounts = new Map<string, { count: number; resetTime: number }>();

  onResponse(response: Response, data: any): { response: Response; data: any } {
    // Extract rate limit headers
    const limit = response.headers.get('x-ratelimit-limit');
    const remaining = response.headers.get('x-ratelimit-remaining');
    const reset = response.headers.get('x-ratelimit-reset');

    if (limit && remaining && reset) {
      const resetTime = parseInt(reset) * 1000; // Convert to milliseconds
      const remainingCount = parseInt(remaining);
      
      if (remainingCount < 10) {
        console.warn(`⚠️  Rate limit warning: ${remainingCount} requests remaining until ${new Date(resetTime).toISOString()}`);
      }
    }

    return { response, data };
  }

  onError(error: ApiError): ApiError {
    if (error.code === ApiErrorCode.TOO_MANY_REQUESTS) {
      // Extract retry-after header if available
      const retryAfter = error.details?.find(d => d.field === 'retry-after')?.value;
      if (retryAfter) {
        console.warn(`⚠️  Rate limited. Retry after ${retryAfter} seconds`);
      }
    }
    
    return error;
  }
}

/**
 * Monitoring Interceptor - collects metrics and performance data
 */
export class MonitoringInterceptor implements RequestInterceptor, ResponseInterceptor {
  name = 'monitoring';
  priority = 60;

  private metrics = {
    requests: 0,
    responses: 0,
    errors: 0,
    totalDuration: 0,
    averageDuration: 0
  };

  onRequest(config: RequestConfig): RequestConfig {
    this.metrics.requests++;
    return config;
  }

  onResponse(response: Response, data: any): { response: Response; data: any } {
    this.metrics.responses++;
    
    const startTime = response.headers.get('x-start-time');
    if (startTime) {
      const duration = Date.now() - parseInt(startTime);
      this.metrics.totalDuration += duration;
      this.metrics.averageDuration = this.metrics.totalDuration / this.metrics.responses;
    }

    return { response, data };
  }

  onError(error: ApiError): ApiError {
    this.metrics.errors++;
    return error;
  }

  getMetrics() {
    return { ...this.metrics };
  }

  resetMetrics() {
    this.metrics = {
      requests: 0,
      responses: 0,
      errors: 0,
      totalDuration: 0,
      averageDuration: 0
    };
  }
}

/**
 * Interceptor Manager - manages all interceptors
 */
export class InterceptorManager {
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];

  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor);
    this.requestInterceptors.sort((a, b) => b.priority - a.priority);
  }

  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor);
    this.responseInterceptors.sort((a, b) => b.priority - a.priority);
  }

  removeInterceptor(name: string): void {
    this.requestInterceptors = this.requestInterceptors.filter(i => i.name !== name);
    this.responseInterceptors = this.responseInterceptors.filter(i => i.name !== name);
  }

  async processRequest(config: RequestConfig): Promise<RequestConfig> {
    let processedConfig = config;
    
    for (const interceptor of this.requestInterceptors) {
      try {
        processedConfig = await interceptor.onRequest(processedConfig);
      } catch (error) {
        if (interceptor.onError) {
          await interceptor.onError(error as Error);
        }
        throw error;
      }
    }
    
    return processedConfig;
  }

  async processResponse(response: Response, data: any): Promise<{ response: Response; data: any }> {
    let processedResult = { response, data };
    
    for (const interceptor of this.responseInterceptors) {
      try {
        processedResult = await interceptor.onResponse(processedResult.response, processedResult.data);
      } catch (error) {
        if (interceptor.onError) {
          throw await interceptor.onError(error as ApiError);
        }
        throw error;
      }
    }
    
    return processedResult;
  }

  async processError(error: ApiError): Promise<ApiError> {
    let processedError = error;
    
    for (const interceptor of this.responseInterceptors) {
      if (interceptor.onError) {
        try {
          processedError = await interceptor.onError(processedError);
        } catch (newError) {
          processedError = newError as ApiError;
        }
      }
    }
    
    return processedError;
  }
}
