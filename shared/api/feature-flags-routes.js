/**
 * Feature Flags API Routes
 * Provides endpoints for managing and retrieving feature flags
 */

const express = require('express');
const featureFlags = require('../config/feature-flags');

function createFeatureFlagsRoutes() {
    const router = express.Router();

    /**
     * GET /api/feature-flags
     * Get all feature flags and migration phase
     */
    router.get('/', (req, res) => {
        try {
            const userId = req.headers['x-user-id'] || req.ip;
            const flags = featureFlags.getAllFlags();
            const migrationPhase = featureFlags.getMigrationPhase();
            
            // Add user-specific routing decisions
            const userFlags = {};
            Object.keys(flags).forEach(flag => {
                if (flag.startsWith('TRAFFIC_PERCENTAGE_')) {
                    const baseFlag = flag.replace('TRAFFIC_PERCENTAGE_', '');
                    userFlags[`USE_${baseFlag}`] = featureFlags.shouldUseNewFeature(flag, userId);
                } else {
                    userFlags[flag] = flags[flag];
                }
            });
            
            res.json({
                flags: userFlags,
                migrationPhase,
                userId: userId.substring(0, 8) + '...', // Partial ID for debugging
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Error getting feature flags:', error);
            res.status(500).json({
                error: 'Failed to get feature flags',
                message: error.message
            });
        }
    });

    /**
     * GET /api/feature-flags/:flagName
     * Get specific feature flag status
     */
    router.get('/:flagName', (req, res) => {
        try {
            const { flagName } = req.params;
            const userId = req.headers['x-user-id'] || req.ip;
            
            const isEnabled = featureFlags.isEnabled(flagName);
            const shouldUseNew = featureFlags.shouldUseNewFeature(flagName, userId);
            
            res.json({
                flag: flagName,
                enabled: isEnabled,
                shouldUseNew,
                trafficPercentage: featureFlags.getTrafficPercentage(flagName),
                userId: userId.substring(0, 8) + '...'
            });
        } catch (error) {
            console.error('Error getting feature flag:', error);
            res.status(500).json({
                error: 'Failed to get feature flag',
                message: error.message
            });
        }
    });

    /**
     * POST /api/feature-flags/:flagName
     * Update feature flag (for testing/admin use)
     */
    router.post('/:flagName', (req, res) => {
        try {
            const { flagName } = req.params;
            const { value } = req.body;
            
            // Security check - only allow in development or with admin token
            if (process.env.NODE_ENV === 'production' && !req.headers['x-admin-token']) {
                return res.status(403).json({
                    error: 'Feature flag updates not allowed in production without admin token'
                });
            }
            
            featureFlags.setFlag(flagName, value);
            
            res.json({
                flag: flagName,
                oldValue: featureFlags.getAllFlags()[flagName],
                newValue: value,
                updated: true,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Error updating feature flag:', error);
            res.status(400).json({
                error: 'Failed to update feature flag',
                message: error.message
            });
        }
    });

    /**
     * GET /api/feature-flags/migration/phase
     * Get detailed migration phase information
     */
    router.get('/migration/phase', (req, res) => {
        try {
            const migrationPhase = featureFlags.getMigrationPhase();
            const allFlags = featureFlags.getAllFlags();
            
            // Calculate overall migration progress
            const totalFlags = Object.keys(allFlags).filter(flag => 
                !flag.startsWith('TRAFFIC_') && 
                !flag.includes('DEBUG') && 
                !flag.includes('LOGGING') &&
                !flag.includes('MONITORING')
            ).length;
            
            const enabledFlags = Object.keys(allFlags).filter(flag => 
                !flag.startsWith('TRAFFIC_') && 
                !flag.includes('DEBUG') && 
                !flag.includes('LOGGING') &&
                !flag.includes('MONITORING') &&
                allFlags[flag] === true
            ).length;
            
            const overallProgress = Math.round((enabledFlags / totalFlags) * 100);
            
            res.json({
                ...migrationPhase,
                overall: {
                    enabled: enabledFlags,
                    total: totalFlags,
                    percentage: overallProgress
                },
                traffic: {
                    api: featureFlags.getTrafficPercentage('TRAFFIC_PERCENTAGE_NEW_API'),
                    ui: featureFlags.getTrafficPercentage('TRAFFIC_PERCENTAGE_NEW_UI')
                },
                safety: {
                    dualExecution: featureFlags.isEnabled('DUAL_EXECUTION_MODE'),
                    performanceMonitoring: featureFlags.isEnabled('PERFORMANCE_MONITORING'),
                    automaticRollback: featureFlags.isEnabled('AUTOMATIC_ROLLBACK')
                }
            });
        } catch (error) {
            console.error('Error getting migration phase:', error);
            res.status(500).json({
                error: 'Failed to get migration phase',
                message: error.message
            });
        }
    });

    /**
     * POST /api/feature-flags/migration/rollout
     * Gradual rollout control
     */
    router.post('/migration/rollout', (req, res) => {
        try {
            const { phase, percentage } = req.body;
            
            // Security check
            if (process.env.NODE_ENV === 'production' && !req.headers['x-admin-token']) {
                return res.status(403).json({
                    error: 'Rollout control not allowed in production without admin token'
                });
            }
            
            if (phase === 'api') {
                featureFlags.setFlag('TRAFFIC_PERCENTAGE_NEW_API', percentage);
            } else if (phase === 'ui') {
                featureFlags.setFlag('TRAFFIC_PERCENTAGE_NEW_UI', percentage);
            } else {
                return res.status(400).json({
                    error: 'Invalid phase. Must be "api" or "ui"'
                });
            }
            
            res.json({
                phase,
                percentage,
                updated: true,
                timestamp: new Date().toISOString(),
                migrationPhase: featureFlags.getMigrationPhase()
            });
        } catch (error) {
            console.error('Error updating rollout:', error);
            res.status(400).json({
                error: 'Failed to update rollout',
                message: error.message
            });
        }
    });

    return router;
}

module.exports = createFeatureFlagsRoutes;
