/**
 * Unified API Error System
 * 
 * Consistent error handling across all API services
 */

export enum ApiErrorCode {
  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  
  // HTTP errors
  BAD_REQUEST = 'BAD_REQUEST',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  METHOD_NOT_ALLOWED = 'METHOD_NOT_ALLOWED',
  CONFLICT = 'CONFLICT',
  UNPROCESSABLE_ENTITY = 'UNPROCESSABLE_ENTITY',
  TOO_MANY_REQUESTS = 'TOO_MANY_REQUESTS',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  BAD_GATEWAY = 'BAD_GATEWAY',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  GATEWAY_TIMEOUT = 'GATEWAY_TIMEOUT',
  
  // Application errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  BUSINESS_LOGIC_ERROR = 'BUSINESS_LOGIC_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  
  // Unknown errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface ApiErrorDetails {
  field?: string;
  code?: string;
  message?: string;
  value?: any;
}

export class ApiError extends Error {
  public readonly name = 'ApiError';
  public readonly timestamp: string;
  public readonly requestId?: string;

  constructor(
    message: string,
    public readonly code: ApiErrorCode,
    public readonly statusCode: number = 500,
    public readonly details?: ApiErrorDetails[],
    public readonly originalError?: Error
  ) {
    super(message);
    this.timestamp = new Date().toISOString();
    
    // Maintain proper prototype chain
    Object.setPrototypeOf(this, ApiError.prototype);
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ApiError);
    }
  }

  /**
   * Create ApiError from HTTP response
   */
  static fromResponse(response: Response, responseData?: any): ApiError {
    const statusCode = response.status;
    const statusText = response.statusText;
    
    let code: ApiErrorCode;
    let message: string;
    let details: ApiErrorDetails[] | undefined;

    // Extract error information from response data
    if (responseData && typeof responseData === 'object') {
      message = responseData.message || responseData.error || statusText;
      details = responseData.details || responseData.errors;
      code = responseData.code || ApiError.getCodeFromStatus(statusCode);
    } else {
      message = statusText || `HTTP ${statusCode}`;
      code = ApiError.getCodeFromStatus(statusCode);
    }

    return new ApiError(message, code, statusCode, details);
  }

  /**
   * Create ApiError from network error
   */
  static fromNetworkError(error: Error): ApiError {
    let code: ApiErrorCode;
    let message: string;

    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      code = ApiErrorCode.TIMEOUT_ERROR;
      message = 'Request timeout';
    } else if (error.message.includes('fetch')) {
      code = ApiErrorCode.NETWORK_ERROR;
      message = 'Network error';
    } else {
      code = ApiErrorCode.CONNECTION_ERROR;
      message = error.message || 'Connection error';
    }

    return new ApiError(message, code, 0, undefined, error);
  }

  /**
   * Get error code from HTTP status
   */
  private static getCodeFromStatus(status: number): ApiErrorCode {
    switch (status) {
      case 400: return ApiErrorCode.BAD_REQUEST;
      case 401: return ApiErrorCode.UNAUTHORIZED;
      case 403: return ApiErrorCode.FORBIDDEN;
      case 404: return ApiErrorCode.NOT_FOUND;
      case 405: return ApiErrorCode.METHOD_NOT_ALLOWED;
      case 409: return ApiErrorCode.CONFLICT;
      case 422: return ApiErrorCode.UNPROCESSABLE_ENTITY;
      case 429: return ApiErrorCode.TOO_MANY_REQUESTS;
      case 500: return ApiErrorCode.INTERNAL_SERVER_ERROR;
      case 502: return ApiErrorCode.BAD_GATEWAY;
      case 503: return ApiErrorCode.SERVICE_UNAVAILABLE;
      case 504: return ApiErrorCode.GATEWAY_TIMEOUT;
      default: return ApiErrorCode.UNKNOWN_ERROR;
    }
  }

  /**
   * Check if error is retryable
   */
  isRetryable(): boolean {
    const retryableCodes = [
      ApiErrorCode.NETWORK_ERROR,
      ApiErrorCode.TIMEOUT_ERROR,
      ApiErrorCode.TOO_MANY_REQUESTS,
      ApiErrorCode.INTERNAL_SERVER_ERROR,
      ApiErrorCode.BAD_GATEWAY,
      ApiErrorCode.SERVICE_UNAVAILABLE,
      ApiErrorCode.GATEWAY_TIMEOUT
    ];

    return retryableCodes.includes(this.code);
  }

  /**
   * Check if error is client error (4xx)
   */
  isClientError(): boolean {
    return this.statusCode >= 400 && this.statusCode < 500;
  }

  /**
   * Check if error is server error (5xx)
   */
  isServerError(): boolean {
    return this.statusCode >= 500 && this.statusCode < 600;
  }

  /**
   * Convert to JSON for logging/serialization
   */
  toJSON(): object {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      timestamp: this.timestamp,
      details: this.details,
      stack: this.stack,
      requestId: this.requestId
    };
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(): string {
    switch (this.code) {
      case ApiErrorCode.NETWORK_ERROR:
        return 'Unable to connect to the server. Please check your internet connection.';
      case ApiErrorCode.TIMEOUT_ERROR:
        return 'The request took too long to complete. Please try again.';
      case ApiErrorCode.UNAUTHORIZED:
        return 'You are not authorized to perform this action. Please log in.';
      case ApiErrorCode.FORBIDDEN:
        return 'You do not have permission to access this resource.';
      case ApiErrorCode.NOT_FOUND:
        return 'The requested resource was not found.';
      case ApiErrorCode.TOO_MANY_REQUESTS:
        return 'Too many requests. Please wait a moment and try again.';
      case ApiErrorCode.SERVICE_UNAVAILABLE:
        return 'The service is temporarily unavailable. Please try again later.';
      case ApiErrorCode.VALIDATION_ERROR:
        return this.details && this.details.length > 0 
          ? this.details.map(d => d.message).join(', ')
          : 'Invalid input data.';
      default:
        return this.message || 'An unexpected error occurred. Please try again.';
    }
  }
}

/**
 * Validation Error - specific type for input validation failures
 */
export class ValidationError extends ApiError {
  constructor(message: string, details?: ApiErrorDetails[]) {
    super(message, ApiErrorCode.VALIDATION_ERROR, 400, details);
  }

  static fromFieldErrors(fieldErrors: Record<string, string>): ValidationError {
    const details: ApiErrorDetails[] = Object.entries(fieldErrors).map(([field, message]) => ({
      field,
      message
    }));

    return new ValidationError('Validation failed', details);
  }
}

/**
 * Business Logic Error - for application-specific errors
 */
export class BusinessLogicError extends ApiError {
  constructor(message: string, details?: ApiErrorDetails[]) {
    super(message, ApiErrorCode.BUSINESS_LOGIC_ERROR, 422, details);
  }
}

/**
 * Database Error - for database-related errors
 */
export class DatabaseError extends ApiError {
  constructor(message: string, originalError?: Error) {
    super(message, ApiErrorCode.DATABASE_ERROR, 500, undefined, originalError);
  }
}

/**
 * External Service Error - for third-party service errors
 */
export class ExternalServiceError extends ApiError {
  constructor(message: string, serviceName: string, originalError?: Error) {
    super(
      message, 
      ApiErrorCode.EXTERNAL_SERVICE_ERROR, 
      502, 
      [{ field: 'service', value: serviceName }], 
      originalError
    );
  }
}
