/**
 * Unified API Client
 * 
 * Single, consistent API client for all services with built-in error handling,
 * retries, logging, and monitoring
 */

import { ApiError, ApiErrorCode } from './errors';
import { 
  InterceptorManager, 
  LoggingInterceptor, 
  RetryInterceptor, 
  RateLimitInterceptor,
  MonitoringInterceptor,
  RequestConfig 
} from './interceptors';

export interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
  retries?: number;
  headers?: Record<string, string>;
  enableLogging?: boolean;
  enableRetries?: boolean;
  enableRateLimit?: boolean;
  enableMonitoring?: boolean;
}

export interface RequestOptions {
  timeout?: number;
  retries?: number;
  headers?: Record<string, string>;
  signal?: AbortSignal;
  metadata?: Record<string, any>;
}

export class ApiClient {
  private baseURL: string;
  private defaultTimeout: number;
  private defaultRetries: number;
  private defaultHeaders: Record<string, string>;
  private interceptorManager: InterceptorManager;

  constructor(config: ApiClientConfig) {
    this.baseURL = config.baseURL.replace(/\/$/, ''); // Remove trailing slash
    this.defaultTimeout = config.timeout || 30000;
    this.defaultRetries = config.retries || 3;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...config.headers
    };

    this.interceptorManager = new InterceptorManager();
    this.setupInterceptors(config);
  }

  private setupInterceptors(config: ApiClientConfig): void {
    // Add default interceptors based on configuration
    if (config.enableLogging !== false) {
      this.interceptorManager.addRequestInterceptor(new LoggingInterceptor());
      this.interceptorManager.addResponseInterceptor(new LoggingInterceptor());
    }

    if (config.enableRetries !== false) {
      this.interceptorManager.addResponseInterceptor(new RetryInterceptor(this.defaultRetries));
    }

    if (config.enableRateLimit !== false) {
      this.interceptorManager.addResponseInterceptor(new RateLimitInterceptor());
    }

    if (config.enableMonitoring !== false) {
      const monitoringInterceptor = new MonitoringInterceptor();
      this.interceptorManager.addRequestInterceptor(monitoringInterceptor);
      this.interceptorManager.addResponseInterceptor(monitoringInterceptor);
    }
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, params?: Record<string, any>, options?: RequestOptions): Promise<T> {
    const url = this.buildURL(endpoint, params);
    return this.request<T>('GET', url, undefined, options);
  }

  /**
   * POST request
   */
  async post<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<T> {
    const url = this.buildURL(endpoint);
    return this.request<T>('POST', url, data, options);
  }

  /**
   * PUT request
   */
  async put<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<T> {
    const url = this.buildURL(endpoint);
    return this.request<T>('PUT', url, data, options);
  }

  /**
   * PATCH request
   */
  async patch<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<T> {
    const url = this.buildURL(endpoint);
    return this.request<T>('PATCH', url, data, options);
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string, options?: RequestOptions): Promise<T> {
    const url = this.buildURL(endpoint);
    return this.request<T>('DELETE', url, undefined, options);
  }

  /**
   * Generic request method
   */
  private async request<T>(
    method: string, 
    url: string, 
    data?: any, 
    options?: RequestOptions
  ): Promise<T> {
    const config: RequestConfig = {
      url,
      method,
      headers: {
        ...this.defaultHeaders,
        ...options?.headers
      },
      body: data,
      timeout: options?.timeout || this.defaultTimeout,
      retries: options?.retries || this.defaultRetries,
      metadata: options?.metadata
    };

    // Process request through interceptors
    const processedConfig = await this.interceptorManager.processRequest(config);

    // Execute request with retry logic
    return this.executeWithRetry<T>(processedConfig, options?.signal);
  }

  /**
   * Execute request with automatic retry logic
   */
  private async executeWithRetry<T>(config: RequestConfig, signal?: AbortSignal): Promise<T> {
    let lastError: ApiError | null = null;
    const maxRetries = config.retries || 0;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await this.executeRequest<T>(config, signal);
      } catch (error) {
        lastError = error as ApiError;

        // Don't retry if it's the last attempt or error is not retryable
        if (attempt === maxRetries || !lastError.isRetryable()) {
          break;
        }

        // Calculate delay for exponential backoff
        const delay = this.calculateRetryDelay(attempt);
        console.warn(`⚠️  Request failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${delay}ms...`);
        
        await this.sleep(delay);
      }
    }

    // Process error through interceptors
    const processedError = await this.interceptorManager.processError(lastError!);
    throw processedError;
  }

  /**
   * Execute single request
   */
  private async executeRequest<T>(config: RequestConfig, signal?: AbortSignal): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout);

    // Combine signals if provided
    if (signal) {
      signal.addEventListener('abort', () => controller.abort());
    }

    try {
      const fetchOptions: RequestInit = {
        method: config.method,
        headers: config.headers,
        signal: controller.signal
      };

      // Add body for non-GET requests
      if (config.body && config.method !== 'GET') {
        if (typeof config.body === 'object') {
          fetchOptions.body = JSON.stringify(config.body);
        } else {
          fetchOptions.body = config.body;
        }
      }

      const response = await fetch(config.url, fetchOptions);
      clearTimeout(timeoutId);

      // Parse response data
      let responseData: any;
      const contentType = response.headers.get('content-type');
      
      if (contentType?.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      // Handle error responses
      if (!response.ok) {
        const error = ApiError.fromResponse(response, responseData);
        throw error;
      }

      // Process successful response through interceptors
      const processedResult = await this.interceptorManager.processResponse(response, responseData);
      return processedResult.data as T;

    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof ApiError) {
        throw error;
      }

      // Handle network errors
      if (error instanceof Error) {
        throw ApiError.fromNetworkError(error);
      }

      // Handle unknown errors
      throw new ApiError(
        'Unknown error occurred',
        ApiErrorCode.UNKNOWN_ERROR,
        0,
        undefined,
        error as Error
      );
    }
  }

  /**
   * Build full URL with query parameters
   */
  private buildURL(endpoint: string, params?: Record<string, any>): string {
    const url = endpoint.startsWith('http') ? endpoint : `${this.baseURL}${endpoint}`;
    
    if (!params || Object.keys(params).length === 0) {
      return url;
    }

    const urlObj = new URL(url);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        urlObj.searchParams.append(key, String(value));
      }
    });

    return urlObj.toString();
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(attempt: number): number {
    const baseDelay = 1000; // 1 second
    const maxDelay = 30000; // 30 seconds
    const delay = baseDelay * Math.pow(2, attempt);
    return Math.min(delay, maxDelay);
  }

  /**
   * Sleep utility for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Add custom interceptor
   */
  addInterceptor(interceptor: any): void {
    if ('onRequest' in interceptor) {
      this.interceptorManager.addRequestInterceptor(interceptor);
    }
    if ('onResponse' in interceptor || 'onError' in interceptor) {
      this.interceptorManager.addResponseInterceptor(interceptor);
    }
  }

  /**
   * Remove interceptor by name
   */
  removeInterceptor(name: string): void {
    this.interceptorManager.removeInterceptor(name);
  }

  /**
   * Get client metrics (if monitoring is enabled)
   */
  getMetrics(): any {
    // This would need to be implemented to extract metrics from monitoring interceptor
    return {};
  }

  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    try {
      const response = await this.get<any>('/health');
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        ...response
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof ApiError ? error.getUserMessage() : 'Unknown error'
      };
    }
  }
}

/**
 * Create API client with default configuration
 */
export function createApiClient(config: ApiClientConfig): ApiClient {
  return new ApiClient(config);
}

/**
 * Create API client for specific service
 */
export function createServiceApiClient(serviceName: string, baseURL: string): ApiClient {
  return new ApiClient({
    baseURL,
    enableLogging: true,
    enableRetries: true,
    enableRateLimit: true,
    enableMonitoring: true,
    headers: {
      'X-Service-Name': serviceName,
      'X-Client-Version': '1.0.0'
    }
  });
}
