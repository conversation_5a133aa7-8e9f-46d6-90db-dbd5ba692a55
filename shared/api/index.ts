/**
 * Unified API Services Export
 * 
 * Central export point for all API services and utilities
 */

// Core API client
export { ApiClient, createApiClient, createServiceApiClient } from './api-client';
export type { ApiClientConfig, RequestOptions } from './api-client';

// Error handling
export { 
  ApiError, 
  ValidationError, 
  BusinessLogicError, 
  DatabaseError, 
  ExternalServiceError,
  ApiErrorCode 
} from './errors';
export type { ApiErrorDetails } from './errors';

// Interceptors
export {
  InterceptorManager,
  LoggingInterceptor,
  AuthenticationInterceptor,
  RetryInterceptor,
  RateLimitInterceptor,
  MonitoringInterceptor
} from './interceptors';
export type { 
  RequestInterceptor, 
  ResponseInterceptor, 
  RequestConfig 
} from './interceptors';

// Service APIs
export { GraphApiService } from './services/graph-api';
export type { 
  Node, 
  Edge, 
  GraphData, 
  GraphSearchParams, 
  GraphSearchResult,
  SubgraphParams,
  NeighborParams,
  GraphFilter
} from './services/graph-api';

export { AnalysisApiService } from './services/analysis-api';
export type {
  ClusterParams,
  ClusterResult,
  HiddenLinksParams,
  HiddenLink,
  CentralityParams,
  CentralityResult,
  PathAnalysisParams,
  PathResult,
  SimilarityParams,
  SimilarityResult
} from './services/analysis-api';

// Service factory functions
export function createGraphApiService(baseURL?: string): GraphApiService {
  return new GraphApiService(baseURL);
}

export function createAnalysisApiService(baseURL?: string): AnalysisApiService {
  return new AnalysisApiService(baseURL);
}

// Default service instances (singleton pattern)
let defaultGraphApi: GraphApiService | null = null;
let defaultAnalysisApi: AnalysisApiService | null = null;

export function getGraphApiService(): GraphApiService {
  if (!defaultGraphApi) {
    defaultGraphApi = new GraphApiService();
  }
  return defaultGraphApi;
}

export function getAnalysisApiService(): AnalysisApiService {
  if (!defaultAnalysisApi) {
    defaultAnalysisApi = new AnalysisApiService();
  }
  return defaultAnalysisApi;
}

// Reset default instances (useful for testing)
export function resetDefaultServices(): void {
  defaultGraphApi = null;
  defaultAnalysisApi = null;
}

// Utility functions
export function isApiError(error: any): error is ApiError {
  return error instanceof ApiError;
}

export function handleApiError(error: unknown): ApiError {
  if (isApiError(error)) {
    return error;
  }
  
  if (error instanceof Error) {
    return ApiError.fromNetworkError(error);
  }
  
  return new ApiError(
    'Unknown error occurred',
    ApiErrorCode.UNKNOWN_ERROR,
    0,
    undefined,
    error as Error
  );
}

// Configuration helpers
export function getDefaultApiConfig(): ApiClientConfig {
  return {
    baseURL: process.env.VITE_API_URL || 'http://localhost:3002/api',
    timeout: 30000,
    retries: 3,
    enableLogging: process.env.NODE_ENV === 'development',
    enableRetries: true,
    enableRateLimit: true,
    enableMonitoring: true
  };
}

export function getApiConfigForEnvironment(env: 'development' | 'staging' | 'production' | 'test'): ApiClientConfig {
  const baseConfig = getDefaultApiConfig();
  
  switch (env) {
    case 'development':
      return {
        ...baseConfig,
        enableLogging: true,
        timeout: 30000,
        retries: 2
      };
      
    case 'test':
      return {
        ...baseConfig,
        baseURL: 'http://localhost:3003/api',
        enableLogging: false,
        timeout: 10000,
        retries: 1
      };
      
    case 'staging':
      return {
        ...baseConfig,
        enableLogging: true,
        timeout: 45000,
        retries: 3
      };
      
    case 'production':
      return {
        ...baseConfig,
        enableLogging: false,
        timeout: 60000,
        retries: 5
      };
      
    default:
      return baseConfig;
  }
}
