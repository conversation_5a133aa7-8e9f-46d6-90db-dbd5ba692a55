/**
 * Analysis API Service
 * 
 * Unified API service for graph analysis operations
 */

import { ApiClient, createServiceApiClient } from '../api-client';

export interface ClusterParams {
  resolution?: number;
  subGraph?: string;
  algorithm?: 'louvain' | 'leiden' | 'label_propagation';
  minClusterSize?: number;
  maxClusters?: number;
}

export interface ClusterResult {
  clusters: Array<{
    id: string;
    name: string;
    size: number;
    nodes: string[];
    centrality: number;
    description?: string;
  }>;
  modularity: number;
  totalClusters: number;
  algorithm: string;
  parameters: ClusterParams;
}

export interface HiddenLinksParams {
  topN?: number;
  threshold?: number;
  algorithm?: 'adamic_adar' | 'jaccard' | 'preferential_attachment';
  excludeExisting?: boolean;
}

export interface HiddenLink {
  source: string;
  target: string;
  score: number;
  algorithm: string;
  explanation?: string;
}

export interface CentralityParams {
  type?: 'pagerank' | 'betweenness' | 'closeness' | 'eigenvector';
  topN?: number;
  damping?: number;
  iterations?: number;
}

export interface CentralityResult {
  nodes: Array<{
    id: string;
    name: string;
    score: number;
    rank: number;
    type?: string;
  }>;
  algorithm: string;
  parameters: CentralityParams;
  statistics: {
    mean: number;
    median: number;
    std: number;
    min: number;
    max: number;
  };
}

export interface PathAnalysisParams {
  source: string;
  target: string;
  maxLength?: number;
  algorithm?: 'shortest' | 'all_simple' | 'node_disjoint';
  limit?: number;
}

export interface PathResult {
  paths: Array<{
    nodes: string[];
    length: number;
    weight?: number;
  }>;
  source: string;
  target: string;
  algorithm: string;
  totalPaths: number;
}

export interface SimilarityParams {
  nodeId: string;
  topN?: number;
  algorithm?: 'jaccard' | 'cosine' | 'pearson';
  threshold?: number;
}

export interface SimilarityResult {
  sourceNode: string;
  similarNodes: Array<{
    id: string;
    name: string;
    similarity: number;
    commonNeighbors?: string[];
  }>;
  algorithm: string;
  parameters: SimilarityParams;
}

export class AnalysisApiService {
  private client: ApiClient;

  constructor(baseURL?: string) {
    this.client = createServiceApiClient(
      'analysis-api',
      baseURL || process.env.VITE_API_URL || 'http://localhost:3002/api'
    );
  }

  /**
   * Perform community detection/clustering analysis
   */
  async getClusters(params: ClusterParams = {}): Promise<ClusterResult> {
    return this.client.get<ClusterResult>('/analysis/clusters', params);
  }

  /**
   * Find hidden/missing links in the graph
   */
  async getHiddenLinks(params: HiddenLinksParams = {}): Promise<HiddenLink[]> {
    return this.client.get<HiddenLink[]>('/analysis/hidden-links', params);
  }

  /**
   * Calculate node centrality measures
   */
  async getCentrality(params: CentralityParams = {}): Promise<CentralityResult> {
    return this.client.get<CentralityResult>('/analysis/centrality', params);
  }

  /**
   * Find paths between nodes
   */
  async findPaths(params: PathAnalysisParams): Promise<PathResult> {
    return this.client.get<PathResult>('/analysis/paths', params);
  }

  /**
   * Find similar nodes
   */
  async getSimilarNodes(params: SimilarityParams): Promise<SimilarityResult> {
    return this.client.get<SimilarityResult>('/analysis/similarity', params);
  }

  /**
   * Get graph statistics
   */
  async getGraphStatistics(): Promise<{
    nodeCount: number;
    edgeCount: number;
    density: number;
    averageDegree: number;
    diameter: number;
    clusteringCoefficient: number;
    components: number;
  }> {
    return this.client.get('/analysis/statistics');
  }

  /**
   * Run custom analysis algorithm
   */
  async runCustomAnalysis(algorithm: string, params: Record<string, any>): Promise<any> {
    return this.client.post('/analysis/custom', {
      algorithm,
      parameters: params
    });
  }

  /**
   * Get available analysis algorithms
   */
  async getAvailableAlgorithms(): Promise<Array<{
    name: string;
    description: string;
    parameters: Array<{
      name: string;
      type: string;
      required: boolean;
      default?: any;
      description: string;
    }>;
  }>> {
    return this.client.get('/analysis/algorithms');
  }

  /**
   * Export analysis results
   */
  async exportResults(analysisId: string, format: 'json' | 'csv' | 'gexf'): Promise<Blob> {
    const response = await this.client.get(`/analysis/export/${analysisId}`, { format });
    return new Blob([JSON.stringify(response)], { 
      type: format === 'json' ? 'application/json' : 'text/plain' 
    });
  }

  /**
   * Get analysis history
   */
  async getAnalysisHistory(limit: number = 10): Promise<Array<{
    id: string;
    type: string;
    parameters: any;
    timestamp: string;
    duration: number;
    status: 'completed' | 'failed' | 'running';
  }>> {
    return this.client.get('/analysis/history', { limit });
  }

  /**
   * Cancel running analysis
   */
  async cancelAnalysis(analysisId: string): Promise<void> {
    await this.client.delete(`/analysis/${analysisId}`);
  }
}
