/**
 * Graph API Service
 * 
 * Unified API service for graph data operations
 */

import { ApiClient, createServiceApiClient } from '../api-client';

export interface Node {
  id: string;
  name: string;
  type: string;
  properties: Record<string, any>;
  x?: number;
  y?: number;
  size?: number;
  color?: string;
}

export interface Edge {
  id: string;
  source: string;
  target: string;
  type: string;
  properties: Record<string, any>;
  weight?: number;
  color?: string;
}

export interface GraphData {
  nodes: Node[];
  edges: Edge[];
  metadata: {
    nodeCount: number;
    edgeCount: number;
    nodeTypes: string[];
    edgeTypes: string[];
    lastUpdated: string;
  };
}

export interface GraphSearchParams {
  query?: string;
  nodeTypes?: string[];
  edgeTypes?: string[];
  properties?: Record<string, any>;
  limit?: number;
  offset?: number;
}

export interface GraphSearchResult {
  nodes: Node[];
  edges: Edge[];
  total: number;
  query: string;
  executionTime: number;
}

export interface SubgraphParams {
  nodeIds?: string[];
  depth?: number;
  direction?: 'in' | 'out' | 'both';
  nodeTypes?: string[];
  edgeTypes?: string[];
  maxNodes?: number;
}

export interface NeighborParams {
  nodeId: string;
  depth?: number;
  direction?: 'in' | 'out' | 'both';
  nodeTypes?: string[];
  edgeTypes?: string[];
  limit?: number;
}

export interface GraphFilter {
  nodeTypes?: string[];
  edgeTypes?: string[];
  properties?: Record<string, any>;
  dateRange?: {
    start: string;
    end: string;
    property: string;
  };
}

export class GraphApiService {
  private client: ApiClient;

  constructor(baseURL?: string) {
    this.client = createServiceApiClient(
      'graph-api',
      baseURL || process.env.VITE_API_URL || 'http://localhost:3002/api'
    );
  }

  /**
   * Get initial graph data
   */
  async getInitialGraph(limit: number = 100): Promise<GraphData> {
    return this.client.get<GraphData>('/graph/initial', { limit });
  }

  /**
   * Search graph data
   */
  async searchGraph(params: GraphSearchParams): Promise<GraphSearchResult> {
    return this.client.get<GraphSearchResult>('/graph/search', params);
  }

  /**
   * Get node by ID
   */
  async getNode(nodeId: string): Promise<Node> {
    return this.client.get<Node>(`/graph/nodes/${nodeId}`);
  }

  /**
   * Get multiple nodes by IDs
   */
  async getNodes(nodeIds: string[]): Promise<Node[]> {
    return this.client.post<Node[]>('/graph/nodes/batch', { nodeIds });
  }

  /**
   * Get edge by ID
   */
  async getEdge(edgeId: string): Promise<Edge> {
    return this.client.get<Edge>(`/graph/edges/${edgeId}`);
  }

  /**
   * Get node neighbors
   */
  async getNeighbors(params: NeighborParams): Promise<GraphData> {
    return this.client.get<GraphData>('/graph/neighbors', params);
  }

  /**
   * Get subgraph
   */
  async getSubgraph(params: SubgraphParams): Promise<GraphData> {
    return this.client.get<GraphData>('/graph/subgraph', params);
  }

  /**
   * Apply graph filter
   */
  async applyFilter(filter: GraphFilter): Promise<GraphData> {
    return this.client.post<GraphData>('/graph/filter', filter);
  }

  /**
   * Get graph schema
   */
  async getSchema(): Promise<{
    nodeTypes: Array<{
      type: string;
      count: number;
      properties: Array<{
        name: string;
        type: string;
        required: boolean;
      }>;
    }>;
    edgeTypes: Array<{
      type: string;
      count: number;
      properties: Array<{
        name: string;
        type: string;
        required: boolean;
      }>;
    }>;
  }> {
    return this.client.get('/graph/schema');
  }

  /**
   * Create new node
   */
  async createNode(node: Omit<Node, 'id'>): Promise<Node> {
    return this.client.post<Node>('/graph/nodes', node);
  }

  /**
   * Update node
   */
  async updateNode(nodeId: string, updates: Partial<Node>): Promise<Node> {
    return this.client.patch<Node>(`/graph/nodes/${nodeId}`, updates);
  }

  /**
   * Delete node
   */
  async deleteNode(nodeId: string): Promise<void> {
    await this.client.delete(`/graph/nodes/${nodeId}`);
  }

  /**
   * Create new edge
   */
  async createEdge(edge: Omit<Edge, 'id'>): Promise<Edge> {
    return this.client.post<Edge>('/graph/edges', edge);
  }

  /**
   * Update edge
   */
  async updateEdge(edgeId: string, updates: Partial<Edge>): Promise<Edge> {
    return this.client.patch<Edge>(`/graph/edges/${edgeId}`, updates);
  }

  /**
   * Delete edge
   */
  async deleteEdge(edgeId: string): Promise<void> {
    await this.client.delete(`/graph/edges/${edgeId}`);
  }

  /**
   * Bulk import nodes and edges
   */
  async bulkImport(data: {
    nodes: Omit<Node, 'id'>[];
    edges: Omit<Edge, 'id'>[];
  }): Promise<{
    nodesCreated: number;
    edgesCreated: number;
    errors: string[];
  }> {
    return this.client.post('/graph/bulk-import', data);
  }

  /**
   * Export graph data
   */
  async exportGraph(format: 'json' | 'gexf' | 'graphml' | 'csv', filter?: GraphFilter): Promise<Blob> {
    const params = { format, ...filter };
    const response = await this.client.get('/graph/export', params);
    
    const mimeTypes = {
      json: 'application/json',
      gexf: 'application/xml',
      graphml: 'application/xml',
      csv: 'text/csv'
    };
    
    return new Blob([JSON.stringify(response)], { 
      type: mimeTypes[format] || 'application/octet-stream'
    });
  }

  /**
   * Get graph statistics
   */
  async getStatistics(): Promise<{
    nodeCount: number;
    edgeCount: number;
    nodeTypes: Record<string, number>;
    edgeTypes: Record<string, number>;
    density: number;
    averageDegree: number;
    lastUpdated: string;
  }> {
    return this.client.get('/graph/statistics');
  }

  /**
   * Validate graph data
   */
  async validateGraph(): Promise<{
    valid: boolean;
    errors: Array<{
      type: 'node' | 'edge';
      id: string;
      message: string;
    }>;
    warnings: Array<{
      type: 'node' | 'edge';
      id: string;
      message: string;
    }>;
  }> {
    return this.client.get('/graph/validate');
  }

  /**
   * Clear all graph data
   */
  async clearGraph(): Promise<void> {
    await this.client.delete('/graph/clear');
  }

  /**
   * Get graph version/revision info
   */
  async getVersion(): Promise<{
    version: string;
    revision: string;
    lastModified: string;
    checksum: string;
  }> {
    return this.client.get('/graph/version');
  }
}
