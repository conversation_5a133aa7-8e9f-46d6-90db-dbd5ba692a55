/**
 * Unified Error Handler
 * 
 * Central error handling system with logging, reporting, and recovery
 */

import { 
  AppError, 
  ErrorCategory, 
  ErrorSeverity, 
  ErrorContext,
  NetworkError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  BusinessLogicError,
  DatabaseError,
  ExternalServiceError,
  ConfigurationError,
  SystemError
} from './error-types';

export interface ErrorHandlerConfig {
  enableLogging?: boolean;
  enableReporting?: boolean;
  enableUserNotification?: boolean;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  reportingEndpoint?: string;
  maxRetries?: number;
  retryDelay?: number;
}

export interface ErrorReport {
  error: AppError;
  timestamp: string;
  environment: string;
  version?: string;
  buildId?: string;
}

export interface ErrorLogger {
  debug(message: string, context?: any): void;
  info(message: string, context?: any): void;
  warn(message: string, context?: any): void;
  error(message: string, context?: any): void;
}

export interface ErrorReporter {
  report(error: AppError): Promise<void>;
}

export interface UserNotifier {
  notify(error: AppError): void;
  showToast(message: string, type: 'error' | 'warning' | 'info'): void;
  showModal(title: string, message: string, actions?: any[]): void;
}

/**
 * Default Console Logger
 */
export class ConsoleLogger implements ErrorLogger {
  debug(message: string, context?: any): void {
    console.debug(`[DEBUG] ${message}`, context);
  }

  info(message: string, context?: any): void {
    console.info(`[INFO] ${message}`, context);
  }

  warn(message: string, context?: any): void {
    console.warn(`[WARN] ${message}`, context);
  }

  error(message: string, context?: any): void {
    console.error(`[ERROR] ${message}`, context);
  }
}

/**
 * Default Error Reporter
 */
export class DefaultErrorReporter implements ErrorReporter {
  constructor(private endpoint?: string) {}

  async report(error: AppError): Promise<void> {
    if (!this.endpoint) {
      console.warn('Error reporting endpoint not configured');
      return;
    }

    try {
      const report: ErrorReport = {
        error,
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        version: process.env.APP_VERSION,
        buildId: process.env.BUILD_ID
      };

      await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(report)
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }
}

/**
 * Default User Notifier
 */
export class DefaultUserNotifier implements UserNotifier {
  notify(error: AppError): void {
    const message = error.getUserMessage();
    
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        this.showModal('Error', message, error.recoveryActions);
        break;
      case ErrorSeverity.MEDIUM:
        this.showToast(message, 'error');
        break;
      case ErrorSeverity.LOW:
        this.showToast(message, 'warning');
        break;
    }
  }

  showToast(message: string, type: 'error' | 'warning' | 'info'): void {
    // This would integrate with your toast notification system
    console.log(`Toast [${type.toUpperCase()}]: ${message}`);
  }

  showModal(title: string, message: string, actions?: any[]): void {
    // This would integrate with your modal system
    console.log(`Modal [${title}]: ${message}`);
    if (actions) {
      console.log('Available actions:', actions.map(a => a.label));
    }
  }
}

/**
 * Central Error Handler
 */
export class ErrorHandler {
  private config: ErrorHandlerConfig;
  private logger: ErrorLogger;
  private reporter: ErrorReporter;
  private notifier: UserNotifier;

  constructor(
    config: ErrorHandlerConfig = {},
    logger?: ErrorLogger,
    reporter?: ErrorReporter,
    notifier?: UserNotifier
  ) {
    this.config = {
      enableLogging: true,
      enableReporting: true,
      enableUserNotification: true,
      logLevel: 'error',
      maxRetries: 3,
      retryDelay: 1000,
      ...config
    };

    this.logger = logger || new ConsoleLogger();
    this.reporter = reporter || new DefaultErrorReporter(config.reportingEndpoint);
    this.notifier = notifier || new DefaultUserNotifier();
  }

  /**
   * Handle any error
   */
  async handle(error: unknown, context?: Partial<ErrorContext>): Promise<AppError> {
    const appError = this.normalizeError(error, context);
    
    // Log error
    if (this.config.enableLogging) {
      this.logError(appError);
    }

    // Report error
    if (this.config.enableReporting && appError.isReportable()) {
      try {
        await this.reporter.report(appError);
      } catch (reportingError) {
        this.logger.error('Failed to report error', { reportingError, originalError: appError });
      }
    }

    // Notify user
    if (this.config.enableUserNotification) {
      this.notifier.notify(appError);
    }

    return appError;
  }

  /**
   * Handle error with retry logic
   */
  async handleWithRetry<T>(
    operation: () => Promise<T>,
    context?: Partial<ErrorContext>,
    maxRetries?: number
  ): Promise<T> {
    const retries = maxRetries ?? this.config.maxRetries ?? 3;
    let lastError: AppError | null = null;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = this.normalizeError(error, context);

        if (attempt === retries || !lastError.isRetryable()) {
          break;
        }

        // Wait before retry
        const delay = this.config.retryDelay! * Math.pow(2, attempt);
        await this.sleep(delay);

        this.logger.warn(`Retrying operation (attempt ${attempt + 1}/${retries + 1})`, {
          error: lastError.code,
          delay
        });
      }
    }

    // Handle the final error
    throw await this.handle(lastError!, context);
  }

  /**
   * Create error context
   */
  createContext(partial?: Partial<ErrorContext>): ErrorContext {
    return {
      timestamp: new Date().toISOString(),
      sessionId: this.getSessionId(),
      requestId: this.generateRequestId(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      ...partial
    };
  }

  /**
   * Normalize any error to AppError
   */
  private normalizeError(error: unknown, context?: Partial<ErrorContext>): AppError {
    const errorContext = this.createContext(context);

    if (error instanceof AppError) {
      return error;
    }

    if (error instanceof Error) {
      // Try to categorize based on error properties
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        return new NetworkError(error.message, errorContext, error);
      }

      if (error.name === 'ValidationError') {
        return new ValidationError(error.message, errorContext);
      }

      if (error.message.includes('unauthorized') || error.message.includes('401')) {
        return new AuthenticationError(error.message, errorContext);
      }

      if (error.message.includes('forbidden') || error.message.includes('403')) {
        return new AuthorizationError(error.message, errorContext);
      }

      if (error.message.includes('database') || error.message.includes('sql')) {
        return new DatabaseError(error.message, errorContext, error);
      }

      // Default to system error
      return new SystemError(error.message, errorContext, error);
    }

    // Handle non-Error objects
    const message = typeof error === 'string' ? error : 'Unknown error occurred';
    return new SystemError(message, errorContext);
  }

  /**
   * Log error based on severity
   */
  private logError(error: AppError): void {
    const logData = {
      code: error.code,
      category: error.category,
      severity: error.severity,
      context: error.context,
      details: error.details,
      stack: error.stack
    };

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        this.logger.error(error.getTechnicalMessage(), logData);
        break;
      case ErrorSeverity.MEDIUM:
        this.logger.warn(error.getTechnicalMessage(), logData);
        break;
      case ErrorSeverity.LOW:
        this.logger.info(error.getTechnicalMessage(), logData);
        break;
    }
  }

  /**
   * Get or generate session ID
   */
  private getSessionId(): string {
    if (typeof window !== 'undefined') {
      let sessionId = sessionStorage.getItem('sessionId');
      if (!sessionId) {
        sessionId = this.generateRequestId();
        sessionStorage.setItem('sessionId', sessionId);
      }
      return sessionId;
    }
    return 'server-session';
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const errorHandler = new ErrorHandler();

// Utility functions
export function handleError(error: unknown, context?: Partial<ErrorContext>): Promise<AppError> {
  return errorHandler.handle(error, context);
}

export function handleWithRetry<T>(
  operation: () => Promise<T>,
  context?: Partial<ErrorContext>,
  maxRetries?: number
): Promise<T> {
  return errorHandler.handleWithRetry(operation, context, maxRetries);
}

export function createErrorContext(partial?: Partial<ErrorContext>): ErrorContext {
  return errorHandler.createContext(partial);
}

// Global error handlers
if (typeof window !== 'undefined') {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    handleError(event.reason, {
      component: 'global',
      action: 'unhandled_promise_rejection'
    });
  });

  // Handle uncaught errors
  window.addEventListener('error', (event) => {
    handleError(event.error, {
      component: 'global',
      action: 'uncaught_error',
      metadata: {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      }
    });
  });
}

// Node.js global error handlers
if (typeof process !== 'undefined') {
  process.on('unhandledRejection', (reason) => {
    handleError(reason, {
      component: 'global',
      action: 'unhandled_promise_rejection'
    });
  });

  process.on('uncaughtException', (error) => {
    handleError(error, {
      component: 'global',
      action: 'uncaught_exception'
    });
  });
}
