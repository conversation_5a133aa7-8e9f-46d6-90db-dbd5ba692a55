/**
 * Unified Error Handling System
 * 
 * Central export point for all error handling utilities
 */

// Error Types
export {
  AppError,
  NetworkError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  BusinessLogicError,
  DatabaseError,
  ExternalServiceError,
  ConfigurationError,
  SystemError,
  ErrorCategory,
  ErrorSeverity
} from './error-types';

export type {
  ErrorContext,
  ErrorDetails,
  ErrorRecoveryAction,
  BaseErrorInfo
} from './error-types';

// Error Handler
export {
  <PERSON>rror<PERSON><PERSON><PERSON>,
  ConsoleLogger,
  DefaultErrorReporter,
  DefaultUserNotifier,
  errorHandler,
  handleError,
  handleWithRetry,
  createErrorContext
} from './error-handler';

export type {
  ErrorHandlerConfig,
  ErrorReport,
  ErrorLogger,
  ErrorReporter,
  UserNotifier
} from './error-handler';

// React Error Boundary
export {
  ErrorBoundary,
  withErrorBoundary,
  useErrorHandler
} from './error-boundary';

// Utility functions
export function isAppError(error: any): error is AppError {
  return error instanceof AppError;
}

export function getErrorMessage(error: unknown): string {
  if (isAppError(error)) {
    return error.getUserMessage();
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'An unknown error occurred';
}

export function getErrorCode(error: unknown): string {
  if (isAppError(error)) {
    return error.code;
  }
  
  if (error instanceof Error) {
    return error.name;
  }
  
  return 'UNKNOWN_ERROR';
}

export function isRetryableError(error: unknown): boolean {
  if (isAppError(error)) {
    return error.isRetryable();
  }
  
  // Default retry logic for non-AppError instances
  if (error instanceof Error) {
    const retryablePatterns = [
      /network/i,
      /timeout/i,
      /connection/i,
      /503/,
      /502/,
      /504/
    ];
    
    return retryablePatterns.some(pattern => 
      pattern.test(error.message) || pattern.test(error.name)
    );
  }
  
  return false;
}

export function shouldReportError(error: unknown): boolean {
  if (isAppError(error)) {
    return error.isReportable();
  }
  
  // Default reporting logic
  if (error instanceof Error) {
    // Don't report validation errors or user input errors
    const nonReportablePatterns = [
      /validation/i,
      /invalid input/i,
      /bad request/i,
      /400/
    ];
    
    return !nonReportablePatterns.some(pattern => 
      pattern.test(error.message) || pattern.test(error.name)
    );
  }
  
  return true;
}

/**
 * Create error from HTTP response
 */
export function createErrorFromResponse(response: Response, data?: any): AppError {
  const context = createErrorContext({
    action: 'http_request',
    metadata: {
      url: response.url,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    }
  });

  const message = data?.message || data?.error || response.statusText || `HTTP ${response.status}`;

  switch (response.status) {
    case 400:
      return new ValidationError(message, context, data?.details);
    case 401:
      return new AuthenticationError(message, context);
    case 403:
      return new AuthorizationError(message, context);
    case 404:
      return new BusinessLogicError(`Resource not found: ${message}`, context);
    case 422:
      return new ValidationError(message, context, data?.details);
    case 429:
      return new ExternalServiceError(`Rate limited: ${message}`, context, 'api');
    case 500:
    case 502:
    case 503:
    case 504:
      return new ExternalServiceError(message, context, 'api');
    default:
      return new SystemError(message, context);
  }
}

/**
 * Create error from network failure
 */
export function createNetworkError(error: Error): NetworkError {
  const context = createErrorContext({
    action: 'network_request',
    metadata: {
      errorName: error.name,
      errorMessage: error.message
    }
  });

  return new NetworkError(error.message, context, error);
}

/**
 * Create validation error from field errors
 */
export function createValidationError(
  fieldErrors: Record<string, string>,
  message = 'Validation failed'
): ValidationError {
  const context = createErrorContext({
    action: 'validation',
    metadata: { fieldCount: Object.keys(fieldErrors).length }
  });

  const details = Object.entries(fieldErrors).map(([field, message]) => ({
    field,
    message
  }));

  return new ValidationError(message, context, details);
}

/**
 * Error handling middleware for async operations
 */
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  context?: Partial<ErrorContext>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      throw await handleError(error, context);
    }
  };
}

/**
 * Error handling decorator for class methods
 */
export function HandleErrors(context?: Partial<ErrorContext>) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args);
      } catch (error) {
        throw await handleError(error, {
          component: target.constructor.name,
          action: propertyKey,
          ...context
        });
      }
    };

    return descriptor;
  };
}

/**
 * Global error configuration
 */
export function configureErrorHandling(config: ErrorHandlerConfig) {
  // This would update the global error handler configuration
  // Implementation depends on how you want to manage global state
  console.log('Error handling configured:', config);
}

/**
 * Error boundary for specific error types
 */
export function createTypedErrorBoundary<T extends AppError>(
  errorType: new (...args: any[]) => T,
  fallback: (error: T) => React.ReactNode
) {
  return function TypedErrorBoundary({ children }: { children: React.ReactNode }) {
    return (
      <ErrorBoundary
        fallback={(error, retry) => {
          if (error instanceof errorType) {
            return fallback(error as T);
          }
          // Fall back to default error UI for other error types
          return null;
        }}
      >
        {children}
      </ErrorBoundary>
    );
  };
}
