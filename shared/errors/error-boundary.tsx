/**
 * React Error Boundary
 * 
 * React component for catching and handling errors in component tree
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AppError, ErrorCategory, ErrorSeverity, createErrorContext } from './error-types';
import { errorHandler } from './error-handler';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: AppError, retry: () => void) => ReactNode;
  onError?: (error: AppError, errorInfo: ErrorInfo) => void;
  enableRetry?: boolean;
  retryText?: string;
  component?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: AppError | null;
  errorId: string | null;
}

/**
 * React Error Boundary Component
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      errorId: Math.random().toString(36).substr(2, 9)
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const context = createErrorContext({
      component: this.props.component || 'ErrorBoundary',
      action: 'component_error',
      metadata: {
        componentStack: errorInfo.componentStack,
        errorBoundary: this.constructor.name,
        retryCount: this.retryCount
      }
    });

    // Convert to AppError
    const appError = new AppError({
      code: 'REACT_ERROR_BOUNDARY',
      message: error.message,
      category: ErrorCategory.SYSTEM,
      severity: ErrorSeverity.HIGH,
      context,
      cause: error,
      retryable: this.retryCount < this.maxRetries,
      userMessage: 'Something went wrong. Please try refreshing the page.'
    });

    this.setState({ error: appError });

    // Handle error through central handler
    errorHandler.handle(appError, context);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(appError, errorInfo);
    }
  }

  handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({
        hasError: false,
        error: null,
        errorId: null
      });
    } else {
      // Max retries reached, reload page
      window.location.reload();
    }
  };

  render() {
    if (this.state.hasError && this.state.error) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Default error UI
      return (
        <DefaultErrorFallback
          error={this.state.error}
          onRetry={this.handleRetry}
          enableRetry={this.props.enableRetry !== false}
          retryText={this.props.retryText}
          retryCount={this.retryCount}
          maxRetries={this.maxRetries}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * Default Error Fallback Component
 */
interface DefaultErrorFallbackProps {
  error: AppError;
  onRetry: () => void;
  enableRetry: boolean;
  retryText?: string;
  retryCount: number;
  maxRetries: number;
}

function DefaultErrorFallback({
  error,
  onRetry,
  enableRetry,
  retryText = 'Try Again',
  retryCount,
  maxRetries
}: DefaultErrorFallbackProps) {
  const canRetry = enableRetry && retryCount < maxRetries;
  const isLastRetry = retryCount === maxRetries - 1;

  return (
    <div style={{
      padding: '2rem',
      margin: '1rem',
      border: '1px solid #e74c3c',
      borderRadius: '8px',
      backgroundColor: '#fff5f5',
      textAlign: 'center',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <div style={{
        fontSize: '3rem',
        marginBottom: '1rem'
      }}>
        ⚠️
      </div>
      
      <h2 style={{
        color: '#e74c3c',
        marginBottom: '1rem',
        fontSize: '1.5rem'
      }}>
        Something went wrong
      </h2>
      
      <p style={{
        color: '#666',
        marginBottom: '1.5rem',
        fontSize: '1rem',
        lineHeight: '1.5'
      }}>
        {error.getUserMessage()}
      </p>

      {process.env.NODE_ENV === 'development' && (
        <details style={{
          marginBottom: '1.5rem',
          textAlign: 'left',
          backgroundColor: '#f8f9fa',
          padding: '1rem',
          borderRadius: '4px',
          border: '1px solid #dee2e6'
        }}>
          <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
            Technical Details
          </summary>
          <pre style={{
            marginTop: '0.5rem',
            fontSize: '0.875rem',
            color: '#495057',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word'
          }}>
            Error Code: {error.code}
            {'\n'}Category: {error.category}
            {'\n'}Severity: {error.severity}
            {'\n'}Message: {error.message}
            {error.stack && `\n\nStack Trace:\n${error.stack}`}
          </pre>
        </details>
      )}

      <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
        {canRetry && (
          <button
            onClick={onRetry}
            style={{
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '1rem',
              fontWeight: '500'
            }}
          >
            {isLastRetry ? 'Last Try' : retryText}
            {retryCount > 0 && ` (${retryCount}/${maxRetries})`}
          </button>
        )}
        
        <button
          onClick={() => window.location.reload()}
          style={{
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '500'
          }}
        >
          Refresh Page
        </button>
        
        <button
          onClick={() => window.location.href = '/'}
          style={{
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '500'
          }}
        >
          Go Home
        </button>
      </div>

      {error.recoveryActions && error.recoveryActions.length > 0 && (
        <div style={{ marginTop: '1.5rem' }}>
          <h3 style={{ marginBottom: '0.5rem', fontSize: '1.1rem' }}>
            Suggested Actions:
          </h3>
          <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'center', flexWrap: 'wrap' }}>
            {error.recoveryActions.map((action, index) => (
              <button
                key={index}
                onClick={action.action}
                style={{
                  backgroundColor: action.primary ? '#17a2b8' : '#f8f9fa',
                  color: action.primary ? 'white' : '#495057',
                  border: '1px solid #dee2e6',
                  padding: '0.5rem 1rem',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '0.875rem'
                }}
              >
                {action.label}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Higher-order component for error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Hook for error handling in functional components
 */
export function useErrorHandler() {
  const handleError = React.useCallback((error: unknown, context?: Partial<any>) => {
    return errorHandler.handle(error, {
      component: 'useErrorHandler',
      ...context
    });
  }, []);

  const handleWithRetry = React.useCallback(<T,>(
    operation: () => Promise<T>,
    context?: Partial<any>,
    maxRetries?: number
  ) => {
    return errorHandler.handleWithRetry(operation, {
      component: 'useErrorHandler',
      ...context
    }, maxRetries);
  }, []);

  return {
    handleError,
    handleWithRetry
  };
}
