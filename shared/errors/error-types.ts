/**
 * Unified Error Types
 * 
 * Standardized error types and interfaces for consistent error handling
 */

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  NETWORK = 'network',
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  BUSINESS_LOGIC = 'business_logic',
  DATABASE = 'database',
  EXTERNAL_SERVICE = 'external_service',
  CONFIGURATION = 'configuration',
  SYSTEM = 'system',
  USER_INPUT = 'user_input',
  UNKNOWN = 'unknown'
}

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  component?: string;
  action?: string;
  timestamp: string;
  userAgent?: string;
  url?: string;
  metadata?: Record<string, any>;
}

export interface ErrorDetails {
  field?: string;
  code?: string;
  message?: string;
  value?: any;
  constraint?: string;
  expected?: any;
  actual?: any;
}

export interface ErrorRecoveryAction {
  type: 'retry' | 'redirect' | 'refresh' | 'contact_support' | 'custom';
  label: string;
  action: () => void | Promise<void>;
  primary?: boolean;
}

export interface BaseErrorInfo {
  code: string;
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  context: ErrorContext;
  details?: ErrorDetails[];
  cause?: Error;
  stack?: string;
  userMessage?: string;
  technicalMessage?: string;
  recoveryActions?: ErrorRecoveryAction[];
  retryable?: boolean;
  reportable?: boolean;
}

/**
 * Base Application Error
 */
export class AppError extends Error {
  public readonly code: string;
  public readonly category: ErrorCategory;
  public readonly severity: ErrorSeverity;
  public readonly context: ErrorContext;
  public readonly details?: ErrorDetails[];
  public readonly cause?: Error;
  public readonly userMessage?: string;
  public readonly technicalMessage?: string;
  public readonly recoveryActions?: ErrorRecoveryAction[];
  public readonly retryable: boolean;
  public readonly reportable: boolean;

  constructor(info: BaseErrorInfo) {
    super(info.message);
    
    this.name = this.constructor.name;
    this.code = info.code;
    this.category = info.category;
    this.severity = info.severity;
    this.context = info.context;
    this.details = info.details;
    this.cause = info.cause;
    this.userMessage = info.userMessage;
    this.technicalMessage = info.technicalMessage;
    this.recoveryActions = info.recoveryActions;
    this.retryable = info.retryable ?? false;
    this.reportable = info.reportable ?? true;

    // Maintain proper prototype chain
    Object.setPrototypeOf(this, AppError.prototype);

    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(): string {
    return this.userMessage || this.getDefaultUserMessage();
  }

  /**
   * Get technical error message for logging
   */
  getTechnicalMessage(): string {
    return this.technicalMessage || this.message;
  }

  /**
   * Check if error should be retried
   */
  isRetryable(): boolean {
    return this.retryable;
  }

  /**
   * Check if error should be reported to monitoring
   */
  isReportable(): boolean {
    return this.reportable;
  }

  /**
   * Convert to JSON for logging/serialization
   */
  toJSON(): object {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      category: this.category,
      severity: this.severity,
      context: this.context,
      details: this.details,
      userMessage: this.userMessage,
      technicalMessage: this.technicalMessage,
      retryable: this.retryable,
      reportable: this.reportable,
      stack: this.stack,
      cause: this.cause ? {
        name: this.cause.name,
        message: this.cause.message,
        stack: this.cause.stack
      } : undefined
    };
  }

  /**
   * Get default user message based on category
   */
  private getDefaultUserMessage(): string {
    switch (this.category) {
      case ErrorCategory.NETWORK:
        return 'Unable to connect to the server. Please check your internet connection and try again.';
      case ErrorCategory.VALIDATION:
        return 'Please check your input and try again.';
      case ErrorCategory.AUTHENTICATION:
        return 'Please log in to continue.';
      case ErrorCategory.AUTHORIZATION:
        return 'You do not have permission to perform this action.';
      case ErrorCategory.DATABASE:
        return 'A database error occurred. Please try again later.';
      case ErrorCategory.EXTERNAL_SERVICE:
        return 'An external service is temporarily unavailable. Please try again later.';
      case ErrorCategory.CONFIGURATION:
        return 'A configuration error occurred. Please contact support.';
      case ErrorCategory.SYSTEM:
        return 'A system error occurred. Please try again later.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}

/**
 * Network Error
 */
export class NetworkError extends AppError {
  constructor(message: string, context: ErrorContext, cause?: Error) {
    super({
      code: 'NETWORK_ERROR',
      message,
      category: ErrorCategory.NETWORK,
      severity: ErrorSeverity.MEDIUM,
      context,
      cause,
      retryable: true,
      userMessage: 'Unable to connect to the server. Please check your internet connection and try again.'
    });
  }
}

/**
 * Validation Error
 */
export class ValidationError extends AppError {
  constructor(message: string, context: ErrorContext, details?: ErrorDetails[]) {
    super({
      code: 'VALIDATION_ERROR',
      message,
      category: ErrorCategory.VALIDATION,
      severity: ErrorSeverity.LOW,
      context,
      details,
      retryable: false,
      reportable: false,
      userMessage: details && details.length > 0 
        ? details.map(d => d.message).filter(Boolean).join(', ')
        : 'Please check your input and try again.'
    });
  }
}

/**
 * Authentication Error
 */
export class AuthenticationError extends AppError {
  constructor(message: string, context: ErrorContext) {
    super({
      code: 'AUTHENTICATION_ERROR',
      message,
      category: ErrorCategory.AUTHENTICATION,
      severity: ErrorSeverity.MEDIUM,
      context,
      retryable: false,
      userMessage: 'Please log in to continue.'
    });
  }
}

/**
 * Authorization Error
 */
export class AuthorizationError extends AppError {
  constructor(message: string, context: ErrorContext, requiredPermission?: string) {
    super({
      code: 'AUTHORIZATION_ERROR',
      message,
      category: ErrorCategory.AUTHORIZATION,
      severity: ErrorSeverity.MEDIUM,
      context,
      details: requiredPermission ? [{ field: 'permission', value: requiredPermission }] : undefined,
      retryable: false,
      userMessage: 'You do not have permission to perform this action.'
    });
  }
}

/**
 * Business Logic Error
 */
export class BusinessLogicError extends AppError {
  constructor(message: string, context: ErrorContext, details?: ErrorDetails[]) {
    super({
      code: 'BUSINESS_LOGIC_ERROR',
      message,
      category: ErrorCategory.BUSINESS_LOGIC,
      severity: ErrorSeverity.MEDIUM,
      context,
      details,
      retryable: false
    });
  }
}

/**
 * Database Error
 */
export class DatabaseError extends AppError {
  constructor(message: string, context: ErrorContext, cause?: Error) {
    super({
      code: 'DATABASE_ERROR',
      message,
      category: ErrorCategory.DATABASE,
      severity: ErrorSeverity.HIGH,
      context,
      cause,
      retryable: true,
      userMessage: 'A database error occurred. Please try again later.'
    });
  }
}

/**
 * External Service Error
 */
export class ExternalServiceError extends AppError {
  constructor(message: string, context: ErrorContext, serviceName: string, cause?: Error) {
    super({
      code: 'EXTERNAL_SERVICE_ERROR',
      message,
      category: ErrorCategory.EXTERNAL_SERVICE,
      severity: ErrorSeverity.MEDIUM,
      context,
      details: [{ field: 'service', value: serviceName }],
      cause,
      retryable: true,
      userMessage: 'An external service is temporarily unavailable. Please try again later.'
    });
  }
}

/**
 * Configuration Error
 */
export class ConfigurationError extends AppError {
  constructor(message: string, context: ErrorContext, configKey?: string) {
    super({
      code: 'CONFIGURATION_ERROR',
      message,
      category: ErrorCategory.CONFIGURATION,
      severity: ErrorSeverity.CRITICAL,
      context,
      details: configKey ? [{ field: 'configKey', value: configKey }] : undefined,
      retryable: false,
      userMessage: 'A configuration error occurred. Please contact support.'
    });
  }
}

/**
 * System Error
 */
export class SystemError extends AppError {
  constructor(message: string, context: ErrorContext, cause?: Error) {
    super({
      code: 'SYSTEM_ERROR',
      message,
      category: ErrorCategory.SYSTEM,
      severity: ErrorSeverity.HIGH,
      context,
      cause,
      retryable: true,
      userMessage: 'A system error occurred. Please try again later.'
    });
  }
}
