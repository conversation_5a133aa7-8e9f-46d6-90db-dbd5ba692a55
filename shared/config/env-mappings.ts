/**
 * Environment Variable Mappings
 * 
 * Centralized mapping of environment variables for consistent
 * configuration across all services.
 */

export interface EnvironmentMapping {
  key: string;
  description: string;
  required: boolean;
  defaultValue?: string;
  validation?: (value: string) => boolean;
  services: string[];
}

export const ENVIRONMENT_MAPPINGS: Record<string, EnvironmentMapping> = {
  // Core Application
  NODE_ENV: {
    key: 'NODE_ENV',
    description: 'Application environment',
    required: false,
    defaultValue: 'development',
    validation: (value) => ['development', 'staging', 'production'].includes(value),
    services: ['api', 'ui', 'proxy']
  },

  // Database Configuration
  NEO4J_URI: {
    key: 'NEO4J_URI',
    description: 'Neo4j database connection URI',
    required: true,
    validation: (value) => value.startsWith('neo4j://') || value.startsWith('bolt://'),
    services: ['api', 'chat-api']
  },

  NEO4J_USERNAME: {
    key: 'NEO4J_USERNAME',
    description: 'Neo4j database username',
    required: true,
    defaultValue: 'neo4j',
    services: ['api', 'chat-api']
  },

  NEO4J_PASSWORD: {
    key: 'NEO4J_PASSWORD',
    description: 'Neo4j database password',
    required: true,
    services: ['api', 'chat-api']
  },

  NEO4J_DATABASE: {
    key: 'NEO4J_DATABASE',
    description: 'Neo4j database name',
    required: false,
    defaultValue: 'neo4j',
    services: ['api', 'chat-api']
  },

  // API Configuration
  API_PORT: {
    key: 'API_PORT',
    description: 'API server port',
    required: false,
    defaultValue: '3002',
    validation: (value) => !isNaN(parseInt(value)) && parseInt(value) > 1000,
    services: ['api']
  },

  CORS_ORIGINS: {
    key: 'CORS_ORIGINS',
    description: 'Allowed CORS origins (comma-separated)',
    required: false,
    defaultValue: '*',
    services: ['api', 'proxy']
  },

  // Proxy Configuration
  PROXY_SERVER_PORT: {
    key: 'PROXY_SERVER_PORT',
    description: 'Proxy server port',
    required: false,
    defaultValue: '3001',
    validation: (value) => !isNaN(parseInt(value)) && parseInt(value) > 1000,
    services: ['proxy']
  },

  CHAT_API_URL: {
    key: 'CHAT_API_URL',
    description: 'Chat API service URL',
    required: false,
    defaultValue: 'http://localhost:8000',
    validation: (value) => value.startsWith('http://') || value.startsWith('https://'),
    services: ['proxy']
  },

  // Frontend Configuration
  VITE_API_URL: {
    key: 'VITE_API_URL',
    description: 'API URL for frontend',
    required: false,
    defaultValue: 'http://localhost:3002/api',
    validation: (value) => value.startsWith('http://') || value.startsWith('https://'),
    services: ['ui']
  },

  // LLM Provider Configuration
  LLM_PRIMARY_PROVIDER: {
    key: 'LLM_PRIMARY_PROVIDER',
    description: 'Primary LLM provider',
    required: false,
    defaultValue: 'ollama',
    validation: (value) => ['ollama', 'azure', 'google', 'anthropic'].includes(value),
    services: ['chat-api']
  },

  LLM_OLLAMA_BASE_URL: {
    key: 'LLM_OLLAMA_BASE_URL',
    description: 'Ollama service base URL',
    required: false,
    defaultValue: 'http://localhost:11434',
    validation: (value) => value.startsWith('http://') || value.startsWith('https://'),
    services: ['chat-api']
  },

  LLM_ANTHROPIC_API_KEY: {
    key: 'LLM_ANTHROPIC_API_KEY',
    description: 'Anthropic API key',
    required: false,
    services: ['chat-api']
  },

  LLM_GOOGLE_API_KEY: {
    key: 'LLM_GOOGLE_API_KEY',
    description: 'Google AI API key',
    required: false,
    services: ['chat-api']
  },

  LLM_AZURE_API_KEY: {
    key: 'LLM_AZURE_API_KEY',
    description: 'Azure OpenAI API key',
    required: false,
    services: ['chat-api']
  },

  LLM_AZURE_ENDPOINT: {
    key: 'LLM_AZURE_ENDPOINT',
    description: 'Azure OpenAI endpoint',
    required: false,
    validation: (value) => value.startsWith('https://'),
    services: ['chat-api']
  },

  // Security Configuration
  SESSION_SECRET: {
    key: 'SESSION_SECRET',
    description: 'Session secret for authentication',
    required: false,
    defaultValue: 'development-secret-change-in-production',
    services: ['proxy']
  },

  // Monitoring Configuration
  LOG_LEVEL: {
    key: 'LOG_LEVEL',
    description: 'Application log level',
    required: false,
    defaultValue: 'INFO',
    validation: (value) => ['DEBUG', 'INFO', 'WARN', 'ERROR'].includes(value.toUpperCase()),
    services: ['api', 'chat-api', 'proxy']
  }
};

/**
 * Get environment mappings for a specific service
 */
export function getServiceEnvironmentMappings(service: string): Record<string, EnvironmentMapping> {
  const serviceMappings: Record<string, EnvironmentMapping> = {};
  
  Object.entries(ENVIRONMENT_MAPPINGS).forEach(([key, mapping]) => {
    if (mapping.services.includes(service)) {
      serviceMappings[key] = mapping;
    }
  });
  
  return serviceMappings;
}

/**
 * Validate environment variables for a service
 */
export function validateServiceEnvironment(service: string): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const mappings = getServiceEnvironmentMappings(service);
  const errors: string[] = [];
  const warnings: string[] = [];

  Object.entries(mappings).forEach(([key, mapping]) => {
    const value = process.env[key];

    if (mapping.required && !value) {
      errors.push(`Missing required environment variable: ${key}`);
    } else if (value && mapping.validation && !mapping.validation(value)) {
      errors.push(`Invalid value for environment variable ${key}: ${value}`);
    } else if (!value && mapping.defaultValue) {
      warnings.push(`Using default value for ${key}: ${mapping.defaultValue}`);
    }
  });

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}
