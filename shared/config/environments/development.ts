/**
 * Development Environment Configuration
 * 
 * Configuration overrides for development environment
 */

import { Partial } from 'utility-types';
import { AppConfig } from '../types';

export const developmentConfig: Partial<AppConfig> = {
  environment: 'development',
  
  database: {
    uri: 'neo4j://localhost:7687',
    username: 'neo4j',
    password: 'development_password',
    database: 'neo4j',
    maxConnectionPoolSize: 10,
    connectionTimeout: 30000
  },
  
  api: {
    port: 3002,
    host: '0.0.0.0',
    cors: {
      origins: ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:3001'],
      credentials: true
    },
    timeout: 30000
  },
  
  llm: {
    primaryProvider: 'ollama',
    retryAttempts: 2,
    providers: {
      ollama: {
        baseUrl: 'http://localhost:11434',
        timeout: 30000
      }
    }
  },
  
  proxy: {
    port: 3001,
    host: '0.0.0.0',
    chatApiUrl: 'http://localhost:8000',
    timeout: 30000,
    cors: {
      origins: ['http://localhost:5173']
    }
  },
  
  frontend: {
    apiUrl: 'http://localhost:3002/api',
    proxyUrl: 'http://localhost:3001',
    enableDevTools: true,
    theme: 'auto'
  },
  
  security: {
    sessionSecret: 'development-secret-change-in-production'
  },
  
  logging: {
    level: 'DEBUG',
    format: 'text',
    console: {
      enabled: true,
      colorize: true
    },
    file: {
      enabled: false,
      path: './logs/development.log',
      maxSize: '10MB',
      maxFiles: 3
    }
  },
  
  monitoring: {
    healthCheck: {
      enabled: true,
      interval: 30000,
      timeout: 5000
    },
    metrics: {
      enabled: true,
      port: 9090
    },
    alerts: {
      enabled: false
    }
  }
};
