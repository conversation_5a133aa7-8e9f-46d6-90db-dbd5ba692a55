/**
 * Production Environment Configuration
 * 
 * Configuration overrides for production environment
 */

import { Partial } from 'utility-types';
import { AppConfig } from '../types';

export const productionConfig: Partial<AppConfig> = {
  environment: 'production',
  
  database: {
    maxConnectionPoolSize: 100,
    connectionTimeout: 10000
  },
  
  api: {
    host: '0.0.0.0',
    cors: {
      credentials: true
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000 // limit each IP to 1000 requests per windowMs
    },
    timeout: 30000
  },
  
  llm: {
    retryAttempts: 5,
    providers: {
      ollama: {
        timeout: 60000
      },
      anthropic: {
        maxTokens: 4000
      },
      google: {
        maxTokens: 4000
      },
      azure: {
        maxTokens: 4000
      }
    }
  },
  
  proxy: {
    host: '0.0.0.0',
    timeout: 60000
  },
  
  frontend: {
    enableDevTools: false,
    theme: 'light'
  },
  
  logging: {
    level: 'INFO',
    format: 'json',
    console: {
      enabled: true,
      colorize: false
    },
    file: {
      enabled: true,
      path: './logs/production.log',
      maxSize: '100MB',
      maxFiles: 10
    }
  },
  
  monitoring: {
    healthCheck: {
      enabled: true,
      interval: 15000, // More frequent in production
      timeout: 3000
    },
    metrics: {
      enabled: true,
      port: 9090
    },
    alerts: {
      enabled: true
    }
  }
};
