/**
 * Test Environment Configuration
 * 
 * Configuration overrides for test environment
 */

import { Partial } from 'utility-types';
import { AppConfig } from '../types';

export const testConfig: Partial<AppConfig> = {
  environment: 'test',
  
  database: {
    uri: 'neo4j://localhost:7687',
    username: 'neo4j',
    password: 'test_password',
    database: 'test',
    maxConnectionPoolSize: 5,
    connectionTimeout: 5000
  },
  
  api: {
    port: 3003, // Different port for tests
    host: '127.0.0.1',
    cors: {
      origins: ['*'],
      credentials: false
    },
    timeout: 10000
  },
  
  llm: {
    primaryProvider: 'ollama',
    retryAttempts: 1, // Faster failures in tests
    providers: {
      ollama: {
        baseUrl: 'http://localhost:11434',
        timeout: 5000
      }
    }
  },
  
  proxy: {
    port: 3004, // Different port for tests
    host: '127.0.0.1',
    chatApiUrl: 'http://localhost:8001',
    timeout: 10000
  },
  
  frontend: {
    apiUrl: 'http://localhost:3003/api',
    proxyUrl: 'http://localhost:3004',
    enableDevTools: false,
    theme: 'light'
  },
  
  security: {
    sessionSecret: 'test-secret-key-for-testing-only-not-secure'
  },
  
  logging: {
    level: 'ERROR', // Minimal logging in tests
    format: 'text',
    console: {
      enabled: false, // Disable console logging in tests
      colorize: false
    },
    file: {
      enabled: false // Disable file logging in tests
    }
  },
  
  monitoring: {
    healthCheck: {
      enabled: false, // Disable health checks in tests
      interval: 60000,
      timeout: 1000
    },
    metrics: {
      enabled: false // Disable metrics in tests
    },
    alerts: {
      enabled: false // Disable alerts in tests
    }
  }
};
