/**
 * Unified Configuration Loader
 * 
 * Loads and validates configuration from multiple sources with type safety
 */

import { z } from 'zod';
import { AppConfig, ConfigValidationResult, ConfigSource, ConfigLoader as IConfigLoader } from './types';
import { LegacyConfigAdapter } from './legacy/adapter';

// Zod schemas for validation
const DatabaseConfigSchema = z.object({
  uri: z.string().url(),
  username: z.string().min(1),
  password: z.string().min(1),
  database: z.string().min(1),
  maxConnectionPoolSize: z.number().min(1).max(1000).optional(),
  connectionTimeout: z.number().min(1000).max(60000).optional()
});

const ApiConfigSchema = z.object({
  port: z.number().min(1000).max(65535),
  host: z.string().optional(),
  cors: z.object({
    origins: z.array(z.string()),
    credentials: z.boolean().optional()
  }),
  rateLimit: z.object({
    windowMs: z.number().min(1000),
    max: z.number().min(1)
  }).optional(),
  timeout: z.number().min(1000).optional()
});

const LLMConfigSchema = z.object({
  primaryProvider: z.enum(['ollama', 'anthropic', 'google', 'azure']),
  fallbackProviders: z.array(z.string()).optional(),
  retryAttempts: z.number().min(0).max(10).optional(),
  providers: z.object({
    ollama: z.object({
      baseUrl: z.string().url(),
      timeout: z.number().min(1000).optional()
    }).optional(),
    anthropic: z.object({
      apiKey: z.string().min(1),
      model: z.string().optional(),
      maxTokens: z.number().min(1).optional()
    }).optional(),
    google: z.object({
      apiKey: z.string().min(1),
      model: z.string().optional(),
      maxTokens: z.number().min(1).optional()
    }).optional(),
    azure: z.object({
      apiKey: z.string().min(1),
      endpoint: z.string().url(),
      model: z.string().optional(),
      maxTokens: z.number().min(1).optional()
    }).optional()
  })
});

const SecurityConfigSchema = z.object({
  sessionSecret: z.string().min(32),
  jwtSecret: z.string().min(32).optional(),
  encryptionKey: z.string().min(32).optional(),
  allowedHosts: z.array(z.string()).optional()
});

const LoggingConfigSchema = z.object({
  level: z.enum(['DEBUG', 'INFO', 'WARN', 'ERROR']),
  format: z.enum(['json', 'text']).optional(),
  file: z.object({
    enabled: z.boolean(),
    path: z.string(),
    maxSize: z.string(),
    maxFiles: z.number().min(1)
  }).optional(),
  console: z.object({
    enabled: z.boolean(),
    colorize: z.boolean()
  }).optional()
});

const MonitoringConfigSchema = z.object({
  healthCheck: z.object({
    enabled: z.boolean(),
    interval: z.number().min(1000),
    timeout: z.number().min(1000)
  }),
  metrics: z.object({
    enabled: z.boolean(),
    port: z.number().min(1000).max(65535).optional()
  }),
  alerts: z.object({
    enabled: z.boolean(),
    webhookUrl: z.string().url().optional()
  })
});

const AppConfigSchema = z.object({
  environment: z.enum(['development', 'staging', 'production', 'test']),
  version: z.string().optional(),
  database: DatabaseConfigSchema,
  api: ApiConfigSchema,
  llm: LLMConfigSchema,
  proxy: z.object({
    port: z.number().min(1000).max(65535),
    host: z.string().optional(),
    chatApiUrl: z.string().url(),
    timeout: z.number().min(1000).optional(),
    cors: z.object({
      origins: z.array(z.string())
    }).optional()
  }).optional(),
  frontend: z.object({
    apiUrl: z.string().url(),
    proxyUrl: z.string().url().optional(),
    enableDevTools: z.boolean().optional(),
    theme: z.enum(['light', 'dark', 'auto']).optional()
  }).optional(),
  security: SecurityConfigSchema,
  logging: LoggingConfigSchema,
  monitoring: MonitoringConfigSchema
});

export class ConfigLoader implements IConfigLoader {
  private sources: ConfigSource[] = [];
  private cache: AppConfig | null = null;

  constructor() {
    // Add default sources
    this.addSource(new EnvironmentConfigSource());
    this.addSource(new DefaultConfigSource());
  }

  addSource(source: ConfigSource): void {
    this.sources.push(source);
    this.sources.sort((a, b) => b.priority - a.priority);
    this.cache = null; // Invalidate cache
  }

  removeSource(name: string): void {
    this.sources = this.sources.filter(source => source.name !== name);
    this.cache = null; // Invalidate cache
  }

  load(): AppConfig {
    if (this.cache) {
      return this.cache;
    }

    // Apply legacy adapter first
    LegacyConfigAdapter.adapt();

    // Merge configuration from all sources
    let config: Partial<AppConfig> = {};
    
    for (const source of this.sources) {
      const sourceConfig = source.load();
      config = this.mergeConfig(config, sourceConfig);
    }

    // Validate configuration
    const validation = this.validate(config);
    if (!validation.valid) {
      throw new Error(`Configuration validation failed: ${validation.errors.join(', ')}`);
    }

    this.cache = config as AppConfig;
    return this.cache;
  }

  validate(config: Partial<AppConfig>): ConfigValidationResult {
    const result = AppConfigSchema.safeParse(config);
    
    if (result.success) {
      return {
        valid: true,
        errors: [],
        warnings: this.getWarnings(config as AppConfig)
      };
    } else {
      return {
        valid: false,
        errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
        warnings: []
      };
    }
  }

  private mergeConfig(target: Partial<AppConfig>, source: Partial<AppConfig>): Partial<AppConfig> {
    const result = { ...target };
    
    for (const [key, value] of Object.entries(source)) {
      if (value !== undefined) {
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          result[key as keyof AppConfig] = this.mergeConfig(
            (result[key as keyof AppConfig] as any) || {},
            value
          ) as any;
        } else {
          result[key as keyof AppConfig] = value as any;
        }
      }
    }
    
    return result;
  }

  private getWarnings(config: AppConfig): string[] {
    const warnings: string[] = [];

    // Check for development settings in production
    if (config.environment === 'production') {
      if (config.security.sessionSecret === 'development-secret-change-in-production') {
        warnings.push('Using default session secret in production');
      }
      
      if (config.api.cors.origins.includes('*')) {
        warnings.push('CORS allows all origins in production');
      }
      
      if (config.logging.level === 'DEBUG') {
        warnings.push('Debug logging enabled in production');
      }
    }

    // Check LLM provider configuration
    const primaryProvider = config.llm.primaryProvider;
    if (!config.llm.providers[primaryProvider]) {
      warnings.push(`Primary LLM provider '${primaryProvider}' is not configured`);
    }

    return warnings;
  }
}

// Environment variable configuration source
class EnvironmentConfigSource implements ConfigSource {
  name = 'environment';
  priority = 100;

  load(): Partial<AppConfig> {
    return {
      environment: (process.env.NODE_ENV as any) || 'development',
      version: process.env.APP_VERSION,
      database: {
        uri: process.env.NEO4J_URI || process.env.DATABASE_URI || '',
        username: process.env.NEO4J_USERNAME || '',
        password: process.env.NEO4J_PASSWORD || '',
        database: process.env.NEO4J_DATABASE || 'neo4j',
        maxConnectionPoolSize: process.env.NEO4J_MAX_POOL_SIZE ? parseInt(process.env.NEO4J_MAX_POOL_SIZE) : undefined,
        connectionTimeout: process.env.NEO4J_CONNECTION_TIMEOUT ? parseInt(process.env.NEO4J_CONNECTION_TIMEOUT) : undefined
      },
      api: {
        port: parseInt(process.env.API_PORT || process.env.PORT || '3002'),
        host: process.env.API_HOST,
        cors: {
          origins: process.env.CORS_ORIGINS?.split(',') || ['*'],
          credentials: process.env.CORS_CREDENTIALS === 'true'
        },
        timeout: process.env.API_TIMEOUT ? parseInt(process.env.API_TIMEOUT) : undefined
      },
      llm: {
        primaryProvider: (process.env.LLM_PRIMARY_PROVIDER as any) || 'ollama',
        fallbackProviders: process.env.LLM_FALLBACK_PROVIDERS?.split(','),
        retryAttempts: process.env.LLM_RETRY_ATTEMPTS ? parseInt(process.env.LLM_RETRY_ATTEMPTS) : undefined,
        providers: {
          ollama: process.env.LLM_OLLAMA_BASE_URL ? {
            baseUrl: process.env.LLM_OLLAMA_BASE_URL,
            timeout: process.env.LLM_OLLAMA_TIMEOUT ? parseInt(process.env.LLM_OLLAMA_TIMEOUT) : undefined
          } : undefined,
          anthropic: process.env.LLM_ANTHROPIC_API_KEY ? {
            apiKey: process.env.LLM_ANTHROPIC_API_KEY,
            model: process.env.LLM_ANTHROPIC_MODEL,
            maxTokens: process.env.LLM_ANTHROPIC_MAX_TOKENS ? parseInt(process.env.LLM_ANTHROPIC_MAX_TOKENS) : undefined
          } : undefined,
          google: process.env.LLM_GOOGLE_API_KEY ? {
            apiKey: process.env.LLM_GOOGLE_API_KEY,
            model: process.env.LLM_GOOGLE_MODEL,
            maxTokens: process.env.LLM_GOOGLE_MAX_TOKENS ? parseInt(process.env.LLM_GOOGLE_MAX_TOKENS) : undefined
          } : undefined,
          azure: process.env.LLM_AZURE_API_KEY ? {
            apiKey: process.env.LLM_AZURE_API_KEY,
            endpoint: process.env.LLM_AZURE_ENDPOINT || '',
            model: process.env.LLM_AZURE_MODEL,
            maxTokens: process.env.LLM_AZURE_MAX_TOKENS ? parseInt(process.env.LLM_AZURE_MAX_TOKENS) : undefined
          } : undefined
        }
      },
      proxy: process.env.PROXY_SERVER_PORT ? {
        port: parseInt(process.env.PROXY_SERVER_PORT),
        host: process.env.PROXY_HOST,
        chatApiUrl: process.env.CHAT_API_URL || 'http://localhost:8000',
        timeout: process.env.PROXY_TIMEOUT ? parseInt(process.env.PROXY_TIMEOUT) : undefined
      } : undefined,
      frontend: process.env.VITE_API_URL ? {
        apiUrl: process.env.VITE_API_URL,
        proxyUrl: process.env.VITE_PROXY_URL,
        enableDevTools: process.env.VITE_ENABLE_DEV_TOOLS === 'true',
        theme: (process.env.VITE_THEME as any)
      } : undefined,
      security: {
        sessionSecret: process.env.SESSION_SECRET || 'development-secret-change-in-production',
        jwtSecret: process.env.JWT_SECRET,
        encryptionKey: process.env.ENCRYPTION_KEY,
        allowedHosts: process.env.ALLOWED_HOSTS?.split(',')
      },
      logging: {
        level: (process.env.LOG_LEVEL as any) || 'INFO',
        format: (process.env.LOG_FORMAT as any),
        file: process.env.LOG_FILE_ENABLED === 'true' ? {
          enabled: true,
          path: process.env.LOG_FILE_PATH || './logs/app.log',
          maxSize: process.env.LOG_FILE_MAX_SIZE || '10MB',
          maxFiles: process.env.LOG_FILE_MAX_FILES ? parseInt(process.env.LOG_FILE_MAX_FILES) : 5
        } : undefined,
        console: {
          enabled: process.env.LOG_CONSOLE_ENABLED !== 'false',
          colorize: process.env.LOG_CONSOLE_COLORIZE !== 'false'
        }
      },
      monitoring: {
        healthCheck: {
          enabled: process.env.HEALTH_CHECK_ENABLED !== 'false',
          interval: process.env.HEALTH_CHECK_INTERVAL ? parseInt(process.env.HEALTH_CHECK_INTERVAL) : 30000,
          timeout: process.env.HEALTH_CHECK_TIMEOUT ? parseInt(process.env.HEALTH_CHECK_TIMEOUT) : 5000
        },
        metrics: {
          enabled: process.env.METRICS_ENABLED === 'true',
          port: process.env.METRICS_PORT ? parseInt(process.env.METRICS_PORT) : undefined
        },
        alerts: {
          enabled: process.env.ALERTS_ENABLED === 'true',
          webhookUrl: process.env.ALERTS_WEBHOOK_URL
        }
      }
    };
  }
}

// Default configuration source
class DefaultConfigSource implements ConfigSource {
  name = 'defaults';
  priority = 0;

  load(): Partial<AppConfig> {
    return {
      environment: 'development',
      database: {
        uri: 'neo4j://localhost:7687',
        username: 'neo4j',
        password: 'development_password',
        database: 'neo4j',
        maxConnectionPoolSize: 50,
        connectionTimeout: 30000
      },
      api: {
        port: 3002,
        host: '0.0.0.0',
        cors: {
          origins: ['*'],
          credentials: false
        },
        timeout: 30000
      },
      llm: {
        primaryProvider: 'ollama',
        retryAttempts: 3,
        providers: {
          ollama: {
            baseUrl: 'http://localhost:11434',
            timeout: 30000
          }
        }
      },
      security: {
        sessionSecret: 'development-secret-change-in-production'
      },
      logging: {
        level: 'INFO',
        format: 'text',
        console: {
          enabled: true,
          colorize: true
        }
      },
      monitoring: {
        healthCheck: {
          enabled: true,
          interval: 30000,
          timeout: 5000
        },
        metrics: {
          enabled: false
        },
        alerts: {
          enabled: false
        }
      }
    };
  }
}

// Export singleton instance
export const configLoader = new ConfigLoader();
