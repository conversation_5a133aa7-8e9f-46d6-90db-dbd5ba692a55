/**
 * Configuration Validator
 * 
 * Validates configuration across all services and provides
 * detailed error reporting and suggestions.
 */

import { LegacyConfigAdapter } from './legacy/adapter';
import { validateServiceEnvironment, ENVIRONMENT_MAPPINGS } from './env-mappings';

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
  migrationStatus: {
    legacyVariablesFound: string[];
    newVariablesFound: string[];
    migrationComplete: boolean;
  };
}

export class ConfigValidator {
  private static readonly SERVICES = ['api', 'ui', 'proxy', 'chat-api'];

  /**
   * Validate configuration for all services
   */
  static validateAll(): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
      suggestions: [],
      migrationStatus: LegacyConfigAdapter.getMigrationStatus()
    };

    // Apply legacy adapter first
    LegacyConfigAdapter.adapt();

    // Validate each service
    this.SERVICES.forEach(service => {
      const serviceValidation = validateServiceEnvironment(service);
      
      if (!serviceValidation.valid) {
        result.valid = false;
        result.errors.push(...serviceValidation.errors.map(err => `[${service}] ${err}`));
      }
      
      result.warnings.push(...serviceValidation.warnings.map(warn => `[${service}] ${warn}`));
    });

    // Add migration suggestions
    if (!result.migrationStatus.migrationComplete) {
      result.suggestions.push('Consider migrating to new environment variable names');
      result.suggestions.push('Run: node -e "console.log(require(\'./shared/config/legacy/adapter\').LegacyConfigAdapter.generateMigrationGuide())"');
    }

    // Check for common configuration issues
    this.checkCommonIssues(result);

    return result;
  }

  /**
   * Validate configuration for a specific service
   */
  static validateService(service: string): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
      suggestions: [],
      migrationStatus: LegacyConfigAdapter.getMigrationStatus()
    };

    // Apply legacy adapter first
    LegacyConfigAdapter.adapt();

    const serviceValidation = validateServiceEnvironment(service);
    
    if (!serviceValidation.valid) {
      result.valid = false;
      result.errors = serviceValidation.errors;
    }
    
    result.warnings = serviceValidation.warnings;

    return result;
  }

  /**
   * Check for common configuration issues
   */
  private static checkCommonIssues(result: ValidationResult): void {
    // Check for port conflicts
    const apiPort = process.env.API_PORT || process.env.PORT || '3002';
    const proxyPort = process.env.PROXY_SERVER_PORT || process.env.PROXY_PORT || '3001';
    const chatPort = '8000'; // Fixed for chat API
    const uiPort = '5173'; // Fixed for Vite dev server

    const ports = [apiPort, proxyPort, chatPort, uiPort];
    const uniquePorts = new Set(ports);
    
    if (ports.length !== uniquePorts.size) {
      result.errors.push('Port conflict detected. Each service must use a unique port.');
      result.suggestions.push('Check API_PORT, PROXY_SERVER_PORT, and ensure they don\'t conflict with 8000 (chat) or 5173 (UI)');
    }

    // Check for missing LLM provider configuration
    const primaryProvider = process.env.LLM_PRIMARY_PROVIDER || 'ollama';
    const hasProviderConfig = this.hasLLMProviderConfig(primaryProvider);
    
    if (!hasProviderConfig) {
      result.warnings.push(`Primary LLM provider '${primaryProvider}' is not configured`);
      result.suggestions.push(`Configure ${primaryProvider.toUpperCase()} environment variables`);
    }

    // Check for development vs production settings
    const nodeEnv = process.env.NODE_ENV || 'development';
    if (nodeEnv === 'production') {
      this.checkProductionSettings(result);
    }
  }

  /**
   * Check if LLM provider is properly configured
   */
  private static hasLLMProviderConfig(provider: string): boolean {
    switch (provider) {
      case 'ollama':
        return !!process.env.LLM_OLLAMA_BASE_URL;
      case 'anthropic':
        return !!process.env.LLM_ANTHROPIC_API_KEY;
      case 'google':
        return !!process.env.LLM_GOOGLE_API_KEY;
      case 'azure':
        return !!(process.env.LLM_AZURE_API_KEY && process.env.LLM_AZURE_ENDPOINT);
      default:
        return false;
    }
  }

  /**
   * Check production-specific settings
   */
  private static checkProductionSettings(result: ValidationResult): void {
    // Check for secure session secret
    const sessionSecret = process.env.SESSION_SECRET;
    if (!sessionSecret || sessionSecret === 'development-secret-change-in-production') {
      result.errors.push('SESSION_SECRET must be set to a secure value in production');
    }

    // Check for HTTPS URLs in production
    const apiUrl = process.env.VITE_API_URL;
    if (apiUrl && !apiUrl.startsWith('https://')) {
      result.warnings.push('Consider using HTTPS for API URL in production');
    }

    // Check CORS configuration
    const corsOrigins = process.env.CORS_ORIGINS;
    if (!corsOrigins || corsOrigins === '*') {
      result.warnings.push('CORS_ORIGINS should be restricted in production');
    }
  }

  /**
   * Generate configuration report
   */
  static generateReport(): string {
    const validation = this.validateAll();
    
    let report = '📋 Configuration Validation Report\n';
    report += '=====================================\n\n';

    if (validation.valid) {
      report += '✅ Configuration is valid\n\n';
    } else {
      report += '❌ Configuration has errors\n\n';
    }

    if (validation.errors.length > 0) {
      report += '🚨 Errors:\n';
      validation.errors.forEach(error => {
        report += `  • ${error}\n`;
      });
      report += '\n';
    }

    if (validation.warnings.length > 0) {
      report += '⚠️  Warnings:\n';
      validation.warnings.forEach(warning => {
        report += `  • ${warning}\n`;
      });
      report += '\n';
    }

    if (validation.suggestions.length > 0) {
      report += '💡 Suggestions:\n';
      validation.suggestions.forEach(suggestion => {
        report += `  • ${suggestion}\n`;
      });
      report += '\n';
    }

    // Migration status
    if (!validation.migrationStatus.migrationComplete) {
      report += '🔄 Migration Status:\n';
      report += `  • Legacy variables found: ${validation.migrationStatus.legacyVariablesFound.length}\n`;
      report += `  • New variables found: ${validation.migrationStatus.newVariablesFound.length}\n`;
      report += '  • Migration incomplete\n\n';
    } else {
      report += '✅ Configuration migration is complete\n\n';
    }

    return report;
  }
}
