/**
 * Unified Configuration Types
 * 
 * Type-safe configuration interfaces for all services
 */

export interface DatabaseConfig {
  uri: string;
  username: string;
  password: string;
  database: string;
  maxConnectionPoolSize?: number;
  connectionTimeout?: number;
}

export interface ApiConfig {
  port: number;
  host?: string;
  cors: {
    origins: string[];
    credentials?: boolean;
  };
  rateLimit?: {
    windowMs: number;
    max: number;
  };
  timeout?: number;
}

export interface LLMProviderConfig {
  ollama?: {
    baseUrl: string;
    timeout?: number;
  };
  anthropic?: {
    apiKey: string;
    model?: string;
    maxTokens?: number;
  };
  google?: {
    apiKey: string;
    model?: string;
    maxTokens?: number;
  };
  azure?: {
    apiKey: string;
    endpoint: string;
    model?: string;
    maxTokens?: number;
  };
}

export interface LLMConfig {
  primaryProvider: 'ollama' | 'anthropic' | 'google' | 'azure';
  fallbackProviders?: string[];
  retryAttempts?: number;
  providers: LLMProviderConfig;
}

export interface ProxyConfig {
  port: number;
  host?: string;
  chatApiUrl: string;
  timeout?: number;
  cors?: {
    origins: string[];
  };
}

export interface FrontendConfig {
  apiUrl: string;
  proxyUrl?: string;
  enableDevTools?: boolean;
  theme?: 'light' | 'dark' | 'auto';
}

export interface SecurityConfig {
  sessionSecret: string;
  jwtSecret?: string;
  encryptionKey?: string;
  allowedHosts?: string[];
}

export interface LoggingConfig {
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  format?: 'json' | 'text';
  file?: {
    enabled: boolean;
    path: string;
    maxSize: string;
    maxFiles: number;
  };
  console?: {
    enabled: boolean;
    colorize: boolean;
  };
}

export interface MonitoringConfig {
  healthCheck: {
    enabled: boolean;
    interval: number;
    timeout: number;
  };
  metrics: {
    enabled: boolean;
    port?: number;
  };
  alerts: {
    enabled: boolean;
    webhookUrl?: string;
  };
}

export interface AppConfig {
  environment: 'development' | 'staging' | 'production' | 'test';
  version?: string;
  database: DatabaseConfig;
  api: ApiConfig;
  llm: LLMConfig;
  proxy?: ProxyConfig;
  frontend?: FrontendConfig;
  security: SecurityConfig;
  logging: LoggingConfig;
  monitoring: MonitoringConfig;
}

export interface ConfigValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ConfigSource {
  name: string;
  priority: number;
  load(): Partial<AppConfig>;
}

export interface ConfigLoader {
  load(): AppConfig;
  validate(config: Partial<AppConfig>): ConfigValidationResult;
  addSource(source: ConfigSource): void;
  removeSource(name: string): void;
}
