/**
 * Feature Flag Configuration System
 * Supports gradual migration rollout with environment variable overrides
 */

class FeatureFlags {
    constructor() {
        this.flags = {
            // Backend Architecture Migration
            NEW_CONTROLLER_LAYER: this.getFlag('NEW_CONTROLLER_LAYER', false),
            NEW_SERVICE_LAYER: this.getFlag('NEW_SERVICE_LAYER', false),
            NEW_REPOSITORY_PATTERN: this.getFlag('NEW_REPOSITORY_PATTERN', false),
            NEW_MIDDLEWARE_STACK: this.getFlag('NEW_MIDDLEWARE_STACK', false),
            
            // Frontend Architecture Migration
            NEW_FEATURE_SLICE_ARCHITECTURE: this.getFlag('NEW_FEATURE_SLICE_ARCHITECTURE', false),
            NEW_GRAPH_COMPONENTS: this.getFlag('NEW_GRAPH_COMPONENTS', false),
            NEW_CHAT_INTERFACE: this.getFlag('NEW_CHAT_INTERFACE', false),
            NEW_STATE_MANAGEMENT: this.getFlag('NEW_STATE_MANAGEMENT', false),
            
            // Service Extraction
            NEW_CHAT_SERVICE: this.getFlag('NEW_CHAT_SERVICE', false),
            CIRCUIT_BREAKER_ENABLED: this.getFlag('CIRCUIT_BREAKER_ENABLED', true),
            
            // Migration Safety
            DUAL_EXECUTION_MODE: this.getFlag('DUAL_EXECUTION_MODE', true),
            PERFORMANCE_MONITORING: this.getFlag('PERFORMANCE_MONITORING', true),
            AUTOMATIC_ROLLBACK: this.getFlag('AUTOMATIC_ROLLBACK', true),
            
            // Traffic Routing
            TRAFFIC_PERCENTAGE_NEW_API: this.getFlag('TRAFFIC_PERCENTAGE_NEW_API', 0),
            TRAFFIC_PERCENTAGE_NEW_UI: this.getFlag('TRAFFIC_PERCENTAGE_NEW_UI', 0),
            
            // Development & Testing
            DEBUG_MODE: this.getFlag('DEBUG_MODE', false),
            VERBOSE_LOGGING: this.getFlag('VERBOSE_LOGGING', false),
            MIGRATION_METRICS: this.getFlag('MIGRATION_METRICS', true)
        };
        
        // Validation rules
        this.validateFlags();
    }
    
    /**
     * Get feature flag value with environment variable override
     */
    getFlag(flagName, defaultValue) {
        const envKey = `FEATURE_FLAG_${flagName}`;
        const envValue = process.env[envKey];
        
        if (envValue !== undefined) {
            // Handle boolean conversion
            if (envValue.toLowerCase() === 'true') return true;
            if (envValue.toLowerCase() === 'false') return false;
            
            // Handle numeric conversion
            const numValue = Number(envValue);
            if (!isNaN(numValue)) return numValue;
            
            // Return string value
            return envValue;
        }
        
        return defaultValue;
    }
    
    /**
     * Check if a feature is enabled
     */
    isEnabled(flagName) {
        return Boolean(this.flags[flagName]);
    }
    
    /**
     * Get traffic percentage for gradual rollout
     */
    getTrafficPercentage(flagName) {
        const value = this.flags[flagName];
        return Math.max(0, Math.min(100, Number(value) || 0));
    }
    
    /**
     * Determine if user should get new feature based on traffic percentage
     */
    shouldUseNewFeature(flagName, userId = null) {
        const percentage = this.getTrafficPercentage(flagName);
        
        if (percentage === 0) return false;
        if (percentage === 100) return true;
        
        // Use deterministic hash for consistent user experience
        const hash = userId ? this.hashUserId(userId) : Math.random() * 100;
        return hash < percentage;
    }
    
    /**
     * Simple hash function for user ID
     */
    hashUserId(userId) {
        let hash = 0;
        for (let i = 0; i < userId.length; i++) {
            const char = userId.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash) % 100;
    }
    
    /**
     * Get all flags for debugging
     */
    getAllFlags() {
        return { ...this.flags };
    }
    
    /**
     * Update flag at runtime (for testing)
     */
    setFlag(flagName, value) {
        if (this.flags.hasOwnProperty(flagName)) {
            this.flags[flagName] = value;
            this.validateFlags();
        } else {
            throw new Error(`Unknown feature flag: ${flagName}`);
        }
    }
    
    /**
     * Validate flag values
     */
    validateFlags() {
        // Ensure traffic percentages are valid
        const trafficFlags = ['TRAFFIC_PERCENTAGE_NEW_API', 'TRAFFIC_PERCENTAGE_NEW_UI'];
        trafficFlags.forEach(flag => {
            const value = this.flags[flag];
            if (value < 0 || value > 100) {
                throw new Error(`Traffic percentage ${flag} must be between 0 and 100, got: ${value}`);
            }
        });
        
        // Ensure dependent flags are consistent
        if (this.flags.NEW_CHAT_SERVICE && !this.flags.CIRCUIT_BREAKER_ENABLED) {
            console.warn('Warning: NEW_CHAT_SERVICE enabled without CIRCUIT_BREAKER_ENABLED');
        }
    }
    
    /**
     * Get migration phase status
     */
    getMigrationPhase() {
        const backendFlags = [
            'NEW_CONTROLLER_LAYER',
            'NEW_SERVICE_LAYER', 
            'NEW_REPOSITORY_PATTERN',
            'NEW_MIDDLEWARE_STACK'
        ];
        
        const frontendFlags = [
            'NEW_FEATURE_SLICE_ARCHITECTURE',
            'NEW_GRAPH_COMPONENTS',
            'NEW_CHAT_INTERFACE',
            'NEW_STATE_MANAGEMENT'
        ];
        
        const backendEnabled = backendFlags.filter(flag => this.isEnabled(flag)).length;
        const frontendEnabled = frontendFlags.filter(flag => this.isEnabled(flag)).length;
        
        return {
            backend: {
                enabled: backendEnabled,
                total: backendFlags.length,
                percentage: Math.round((backendEnabled / backendFlags.length) * 100)
            },
            frontend: {
                enabled: frontendEnabled,
                total: frontendFlags.length,
                percentage: Math.round((frontendEnabled / frontendFlags.length) * 100)
            },
            services: {
                chatService: this.isEnabled('NEW_CHAT_SERVICE'),
                circuitBreaker: this.isEnabled('CIRCUIT_BREAKER_ENABLED')
            }
        };
    }
}

// Singleton instance
const featureFlags = new FeatureFlags();

module.exports = featureFlags;
