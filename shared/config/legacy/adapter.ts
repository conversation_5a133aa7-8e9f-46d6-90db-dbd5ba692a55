/**
 * Legacy Configuration Adapter
 * 
 * Provides backward compatibility for existing environment variables
 * during the transition to the new unified configuration system.
 */

export class LegacyConfigAdapter {
  private static readonly VARIABLE_MAPPINGS = {
    // Database mappings
    'NEO4J_USER': 'NEO4J_USERNAME',
    'NEO4J_URI': 'DATABASE_URI',
    
    // API Service mappings
    'PORT': 'API_PORT',
    
    // Proxy Service mappings
    'PROXY_PORT': 'PROXY_SERVER_PORT',
    'FASTAPI_URL': 'CHAT_API_URL',
    
    // LLM Provider mappings
    'ANTHROPIC_API_KEY': 'LLM_ANTHROPIC_API_KEY',
    'GOOGLE_API_KEY': 'LLM_GOOGLE_API_KEY',
    'PERPLEXITY_API_KEY': 'LLM_PERPLEXITY_API_KEY',
    
    // Legacy naming patterns
    'OLLAMA_BASE_URL': 'LLM_OLLAMA_BASE_URL',
    'AZURE_OPENAI_API_KEY': 'LLM_AZURE_API_KEY',
    'AZURE_OPENAI_ENDPOINT': 'LLM_AZURE_ENDPOINT'
  };

  private static readonly DEPRECATED_VARIABLES = [
    'NEO4J_USER',
    'PORT',
    'PROXY_PORT',
    'FASTAPI_URL',
    'OLLAMA_BASE_URL'
  ];

  /**
   * Adapt legacy environment variables to new naming conventions
   */
  static adapt(): void {
    Object.entries(this.VARIABLE_MAPPINGS).forEach(([oldKey, newKey]) => {
      if (process.env[oldKey] && !process.env[newKey]) {
        process.env[newKey] = process.env[oldKey];
        
        if (this.DEPRECATED_VARIABLES.includes(oldKey)) {
          console.warn(`⚠️  Using legacy environment variable ${oldKey}. Please update to ${newKey}`);
        }
      }
    });
  }

  /**
   * Validate that all required environment variables are present
   */
  static validate(): string[] {
    const errors: string[] = [];
    const requiredVars = [
      'NEO4J_URI',
      'NEO4J_USERNAME', 
      'NEO4J_PASSWORD'
    ];
    
    requiredVars.forEach(varName => {
      if (!process.env[varName]) {
        errors.push(`Missing required environment variable: ${varName}`);
      }
    });
    
    return errors;
  }

  /**
   * Get configuration migration status
   */
  static getMigrationStatus(): {
    legacyVariablesFound: string[];
    newVariablesFound: string[];
    migrationComplete: boolean;
  } {
    const legacyVariablesFound: string[] = [];
    const newVariablesFound: string[] = [];

    Object.entries(this.VARIABLE_MAPPINGS).forEach(([oldKey, newKey]) => {
      if (process.env[oldKey]) {
        legacyVariablesFound.push(oldKey);
      }
      if (process.env[newKey]) {
        newVariablesFound.push(newKey);
      }
    });

    return {
      legacyVariablesFound,
      newVariablesFound,
      migrationComplete: legacyVariablesFound.length === 0
    };
  }

  /**
   * Generate migration guide for updating environment variables
   */
  static generateMigrationGuide(): string {
    const status = this.getMigrationStatus();
    
    if (status.migrationComplete) {
      return '✅ Configuration migration is complete. No legacy variables detected.';
    }

    let guide = '📋 Configuration Migration Guide:\n\n';
    guide += 'Please update the following environment variables:\n\n';

    status.legacyVariablesFound.forEach(oldVar => {
      const newVar = this.VARIABLE_MAPPINGS[oldVar];
      const value = process.env[oldVar];
      guide += `${oldVar}=${value} → ${newVar}=${value}\n`;
    });

    guide += '\n💡 After updating, remove the old variables to complete the migration.';
    
    return guide;
  }

  /**
   * Check if running in compatibility mode
   */
  static isCompatibilityMode(): boolean {
    const status = this.getMigrationStatus();
    return status.legacyVariablesFound.length > 0;
  }
}
