# Migration Feature Flags Configuration
# Copy these to your .env file and adjust as needed for gradual rollout

# Backend Architecture Migration Flags
FEATURE_FLAG_NEW_CONTROLLER_LAYER=false
FEATURE_FLAG_NEW_SERVICE_LAYER=false
FEATURE_FLAG_NEW_REPOSITORY_PATTERN=false
FEATURE_FLAG_NEW_MIDDLEWARE_STACK=false

# Frontend Architecture Migration Flags
FEATURE_FLAG_NEW_FEATURE_SLICE_ARCHITECTURE=false
FEATURE_FLAG_NEW_GRAPH_COMPONENTS=false
FEATURE_FLAG_NEW_CHAT_INTERFACE=false
FEATURE_FLAG_NEW_STATE_MANAGEMENT=false

# Service Extraction Flags
FEATURE_FLAG_NEW_CHAT_SERVICE=false
FEATURE_FLAG_CIRCUIT_BREAKER_ENABLED=true

# Migration Safety Flags
FEATURE_FLAG_DUAL_EXECUTION_MODE=true
FEATURE_FLAG_PERFORMANCE_MONITORING=true
FEATURE_FLAG_AUTOMATIC_ROLLBACK=true

# Traffic Routing (0-100 percentage)
FEATURE_FLAG_TRAFFIC_PERCENTAGE_NEW_API=0
FEATURE_FLAG_TRAFFIC_PERCENTAGE_NEW_UI=0

# Development & Testing Flags
FEATURE_FLAG_DEBUG_MODE=false
FEATURE_FLAG_VERBOSE_LOGGING=false
FEATURE_FLAG_MIGRATION_METRICS=true

# Migration Rollout Schedule (example)
# Week 1: Enable NEW_CONTROLLER_LAYER=true, TRAFFIC_PERCENTAGE_NEW_API=10
# Week 2: Enable NEW_SERVICE_LAYER=true, TRAFFIC_PERCENTAGE_NEW_API=25
# Week 3: Enable NEW_REPOSITORY_PATTERN=true, TRAFFIC_PERCENTAGE_NEW_API=50
# Week 4: Enable NEW_MIDDLEWARE_STACK=true, TRAFFIC_PERCENTAGE_NEW_API=75
# Week 5: TRAFFIC_PERCENTAGE_NEW_API=100, start frontend migration
