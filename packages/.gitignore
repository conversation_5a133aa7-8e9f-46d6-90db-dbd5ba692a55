# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Compiled output
dist/
build/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# IDE and editor files
.vscode/settings.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Migration and monitoring files
migration-*.log
rollback-*.log
baseline-metrics-*.json
alerts.json
metrics.json

# Backup files
backups/
*.backup
*.bak

# Test files
test-results/
playwright-report/
test-results.xml

# Docker
.dockerignore
docker-compose.override.yml

# Local development
.local/
.cache/

# Package manager lock files (keep only one)
# yarn.lock
# pnpm-lock.yaml

# TypeScript
*.tsbuildinfo

# Webpack
.webpack/

# Vite
.vite/

# Rollup
.rollup.cache/

# SvelteKit
.svelte-kit/

# Storybook
storybook-static/

# Cypress
cypress/videos/
cypress/screenshots/

# Playwright
/test-results/
/playwright-report/
/playwright/.cache/
