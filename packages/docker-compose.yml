version: '3.8'

services:
  # Neo4j Database
  neo4j:
    image: neo4j:5.13-community
    container_name: kg-neo4j
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      - NEO4J_AUTH=neo4j/password
      - NEO4J_PLUGINS=["graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=gds.*
      - NEO4J_dbms_memory_heap_initial__size=1G
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - kg-network
    healthcheck:
      test: ["CMD-SHELL", "cypher-shell -u neo4j -p password 'RETURN 1'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Graph API Service
  graph-api:
    build:
      context: .
      target: graph-api-production
    container_name: kg-graph-api
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - DATABASE_URI=neo4j://neo4j:7687
      - DATABASE_USERNAME=neo4j
      - DATABASE_PASSWORD=password
      - PORT=3002
      - CORS_ORIGIN=http://localhost:3000
      - LOG_LEVEL=info
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - kg-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3002/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped

  # Chat Service
  chat-service:
    build:
      context: .
      target: chat-service-production
    container_name: kg-chat-service
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
      - DATABASE_URI=neo4j://neo4j:7687
      - DATABASE_USERNAME=neo4j
      - DATABASE_PASSWORD=password
      - PORT=8000
      - API_BASE_URL=http://graph-api:3002
      - LOG_LEVEL=info
    depends_on:
      neo4j:
        condition: service_healthy
      graph-api:
        condition: service_healthy
    networks:
      - kg-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped

  # Web UI
  web-ui:
    build:
      context: .
      target: web-ui-production
    container_name: kg-web-ui
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://localhost:3002
      - VITE_CHAT_URL=http://localhost:8000
    depends_on:
      graph-api:
        condition: service_healthy
      chat-service:
        condition: service_healthy
    networks:
      - kg-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: kg-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - kg-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: kg-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - kg-network
    restart: unless-stopped

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: kg-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - kg-network
    restart: unless-stopped

networks:
  kg-network:
    driver: bridge
    name: kg-network

volumes:
  neo4j_data:
    name: kg-neo4j-data
  neo4j_logs:
    name: kg-neo4j-logs
  neo4j_import:
    name: kg-neo4j-import
  neo4j_plugins:
    name: kg-neo4j-plugins
  redis_data:
    name: kg-redis-data
  prometheus_data:
    name: kg-prometheus-data
  grafana_data:
    name: kg-grafana-data
