# Environment Configuration Example
# Copy this file to .env and update the values

# Application Environment
NODE_ENV=development

# Server Configuration
PORT=3003
HOST=0.0.0.0
CORS_ORIGIN=http://localhost:5173

# Database Configuration
DATABASE_URI=neo4j://localhost:7687
DATABASE_USERNAME=neo4j
DATABASE_PASSWORD=password
DATABASE_NAME=neo4j

# Security Configuration
JWT_SECRET=your-jwt-secret-here
SESSION_SECRET=your-session-secret-here
BCRYPT_ROUNDS=12

# API Keys
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key
GROQ_API_KEY=your-groq-api-key

# LLM Configuration
LLM_DEFAULT_PROVIDER=openai
LLM_DEFAULT_MODEL=gpt-3.5-turbo
LLM_MAX_TOKENS=4000
LLM_TEMPERATURE=0.7

# Chat Service Configuration
CHAT_SERVICE_PORT=8000
CHAT_SERVICE_URL=http://localhost:8000

# Frontend Configuration
VITE_API_URL=http://localhost:3003
VITE_CHAT_URL=http://localhost:8000
VITE_ENVIRONMENT=development

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=text

# Feature Flags
ENABLE_METRICS=true
ENABLE_SWAGGER=true
ENABLE_RATE_LIMIT=true
ENABLE_CACHING=true

# Monitoring Configuration
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
GRAFANA_PASSWORD=admin

# Email Configuration (for notifications)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

# Migration Configuration - LEGACY (Migration Complete)
# MIGRATION_ENABLED=false
# MIGRATION_PERCENTAGE=100
# MIGRATION_ROLLBACK_THRESHOLD=3.0

# Development Configuration
DEBUG=*
ENABLE_HOT_RELOAD=true
ENABLE_SOURCE_MAPS=true

# Testing Configuration
TEST_DATABASE_URI=neo4j://localhost:7687
TEST_DATABASE_NAME=test

# Docker Configuration
COMPOSE_PROJECT_NAME=kg-visualizer
COMPOSE_FILE=docker-compose.yml
