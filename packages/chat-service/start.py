#!/usr/bin/env python3
"""
Development startup script for the chat service.

This script starts the FastAPI chat service with development settings.
"""

import os
import sys
import uvicorn

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

if __name__ == "__main__":
    # Set development environment
    os.environ.setdefault("CHAT_SERVICE_ENVIRONMENT", "development")
    os.environ.setdefault("CHAT_SERVICE_LOG_LEVEL", "INFO")
    os.environ.setdefault("CHAT_SERVICE_HOST", "127.0.0.1")
    os.environ.setdefault("CHAT_SERVICE_PORT", "8001")
    
    # Start the server
    uvicorn.run(
        "chat_service.main:app",
        host=os.getenv("CHAT_SERVICE_HOST", "127.0.0.1"),
        port=int(os.getenv("CHAT_SERVICE_PORT", "8001")),
        reload=True,
        log_level="info",
        access_log=True,
    )
