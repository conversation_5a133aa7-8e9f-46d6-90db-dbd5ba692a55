# Knowledge Graph Chat Service

FastAPI-based chat service for the Knowledge Graph Visualizer. Provides LLM integration, knowledge graph queries, and conversation management with support for multiple LLM providers.

## Features

- **Multi-LLM Support**: OpenAI, Groq, and Google Gemini integration
- **Knowledge Graph Integration**: Neo4j-based semantic search and context
- **Streaming Responses**: Real-time chat with streaming support
- **Circuit Breaker Pattern**: Resilient service with automatic failover
- **Comprehensive Monitoring**: Prometheus metrics and structured logging
- **Health Checks**: Detailed health monitoring for all dependencies
- **Rate Limiting**: Built-in rate limiting and security middleware

## Quick Start

### Prerequisites

- Python 3.9+
- Poetry (for dependency management)
- Neo4j database
- LLM provider API keys (OpenAI, Groq, or Google)

### Installation

1. Install dependencies:
```bash
cd packages/chat-service
poetry install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Start the service:
```bash
poetry run uvicorn chat_service.main:app --reload
```

The service will be available at `http://localhost:8001`

## Configuration

The service uses environment variables with the `CHAT_SERVICE_` prefix:

### Core Settings
- `CHAT_SERVICE_HOST`: Server host (default: 127.0.0.1)
- `CHAT_SERVICE_PORT`: Server port (default: 8001)
- `CHAT_SERVICE_ENVIRONMENT`: Environment (development/staging/production)

### Database Settings
- `CHAT_SERVICE_NEO4J_URI`: Neo4j connection URI
- `CHAT_SERVICE_NEO4J_USER`: Neo4j username
- `CHAT_SERVICE_NEO4J_PASSWORD`: Neo4j password
- `CHAT_SERVICE_NEO4J_DATABASE`: Neo4j database name

### LLM Provider Settings
- `CHAT_SERVICE_OPENAI_API_KEY`: OpenAI API key
- `CHAT_SERVICE_GROQ_API_KEY`: Groq API key
- `CHAT_SERVICE_GOOGLE_API_KEY`: Google API key
- `CHAT_SERVICE_DEFAULT_LLM_PROVIDER`: Default provider (openai/groq/google)

## API Endpoints

### Health Checks
- `GET /health/` - Basic health check
- `GET /health/ready` - Readiness check with dependencies
- `GET /health/live` - Liveness check
- `GET /health/dependencies` - Detailed dependency status

### Chat API
- `POST /api/v1/chat/message` - Send chat message
- `POST /api/v1/chat/stream` - Stream chat response
- `GET /api/v1/chat/conversations` - List conversations
- `GET /api/v1/chat/conversations/{id}` - Get conversation details
- `DELETE /api/v1/chat/conversations/{id}` - Delete conversation

### Knowledge Graph API
- `POST /api/v1/kg/search` - Search knowledge graph
- `GET /api/v1/kg/entities/{id}` - Get entity details
- `GET /api/v1/kg/entities/{id}/relationships` - Get entity relationships
- `GET /api/v1/kg/context` - Get graph context
- `GET /api/v1/kg/stats` - Get graph statistics

### Monitoring
- `GET /metrics` - Prometheus metrics

## Development

### Running Tests
```bash
poetry run pytest
```

### Code Quality
```bash
# Format code
poetry run black .
poetry run isort .

# Lint code
poetry run flake8
poetry run mypy .
```

### Docker Development
```bash
# Build image
docker build -t chat-service .

# Run container
docker run -p 8001:8001 --env-file .env chat-service
```

## Architecture

The service follows a layered architecture:

- **API Layer**: FastAPI routes and request/response handling
- **Service Layer**: Business logic and orchestration
- **Core Layer**: Configuration, logging, exceptions, and middleware
- **External Integrations**: LLM providers and Neo4j database

### Key Components

1. **ChatService**: Main orchestration service
2. **LLMService**: Multi-provider LLM integration
3. **KnowledgeGraphService**: Neo4j operations and search
4. **Middleware**: Logging, metrics, rate limiting, request tracking

## Monitoring and Observability

### Structured Logging
- JSON-formatted logs in production
- Correlation IDs for request tracing
- Sensitive data filtering
- Contextual logging with metadata

### Metrics
- HTTP request metrics (count, duration, status codes)
- LLM provider response times
- Knowledge graph query performance
- Active request tracking

### Health Checks
- Service health monitoring
- Dependency health verification
- Circuit breaker status
- Performance metrics

## Security

- Rate limiting by IP address
- CORS configuration
- Trusted host middleware
- API key validation
- Sensitive data filtering in logs

## Production Deployment

### Environment Variables
Set all required environment variables for production:
- Database credentials
- LLM provider API keys
- Security settings
- Monitoring configuration

### Scaling Considerations
- Stateless design for horizontal scaling
- Redis for distributed rate limiting (recommended)
- Database connection pooling
- Load balancer health checks

### Monitoring Setup
- Prometheus metrics collection
- Log aggregation (ELK stack recommended)
- Error tracking (Sentry recommended)
- Performance monitoring

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run quality checks
5. Submit a pull request

## License

This project is licensed under the MIT License.
