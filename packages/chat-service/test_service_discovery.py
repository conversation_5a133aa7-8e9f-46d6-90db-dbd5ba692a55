#!/usr/bin/env python3
"""
Service Discovery and Health Monitoring Test

This script tests the service discovery, health monitoring,
and alerting functionality of the microservices architecture.
"""

import asyncio
import sys
import os
import time
import json

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from fastapi.testclient import TestClient
from chat_service.main import create_app
from chat_service.core.config import get_settings
from chat_service.core.service_discovery import ServiceRegistry, ServiceStatus
from chat_service.core.health_monitor import HealthMonitor, AlertLevel


async def test_service_registry():
    """Test service registry functionality."""
    print("🔧 Testing Service Registry")
    print("-" * 40)
    
    try:
        settings = get_settings()
        registry = ServiceRegistry(settings)
        
        # Test service registration
        print("  Testing service registration...")
        await registry.register_service(
            name="test-service",
            url="http://localhost:9999",
            version="1.0.0",
            metadata={"description": "Test service"},
            tags={"test", "api"}
        )
        
        # Test service discovery
        print("  Testing service discovery...")
        services = await registry.discover_services()
        
        if len(services) > 0:
            print(f"  ✅ Discovered {len(services)} services")
        else:
            print("  ⚠️  No services discovered")
        
        # Test service lookup
        print("  Testing service lookup...")
        service = await registry.get_service("test-service")
        
        if service and service.name == "test-service":
            print("  ✅ Service lookup successful")
        else:
            print("  ❌ Service lookup failed")
        
        # Test service deregistration
        print("  Testing service deregistration...")
        success = await registry.deregister_service("test-service")
        
        if success:
            print("  ✅ Service deregistration successful")
        else:
            print("  ❌ Service deregistration failed")
        
        # Cleanup
        await registry.cleanup()
        
        return True
        
    except Exception as e:
        print(f"  ❌ Service registry test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_health_monitoring():
    """Test health monitoring functionality."""
    print("\n💓 Testing Health Monitoring")
    print("-" * 40)
    
    try:
        settings = get_settings()
        registry = ServiceRegistry(settings)
        monitor = HealthMonitor(registry, settings)
        
        # Register test services
        print("  Setting up test services...")
        await registry.register_service(
            name="healthy-service",
            url="http://httpbin.org",  # Public test service
            version="1.0.0",
            tags={"test", "healthy"}
        )
        
        await registry.register_service(
            name="unhealthy-service",
            url="http://localhost:99999",  # Non-existent service
            version="1.0.0",
            tags={"test", "unhealthy"}
        )
        
        # Start monitoring
        print("  Starting health monitoring...")
        await monitor.start_monitoring()
        
        # Wait for initial health checks
        await asyncio.sleep(5)
        
        # Get health status
        print("  Testing health status retrieval...")
        health_status = await monitor.get_health_status(use_cache=False)
        
        if "overall_status" in health_status:
            print(f"  ✅ Health status retrieved: {health_status['overall_status']}")
        else:
            print("  ❌ Health status missing overall_status")
        
        # Test metrics collection
        print("  Testing metrics collection...")
        if monitor.metrics:
            print(f"  ✅ Metrics collected: {len(monitor.metrics)} types")
        else:
            print("  ⚠️  No metrics collected yet")
        
        # Test alert generation (simulate threshold breach)
        print("  Testing alert generation...")
        await monitor._generate_alert(
            level=AlertLevel.WARNING,
            title="Test Alert",
            description="This is a test alert",
            service_name="test-service",
            metric_name="test_metric"
        )
        
        alerts = await monitor.get_alerts(resolved=False, limit=10)
        if alerts:
            print(f"  ✅ Alerts generated: {len(alerts)} alerts")
        else:
            print("  ⚠️  No alerts found")
        
        # Cleanup
        await monitor.stop_monitoring()
        await registry.cleanup()
        
        return True
        
    except Exception as e:
        print(f"  ❌ Health monitoring test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_monitoring_api():
    """Test monitoring API endpoints."""
    print("\n🌐 Testing Monitoring API")
    print("-" * 40)
    
    try:
        # Create test app
        app = create_app()
        client = TestClient(app)
        
        # Test health endpoint
        print("  Testing health endpoint...")
        response = client.get("/api/v1/monitoring/health")
        
        if response.status_code == 200:
            print("  ✅ Health endpoint accessible")
            
            try:
                health_data = response.json()
                if "overall_status" in health_data:
                    print(f"  📊 Overall status: {health_data['overall_status']}")
                else:
                    print("  ⚠️  Health response missing overall_status")
            except json.JSONDecodeError:
                print("  ⚠️  Health response not valid JSON")
        else:
            print(f"  ❌ Health endpoint failed: {response.status_code}")
        
        # Test services endpoint
        print("  Testing services endpoint...")
        response = client.get("/api/v1/monitoring/services")
        
        if response.status_code == 200:
            print("  ✅ Services endpoint accessible")
            
            try:
                services_data = response.json()
                service_count = services_data.get("total_count", 0)
                print(f"  📊 Services discovered: {service_count}")
            except json.JSONDecodeError:
                print("  ⚠️  Services response not valid JSON")
        else:
            print(f"  ❌ Services endpoint failed: {response.status_code}")
        
        # Test service registration
        print("  Testing service registration...")
        registration_data = {
            "name": "test-api-service",
            "url": "http://localhost:8888",
            "version": "1.0.0",
            "tags": ["test", "api"],
            "metadata": {"description": "Test API service"}
        }
        
        response = client.post("/api/v1/monitoring/services/register", json=registration_data)
        
        if response.status_code == 200:
            print("  ✅ Service registration successful")
        else:
            print(f"  ❌ Service registration failed: {response.status_code}")
        
        # Test alerts endpoint
        print("  Testing alerts endpoint...")
        response = client.get("/api/v1/monitoring/alerts")
        
        if response.status_code == 200:
            print("  ✅ Alerts endpoint accessible")
            
            try:
                alerts_data = response.json()
                alert_count = alerts_data.get("total_count", 0)
                print(f"  📊 Alerts found: {alert_count}")
            except json.JSONDecodeError:
                print("  ⚠️  Alerts response not valid JSON")
        else:
            print(f"  ❌ Alerts endpoint failed: {response.status_code}")
        
        # Test metrics endpoint
        print("  Testing metrics endpoint...")
        response = client.get("/api/v1/monitoring/metrics")
        
        if response.status_code == 200:
            print("  ✅ Metrics endpoint accessible")
            
            try:
                metrics_data = response.json()
                metric_count = len(metrics_data.get("metrics", {}))
                print(f"  📊 Metrics available: {metric_count} types")
            except json.JSONDecodeError:
                print("  ⚠️  Metrics response not valid JSON")
        else:
            print(f"  ❌ Metrics endpoint failed: {response.status_code}")
        
        # Test registry status endpoint
        print("  Testing registry status endpoint...")
        response = client.get("/api/v1/monitoring/registry/status")
        
        if response.status_code == 200:
            print("  ✅ Registry status endpoint accessible")
            
            try:
                status_data = response.json()
                total_services = status_data.get("total_services", 0)
                healthy_services = status_data.get("healthy_services", 0)
                print(f"  📊 Registry: {healthy_services}/{total_services} services healthy")
            except json.JSONDecodeError:
                print("  ⚠️  Registry status response not valid JSON")
        else:
            print(f"  ❌ Registry status endpoint failed: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Monitoring API test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_service_filtering():
    """Test service discovery filtering."""
    print("\n🔍 Testing Service Filtering")
    print("-" * 40)
    
    try:
        settings = get_settings()
        registry = ServiceRegistry(settings)
        
        # Register multiple test services
        print("  Setting up test services...")
        
        services_to_register = [
            ("web-service", "http://localhost:3000", {"web", "frontend"}),
            ("api-service", "http://localhost:8000", {"api", "backend"}),
            ("db-service", "http://localhost:5432", {"database", "backend"}),
            ("cache-service", "http://localhost:6379", {"cache", "backend"})
        ]
        
        for name, url, tags in services_to_register:
            await registry.register_service(
                name=name,
                url=url,
                version="1.0.0",
                tags=tags
            )
        
        # Test tag filtering
        print("  Testing tag filtering...")
        backend_services = await registry.discover_services(tag="backend")
        
        if len(backend_services) >= 2:  # api-service, db-service, cache-service
            print(f"  ✅ Tag filtering works: {len(backend_services)} backend services")
        else:
            print(f"  ⚠️  Tag filtering may not work: {len(backend_services)} backend services")
        
        # Test status filtering
        print("  Testing status filtering...")
        healthy_services = await registry.discover_services(status=ServiceStatus.HEALTHY)
        
        print(f"  📊 Healthy services: {len(healthy_services)}")
        
        # Test combined filtering
        print("  Testing combined filtering...")
        healthy_backend = await registry.discover_services(tag="backend", status=ServiceStatus.HEALTHY)
        
        print(f"  📊 Healthy backend services: {len(healthy_backend)}")
        
        # Cleanup
        await registry.cleanup()
        
        return True
        
    except Exception as e:
        print(f"  ❌ Service filtering test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_alert_system():
    """Test alert system functionality."""
    print("\n🚨 Testing Alert System")
    print("-" * 40)
    
    try:
        settings = get_settings()
        registry = ServiceRegistry(settings)
        monitor = HealthMonitor(registry, settings)
        
        # Test alert generation
        print("  Testing alert generation...")
        
        alert_levels = [AlertLevel.INFO, AlertLevel.WARNING, AlertLevel.ERROR, AlertLevel.CRITICAL]
        
        for i, level in enumerate(alert_levels):
            await monitor._generate_alert(
                level=level,
                title=f"Test {level.value.title()} Alert",
                description=f"This is a test {level.value} alert #{i+1}",
                service_name="test-service",
                metric_name="test_metric",
                current_value=float(i * 10),
                threshold_value=float(i * 5)
            )
        
        # Test alert retrieval
        print("  Testing alert retrieval...")
        all_alerts = await monitor.get_alerts(limit=100)
        
        if len(all_alerts) >= len(alert_levels):
            print(f"  ✅ Alerts generated and retrieved: {len(all_alerts)} alerts")
        else:
            print(f"  ⚠️  Alert count mismatch: {len(all_alerts)} alerts")
        
        # Test alert filtering by level
        print("  Testing alert filtering...")
        critical_alerts = await monitor.get_alerts(level=AlertLevel.CRITICAL, limit=10)
        
        if len(critical_alerts) >= 1:
            print(f"  ✅ Alert filtering works: {len(critical_alerts)} critical alerts")
        else:
            print("  ⚠️  Alert filtering may not work")
        
        # Test alert filtering by service
        service_alerts = await monitor.get_alerts(service_name="test-service", limit=10)
        
        if len(service_alerts) >= len(alert_levels):
            print(f"  ✅ Service filtering works: {len(service_alerts)} service alerts")
        else:
            print(f"  ⚠️  Service filtering may not work: {len(service_alerts)} service alerts")
        
        # Cleanup
        await monitor.cleanup()
        await registry.cleanup()
        
        return True
        
    except Exception as e:
        print(f"  ❌ Alert system test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all service discovery and health monitoring tests."""
    print("🚀 Service Discovery & Health Monitoring Test Suite")
    print("🔧 Microservices Architecture Testing")
    print("=" * 60)
    
    # Show configuration
    settings = get_settings()
    print(f"Environment: {settings.environment}")
    print(f"FastAPI URL: {getattr(settings, 'fastapi_url', 'Not set')}")
    print(f"Service API Key: {'Set' if settings.service_api_key else 'Not set'}")
    
    # Run tests
    tests = [
        ("Service Registry", test_service_registry),
        ("Health Monitoring", test_health_monitoring),
        ("Monitoring API", test_monitoring_api),
        ("Service Filtering", test_service_filtering),
        ("Alert System", test_alert_system),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ Test '{test_name}' crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Service Discovery & Health Monitoring Test Results")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All service discovery and health monitoring tests passed!")
    elif passed >= total * 0.8:
        print("⚠️  Most tests passed. Minor issues may need attention.")
    else:
        print("🚨 Several tests failed. Service discovery needs work.")
    
    return passed / total


if __name__ == "__main__":
    asyncio.run(main())
