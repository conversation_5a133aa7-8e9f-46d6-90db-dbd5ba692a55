#!/usr/bin/env python3
"""
LLM Integration Migration Demonstration

This script demonstrates the migrated LLM integration functionality,
showing how the enhanced services work with different providers and
knowledge graph integration.
"""

import asyncio
import sys
import os
import time

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from chat_service.services.enhanced_llm_service import EnhancedLLMService
from chat_service.services.enhanced_kg_service import EnhancedKnowledgeGraphService
from chat_service.services.dependencies import get_chat_service, get_llm_service, get_kg_service
from chat_service.core.config import get_settings


async def demonstrate_enhanced_llm_service():
    """Demonstrate the enhanced LLM service functionality."""
    print("🤖 Enhanced LLM Service Demonstration")
    print("=" * 50)
    
    try:
        # Get the LLM service (will be enhanced if configured)
        llm_service = get_llm_service()
        service_type = type(llm_service).__name__
        
        print(f"LLM Service Type: {service_type}")
        
        # Test health check
        health = await llm_service.health_check()
        print(f"Health Status: {health['status']}")
        print(f"Available Providers: {list(health.get('providers', {}).keys())}")
        
        # Test message generation
        test_messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello! Can you tell me about knowledge graphs?"}
        ]
        
        print("\n📝 Testing Message Generation:")
        start_time = time.time()
        
        if hasattr(llm_service, 'providers') and 'ollama' in llm_service.providers:
            # Test Ollama if available
            print("  Testing Ollama provider...")
            try:
                response = await llm_service.generate_response(
                    messages=test_messages,
                    provider="ollama",
                    options={"max_tokens": 100}
                )
                duration = time.time() - start_time
                print(f"  ✅ Ollama Response ({duration:.2f}s): {response['content'][:100]}...")
                print(f"     Model: {response.get('model', 'unknown')}")
                print(f"     Tokens: {response.get('tokens', 0)}")
            except Exception as e:
                print(f"  ❌ Ollama Error: {str(e)}")
        
        # Test default provider
        print(f"\n  Testing default provider...")
        try:
            start_time = time.time()
            response = await llm_service.generate_response(
                messages=test_messages,
                options={"max_tokens": 100}
            )
            duration = time.time() - start_time
            print(f"  ✅ Default Response ({duration:.2f}s): {response['content'][:100]}...")
            print(f"     Provider: {response.get('provider', 'unknown')}")
            print(f"     Model: {response.get('model', 'unknown')}")
        except Exception as e:
            print(f"  ❌ Default Provider Error: {str(e)}")
        
        # Test streaming if available
        if hasattr(llm_service, 'stream_response'):
            print(f"\n  Testing streaming response...")
            try:
                print("  🔄 Streaming: ", end="", flush=True)
                chunk_count = 0
                async for chunk in llm_service.stream_response(
                    messages=test_messages,
                    options={"max_tokens": 50}
                ):
                    if chunk.get("content"):
                        print(chunk["content"], end="", flush=True)
                        chunk_count += 1
                        if chunk_count > 20:  # Limit output
                            break
                print(f"\n  ✅ Streaming completed ({chunk_count} chunks)")
            except Exception as e:
                print(f"\n  ❌ Streaming Error: {str(e)}")
        
    except Exception as e:
        print(f"❌ LLM Service Error: {str(e)}")
        import traceback
        traceback.print_exc()


async def demonstrate_enhanced_kg_service():
    """Demonstrate the enhanced knowledge graph service functionality."""
    print("\n🕸️  Enhanced Knowledge Graph Service Demonstration")
    print("=" * 50)
    
    try:
        # Get the KG service (will be enhanced if configured)
        kg_service = get_kg_service()
        service_type = type(kg_service).__name__
        
        print(f"KG Service Type: {service_type}")
        
        # Test health check
        health = await kg_service.health_check()
        print(f"Health Status: {health['status']}")
        print(f"Database: {health.get('database', 'unknown')}")
        print(f"Graphiti Available: {health.get('graphiti_available', False)}")
        
        # Test search functionality
        test_queries = [
            "user guides",
            "knowledge graph",
            "API documentation",
            "tutorial"
        ]
        
        print("\n🔍 Testing Search Functionality:")
        for query in test_queries:
            try:
                start_time = time.time()
                results = await kg_service.search(
                    query=query,
                    limit=3,
                    filters={"group_id": "user_guides"}
                )
                duration = time.time() - start_time
                
                print(f"  Query: '{query}' ({duration:.2f}s)")
                print(f"    Results: {results['total_count']}")
                print(f"    Method: {results.get('search_method', 'unknown')}")
                
                if results.get('context'):
                    context_preview = results['context'][:100] + "..." if len(results['context']) > 100 else results['context']
                    print(f"    Context: {context_preview}")
                
                if results.get('citations'):
                    print(f"    Citations: {len(results['citations'])}")
                
            except Exception as e:
                print(f"  ❌ Query '{query}' failed: {str(e)}")
        
        # Test LLM context formatting if available
        if hasattr(kg_service, 'get_context_for_llm'):
            print("\n📝 Testing LLM Context Formatting:")
            try:
                context_result = await kg_service.get_context_for_llm(
                    query="How do I use the knowledge graph?",
                    max_context_length=500
                )
                
                print(f"  Entities Found: {context_result['entities_found']}")
                print(f"  Citations: {len(context_result['citations'])}")
                print(f"  Truncated: {context_result.get('truncated', False)}")
                
                if context_result['context']:
                    context_preview = context_result['context'][:200] + "..." if len(context_result['context']) > 200 else context_result['context']
                    print(f"  Formatted Context: {context_preview}")
                
            except Exception as e:
                print(f"  ❌ LLM Context Error: {str(e)}")
        
    except Exception as e:
        print(f"❌ KG Service Error: {str(e)}")
        import traceback
        traceback.print_exc()


async def demonstrate_integrated_chat_service():
    """Demonstrate the integrated chat service with enhanced components."""
    print("\n💬 Integrated Chat Service Demonstration")
    print("=" * 50)
    
    try:
        # Get the chat service (uses enhanced components)
        chat_service = get_chat_service()
        service_type = type(chat_service).__name__
        
        print(f"Chat Service Type: {service_type}")
        
        # Test health check
        health = await chat_service.health_check()
        print(f"Health Status: {health['status']}")
        print(f"Service Mode: {health.get('mode', 'unknown')}")
        
        # Test chat with knowledge graph integration
        test_messages = [
            "Hello! Can you help me understand knowledge graphs?",
            "What are the main components of a knowledge graph?",
            "How do I query a knowledge graph?",
            "Can you give me an example of using Neo4j?"
        ]
        
        print("\n💬 Testing Chat with Knowledge Graph Integration:")
        conversation_id = None
        
        for i, message in enumerate(test_messages, 1):
            try:
                print(f"\n  Message {i}: {message}")
                
                start_time = time.time()
                response = await chat_service.process_message(
                    message=message,
                    conversation_id=conversation_id,
                    options={
                        "group_id": "user_guides",
                        "max_tokens": 150
                    }
                )
                duration = time.time() - start_time
                
                conversation_id = response["conversation_id"]
                service_mode = response["metadata"].get("service_mode", "unknown")
                kg_context_used = response["metadata"].get("kg_context_used", False)
                
                content_preview = response["message"]["content"][:200] + "..." if len(response["message"]["content"]) > 200 else response["message"]["content"]
                
                print(f"  ✅ Response ({duration:.2f}s, {service_mode}):")
                print(f"     KG Context: {'Yes' if kg_context_used else 'No'}")
                print(f"     Content: {content_preview}")
                
                # Show statistics if available
                if hasattr(chat_service, 'get_statistics'):
                    stats = chat_service.get_statistics()
                    print(f"     Stats: {stats.get('primary_success_rate', 0):.2f} success rate")
                
            except Exception as e:
                print(f"  ❌ Message {i} failed: {str(e)}")
        
        # Test conversation management
        print(f"\n📚 Testing Conversation Management:")
        try:
            conversations = await chat_service.list_conversations(limit=5)
            print(f"  Total Conversations: {len(conversations)}")
            
            if conversation_id:
                conversation = await chat_service.get_conversation(conversation_id)
                if conversation:
                    print(f"  Current Conversation Messages: {len(conversation.get('messages', []))}")
        except Exception as e:
            print(f"  ❌ Conversation Management Error: {str(e)}")
        
    except Exception as e:
        print(f"❌ Chat Service Error: {str(e)}")
        import traceback
        traceback.print_exc()


async def main():
    """Run all migration demonstrations."""
    print("🚀 LLM Integration Migration Demonstration")
    print("🔧 Knowledge Graph Chat Service")
    print("=" * 60)
    
    # Show configuration
    settings = get_settings()
    print(f"Environment: {settings.environment}")
    print(f"Default LLM Provider: {settings.default_llm_provider}")
    print(f"Neo4j URI: {settings.neo4j_uri}")
    print(f"Enhanced Services: {os.getenv('USE_ENHANCED_LLM', 'auto')}/{os.getenv('USE_ENHANCED_KG', 'true')}")
    
    try:
        await demonstrate_enhanced_llm_service()
        await demonstrate_enhanced_kg_service()
        await demonstrate_integrated_chat_service()
        
        print("\n✅ All migration demonstrations completed successfully!")
        print("\n📋 Migration Summary:")
        print("  • Enhanced LLM service with Ollama, OpenAI, Groq, and Google support")
        print("  • Enhanced KG service with Graphiti integration and LLM context formatting")
        print("  • Integrated chat service with knowledge graph context")
        print("  • Circuit breaker protection and fallback mechanisms")
        print("  • Streaming support and performance monitoring")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demonstration interrupted by user")
    except Exception as e:
        print(f"\n❌ Demonstration failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
