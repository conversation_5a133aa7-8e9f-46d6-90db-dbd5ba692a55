"""
Chat Service Client

This module provides a client for integrating with the FastAPI chat service
from the existing Node.js backend.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, AsyncGenerator

import httpx
import structlog

from chat_service.core.exceptions import ChatServiceException


class ChatServiceClient:
    """
    Client for communicating with the FastAPI chat service.
    
    This client can be used by the Node.js backend to communicate
    with the chat service via HTTP.
    """
    
    def __init__(
        self,
        base_url: str = "http://localhost:8001",
        timeout: float = 30.0,
        api_key: Optional[str] = None
    ) -> None:
        """
        Initialize chat service client.
        
        Args:
            base_url: Base URL of the chat service
            timeout: Request timeout in seconds
            api_key: Optional API key for authentication
        """
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
        self.api_key = api_key
        self.logger = structlog.get_logger("chat_service_client")
        
        # Create HTTP client
        headers = {"Content-Type": "application/json"}
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"
        
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=timeout,
            headers=headers
        )
    
    async def send_message(
        self,
        message: str,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Send a chat message and get a response.
        
        Args:
            message: User message
            conversation_id: Optional conversation ID
            context: Additional context
            options: Chat options
            
        Returns:
            Dict[str, Any]: Chat response
            
        Raises:
            ChatServiceException: If request fails
        """
        try:
            payload = {
                "message": message,
                "conversation_id": conversation_id,
                "context": context or {},
                "options": options or {}
            }
            
            response = await self.client.post(
                "/api/v1/chat/message",
                json=payload
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                error_data = response.json() if response.content else {}
                raise ChatServiceException(
                    f"Chat request failed: {response.status_code}",
                    error_code="CHAT_REQUEST_FAILED",
                    status_code=response.status_code,
                    details=error_data
                )
                
        except httpx.RequestError as e:
            self.logger.error("Chat request error", error=str(e))
            raise ChatServiceException(
                f"Failed to connect to chat service: {str(e)}",
                error_code="CONNECTION_ERROR"
            )
    
    async def stream_message(
        self,
        message: str,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Send a chat message and get a streaming response.
        
        Args:
            message: User message
            conversation_id: Optional conversation ID
            context: Additional context
            options: Chat options
            
        Yields:
            Dict[str, Any]: Response chunks
            
        Raises:
            ChatServiceException: If request fails
        """
        try:
            payload = {
                "message": message,
                "conversation_id": conversation_id,
                "context": context or {},
                "options": options or {}
            }
            
            async with self.client.stream(
                "POST",
                "/api/v1/chat/stream",
                json=payload
            ) as response:
                if response.status_code != 200:
                    error_data = await response.aread()
                    raise ChatServiceException(
                        f"Streaming request failed: {response.status_code}",
                        error_code="STREAMING_REQUEST_FAILED",
                        status_code=response.status_code
                    )
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            data = json.loads(line[6:])  # Remove "data: " prefix
                            yield data
                        except json.JSONDecodeError:
                            continue
                            
        except httpx.RequestError as e:
            self.logger.error("Streaming request error", error=str(e))
            raise ChatServiceException(
                f"Failed to connect to chat service: {str(e)}",
                error_code="CONNECTION_ERROR"
            )
    
    async def list_conversations(
        self,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        List conversations.
        
        Args:
            limit: Maximum number of conversations
            offset: Number of conversations to skip
            
        Returns:
            List[Dict[str, Any]]: List of conversations
            
        Raises:
            ChatServiceException: If request fails
        """
        try:
            response = await self.client.get(
                "/api/v1/chat/conversations",
                params={"limit": limit, "offset": offset}
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                raise ChatServiceException(
                    f"Failed to list conversations: {response.status_code}",
                    error_code="LIST_CONVERSATIONS_FAILED",
                    status_code=response.status_code
                )
                
        except httpx.RequestError as e:
            self.logger.error("List conversations error", error=str(e))
            raise ChatServiceException(
                f"Failed to connect to chat service: {str(e)}",
                error_code="CONNECTION_ERROR"
            )
    
    async def get_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """
        Get conversation details.
        
        Args:
            conversation_id: Conversation identifier
            
        Returns:
            Dict[str, Any]: Conversation details
            
        Raises:
            ChatServiceException: If request fails
        """
        try:
            response = await self.client.get(
                f"/api/v1/chat/conversations/{conversation_id}"
            )
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                raise ChatServiceException(
                    "Conversation not found",
                    error_code="CONVERSATION_NOT_FOUND",
                    status_code=404
                )
            else:
                raise ChatServiceException(
                    f"Failed to get conversation: {response.status_code}",
                    error_code="GET_CONVERSATION_FAILED",
                    status_code=response.status_code
                )
                
        except httpx.RequestError as e:
            self.logger.error("Get conversation error", error=str(e))
            raise ChatServiceException(
                f"Failed to connect to chat service: {str(e)}",
                error_code="CONNECTION_ERROR"
            )
    
    async def search_knowledge_graph(
        self,
        query: str,
        limit: int = 10,
        threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Search the knowledge graph.
        
        Args:
            query: Search query
            limit: Maximum results
            threshold: Similarity threshold
            filters: Search filters
            
        Returns:
            Dict[str, Any]: Search results
            
        Raises:
            ChatServiceException: If request fails
        """
        try:
            payload = {
                "query": query,
                "limit": limit,
                "threshold": threshold,
                "filters": filters or {}
            }
            
            response = await self.client.post(
                "/api/v1/kg/search",
                json=payload
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                raise ChatServiceException(
                    f"Knowledge graph search failed: {response.status_code}",
                    error_code="KG_SEARCH_FAILED",
                    status_code=response.status_code
                )
                
        except httpx.RequestError as e:
            self.logger.error("Knowledge graph search error", error=str(e))
            raise ChatServiceException(
                f"Failed to connect to chat service: {str(e)}",
                error_code="CONNECTION_ERROR"
            )
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check.
        
        Returns:
            Dict[str, Any]: Health status
            
        Raises:
            ChatServiceException: If health check fails
        """
        try:
            response = await self.client.get("/health/")
            
            if response.status_code == 200:
                return response.json()
            else:
                raise ChatServiceException(
                    f"Health check failed: {response.status_code}",
                    error_code="HEALTH_CHECK_FAILED",
                    status_code=response.status_code
                )
                
        except httpx.RequestError as e:
            self.logger.error("Health check error", error=str(e))
            raise ChatServiceException(
                f"Failed to connect to chat service: {str(e)}",
                error_code="CONNECTION_ERROR"
            )
    
    async def close(self) -> None:
        """Close the HTTP client."""
        await self.client.aclose()
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()


# Convenience function for Node.js integration
async def create_chat_client(
    base_url: str = "http://localhost:8001",
    timeout: float = 30.0,
    api_key: Optional[str] = None
) -> ChatServiceClient:
    """
    Create a chat service client.
    
    Args:
        base_url: Base URL of the chat service
        timeout: Request timeout
        api_key: Optional API key
        
    Returns:
        ChatServiceClient: Configured client instance
    """
    return ChatServiceClient(
        base_url=base_url,
        timeout=timeout,
        api_key=api_key
    )
