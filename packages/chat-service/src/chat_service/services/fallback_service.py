"""
Fallback Service

This module provides fallback functionality to the legacy Python script
when the main chat service fails. Ensures zero-downtime migration.
"""

import asyncio
import json
import os
import subprocess
import tempfile
import time
from typing import Dict, List, Optional, Any

import structlog

from chat_service.core.config import Settings
from chat_service.core.exceptions import ExternalServiceError
from chat_service.core.logging import LoggerMixin


class LegacyPythonScriptService(LoggerMixin):
    """
    Service for executing the legacy Python script as a fallback.
    
    This service provides a bridge to the existing Python script
    when the new FastAPI service is unavailable.
    """
    
    def __init__(
        self,
        script_path: str,
        python_executable: str = "python",
        timeout: float = 60.0,
        settings: Optional[Settings] = None
    ) -> None:
        """
        Initialize legacy script service.
        
        Args:
            script_path: Path to the legacy Python script
            python_executable: Python executable to use
            timeout: Script execution timeout
            settings: Application settings
        """
        self.script_path = script_path
        self.python_executable = python_executable
        self.timeout = timeout
        self.settings = settings
        
        # Verify script exists
        if not os.path.exists(script_path):
            raise FileNotFoundError(f"Legacy script not found: {script_path}")
        
        self.logger.info(
            "Legacy Python script service initialized",
            script_path=script_path,
            python_executable=python_executable,
            timeout=timeout
        )
    
    async def process_message(
        self,
        message: str,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a message using the legacy Python script.
        
        Args:
            message: User message
            conversation_id: Optional conversation ID
            context: Additional context
            options: Processing options
            
        Returns:
            Dict[str, Any]: Response from legacy script
            
        Raises:
            ExternalServiceError: If script execution fails
        """
        start_time = time.time()
        
        self.log_method_call(
            "process_message",
            message_length=len(message),
            conversation_id=conversation_id
        )
        
        try:
            # Prepare input data for the script
            input_data = {
                "message": message,
                "conversation_id": conversation_id,
                "context": context or {},
                "options": options or {},
                "timestamp": time.time()
            }
            
            # Execute the script
            result = await self._execute_script(input_data)
            
            # Parse and validate result
            response = self._parse_script_output(result)
            
            execution_time = time.time() - start_time
            
            self.log_method_result(
                "process_message",
                conversation_id=conversation_id,
                execution_time_ms=round(execution_time * 1000, 2),
                response_length=len(response.get("content", ""))
            )
            
            return response
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.log_error(
                e,
                "process_message",
                conversation_id=conversation_id,
                execution_time_ms=round(execution_time * 1000, 2)
            )
            raise ExternalServiceError(
                f"Legacy script execution failed: {str(e)}",
                service_name="legacy_python_script",
                service_error=str(e)
            )
    
    async def _execute_script(self, input_data: Dict[str, Any]) -> str:
        """
        Execute the legacy Python script with input data.
        
        Args:
            input_data: Input data for the script
            
        Returns:
            str: Script output
            
        Raises:
            ExternalServiceError: If script execution fails
        """
        # Create temporary input file
        with tempfile.NamedTemporaryFile(
            mode='w',
            suffix='.json',
            delete=False
        ) as temp_file:
            json.dump(input_data, temp_file, indent=2)
            temp_input_path = temp_file.name
        
        try:
            # Prepare command
            cmd = [
                self.python_executable,
                self.script_path,
                "--input", temp_input_path,
                "--output", "json"
            ]
            
            # Add environment variables if needed
            env = os.environ.copy()
            if self.settings:
                env.update({
                    "NEO4J_URI": self.settings.neo4j_uri,
                    "NEO4J_USER": self.settings.neo4j_user,
                    "NEO4J_PASSWORD": self.settings.neo4j_password,
                    "NEO4J_DATABASE": self.settings.neo4j_database,
                    "OPENAI_API_KEY": self.settings.openai_api_key or "",
                    "GROQ_API_KEY": self.settings.groq_api_key or "",
                    "GOOGLE_API_KEY": self.settings.google_api_key or "",
                })
            
            self.logger.debug(
                "Executing legacy script",
                command=cmd,
                input_file=temp_input_path
            )
            
            # Execute script
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=self.timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                raise ExternalServiceError(
                    f"Legacy script timed out after {self.timeout} seconds",
                    service_name="legacy_python_script"
                )
            
            # Check return code
            if process.returncode != 0:
                error_msg = stderr.decode('utf-8') if stderr else "Unknown error"
                raise ExternalServiceError(
                    f"Legacy script failed with code {process.returncode}: {error_msg}",
                    service_name="legacy_python_script",
                    service_error=error_msg
                )
            
            return stdout.decode('utf-8')
            
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_input_path)
            except OSError:
                pass
    
    def _parse_script_output(self, output: str) -> Dict[str, Any]:
        """
        Parse the output from the legacy script.
        
        Args:
            output: Raw script output
            
        Returns:
            Dict[str, Any]: Parsed response
            
        Raises:
            ExternalServiceError: If output parsing fails
        """
        try:
            # Try to parse as JSON
            data = json.loads(output.strip())
            
            # Validate required fields
            if not isinstance(data, dict):
                raise ValueError("Output must be a JSON object")
            
            # Convert to expected format
            response = {
                "message": {
                    "id": data.get("message_id", "legacy-" + str(int(time.time()))),
                    "role": "assistant",
                    "content": data.get("response", data.get("content", "")),
                    "timestamp": data.get("timestamp", time.time()),
                    "metadata": {
                        "source": "legacy_script",
                        "model": data.get("model"),
                        "provider": data.get("provider"),
                        "tokens": data.get("tokens"),
                        "execution_time_ms": data.get("execution_time_ms")
                    }
                },
                "conversation_id": data.get("conversation_id"),
                "metadata": {
                    "source": "legacy_script",
                    "fallback": True
                }
            }
            
            return response
            
        except (json.JSONDecodeError, ValueError) as e:
            # If JSON parsing fails, treat as plain text response
            self.logger.warning(
                "Failed to parse script output as JSON, treating as plain text",
                error=str(e),
                output_preview=output[:200]
            )
            
            return {
                "message": {
                    "id": "legacy-" + str(int(time.time())),
                    "role": "assistant",
                    "content": output.strip(),
                    "timestamp": time.time(),
                    "metadata": {
                        "source": "legacy_script",
                        "format": "plain_text"
                    }
                },
                "conversation_id": None,
                "metadata": {
                    "source": "legacy_script",
                    "fallback": True,
                    "format": "plain_text"
                }
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the legacy script.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        start_time = time.time()
        
        try:
            # Test with a simple message
            test_input = {
                "message": "Hello, this is a health check",
                "conversation_id": None,
                "context": {},
                "options": {"max_tokens": 10},
                "timestamp": time.time()
            }
            
            await self._execute_script(test_input)
            
            response_time = round((time.time() - start_time) * 1000, 2)
            
            return {
                "status": "healthy",
                "response_time_ms": response_time,
                "script_path": self.script_path,
                "python_executable": self.python_executable
            }
            
        except Exception as e:
            response_time = round((time.time() - start_time) * 1000, 2)
            
            return {
                "status": "unhealthy",
                "error": str(e),
                "response_time_ms": response_time,
                "script_path": self.script_path,
                "python_executable": self.python_executable
            }
    
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        self.logger.info("Cleaning up legacy Python script service")
        # No specific cleanup needed for this service


class FallbackChatService(LoggerMixin):
    """
    Fallback chat service that uses the legacy Python script.
    
    This service provides the same interface as the main chat service
    but uses the legacy implementation as a fallback.
    """
    
    def __init__(
        self,
        legacy_service: Optional[LegacyPythonScriptService],
        settings: Optional[Settings] = None
    ) -> None:
        """
        Initialize fallback chat service.

        Args:
            legacy_service: Legacy Python script service (optional)
            settings: Application settings
        """
        self.legacy_service = legacy_service
        self.settings = settings

        self.logger.info(
            "Fallback chat service initialized",
            has_legacy_service=legacy_service is not None
        )
    
    async def process_message(
        self,
        message: str,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a message using the fallback service.
        
        Args:
            message: User message
            conversation_id: Optional conversation ID
            context: Additional context
            options: Processing options
            
        Returns:
            Dict[str, Any]: Response from fallback service
        """
        self.logger.info(
            "Processing message with fallback service",
            conversation_id=conversation_id,
            message_length=len(message),
            has_legacy_service=self.legacy_service is not None
        )

        if self.legacy_service:
            return await self.legacy_service.process_message(
                message=message,
                conversation_id=conversation_id,
                context=context,
                options=options
            )
        else:
            # Return a simple error response if no legacy service available
            return {
                "message": {
                    "id": f"fallback-error-{int(time.time())}",
                    "role": "assistant",
                    "content": "I apologize, but the service is currently unavailable. Please try again later.",
                    "timestamp": time.time(),
                    "metadata": {
                        "source": "fallback_error",
                        "error": "No legacy service available"
                    }
                },
                "conversation_id": conversation_id,
                "metadata": {
                    "source": "fallback_error",
                    "fallback": True,
                    "error": "No legacy service configured"
                }
            }
    
    async def list_conversations(
        self,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        List conversations (fallback implementation).
        
        Args:
            limit: Maximum number of conversations
            offset: Number of conversations to skip
            
        Returns:
            List[Dict[str, Any]]: Empty list (legacy script doesn't support this)
        """
        self.logger.warning(
            "Conversation listing not supported in fallback mode"
        )
        return []
    
    async def get_conversation(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get conversation details (fallback implementation).
        
        Args:
            conversation_id: Conversation identifier
            
        Returns:
            Optional[Dict[str, Any]]: None (legacy script doesn't support this)
        """
        self.logger.warning(
            "Conversation retrieval not supported in fallback mode",
            conversation_id=conversation_id
        )
        return None
    
    async def delete_conversation(self, conversation_id: str) -> bool:
        """
        Delete a conversation (fallback implementation).
        
        Args:
            conversation_id: Conversation identifier
            
        Returns:
            bool: False (legacy script doesn't support this)
        """
        self.logger.warning(
            "Conversation deletion not supported in fallback mode",
            conversation_id=conversation_id
        )
        return False
    
    async def update_conversation_title(
        self,
        conversation_id: str,
        title: str
    ) -> bool:
        """
        Update conversation title (fallback implementation).
        
        Args:
            conversation_id: Conversation identifier
            title: New title
            
        Returns:
            bool: False (legacy script doesn't support this)
        """
        self.logger.warning(
            "Conversation title update not supported in fallback mode",
            conversation_id=conversation_id
        )
        return False
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check.

        Returns:
            Dict[str, Any]: Health check results
        """
        if self.legacy_service:
            legacy_health = await self.legacy_service.health_check()

            return {
                "status": legacy_health["status"],
                "response_time_ms": legacy_health["response_time_ms"],
                "mode": "fallback",
                "legacy_script": legacy_health
            }
        else:
            return {
                "status": "degraded",
                "response_time_ms": 0,
                "mode": "fallback",
                "error": "No legacy service configured"
            }
    
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        self.logger.info("Cleaning up fallback chat service")
        if self.legacy_service:
            await self.legacy_service.cleanup()
