"""
Resilient Chat Service

This module provides a resilient chat service that uses circuit breakers
and fallback mechanisms to ensure high availability and zero-downtime migration.
"""

import time
from typing import Dict, List, Optional, Any, AsyncGenerator

import structlog

from chat_service.core.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    get_circuit_breaker
)
from chat_service.core.config import Settings
from chat_service.core.exceptions import CircuitBreakerError, ExternalServiceError
from chat_service.core.logging import LoggerMixin
from chat_service.services.chat_service import ChatService
from chat_service.services.fallback_service import FallbackChatService


class ResilientChatService(LoggerMixin):
    """
    Resilient chat service with circuit breaker and fallback mechanisms.
    
    This service wraps the main chat service with circuit breakers and
    provides automatic fallback to the legacy Python script when needed.
    """
    
    def __init__(
        self,
        primary_service: ChatService,
        fallback_service: FallbackChatService,
        settings: Settings
    ) -> None:
        """
        Initialize resilient chat service.
        
        Args:
            primary_service: Primary chat service
            fallback_service: Fallback chat service
            settings: Application settings
        """
        self.primary_service = primary_service
        self.fallback_service = fallback_service
        self.settings = settings
        
        # Create circuit breakers for different operations
        self.chat_breaker = get_circuit_breaker(
            "chat_service",
            CircuitBreakerConfig(
                failure_threshold=settings.circuit_breaker_failure_threshold,
                recovery_timeout=settings.circuit_breaker_recovery_timeout,
                expected_exception=Exception,
                name="chat_service"
            )
        )
        
        self.llm_breaker = get_circuit_breaker(
            "llm_service",
            CircuitBreakerConfig(
                failure_threshold=3,  # More sensitive for LLM failures
                recovery_timeout=30.0,
                expected_exception=Exception,
                name="llm_service"
            )
        )
        
        self.kg_breaker = get_circuit_breaker(
            "knowledge_graph",
            CircuitBreakerConfig(
                failure_threshold=5,
                recovery_timeout=60.0,
                expected_exception=Exception,
                name="knowledge_graph"
            )
        )
        
        # Statistics tracking
        self.stats = {
            "total_requests": 0,
            "primary_success": 0,
            "fallback_used": 0,
            "circuit_breaker_trips": 0,
            "last_fallback_time": None
        }
        
        self.logger.info(
            "Resilient chat service initialized",
            circuit_breakers=["chat_service", "llm_service", "knowledge_graph"]
        )
    
    async def process_message(
        self,
        message: str,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a message with resilience and fallback.
        
        Args:
            message: User message
            conversation_id: Optional conversation ID
            context: Additional context
            options: Processing options
            
        Returns:
            Dict[str, Any]: Response from primary or fallback service
        """
        start_time = time.time()
        self.stats["total_requests"] += 1
        
        self.log_method_call(
            "process_message",
            conversation_id=conversation_id,
            message_length=len(message),
            total_requests=self.stats["total_requests"]
        )
        
        # Try primary service with circuit breaker
        try:
            response = await self.chat_breaker.call(
                self.primary_service.process_message,
                message=message,
                conversation_id=conversation_id,
                context=context,
                options=options
            )
            
            self.stats["primary_success"] += 1
            execution_time = time.time() - start_time
            
            # Add resilience metadata
            response["metadata"] = response.get("metadata", {})
            response["metadata"].update({
                "service_mode": "primary",
                "circuit_breaker_state": self.chat_breaker.stats.current_state.value,
                "execution_time_ms": round(execution_time * 1000, 2)
            })
            
            self.log_method_result(
                "process_message",
                service_mode="primary",
                conversation_id=conversation_id,
                execution_time_ms=round(execution_time * 1000, 2)
            )
            
            return response
            
        except CircuitBreakerError as e:
            # Circuit breaker is open, use fallback immediately
            self.stats["circuit_breaker_trips"] += 1
            self.stats["last_fallback_time"] = time.time()
            
            self.logger.warning(
                "Circuit breaker open, using fallback service",
                conversation_id=conversation_id,
                circuit_breaker=e.details.get("service_name"),
                consecutive_failures=e.details.get("consecutive_failures")
            )
            
            return await self._use_fallback_service(
                message, conversation_id, context, options, "circuit_breaker_open"
            )
            
        except Exception as e:
            # Primary service failed, try fallback
            self.stats["last_fallback_time"] = time.time()
            
            self.logger.error(
                "Primary service failed, using fallback",
                conversation_id=conversation_id,
                error=str(e),
                error_type=type(e).__name__
            )
            
            return await self._use_fallback_service(
                message, conversation_id, context, options, "primary_service_error"
            )
    
    async def stream_message(
        self,
        message: str,
        conversation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[str, None]:
        """
        Stream a message with resilience (fallback to non-streaming).
        
        Args:
            message: User message
            conversation_id: Optional conversation ID
            context: Additional context
            options: Processing options
            
        Yields:
            str: Response chunks
        """
        self.log_method_call(
            "stream_message",
            conversation_id=conversation_id,
            message_length=len(message)
        )
        
        try:
            # Try primary service streaming
            async for chunk in self.chat_breaker.call(
                self.primary_service.stream_message,
                message=message,
                conversation_id=conversation_id,
                context=context,
                options=options
            ):
                yield chunk
                
        except (CircuitBreakerError, Exception) as e:
            # Fallback to non-streaming response
            self.logger.warning(
                "Streaming failed, falling back to non-streaming response",
                conversation_id=conversation_id,
                error=str(e)
            )
            
            # Get non-streaming response from fallback
            response = await self._use_fallback_service(
                message, conversation_id, context, options, "streaming_failed"
            )
            
            # Convert to streaming format
            import json
            yield json.dumps({
                "type": "start",
                "message_id": response["message"]["id"],
                "conversation_id": response["conversation_id"]
            })
            
            yield json.dumps({
                "type": "content",
                "message_id": response["message"]["id"],
                "content": response["message"]["content"],
                "delta": False
            })
            
            yield json.dumps({
                "type": "complete",
                "message_id": response["message"]["id"],
                "message": response["message"]
            })
    
    async def _use_fallback_service(
        self,
        message: str,
        conversation_id: Optional[str],
        context: Optional[Dict[str, Any]],
        options: Optional[Dict[str, Any]],
        reason: str
    ) -> Dict[str, Any]:
        """
        Use the fallback service to process a message.
        
        Args:
            message: User message
            conversation_id: Optional conversation ID
            context: Additional context
            options: Processing options
            reason: Reason for using fallback
            
        Returns:
            Dict[str, Any]: Response from fallback service
        """
        start_time = time.time()
        self.stats["fallback_used"] += 1
        
        try:
            response = await self.fallback_service.process_message(
                message=message,
                conversation_id=conversation_id,
                context=context,
                options=options
            )
            
            execution_time = time.time() - start_time
            
            # Add fallback metadata
            response["metadata"] = response.get("metadata", {})
            response["metadata"].update({
                "service_mode": "fallback",
                "fallback_reason": reason,
                "execution_time_ms": round(execution_time * 1000, 2)
            })
            
            self.log_method_result(
                "_use_fallback_service",
                service_mode="fallback",
                reason=reason,
                conversation_id=conversation_id,
                execution_time_ms=round(execution_time * 1000, 2)
            )
            
            return response
            
        except Exception as e:
            self.log_error(e, "_use_fallback_service", reason=reason)
            
            # If fallback also fails, return error response
            return {
                "message": {
                    "id": f"error-{int(time.time())}",
                    "role": "assistant",
                    "content": "I apologize, but I'm currently experiencing technical difficulties. Please try again later.",
                    "timestamp": time.time(),
                    "metadata": {
                        "error": True,
                        "service_mode": "error",
                        "fallback_reason": reason,
                        "fallback_error": str(e)
                    }
                },
                "conversation_id": conversation_id,
                "metadata": {
                    "error": True,
                    "service_mode": "error",
                    "fallback_failed": True
                }
            }
    
    async def list_conversations(
        self,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        List conversations with fallback.
        
        Args:
            limit: Maximum number of conversations
            offset: Number of conversations to skip
            
        Returns:
            List[Dict[str, Any]]: List of conversations
        """
        try:
            return await self.chat_breaker.call(
                self.primary_service.list_conversations,
                limit=limit,
                offset=offset
            )
        except (CircuitBreakerError, Exception):
            # Fallback service doesn't support conversation listing
            return await self.fallback_service.list_conversations(limit, offset)
    
    async def get_conversation(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get conversation with fallback.
        
        Args:
            conversation_id: Conversation identifier
            
        Returns:
            Optional[Dict[str, Any]]: Conversation details
        """
        try:
            return await self.chat_breaker.call(
                self.primary_service.get_conversation,
                conversation_id
            )
        except (CircuitBreakerError, Exception):
            return await self.fallback_service.get_conversation(conversation_id)
    
    async def delete_conversation(self, conversation_id: str) -> bool:
        """
        Delete conversation with fallback.
        
        Args:
            conversation_id: Conversation identifier
            
        Returns:
            bool: True if deleted
        """
        try:
            return await self.chat_breaker.call(
                self.primary_service.delete_conversation,
                conversation_id
            )
        except (CircuitBreakerError, Exception):
            return await self.fallback_service.delete_conversation(conversation_id)
    
    async def update_conversation_title(
        self,
        conversation_id: str,
        title: str
    ) -> bool:
        """
        Update conversation title with fallback.
        
        Args:
            conversation_id: Conversation identifier
            title: New title
            
        Returns:
            bool: True if updated
        """
        try:
            return await self.chat_breaker.call(
                self.primary_service.update_conversation_title,
                conversation_id,
                title
            )
        except (CircuitBreakerError, Exception):
            return await self.fallback_service.update_conversation_title(
                conversation_id, title
            )
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform comprehensive health check.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        start_time = time.time()
        
        # Check primary service
        primary_health = {"status": "unknown"}
        try:
            primary_health = await self.primary_service.health_check()
        except Exception as e:
            primary_health = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        # Check fallback service
        fallback_health = {"status": "unknown"}
        try:
            fallback_health = await self.fallback_service.health_check()
        except Exception as e:
            fallback_health = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        # Get circuit breaker stats
        circuit_breaker_stats = {
            "chat_service": self.chat_breaker.get_stats(),
            "llm_service": self.llm_breaker.get_stats(),
            "knowledge_graph": self.kg_breaker.get_stats()
        }
        
        # Determine overall status
        overall_status = "healthy"
        if primary_health["status"] != "healthy" and fallback_health["status"] != "healthy":
            overall_status = "unhealthy"
        elif primary_health["status"] != "healthy":
            overall_status = "degraded"
        
        response_time = round((time.time() - start_time) * 1000, 2)
        
        return {
            "status": overall_status,
            "response_time_ms": response_time,
            "services": {
                "primary": primary_health,
                "fallback": fallback_health
            },
            "circuit_breakers": circuit_breaker_stats,
            "statistics": self.stats,
            "mode": "resilient"
        }
    
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        self.logger.info("Cleaning up resilient chat service")
        
        await self.primary_service.cleanup()
        await self.fallback_service.cleanup()
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get service statistics.
        
        Returns:
            Dict[str, Any]: Service statistics
        """
        return {
            **self.stats,
            "primary_success_rate": (
                self.stats["primary_success"] / self.stats["total_requests"]
                if self.stats["total_requests"] > 0 else 0.0
            ),
            "fallback_usage_rate": (
                self.stats["fallback_used"] / self.stats["total_requests"]
                if self.stats["total_requests"] > 0 else 0.0
            )
        }
