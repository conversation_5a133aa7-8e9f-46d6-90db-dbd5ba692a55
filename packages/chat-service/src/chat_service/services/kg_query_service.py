"""
Knowledge Graph Query Service

This module provides comprehensive knowledge graph query functionality,
migrating all existing Neo4j query patterns, RAG pipeline, and graph
analysis capabilities from the Node.js backend.
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime

import structlog

from chat_service.core.config import Settings
from chat_service.core.exceptions import KnowledgeGraphError, DatabaseError, ValidationError
from chat_service.core.logging import LoggerMixin


class KnowledgeGraphQueryService(LoggerMixin):
    """
    Comprehensive knowledge graph query service.
    
    Migrates functionality from the existing Node.js backend including:
    - Neo4j query execution with timing
    - Graph analysis and metrics
    - Context retrieval for RAG pipeline
    - Entity and relationship queries
    - Graph Data Science (GDS) operations
    - Performance monitoring and statistics
    """
    
    def __init__(
        self,
        neo4j_uri: str,
        neo4j_user: str,
        neo4j_password: str,
        neo4j_database: str = "neo4j",
        settings: Optional[Settings] = None
    ) -> None:
        """
        Initialize knowledge graph query service.
        
        Args:
            neo4j_uri: Neo4j connection URI
            neo4j_user: Neo4j username
            neo4j_password: Neo4j password
            neo4j_database: Neo4j database name
            settings: Application settings
        """
        self.neo4j_uri = neo4j_uri
        self.neo4j_user = neo4j_user
        self.neo4j_password = neo4j_password
        self.neo4j_database = neo4j_database
        self.settings = settings
        
        # Neo4j driver (will be initialized lazily)
        self._driver = None
        
        # Query statistics
        self.query_stats = {
            "total_queries": 0,
            "total_duration_ms": 0,
            "slow_queries": [],
            "recent_queries": [],
            "error_count": 0
        }
        
        self.logger.info(
            "Knowledge graph query service initialized",
            neo4j_uri=neo4j_uri,
            database=neo4j_database
        )
    
    async def _get_driver(self):
        """Get or create Neo4j driver."""
        if self._driver is None:
            try:
                from neo4j import AsyncGraphDatabase
                
                self._driver = AsyncGraphDatabase.driver(
                    self.neo4j_uri,
                    auth=(self.neo4j_user, self.neo4j_password)
                )
                
                # Test connection
                await self._driver.verify_connectivity()
                
                self.logger.info("Neo4j driver initialized successfully")
                
            except Exception as e:
                self.log_error(e, "_get_driver")
                raise DatabaseError(f"Failed to initialize Neo4j driver: {str(e)}")
        
        return self._driver
    
    async def execute_read_query(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        operation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Execute a read query with comprehensive timing and monitoring.
        
        Args:
            query: Cypher query string
            parameters: Query parameters
            operation_id: Operation ID for correlation
            
        Returns:
            Dict[str, Any]: Query results with metadata
            
        Raises:
            KnowledgeGraphError: If query execution fails
        """
        parameters = parameters or {}
        start_time = time.time()
        
        self.log_method_call(
            "execute_read_query",
            query_length=len(query),
            parameters_count=len(parameters),
            operation_id=operation_id
        )
        
        try:
            driver = await self._get_driver()
            
            async with driver.session(database=self.neo4j_database) as session:
                result = await session.run(query, parameters)
                records = await result.data()
                
                execution_time = round((time.time() - start_time) * 1000, 2)
                
                # Update statistics
                self._update_query_stats(query, execution_time, len(records), operation_id)
                
                result_data = {
                    "records": records,
                    "summary": {
                        "query": query,
                        "parameters": parameters,
                        "execution_time_ms": execution_time,
                        "record_count": len(records),
                        "operation_id": operation_id
                    }
                }
                
                self.log_method_result(
                    "execute_read_query",
                    execution_time_ms=execution_time,
                    record_count=len(records),
                    operation_id=operation_id
                )
                
                return result_data
                
        except Exception as e:
            execution_time = round((time.time() - start_time) * 1000, 2)
            self.query_stats["error_count"] += 1
            
            self.log_error(e, "execute_read_query", 
                          query=query[:100] + "..." if len(query) > 100 else query,
                          execution_time_ms=execution_time,
                          operation_id=operation_id)
            
            raise KnowledgeGraphError(
                f"Query execution failed: {str(e)}",
                query=query,
                operation_id=operation_id
            )
    
    async def execute_write_query(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        operation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Execute a write query with comprehensive timing and monitoring.
        
        Args:
            query: Cypher query string
            parameters: Query parameters
            operation_id: Operation ID for correlation
            
        Returns:
            Dict[str, Any]: Query results with metadata
            
        Raises:
            KnowledgeGraphError: If query execution fails
        """
        parameters = parameters or {}
        start_time = time.time()
        
        self.log_method_call(
            "execute_write_query",
            query_length=len(query),
            parameters_count=len(parameters),
            operation_id=operation_id
        )
        
        try:
            driver = await self._get_driver()
            
            async with driver.session(database=self.neo4j_database) as session:
                result = await session.execute_write(
                    lambda tx: tx.run(query, parameters)
                )
                records = await result.data()
                
                execution_time = round((time.time() - start_time) * 1000, 2)
                
                # Update statistics
                self._update_query_stats(query, execution_time, len(records), operation_id)
                
                result_data = {
                    "records": records,
                    "summary": {
                        "query": query,
                        "parameters": parameters,
                        "execution_time_ms": execution_time,
                        "record_count": len(records),
                        "operation_id": operation_id,
                        "counters": result.consume().counters
                    }
                }
                
                self.log_method_result(
                    "execute_write_query",
                    execution_time_ms=execution_time,
                    record_count=len(records),
                    operation_id=operation_id
                )
                
                return result_data
                
        except Exception as e:
            execution_time = round((time.time() - start_time) * 1000, 2)
            self.query_stats["error_count"] += 1
            
            self.log_error(e, "execute_write_query",
                          query=query[:100] + "..." if len(query) > 100 else query,
                          execution_time_ms=execution_time,
                          operation_id=operation_id)
            
            raise KnowledgeGraphError(
                f"Write query execution failed: {str(e)}",
                query=query,
                operation_id=operation_id
            )
    
    async def get_initial_graph(
        self,
        limit: int = 100,
        operation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get initial graph data for visualization.
        
        Args:
            limit: Maximum number of relationships to return
            operation_id: Operation ID for correlation
            
        Returns:
            Dict[str, Any]: Graph data with nodes and relationships
        """
        query = """
        MATCH (n)-[r]->(m)
        RETURN n, r, m, labels(n) as source_labels, labels(m) as target_labels,
               id(n) as source_id, id(m) as target_id, type(r) as rel_type
        LIMIT $limit
        """
        
        result = await self.execute_read_query(
            query,
            {"limit": limit},
            operation_id
        )
        
        # Process results into graph format
        nodes = {}
        relationships = []
        
        for record in result["records"]:
            source_node = record["n"]
            target_node = record["m"]
            relationship = record["r"]
            
            source_id = str(record["source_id"])
            target_id = str(record["target_id"])
            
            # Add nodes
            if source_id not in nodes:
                nodes[source_id] = {
                    "id": source_id,
                    "labels": record["source_labels"],
                    "properties": dict(source_node)
                }
            
            if target_id not in nodes:
                nodes[target_id] = {
                    "id": target_id,
                    "labels": record["target_labels"],
                    "properties": dict(target_node)
                }
            
            # Add relationship
            relationships.append({
                "source": source_id,
                "target": target_id,
                "type": record["rel_type"],
                "properties": dict(relationship)
            })
        
        return {
            "nodes": list(nodes.values()),
            "relationships": relationships,
            "summary": result["summary"]
        }
    
    async def get_graph_context(
        self,
        entity_ids: List[str],
        depth: int = 1,
        operation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get graph context for entities (migrated from Node.js backend).
        
        Args:
            entity_ids: List of entity identifiers
            depth: Context depth (number of hops)
            operation_id: Operation ID for correlation
            
        Returns:
            Dict[str, Any]: Graph context with entities and relationships
        """
        # Convert string IDs to integers for Neo4j
        try:
            int_entity_ids = [int(eid) for eid in entity_ids]
        except ValueError:
            raise ValidationError("Entity IDs must be numeric")
        
        query = f"""
        MATCH (start)
        WHERE id(start) IN $entity_ids
        MATCH path = (start)-[*1..{depth}]-(connected)
        WITH nodes(path) as path_nodes, relationships(path) as path_rels
        UNWIND path_nodes as n
        UNWIND path_rels as r
        RETURN DISTINCT n, labels(n) as node_labels, id(n) as node_id,
               r, type(r) as rel_type, id(r) as rel_id,
               id(startNode(r)) as rel_source, id(endNode(r)) as rel_target
        """
        
        result = await self.execute_read_query(
            query,
            {"entity_ids": int_entity_ids},
            operation_id
        )
        
        # Process results
        entities = {}
        relationships = {}
        
        for record in result["records"]:
            if record["n"]:  # Node data
                node_id = str(record["node_id"])
                if node_id not in entities:
                    entities[node_id] = {
                        "id": node_id,
                        "type": record["node_labels"][0] if record["node_labels"] else "Unknown",
                        "properties": dict(record["n"]),
                        "labels": record["node_labels"]
                    }
            
            if record["r"]:  # Relationship data
                rel_id = str(record["rel_id"])
                if rel_id not in relationships:
                    relationships[rel_id] = {
                        "id": rel_id,
                        "type": record["rel_type"],
                        "source": str(record["rel_source"]),
                        "target": str(record["rel_target"]),
                        "properties": dict(record["r"])
                    }
        
        return {
            "entities": list(entities.values()),
            "relationships": list(relationships.values()),
            "summary": result["summary"]
        }
    
    def _update_query_stats(
        self,
        query: str,
        execution_time_ms: float,
        record_count: int,
        operation_id: Optional[str] = None
    ) -> None:
        """Update query statistics."""
        self.query_stats["total_queries"] += 1
        self.query_stats["total_duration_ms"] += execution_time_ms
        
        # Track slow queries (>1000ms)
        if execution_time_ms > 1000:
            slow_query = {
                "query": query[:200] + "..." if len(query) > 200 else query,
                "execution_time_ms": execution_time_ms,
                "record_count": record_count,
                "timestamp": datetime.utcnow().isoformat(),
                "operation_id": operation_id
            }
            self.query_stats["slow_queries"].append(slow_query)
            
            # Keep only last 10 slow queries
            if len(self.query_stats["slow_queries"]) > 10:
                self.query_stats["slow_queries"] = self.query_stats["slow_queries"][-10:]
        
        # Track recent queries
        recent_query = {
            "query_hash": hash(query) % 10000,  # Simple hash for identification
            "execution_time_ms": execution_time_ms,
            "record_count": record_count,
            "timestamp": datetime.utcnow().isoformat(),
            "operation_id": operation_id
        }
        self.query_stats["recent_queries"].append(recent_query)
        
        # Keep only last 20 recent queries
        if len(self.query_stats["recent_queries"]) > 20:
            self.query_stats["recent_queries"] = self.query_stats["recent_queries"][-20:]
    
    async def get_query_statistics(self) -> Dict[str, Any]:
        """
        Get query performance statistics.
        
        Returns:
            Dict[str, Any]: Query statistics and performance metrics
        """
        avg_duration = (
            self.query_stats["total_duration_ms"] / self.query_stats["total_queries"]
            if self.query_stats["total_queries"] > 0 else 0
        )
        
        return {
            "total_queries": self.query_stats["total_queries"],
            "total_duration_ms": self.query_stats["total_duration_ms"],
            "average_duration_ms": round(avg_duration, 2),
            "error_count": self.query_stats["error_count"],
            "slow_queries_count": len(self.query_stats["slow_queries"]),
            "recent_queries": self.query_stats["recent_queries"][-5:],  # Last 5
            "slow_queries": self.query_stats["slow_queries"][-3:]  # Last 3
        }
    
    async def test_connection(self, operation_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Test database connectivity with timing.
        
        Args:
            operation_id: Operation ID for correlation
            
        Returns:
            Dict[str, Any]: Connection test results
        """
        start_time = time.time()
        
        try:
            result = await self.execute_read_query(
                "RETURN 1 as test",
                {},
                operation_id
            )
            
            response_time = round((time.time() - start_time) * 1000, 2)
            
            return {
                "connected": True,
                "response_time_ms": response_time,
                "database": self.neo4j_database,
                "uri": self.neo4j_uri,
                "test_result": result["records"][0]["test"] if result["records"] else None
            }
            
        except Exception as e:
            response_time = round((time.time() - start_time) * 1000, 2)
            
            return {
                "connected": False,
                "error": str(e),
                "response_time_ms": response_time,
                "database": self.neo4j_database,
                "uri": self.neo4j_uri
            }
    
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        self.logger.info("Cleaning up knowledge graph query service")
        
        if self._driver:
            try:
                await self._driver.close()
            except Exception as e:
                self.logger.warning(
                    "Error closing Neo4j driver",
                    error=str(e)
                )
            finally:
                self._driver = None
