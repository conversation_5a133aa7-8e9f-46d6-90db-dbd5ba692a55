"""
Mock LLM Service

This module provides a mock LLM service for testing and development
when real API keys are not available.
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, AsyncGenerator

import structlog

from chat_service.core.config import Settings
from chat_service.core.logging import LoggerMixin


class MockLLMService(LoggerMixin):
    """
    Mock LLM service for testing and development.
    
    Provides the same interface as the real LLM service but returns
    mock responses without making external API calls.
    """
    
    def __init__(
        self,
        default_provider: str = "mock",
        settings: Optional[Settings] = None
    ) -> None:
        """
        Initialize mock LLM service.
        
        Args:
            default_provider: Default provider name
            settings: Application settings
        """
        self.default_provider = default_provider
        self.settings = settings
        
        # Mock responses
        self.responses = [
            "Hello! I'm a mock AI assistant. How can I help you today?",
            "That's an interesting question. Let me think about that...",
            "Based on the information provided, I would suggest...",
            "I understand your concern. Here's what I think...",
            "Thank you for asking. The answer to your question is...",
        ]
        self.response_index = 0
        
        self.logger.info(
            "Mock LLM service initialized",
            default_provider=default_provider
        )
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        provider: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate a mock response.
        
        Args:
            messages: Conversation messages
            provider: LLM provider to use
            options: Generation options
            
        Returns:
            Dict[str, Any]: Mock response with metadata
        """
        provider = provider or self.default_provider
        options = options or {}
        
        self.log_method_call(
            "generate_response",
            provider=provider,
            messages_count=len(messages)
        )
        
        # Simulate processing time
        await asyncio.sleep(0.1)
        
        # Get mock response
        response_content = self._get_mock_response(messages)
        
        # Simulate token counting
        token_count = len(response_content.split()) * 1.3  # Rough estimate
        
        result = {
            "content": response_content,
            "provider": provider,
            "model": f"mock-{provider}-model",
            "tokens": int(token_count),
            "response_time_ms": 100
        }
        
        self.log_method_result(
            "generate_response",
            provider=provider,
            response_length=len(result["content"]),
            tokens=result["tokens"]
        )
        
        return result
    
    async def stream_response(
        self,
        messages: List[Dict[str, str]],
        provider: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Generate a mock streaming response.
        
        Args:
            messages: Conversation messages
            provider: LLM provider to use
            options: Generation options
            
        Yields:
            Dict[str, Any]: Response chunks
        """
        provider = provider or self.default_provider
        
        self.log_method_call(
            "stream_response",
            provider=provider,
            messages_count=len(messages)
        )
        
        # Get mock response
        response_content = self._get_mock_response(messages)
        
        # Stream response word by word
        words = response_content.split()
        for i, word in enumerate(words):
            await asyncio.sleep(0.05)  # Simulate streaming delay
            
            chunk_content = word
            if i < len(words) - 1:
                chunk_content += " "
            
            yield {
                "content": chunk_content,
                "model": f"mock-{provider}-model"
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform mock health check.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        start_time = time.time()
        
        # Simulate health check
        await asyncio.sleep(0.01)
        
        response_time = round((time.time() - start_time) * 1000, 2)
        
        return {
            "status": "healthy",
            "response_time_ms": response_time,
            "providers": {
                "mock": {
                    "status": "healthy",
                    "response_time_ms": response_time
                }
            },
            "default_provider": self.default_provider
        }
    
    async def cleanup(self) -> None:
        """Cleanup mock service resources."""
        self.logger.info("Cleaning up mock LLM service")
        # No cleanup needed for mock service
    
    def _get_mock_response(self, messages: List[Dict[str, str]]) -> str:
        """
        Generate a mock response based on input messages.
        
        Args:
            messages: Input messages
            
        Returns:
            str: Mock response content
        """
        if not messages:
            return "Hello! How can I help you?"
        
        last_message = messages[-1].get("content", "").lower()
        
        # Simple keyword-based responses
        if "hello" in last_message or "hi" in last_message:
            return "Hello! I'm a mock AI assistant. How can I help you today?"
        elif "how are you" in last_message:
            return "I'm doing well, thank you for asking! I'm a mock AI assistant ready to help."
        elif "what" in last_message and "time" in last_message:
            return f"I'm a mock assistant, so I don't have access to real-time data, but I can help with other questions!"
        elif "error" in last_message or "problem" in last_message:
            return "I understand you're experiencing an issue. As a mock assistant, I can provide general guidance."
        elif "test" in last_message:
            return "This is a test response from the mock LLM service. Everything appears to be working correctly!"
        else:
            # Cycle through predefined responses
            response = self.responses[self.response_index % len(self.responses)]
            self.response_index += 1
            return response


def create_mock_llm_service(settings: Optional[Settings] = None) -> MockLLMService:
    """
    Create a mock LLM service instance.
    
    Args:
        settings: Application settings
        
    Returns:
        MockLLMService: Mock LLM service instance
    """
    return MockLLMService(settings=settings)
