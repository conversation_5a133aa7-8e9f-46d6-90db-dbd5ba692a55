"""
RAG Pipeline Service

This module provides a comprehensive Retrieval-Augmented Generation (RAG)
pipeline service that combines knowledge graph retrieval with LLM generation,
migrating functionality from the existing Python scripts.
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

import structlog

from chat_service.core.config import Settings
from chat_service.core.exceptions import RAGPipelineError, ValidationError
from chat_service.core.logging import LoggerMixin
from chat_service.services.kg_query_service import KnowledgeGraphQueryService
from chat_service.services.enhanced_kg_service import EnhancedKnowledgeGraphService


class RAGPipelineService(LoggerMixin):
    """
    Comprehensive RAG pipeline service.
    
    Migrates functionality from the existing Python scripts including:
    - Knowledge graph context retrieval
    - Hybrid search (vector + BM25 with RRF/MMR)
    - Context building and formatting
    - Citation tracking and source attribution
    - Performance monitoring and optimization
    """
    
    def __init__(
        self,
        kg_query_service: KnowledgeGraphQueryService,
        enhanced_kg_service: Optional[EnhancedKnowledgeGraphService] = None,
        settings: Optional[Settings] = None
    ) -> None:
        """
        Initialize RAG pipeline service.
        
        Args:
            kg_query_service: Knowledge graph query service
            enhanced_kg_service: Enhanced KG service with Graphiti support
            settings: Application settings
        """
        self.kg_query_service = kg_query_service
        self.enhanced_kg_service = enhanced_kg_service
        self.settings = settings
        
        # Pipeline configuration
        self.config = {
            "max_context_length": 4000,
            "max_entities": 10,
            "max_relationships": 5,
            "similarity_threshold": 0.7,
            "context_depth": 2,
            "enable_citations": True,
            "enable_timing": True
        }
        
        # Pipeline statistics
        self.pipeline_stats = {
            "total_requests": 0,
            "successful_retrievals": 0,
            "context_generations": 0,
            "average_context_length": 0,
            "average_retrieval_time_ms": 0
        }
        
        self.logger.info(
            "RAG pipeline service initialized",
            has_enhanced_kg=enhanced_kg_service is not None,
            max_context_length=self.config["max_context_length"]
        )
    
    async def retrieve_and_format_context(
        self,
        query: str,
        options: Optional[Dict[str, Any]] = None,
        operation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Retrieve knowledge graph context and format it for LLM consumption.
        
        Args:
            query: Search query
            options: Retrieval options
            operation_id: Operation ID for correlation
            
        Returns:
            Dict[str, Any]: Formatted context with metadata
        """
        options = options or {}
        start_time = time.time()
        
        self.log_method_call(
            "retrieve_and_format_context",
            query_length=len(query),
            operation_id=operation_id
        )
        
        try:
            self.pipeline_stats["total_requests"] += 1
            
            # Try enhanced service first if available
            if self.enhanced_kg_service:
                context_result = await self._retrieve_with_enhanced_service(
                    query, options, operation_id
                )
            else:
                context_result = await self._retrieve_with_basic_service(
                    query, options, operation_id
                )
            
            # Format context for LLM
            formatted_context = await self._format_context_for_llm(
                context_result, options
            )
            
            retrieval_time = round((time.time() - start_time) * 1000, 2)
            
            # Update statistics
            self._update_pipeline_stats(formatted_context, retrieval_time)
            
            result = {
                "context": formatted_context["context"],
                "citations": formatted_context["citations"],
                "metadata": {
                    "query": query,
                    "entities_found": formatted_context["entities_count"],
                    "relationships_found": formatted_context["relationships_count"],
                    "context_length": len(formatted_context["context"]),
                    "retrieval_time_ms": retrieval_time,
                    "truncated": formatted_context["truncated"],
                    "search_method": context_result.get("search_method", "unknown"),
                    "operation_id": operation_id
                }
            }
            
            self.log_method_result(
                "retrieve_and_format_context",
                entities_found=result["metadata"]["entities_found"],
                context_length=result["metadata"]["context_length"],
                retrieval_time_ms=retrieval_time,
                operation_id=operation_id
            )
            
            return result
            
        except Exception as e:
            retrieval_time = round((time.time() - start_time) * 1000, 2)
            
            self.log_error(e, "retrieve_and_format_context",
                          query=query,
                          retrieval_time_ms=retrieval_time,
                          operation_id=operation_id)
            
            raise RAGPipelineError(
                f"Context retrieval failed: {str(e)}",
                query=query,
                operation_id=operation_id
            )
    
    async def _retrieve_with_enhanced_service(
        self,
        query: str,
        options: Dict[str, Any],
        operation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Retrieve context using enhanced KG service with Graphiti."""
        try:
            # Use enhanced service for LLM-formatted context
            context_result = await self.enhanced_kg_service.get_context_for_llm(
                query=query,
                max_context_length=options.get(
                    "max_context_length", 
                    self.config["max_context_length"]
                )
            )
            
            # Also get raw search results for additional metadata
            search_results = await self.enhanced_kg_service.search(
                query=query,
                limit=options.get("limit", self.config["max_entities"]),
                threshold=options.get("threshold", self.config["similarity_threshold"]),
                filters={"group_id": options.get("group_id", "user_guides")}
            )
            
            return {
                "context": context_result["context"],
                "citations": context_result["citations"],
                "entities": search_results.get("results", []),
                "entities_count": context_result["entities_found"],
                "relationships_count": 0,  # Enhanced service doesn't separate relationships
                "search_method": "enhanced_graphiti",
                "raw_context": search_results.get("context", ""),
                "execution_time_ms": search_results.get("execution_time_ms", 0)
            }
            
        except Exception as e:
            self.logger.warning(
                "Enhanced service retrieval failed, falling back to basic service",
                error=str(e),
                query=query,
                operation_id=operation_id
            )
            # Fallback to basic service
            return await self._retrieve_with_basic_service(query, options, operation_id)
    
    async def _retrieve_with_basic_service(
        self,
        query: str,
        options: Dict[str, Any],
        operation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Retrieve context using basic KG query service."""
        # Perform entity search
        entity_search_query = """
        MATCH (n)
        WHERE any(prop in keys(n) WHERE toString(n[prop]) CONTAINS $query)
        RETURN n, labels(n) as labels, id(n) as node_id
        LIMIT $limit
        """
        
        entity_result = await self.kg_query_service.execute_read_query(
            entity_search_query,
            {
                "query": query.lower(),
                "limit": options.get("limit", self.config["max_entities"])
            },
            operation_id
        )
        
        entities = []
        entity_ids = []
        
        for record in entity_result["records"]:
            node_id = str(record["node_id"])
            entity_ids.append(node_id)
            
            entities.append({
                "id": node_id,
                "type": record["labels"][0] if record["labels"] else "Unknown",
                "properties": dict(record["n"]),
                "labels": record["labels"]
            })
        
        # Get context for found entities
        relationships = []
        if entity_ids:
            context_result = await self.kg_query_service.get_graph_context(
                entity_ids=entity_ids[:5],  # Limit to top 5 entities
                depth=options.get("depth", self.config["context_depth"]),
                operation_id=operation_id
            )
            relationships = context_result["relationships"]
        
        return {
            "context": "",  # Will be built in formatting step
            "citations": [],  # Will be built in formatting step
            "entities": entities,
            "relationships": relationships,
            "entities_count": len(entities),
            "relationships_count": len(relationships),
            "search_method": "basic_neo4j",
            "execution_time_ms": entity_result["summary"]["execution_time_ms"]
        }
    
    async def _format_context_for_llm(
        self,
        context_result: Dict[str, Any],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Format retrieved context for LLM consumption."""
        max_length = options.get("max_context_length", self.config["max_context_length"])
        
        # If enhanced service already provided formatted context, use it
        if context_result.get("context") and context_result.get("citations"):
            context = context_result["context"]
            citations = context_result["citations"]
            
            # Check if truncation is needed
            truncated = len(context) > max_length
            if truncated:
                context = context[:max_length] + "..."
            
            return {
                "context": context,
                "citations": citations,
                "entities_count": context_result["entities_count"],
                "relationships_count": context_result["relationships_count"],
                "truncated": truncated
            }
        
        # Build context from entities and relationships
        context_parts = []
        citations = []
        
        # Add entity information
        entities = context_result.get("entities", [])
        for i, entity in enumerate(entities[:self.config["max_entities"]], 1):
            entity_name = entity["properties"].get("name", entity["properties"].get("id", "Unknown"))
            entity_type = entity["type"]
            
            # Add entity description
            description = entity["properties"].get("summary", entity["properties"].get("description", ""))
            if description:
                context_parts.append(f"{i}. **{entity_name}** ({entity_type}): {description}")
            else:
                context_parts.append(f"{i}. **{entity_name}** ({entity_type})")
            
            # Add citation
            citations.append({
                "id": entity["id"],
                "name": entity_name,
                "type": entity_type,
                "source": "entity"
            })
        
        # Add relationship information
        relationships = context_result.get("relationships", [])
        for rel in relationships[:self.config["max_relationships"]]:
            rel_type = rel["type"]
            source_id = rel["source"]
            target_id = rel["target"]
            
            # Find source and target entity names
            source_name = self._find_entity_name(entities, source_id)
            target_name = self._find_entity_name(entities, target_id)
            
            if source_name and target_name:
                context_parts.append(f"- {source_name} --[{rel_type}]--> {target_name}")
        
        # Combine context
        if context_parts:
            context = "**Knowledge Graph Context:**\n\n" + "\n".join(context_parts)
        else:
            context = "No relevant information found in the knowledge graph."
        
        # Add source attribution
        if citations:
            context += "\n\n**Sources:**\n"
            for i, citation in enumerate(citations[:5], 1):  # Limit to 5 citations
                context += f"{i}. {citation['name']} ({citation['type']})\n"
        
        # Check truncation
        truncated = len(context) > max_length
        if truncated:
            context = context[:max_length] + "..."
        
        return {
            "context": context,
            "citations": citations,
            "entities_count": len(entities),
            "relationships_count": len(relationships),
            "truncated": truncated
        }
    
    def _find_entity_name(self, entities: List[Dict[str, Any]], entity_id: str) -> Optional[str]:
        """Find entity name by ID."""
        for entity in entities:
            if entity["id"] == entity_id:
                return entity["properties"].get("name", entity["properties"].get("id", "Unknown"))
        return None
    
    async def build_rag_prompt(
        self,
        query: str,
        context: str,
        system_instructions: Optional[str] = None
    ) -> str:
        """
        Build a complete RAG prompt with context and instructions.
        
        Args:
            query: User query
            context: Retrieved context
            system_instructions: Optional system instructions
            
        Returns:
            str: Complete RAG prompt
        """
        default_instructions = """You are a helpful assistant with access to a knowledge graph. 
Use the provided context to answer the user's question accurately and comprehensively. 
If the context doesn't contain relevant information, say so clearly.
Always cite your sources when possible."""
        
        instructions = system_instructions or default_instructions
        
        prompt = f"""{instructions}

{context}

**User Question:** {query}

**Answer:**"""
        
        return prompt
    
    def _update_pipeline_stats(
        self,
        formatted_context: Dict[str, Any],
        retrieval_time_ms: float
    ) -> None:
        """Update pipeline statistics."""
        if formatted_context["entities_count"] > 0:
            self.pipeline_stats["successful_retrievals"] += 1
        
        self.pipeline_stats["context_generations"] += 1
        
        # Update averages
        total_requests = self.pipeline_stats["total_requests"]
        
        # Average context length
        current_avg_length = self.pipeline_stats["average_context_length"]
        new_length = len(formatted_context["context"])
        self.pipeline_stats["average_context_length"] = (
            (current_avg_length * (total_requests - 1) + new_length) / total_requests
        )
        
        # Average retrieval time
        current_avg_time = self.pipeline_stats["average_retrieval_time_ms"]
        self.pipeline_stats["average_retrieval_time_ms"] = (
            (current_avg_time * (total_requests - 1) + retrieval_time_ms) / total_requests
        )
    
    async def get_pipeline_statistics(self) -> Dict[str, Any]:
        """
        Get RAG pipeline performance statistics.
        
        Returns:
            Dict[str, Any]: Pipeline statistics and metrics
        """
        success_rate = (
            self.pipeline_stats["successful_retrievals"] / self.pipeline_stats["total_requests"]
            if self.pipeline_stats["total_requests"] > 0 else 0
        )
        
        return {
            "total_requests": self.pipeline_stats["total_requests"],
            "successful_retrievals": self.pipeline_stats["successful_retrievals"],
            "success_rate": round(success_rate, 3),
            "context_generations": self.pipeline_stats["context_generations"],
            "average_context_length": round(self.pipeline_stats["average_context_length"], 1),
            "average_retrieval_time_ms": round(self.pipeline_stats["average_retrieval_time_ms"], 2),
            "configuration": self.config
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform RAG pipeline health check.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        start_time = time.time()
        
        try:
            # Test basic retrieval
            test_result = await self.retrieve_and_format_context(
                "test query",
                {"limit": 1, "max_context_length": 100}
            )
            
            response_time = round((time.time() - start_time) * 1000, 2)
            
            return {
                "status": "healthy",
                "response_time_ms": response_time,
                "test_context_length": len(test_result["context"]),
                "has_enhanced_service": self.enhanced_kg_service is not None,
                "pipeline_stats": await self.get_pipeline_statistics()
            }
            
        except Exception as e:
            response_time = round((time.time() - start_time) * 1000, 2)
            
            return {
                "status": "unhealthy",
                "error": str(e),
                "response_time_ms": response_time,
                "has_enhanced_service": self.enhanced_kg_service is not None
            }
