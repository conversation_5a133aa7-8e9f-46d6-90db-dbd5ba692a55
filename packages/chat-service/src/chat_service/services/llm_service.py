"""
LLM Service

This module provides LLM integration with multiple providers
including OpenAI, Groq, and Google Gemini.
"""

import time
from typing import Dict, List, Optional, Any, AsyncGenerator

import structlog

from chat_service.core.config import Settings
from chat_service.core.exceptions import LL<PERSON>roviderError, ConfigurationError
from chat_service.core.logging import LoggerMixin


class LLMService(LoggerMixin):
    """
    LLM service for generating responses using various providers.
    
    Supports OpenAI, Groq, and Google Gemini with fallback mechanisms
    and provider-specific optimizations.
    """
    
    def __init__(
        self,
        default_provider: str,
        openai_api_key: Optional[str] = None,
        groq_api_key: Optional[str] = None,
        google_api_key: Optional[str] = None,
        settings: Optional[Settings] = None
    ) -> None:
        """
        Initialize LLM service.
        
        Args:
            default_provider: Default LLM provider
            openai_api_key: OpenAI API key
            groq_api_key: Groq API key
            google_api_key: Google API key
            settings: Application settings
        """
        self.default_provider = default_provider
        self.settings = settings
        
        # Initialize providers
        self.providers = {}
        
        if openai_api_key:
            self.providers["openai"] = self._init_openai_client(openai_api_key)
        
        if groq_api_key:
            self.providers["groq"] = self._init_groq_client(groq_api_key)
        
        if google_api_key:
            self.providers["google"] = self._init_google_client(google_api_key)
        
        if not self.providers:
            raise ConfigurationError("No LLM providers configured")
        
        if default_provider not in self.providers:
            raise ConfigurationError(f"Default provider '{default_provider}' not available")
        
        self.logger.info(
            "LLM service initialized",
            default_provider=default_provider,
            available_providers=list(self.providers.keys())
        )
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        provider: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate a response using the specified LLM provider.
        
        Args:
            messages: Conversation messages
            provider: LLM provider to use (defaults to default_provider)
            options: Generation options
            
        Returns:
            Dict[str, Any]: Generated response with metadata
            
        Raises:
            LLMProviderError: If response generation fails
        """
        provider = provider or self.default_provider
        options = options or {}
        
        if provider not in self.providers:
            raise LLMProviderError(f"Provider '{provider}' not available")
        
        self.log_method_call(
            "generate_response",
            provider=provider,
            messages_count=len(messages),
            options=options
        )
        
        start_time = time.time()
        
        try:
            if provider == "openai":
                response = await self._generate_openai_response(messages, options)
            elif provider == "groq":
                response = await self._generate_groq_response(messages, options)
            elif provider == "google":
                response = await self._generate_google_response(messages, options)
            else:
                raise LLMProviderError(f"Unsupported provider: {provider}")
            
            response_time = round((time.time() - start_time) * 1000, 2)
            
            result = {
                "content": response["content"],
                "provider": provider,
                "model": response.get("model"),
                "tokens": response.get("tokens"),
                "response_time_ms": response_time
            }
            
            self.log_method_result(
                "generate_response",
                provider=provider,
                response_length=len(result["content"]),
                response_time_ms=response_time
            )
            
            return result
            
        except Exception as e:
            self.log_error(e, "generate_response", provider=provider)
            
            # Try fallback provider if available
            if provider != self.default_provider and self.default_provider in self.providers:
                self.logger.warning(
                    "Falling back to default provider",
                    failed_provider=provider,
                    fallback_provider=self.default_provider
                )
                return await self.generate_response(messages, self.default_provider, options)
            
            raise LLMProviderError(
                f"Failed to generate response with provider '{provider}': {str(e)}",
                provider=provider
            )
    
    async def stream_response(
        self,
        messages: List[Dict[str, str]],
        provider: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Generate a streaming response using the specified LLM provider.
        
        Args:
            messages: Conversation messages
            provider: LLM provider to use
            options: Generation options
            
        Yields:
            Dict[str, Any]: Response chunks
            
        Raises:
            LLMProviderError: If streaming fails
        """
        provider = provider or self.default_provider
        options = options or {}
        
        if provider not in self.providers:
            raise LLMProviderError(f"Provider '{provider}' not available")
        
        self.log_method_call(
            "stream_response",
            provider=provider,
            messages_count=len(messages)
        )
        
        try:
            if provider == "openai":
                async for chunk in self._stream_openai_response(messages, options):
                    yield chunk
            elif provider == "groq":
                async for chunk in self._stream_groq_response(messages, options):
                    yield chunk
            elif provider == "google":
                async for chunk in self._stream_google_response(messages, options):
                    yield chunk
            else:
                raise LLMProviderError(f"Unsupported provider: {provider}")
                
        except Exception as e:
            self.log_error(e, "stream_response", provider=provider)
            raise LLMProviderError(
                f"Failed to stream response with provider '{provider}': {str(e)}",
                provider=provider
            )
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on all providers.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        start_time = time.time()
        provider_health = {}
        
        for provider_name in self.providers:
            try:
                # Simple test message
                test_messages = [
                    {"role": "user", "content": "Hello"}
                ]
                
                provider_start = time.time()
                await self.generate_response(
                    messages=test_messages,
                    provider=provider_name,
                    options={"max_tokens": 10}
                )
                provider_time = round((time.time() - provider_start) * 1000, 2)
                
                provider_health[provider_name] = {
                    "status": "healthy",
                    "response_time_ms": provider_time
                }
                
            except Exception as e:
                provider_health[provider_name] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
        
        total_time = round((time.time() - start_time) * 1000, 2)
        
        return {
            "status": "healthy" if any(p["status"] == "healthy" for p in provider_health.values()) else "unhealthy",
            "response_time_ms": total_time,
            "providers": provider_health,
            "default_provider": self.default_provider
        }
    
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        self.logger.info("Cleaning up LLM service")
        
        # Close any open connections
        for provider_name, client in self.providers.items():
            try:
                if hasattr(client, 'close'):
                    await client.close()
            except Exception as e:
                self.logger.warning(
                    "Error closing provider client",
                    provider=provider_name,
                    error=str(e)
                )
    
    def _init_openai_client(self, api_key: str):
        """Initialize OpenAI client."""
        try:
            import openai
            return openai.AsyncOpenAI(api_key=api_key)
        except ImportError:
            raise ConfigurationError("OpenAI package not installed")
    
    def _init_groq_client(self, api_key: str):
        """Initialize Groq client."""
        try:
            import groq
            return groq.AsyncGroq(api_key=api_key)
        except ImportError:
            raise ConfigurationError("Groq package not installed")
    
    def _init_google_client(self, api_key: str):
        """Initialize Google client."""
        try:
            import google.generativeai as genai
            genai.configure(api_key=api_key)
            return genai
        except ImportError:
            raise ConfigurationError("Google GenerativeAI package not installed")
    
    async def _generate_openai_response(
        self,
        messages: List[Dict[str, str]],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate response using OpenAI."""
        client = self.providers["openai"]
        
        response = await client.chat.completions.create(
            model=options.get("model", self.settings.openai_model if self.settings else "gpt-3.5-turbo"),
            messages=messages,
            max_tokens=options.get("max_tokens", self.settings.openai_max_tokens if self.settings else 1000),
            temperature=options.get("temperature", self.settings.openai_temperature if self.settings else 0.7),
            stream=False
        )
        
        return {
            "content": response.choices[0].message.content,
            "model": response.model,
            "tokens": response.usage.total_tokens if response.usage else 0
        }
    
    async def _stream_openai_response(
        self,
        messages: List[Dict[str, str]],
        options: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream response using OpenAI."""
        client = self.providers["openai"]
        
        stream = await client.chat.completions.create(
            model=options.get("model", self.settings.openai_model if self.settings else "gpt-3.5-turbo"),
            messages=messages,
            max_tokens=options.get("max_tokens", self.settings.openai_max_tokens if self.settings else 1000),
            temperature=options.get("temperature", self.settings.openai_temperature if self.settings else 0.7),
            stream=True
        )
        
        async for chunk in stream:
            if chunk.choices[0].delta.content:
                yield {
                    "content": chunk.choices[0].delta.content,
                    "model": chunk.model
                }
    
    async def _generate_groq_response(
        self,
        messages: List[Dict[str, str]],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate response using Groq."""
        client = self.providers["groq"]
        
        response = await client.chat.completions.create(
            model=options.get("model", self.settings.groq_model if self.settings else "mixtral-8x7b-32768"),
            messages=messages,
            max_tokens=options.get("max_tokens", self.settings.groq_max_tokens if self.settings else 1000),
            temperature=options.get("temperature", self.settings.groq_temperature if self.settings else 0.7),
            stream=False
        )
        
        return {
            "content": response.choices[0].message.content,
            "model": response.model,
            "tokens": response.usage.total_tokens if response.usage else 0
        }
    
    async def _stream_groq_response(
        self,
        messages: List[Dict[str, str]],
        options: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream response using Groq."""
        client = self.providers["groq"]
        
        stream = await client.chat.completions.create(
            model=options.get("model", self.settings.groq_model if self.settings else "mixtral-8x7b-32768"),
            messages=messages,
            max_tokens=options.get("max_tokens", self.settings.groq_max_tokens if self.settings else 1000),
            temperature=options.get("temperature", self.settings.groq_temperature if self.settings else 0.7),
            stream=True
        )
        
        async for chunk in stream:
            if chunk.choices[0].delta.content:
                yield {
                    "content": chunk.choices[0].delta.content,
                    "model": chunk.model
                }
    
    async def _generate_google_response(
        self,
        messages: List[Dict[str, str]],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate response using Google Gemini."""
        genai = self.providers["google"]
        
        # Convert messages to Google format
        prompt = self._convert_messages_to_google_format(messages)
        
        model = genai.GenerativeModel(
            options.get("model", self.settings.google_model if self.settings else "gemini-pro")
        )
        
        response = await model.generate_content_async(
            prompt,
            generation_config=genai.types.GenerationConfig(
                max_output_tokens=options.get("max_tokens", self.settings.google_max_tokens if self.settings else 1000),
                temperature=options.get("temperature", self.settings.google_temperature if self.settings else 0.7)
            )
        )
        
        return {
            "content": response.text,
            "model": model.model_name,
            "tokens": 0  # Google doesn't provide token count in response
        }
    
    async def _stream_google_response(
        self,
        messages: List[Dict[str, str]],
        options: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream response using Google Gemini."""
        genai = self.providers["google"]
        
        # Convert messages to Google format
        prompt = self._convert_messages_to_google_format(messages)
        
        model = genai.GenerativeModel(
            options.get("model", self.settings.google_model if self.settings else "gemini-pro")
        )
        
        response = await model.generate_content_async(
            prompt,
            generation_config=genai.types.GenerationConfig(
                max_output_tokens=options.get("max_tokens", self.settings.google_max_tokens if self.settings else 1000),
                temperature=options.get("temperature", self.settings.google_temperature if self.settings else 0.7)
            ),
            stream=True
        )
        
        async for chunk in response:
            if chunk.text:
                yield {
                    "content": chunk.text,
                    "model": model.model_name
                }
    
    def _convert_messages_to_google_format(self, messages: List[Dict[str, str]]) -> str:
        """Convert OpenAI-style messages to Google Gemini format."""
        prompt_parts = []
        
        for message in messages:
            role = message["role"]
            content = message["content"]
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"Human: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
        
        return "\n\n".join(prompt_parts) + "\n\nAssistant:"
