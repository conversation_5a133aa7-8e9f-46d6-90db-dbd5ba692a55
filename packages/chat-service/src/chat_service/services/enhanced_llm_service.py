"""
Enhanced LLM Service

This module provides an enhanced LLM service that migrates functionality
from the existing Python scripts, including Ollama, Google Gemini, and
OpenAI integration with proper streaming support.
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, AsyncGenerator

import httpx
import structlog

from chat_service.core.config import Settings
from chat_service.core.exceptions import LLMProviderError, ConfigurationError
from chat_service.core.logging import LoggerMixin


class EnhancedLLMService(LoggerMixin):
    """
    Enhanced LLM service with support for multiple providers and streaming.
    
    Migrates functionality from the existing Python scripts including:
    - Ollama integration with streaming
    - Google Gemini support
    - OpenAI compatibility
    - Proper prompt templating
    - Error handling and fallbacks
    """
    
    def __init__(
        self,
        default_provider: str = "ollama",
        openai_api_key: Optional[str] = None,
        groq_api_key: Optional[str] = None,
        google_api_key: Optional[str] = None,
        ollama_url: str = "http://localhost:11434",
        settings: Optional[Settings] = None
    ) -> None:
        """
        Initialize enhanced LLM service.
        
        Args:
            default_provider: Default LLM provider
            openai_api_key: OpenAI API key
            groq_api_key: Groq API key
            google_api_key: Google API key
            ollama_url: Ollama server URL
            settings: Application settings
        """
        self.default_provider = default_provider
        self.settings = settings
        self.ollama_url = ollama_url.rstrip("/")
        
        # Initialize HTTP client for external APIs
        self.http_client = httpx.AsyncClient(timeout=60.0)
        
        # Provider configurations
        self.providers = {}
        
        # Initialize Ollama (local)
        self.providers["ollama"] = {
            "type": "ollama",
            "url": self.ollama_url,
            "models": {
                "deepseek": "deepseek-r1:8b",
                "gemma": "gemma3:12b",
                "llama": "llama3.2:8b"
            },
            "default_model": "deepseek-r1:8b"
        }
        
        # Initialize OpenAI
        if openai_api_key:
            self.providers["openai"] = {
                "type": "openai",
                "api_key": openai_api_key,
                "models": {
                    "gpt-3.5": "gpt-3.5-turbo",
                    "gpt-4": "gpt-4",
                    "gpt-4-turbo": "gpt-4-turbo-preview"
                },
                "default_model": "gpt-3.5-turbo"
            }
        
        # Initialize Groq
        if groq_api_key:
            self.providers["groq"] = {
                "type": "groq",
                "api_key": groq_api_key,
                "models": {
                    "mixtral": "mixtral-8x7b-32768",
                    "llama": "llama2-70b-4096"
                },
                "default_model": "mixtral-8x7b-32768"
            }
        
        # Initialize Google Gemini
        if google_api_key:
            self.providers["google"] = {
                "type": "google",
                "api_key": google_api_key,
                "models": {
                    "gemini-pro": "gemini-pro",
                    "gemini-pro-vision": "gemini-pro-vision"
                },
                "default_model": "gemini-pro"
            }
        
        if not self.providers:
            raise ConfigurationError("No LLM providers configured")
        
        self.logger.info(
            "Enhanced LLM service initialized",
            default_provider=default_provider,
            available_providers=list(self.providers.keys()),
            ollama_url=ollama_url
        )
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        provider: Optional[str] = None,
        model: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate a response using the specified LLM provider.
        
        Args:
            messages: Conversation messages
            provider: LLM provider to use
            model: Specific model to use
            options: Generation options
            
        Returns:
            Dict[str, Any]: Generated response with metadata
        """
        provider = provider or self.default_provider
        options = options or {}
        
        if provider not in self.providers:
            raise LLMProviderError(f"Provider '{provider}' not available")
        
        provider_config = self.providers[provider]
        model = model or provider_config["default_model"]
        
        self.log_method_call(
            "generate_response",
            provider=provider,
            model=model,
            messages_count=len(messages)
        )
        
        start_time = time.time()
        
        try:
            if provider == "ollama":
                response = await self._generate_ollama_response(messages, model, options)
            elif provider == "openai":
                response = await self._generate_openai_response(messages, model, options)
            elif provider == "groq":
                response = await self._generate_groq_response(messages, model, options)
            elif provider == "google":
                response = await self._generate_google_response(messages, model, options)
            else:
                raise LLMProviderError(f"Unsupported provider: {provider}")
            
            response_time = round((time.time() - start_time) * 1000, 2)
            
            result = {
                "content": response["content"],
                "provider": provider,
                "model": model,
                "tokens": response.get("tokens", 0),
                "response_time_ms": response_time,
                "metadata": response.get("metadata", {})
            }
            
            self.log_method_result(
                "generate_response",
                provider=provider,
                model=model,
                response_length=len(result["content"]),
                response_time_ms=response_time
            )
            
            return result
            
        except Exception as e:
            self.log_error(e, "generate_response", provider=provider, model=model)
            raise LLMProviderError(
                f"Failed to generate response with {provider}/{model}: {str(e)}",
                provider=provider,
                model=model
            )
    
    async def stream_response(
        self,
        messages: List[Dict[str, str]],
        provider: Optional[str] = None,
        model: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Generate a streaming response using the specified LLM provider.
        
        Args:
            messages: Conversation messages
            provider: LLM provider to use
            model: Specific model to use
            options: Generation options
            
        Yields:
            Dict[str, Any]: Response chunks
        """
        provider = provider or self.default_provider
        options = options or {}
        
        if provider not in self.providers:
            raise LLMProviderError(f"Provider '{provider}' not available")
        
        provider_config = self.providers[provider]
        model = model or provider_config["default_model"]
        
        self.log_method_call(
            "stream_response",
            provider=provider,
            model=model,
            messages_count=len(messages)
        )
        
        try:
            if provider == "ollama":
                async for chunk in self._stream_ollama_response(messages, model, options):
                    yield chunk
            elif provider == "openai":
                async for chunk in self._stream_openai_response(messages, model, options):
                    yield chunk
            elif provider == "groq":
                async for chunk in self._stream_groq_response(messages, model, options):
                    yield chunk
            elif provider == "google":
                async for chunk in self._stream_google_response(messages, model, options):
                    yield chunk
            else:
                raise LLMProviderError(f"Streaming not supported for provider: {provider}")
                
        except Exception as e:
            self.log_error(e, "stream_response", provider=provider, model=model)
            raise LLMProviderError(
                f"Failed to stream response with {provider}/{model}: {str(e)}",
                provider=provider,
                model=model
            )
    
    async def _generate_ollama_response(
        self,
        messages: List[Dict[str, str]],
        model: str,
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate response using Ollama."""
        # Convert messages to prompt format
        prompt = self._messages_to_prompt(messages)
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": options.get("temperature", 0.3),
                "top_p": options.get("top_p", 0.9),
                "top_k": options.get("top_k", 40),
                "num_predict": options.get("max_tokens", 1000)
            }
        }
        
        try:
            response = await self.http_client.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=60.0
            )
            response.raise_for_status()
            
            result = response.json()
            
            return {
                "content": result.get("response", ""),
                "tokens": result.get("eval_count", 0),
                "metadata": {
                    "model": model,
                    "eval_duration": result.get("eval_duration", 0),
                    "load_duration": result.get("load_duration", 0)
                }
            }
            
        except httpx.RequestError as e:
            raise LLMProviderError(f"Ollama request failed: {str(e)}")
        except Exception as e:
            raise LLMProviderError(f"Ollama error: {str(e)}")
    
    async def _stream_ollama_response(
        self,
        messages: List[Dict[str, str]],
        model: str,
        options: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream response using Ollama."""
        prompt = self._messages_to_prompt(messages)
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": True,
            "options": {
                "temperature": options.get("temperature", 0.3),
                "top_p": options.get("top_p", 0.9),
                "top_k": options.get("top_k", 40),
                "num_predict": options.get("max_tokens", 1000)
            }
        }
        
        try:
            async with self.http_client.stream(
                "POST",
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=60.0
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.strip():
                        try:
                            chunk = json.loads(line)
                            if "response" in chunk:
                                yield {
                                    "content": chunk["response"],
                                    "model": model,
                                    "done": chunk.get("done", False)
                                }
                                
                                if chunk.get("done", False):
                                    break
                                    
                        except json.JSONDecodeError:
                            continue
                            
        except httpx.RequestError as e:
            raise LLMProviderError(f"Ollama streaming failed: {str(e)}")
    
    def _messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """Convert OpenAI-style messages to a single prompt."""
        prompt_parts = []
        
        for message in messages:
            role = message["role"]
            content = message["content"]
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"Human: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
        
        prompt_parts.append("Assistant:")
        return "\n\n".join(prompt_parts)
    
    async def _generate_openai_response(
        self,
        messages: List[Dict[str, str]],
        model: str,
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate response using OpenAI API."""
        # Implementation similar to existing LLM service
        # This would use the OpenAI client
        raise NotImplementedError("OpenAI integration to be implemented")
    
    async def _stream_openai_response(
        self,
        messages: List[Dict[str, str]],
        model: str,
        options: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream response using OpenAI API."""
        # Implementation for OpenAI streaming
        raise NotImplementedError("OpenAI streaming to be implemented")
        yield  # Make this a generator
    
    async def _generate_groq_response(
        self,
        messages: List[Dict[str, str]],
        model: str,
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate response using Groq API."""
        # Implementation for Groq
        raise NotImplementedError("Groq integration to be implemented")
    
    async def _stream_groq_response(
        self,
        messages: List[Dict[str, str]],
        model: str,
        options: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream response using Groq API."""
        # Implementation for Groq streaming
        raise NotImplementedError("Groq streaming to be implemented")
        yield  # Make this a generator
    
    async def _generate_google_response(
        self,
        messages: List[Dict[str, str]],
        model: str,
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate response using Google Gemini API."""
        # Implementation for Google Gemini
        raise NotImplementedError("Google Gemini integration to be implemented")
    
    async def _stream_google_response(
        self,
        messages: List[Dict[str, str]],
        model: str,
        options: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream response using Google Gemini API."""
        # Implementation for Google Gemini streaming
        raise NotImplementedError("Google Gemini streaming to be implemented")
        yield  # Make this a generator
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on all providers.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        start_time = time.time()
        provider_health = {}
        
        for provider_name, config in self.providers.items():
            try:
                if provider_name == "ollama":
                    # Test Ollama connection
                    response = await self.http_client.get(
                        f"{self.ollama_url}/api/tags",
                        timeout=5.0
                    )
                    if response.status_code == 200:
                        models = response.json().get("models", [])
                        provider_health[provider_name] = {
                            "status": "healthy",
                            "models_available": len(models),
                            "url": self.ollama_url
                        }
                    else:
                        provider_health[provider_name] = {
                            "status": "unhealthy",
                            "error": f"HTTP {response.status_code}"
                        }
                else:
                    # For other providers, assume healthy if configured
                    provider_health[provider_name] = {
                        "status": "healthy",
                        "note": "API key configured"
                    }
                    
            except Exception as e:
                provider_health[provider_name] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
        
        total_time = round((time.time() - start_time) * 1000, 2)
        
        return {
            "status": "healthy" if any(p["status"] == "healthy" for p in provider_health.values()) else "unhealthy",
            "response_time_ms": total_time,
            "providers": provider_health,
            "default_provider": self.default_provider
        }
    
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        self.logger.info("Cleaning up enhanced LLM service")
        await self.http_client.aclose()
