"""
Knowledge Graph Service

This module provides knowledge graph operations including
search, entity retrieval, and relationship queries using Neo4j.
"""

import time
from typing import Dict, List, Optional, Any

import structlog
from neo4j import AsyncGraphDatabase, AsyncDriver

from chat_service.core.config import Settings
from chat_service.core.exceptions import KnowledgeGraphError, DatabaseError
from chat_service.core.logging import LoggerMixin


class KnowledgeGraphService(LoggerMixin):
    """
    Knowledge graph service for Neo4j operations.
    
    Provides search, entity retrieval, relationship queries,
    and context building for the chat service.
    """
    
    def __init__(
        self,
        neo4j_uri: str,
        neo4j_user: str,
        neo4j_password: str,
        neo4j_database: str = "neo4j",
        embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2",
        settings: Optional[Settings] = None
    ) -> None:
        """
        Initialize knowledge graph service.
        
        Args:
            neo4j_uri: Neo4j connection URI
            neo4j_user: Neo4j username
            neo4j_password: Neo4j password
            neo4j_database: Neo4j database name
            embedding_model: Embedding model for similarity search
            settings: Application settings
        """
        self.neo4j_uri = neo4j_uri
        self.neo4j_user = neo4j_user
        self.neo4j_password = neo4j_password
        self.neo4j_database = neo4j_database
        self.embedding_model = embedding_model
        self.settings = settings
        
        # Initialize Neo4j driver
        self.driver: Optional[AsyncDriver] = None
        
        # Initialize embedding model (placeholder - would use sentence-transformers)
        self.embeddings = None
        
        self.logger.info(
            "Knowledge graph service initialized",
            neo4j_uri=neo4j_uri,
            database=neo4j_database,
            embedding_model=embedding_model
        )
    
    async def _get_driver(self) -> AsyncDriver:
        """Get or create Neo4j driver."""
        if not self.driver:
            try:
                self.driver = AsyncGraphDatabase.driver(
                    self.neo4j_uri,
                    auth=(self.neo4j_user, self.neo4j_password)
                )
                
                # Test connection
                await self.driver.verify_connectivity()
                
                self.logger.info("Neo4j connection established")
                
            except Exception as e:
                self.log_error(e, "_get_driver")
                raise DatabaseError(f"Failed to connect to Neo4j: {str(e)}")
        
        return self.driver
    
    async def search(
        self,
        query: str,
        limit: int = 10,
        threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Search the knowledge graph using semantic similarity.
        
        Args:
            query: Search query
            limit: Maximum number of results
            threshold: Similarity threshold
            filters: Additional filters
            
        Returns:
            Dict[str, Any]: Search results with metadata
            
        Raises:
            KnowledgeGraphError: If search fails
        """
        start_time = time.time()
        
        self.log_method_call(
            "search",
            query_length=len(query),
            limit=limit,
            threshold=threshold
        )
        
        try:
            driver = await self._get_driver()
            
            # For now, use simple text matching (would use embeddings in production)
            cypher_query = """
            MATCH (n)
            WHERE any(prop in keys(n) WHERE toString(n[prop]) CONTAINS $query)
            RETURN n, labels(n) as labels, id(n) as node_id
            LIMIT $limit
            """
            
            async with driver.session(database=self.neo4j_database) as session:
                result = await session.run(
                    cypher_query,
                    query=query.lower(),
                    limit=limit
                )
                
                records = await result.data()
                
                # Convert to search results format
                search_results = []
                for record in records:
                    node = record["n"]
                    labels = record["labels"]
                    node_id = record["node_id"]
                    
                    # Calculate mock similarity score (would use embeddings)
                    score = 0.8  # Placeholder
                    
                    if score >= threshold:
                        search_results.append({
                            "entity": {
                                "id": str(node_id),
                                "type": labels[0] if labels else "Unknown",
                                "properties": dict(node),
                                "labels": labels
                            },
                            "score": score,
                            "context": self._extract_context(node, query)
                        })
                
                execution_time = round((time.time() - start_time) * 1000, 2)
                
                result_data = {
                    "query": query,
                    "results": search_results,
                    "total_count": len(search_results),
                    "execution_time_ms": execution_time
                }
                
                self.log_method_result(
                    "search",
                    results_count=len(search_results),
                    execution_time_ms=execution_time
                )
                
                return result_data
                
        except Exception as e:
            self.log_error(e, "search", query=query)
            raise KnowledgeGraphError(f"Search failed: {str(e)}", query=query)
    
    async def get_entity(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """
        Get entity by ID.
        
        Args:
            entity_id: Entity identifier
            
        Returns:
            Optional[Dict[str, Any]]: Entity data or None
            
        Raises:
            KnowledgeGraphError: If query fails
        """
        try:
            driver = await self._get_driver()
            
            cypher_query = """
            MATCH (n)
            WHERE id(n) = $entity_id
            RETURN n, labels(n) as labels, id(n) as node_id
            """
            
            async with driver.session(database=self.neo4j_database) as session:
                result = await session.run(cypher_query, entity_id=int(entity_id))
                record = await result.single()
                
                if not record:
                    return None
                
                node = record["n"]
                labels = record["labels"]
                node_id = record["node_id"]
                
                return {
                    "id": str(node_id),
                    "type": labels[0] if labels else "Unknown",
                    "properties": dict(node),
                    "labels": labels
                }
                
        except Exception as e:
            self.log_error(e, "get_entity", entity_id=entity_id)
            raise KnowledgeGraphError(f"Failed to get entity: {str(e)}")
    
    async def get_entity_relationships(
        self,
        entity_id: str,
        direction: str = "both",
        relationship_type: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get entity relationships.
        
        Args:
            entity_id: Entity identifier
            direction: Relationship direction
            relationship_type: Filter by relationship type
            limit: Maximum relationships
            
        Returns:
            List[Dict[str, Any]]: List of relationships
            
        Raises:
            KnowledgeGraphError: If query fails
        """
        try:
            driver = await self._get_driver()
            
            # Build Cypher query based on direction
            if direction == "outgoing":
                match_pattern = "(n)-[r]->(m)"
            elif direction == "incoming":
                match_pattern = "(m)-[r]->(n)"
            else:  # both
                match_pattern = "(n)-[r]-(m)"
            
            cypher_query = f"""
            MATCH {match_pattern}
            WHERE id(n) = $entity_id
            """
            
            if relationship_type:
                cypher_query += " AND type(r) = $relationship_type"
            
            cypher_query += """
            RETURN r, id(r) as rel_id, type(r) as rel_type,
                   id(startNode(r)) as source_id, id(endNode(r)) as target_id
            LIMIT $limit
            """
            
            params = {"entity_id": int(entity_id), "limit": limit}
            if relationship_type:
                params["relationship_type"] = relationship_type
            
            async with driver.session(database=self.neo4j_database) as session:
                result = await session.run(cypher_query, **params)
                records = await result.data()
                
                relationships = []
                for record in records:
                    rel = record["r"]
                    
                    relationships.append({
                        "id": str(record["rel_id"]),
                        "type": record["rel_type"],
                        "source_id": str(record["source_id"]),
                        "target_id": str(record["target_id"]),
                        "properties": dict(rel)
                    })
                
                return relationships
                
        except Exception as e:
            self.log_error(e, "get_entity_relationships", entity_id=entity_id)
            raise KnowledgeGraphError(f"Failed to get relationships: {str(e)}")
    
    async def get_context(
        self,
        entity_ids: List[str],
        depth: int = 1
    ) -> Dict[str, Any]:
        """
        Get graph context for entities.
        
        Args:
            entity_ids: List of entity identifiers
            depth: Context depth
            
        Returns:
            Dict[str, Any]: Graph context
            
        Raises:
            KnowledgeGraphError: If query fails
        """
        try:
            driver = await self._get_driver()
            
            # Build variable-length path query
            cypher_query = f"""
            MATCH (start)
            WHERE id(start) IN $entity_ids
            MATCH path = (start)-[*1..{depth}]-(connected)
            WITH nodes(path) as path_nodes, relationships(path) as path_rels
            UNWIND path_nodes as n
            UNWIND path_rels as r
            RETURN DISTINCT n, labels(n) as node_labels, id(n) as node_id,
                   r, type(r) as rel_type, id(r) as rel_id,
                   id(startNode(r)) as rel_source, id(endNode(r)) as rel_target
            """
            
            async with driver.session(database=self.neo4j_database) as session:
                result = await session.run(
                    cypher_query,
                    entity_ids=[int(eid) for eid in entity_ids]
                )
                records = await result.data()
                
                entities = {}
                relationships = {}
                
                for record in records:
                    # Process nodes
                    if record["n"]:
                        node_id = str(record["node_id"])
                        if node_id not in entities:
                            entities[node_id] = {
                                "id": node_id,
                                "type": record["node_labels"][0] if record["node_labels"] else "Unknown",
                                "properties": dict(record["n"]),
                                "labels": record["node_labels"]
                            }
                    
                    # Process relationships
                    if record["r"]:
                        rel_id = str(record["rel_id"])
                        if rel_id not in relationships:
                            relationships[rel_id] = {
                                "id": rel_id,
                                "type": record["rel_type"],
                                "source_id": str(record["rel_source"]),
                                "target_id": str(record["rel_target"]),
                                "properties": dict(record["r"])
                            }
                
                return {
                    "entities": list(entities.values()),
                    "relationships": list(relationships.values()),
                    "metadata": {
                        "depth": depth,
                        "source_entities": entity_ids
                    }
                }
                
        except Exception as e:
            self.log_error(e, "get_context", entity_ids=entity_ids)
            raise KnowledgeGraphError(f"Failed to get context: {str(e)}")
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get knowledge graph statistics.
        
        Returns:
            Dict[str, Any]: Graph statistics
            
        Raises:
            KnowledgeGraphError: If query fails
        """
        try:
            driver = await self._get_driver()
            
            cypher_query = """
            MATCH (n)
            OPTIONAL MATCH ()-[r]->()
            RETURN count(DISTINCT n) as node_count,
                   count(DISTINCT r) as relationship_count,
                   count(DISTINCT labels(n)) as label_count
            """
            
            async with driver.session(database=self.neo4j_database) as session:
                result = await session.run(cypher_query)
                record = await result.single()
                
                return {
                    "nodes": record["node_count"],
                    "relationships": record["relationship_count"],
                    "labels": record["label_count"],
                    "database": self.neo4j_database
                }
                
        except Exception as e:
            self.log_error(e, "get_stats")
            raise KnowledgeGraphError(f"Failed to get stats: {str(e)}")
    
    async def execute_cypher(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute a Cypher query.
        
        Args:
            query: Cypher query
            parameters: Query parameters
            
        Returns:
            Dict[str, Any]: Query results
            
        Raises:
            KnowledgeGraphError: If query fails
        """
        start_time = time.time()
        
        try:
            driver = await self._get_driver()
            
            async with driver.session(database=self.neo4j_database) as session:
                result = await session.run(query, parameters or {})
                records = await result.data()
                
                execution_time = round((time.time() - start_time) * 1000, 2)
                
                return {
                    "records": records,
                    "execution_time_ms": execution_time,
                    "record_count": len(records)
                }
                
        except Exception as e:
            self.log_error(e, "execute_cypher", query_length=len(query))
            raise KnowledgeGraphError(f"Cypher query failed: {str(e)}", query=query)
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        start_time = time.time()
        
        try:
            driver = await self._get_driver()
            
            # Simple connectivity test
            async with driver.session(database=self.neo4j_database) as session:
                result = await session.run("RETURN 1 as test")
                await result.single()
            
            response_time = round((time.time() - start_time) * 1000, 2)
            
            return {
                "status": "healthy",
                "response_time_ms": response_time,
                "database": self.neo4j_database,
                "uri": self.neo4j_uri
            }
            
        except Exception as e:
            self.log_error(e, "health_check")
            return {
                "status": "unhealthy",
                "error": str(e),
                "database": self.neo4j_database,
                "uri": self.neo4j_uri
            }
    
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        self.logger.info("Cleaning up knowledge graph service")
        
        if self.driver:
            await self.driver.close()
            self.driver = None
    
    def _extract_context(self, node: Dict[str, Any], query: str) -> Optional[str]:
        """Extract context information from node for search results."""
        # Simple context extraction (would be more sophisticated in production)
        properties = dict(node)
        
        # Look for name, title, or description properties
        for prop in ["name", "title", "description", "content"]:
            if prop in properties:
                value = str(properties[prop])
                if query.lower() in value.lower():
                    # Return snippet around the match
                    start = max(0, value.lower().find(query.lower()) - 50)
                    end = min(len(value), start + 150)
                    return value[start:end]
        
        return None
