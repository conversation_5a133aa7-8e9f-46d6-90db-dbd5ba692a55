"""
Service Communication Middleware

This module provides middleware for secure communication between
the Node.js backend and FastAPI chat service, including authentication,
request validation, and proper error handling.
"""

import time
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import structlog

from chat_service.core.config import Settings
from chat_service.core.exceptions import AuthenticationError, ValidationError
from chat_service.core.logging import LoggerMixin


class ServiceAuthenticationMiddleware(LoggerMixin):
    """
    Middleware for authenticating requests from Node.js backend services.
    
    Supports multiple authentication methods:
    - API Key authentication
    - Session-based authentication
    - Service-to-service tokens
    """
    
    def __init__(self, settings: Settings):
        """
        Initialize service authentication middleware.
        
        Args:
            settings: Application settings
        """
        self.settings = settings
        self.security = HTTPBearer(auto_error=False)
        
        # Valid API keys for service communication
        self.valid_api_keys = set()
        if settings.service_api_key:
            self.valid_api_keys.add(settings.service_api_key)
        
        # Session validation cache
        self.session_cache = {}
        self.cache_ttl = timedelta(minutes=5)
        
        self.logger.info(
            "Service authentication middleware initialized",
            has_api_key=bool(settings.service_api_key),
            cache_ttl_minutes=self.cache_ttl.total_seconds() / 60
        )
    
    async def authenticate_request(self, request: Request) -> Dict[str, Any]:
        """
        Authenticate incoming request from Node.js backend.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Dict[str, Any]: Authentication context
            
        Raises:
            AuthenticationError: If authentication fails
        """
        auth_context = {
            "authenticated": False,
            "session_id": None,
            "correlation_id": None,
            "source_service": None,
            "auth_method": None
        }
        
        # Extract headers
        session_id = request.headers.get("X-Session-ID")
        correlation_id = request.headers.get("X-Correlation-ID")
        api_key = request.headers.get("X-API-Key")
        authorization = request.headers.get("Authorization")
        
        # Set correlation ID
        auth_context["correlation_id"] = correlation_id or str(uuid.uuid4())
        
        # Try API key authentication first
        if api_key and self._validate_api_key(api_key):
            auth_context.update({
                "authenticated": True,
                "auth_method": "api_key",
                "source_service": "node_backend"
            })
            
            self.log_method_call(
                "authenticate_request",
                auth_method="api_key",
                correlation_id=auth_context["correlation_id"]
            )
            
            return auth_context
        
        # Try Bearer token authentication
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ", 1)[1]
            if self._validate_service_token(token):
                auth_context.update({
                    "authenticated": True,
                    "auth_method": "bearer_token",
                    "source_service": "node_backend"
                })
                
                self.log_method_call(
                    "authenticate_request",
                    auth_method="bearer_token",
                    correlation_id=auth_context["correlation_id"]
                )
                
                return auth_context
        
        # Try session-based authentication
        if session_id and self._validate_session(session_id):
            auth_context.update({
                "authenticated": True,
                "session_id": session_id,
                "auth_method": "session",
                "source_service": "node_backend"
            })
            
            self.log_method_call(
                "authenticate_request",
                auth_method="session",
                session_id=session_id,
                correlation_id=auth_context["correlation_id"]
            )
            
            return auth_context
        
        # Development mode - allow unauthenticated requests
        if self.settings.environment == "development":
            auth_context.update({
                "authenticated": True,
                "auth_method": "development",
                "source_service": "development"
            })
            
            self.logger.warning(
                "Development mode: allowing unauthenticated request",
                correlation_id=auth_context["correlation_id"]
            )
            
            return auth_context
        
        # Authentication failed
        self.log_error(
            AuthenticationError("Service authentication failed"),
            "authenticate_request",
            has_api_key=bool(api_key),
            has_authorization=bool(authorization),
            has_session_id=bool(session_id),
            correlation_id=auth_context["correlation_id"]
        )
        
        raise AuthenticationError(
            "Service authentication required",
            details={
                "correlation_id": auth_context["correlation_id"],
                "supported_methods": ["api_key", "bearer_token", "session"]
            }
        )
    
    def _validate_api_key(self, api_key: str) -> bool:
        """Validate API key."""
        return api_key in self.valid_api_keys
    
    def _validate_service_token(self, token: str) -> bool:
        """Validate service-to-service token."""
        # For now, accept any non-empty token
        # In production, implement proper JWT validation
        return bool(token and len(token) > 10)
    
    def _validate_session(self, session_id: str) -> bool:
        """Validate session ID with caching."""
        now = datetime.utcnow()
        
        # Check cache first
        if session_id in self.session_cache:
            cached_time = self.session_cache[session_id]
            if now - cached_time < self.cache_ttl:
                return True
            else:
                # Remove expired entry
                del self.session_cache[session_id]
        
        # Validate session (for now, accept any non-empty session)
        # In production, validate against session store
        is_valid = bool(session_id and len(session_id) > 5)
        
        if is_valid:
            # Cache valid session
            self.session_cache[session_id] = now
            
            # Clean up old cache entries
            self._cleanup_session_cache(now)
        
        return is_valid
    
    def _cleanup_session_cache(self, now: datetime) -> None:
        """Clean up expired session cache entries."""
        expired_sessions = [
            session_id for session_id, cached_time in self.session_cache.items()
            if now - cached_time >= self.cache_ttl
        ]
        
        for session_id in expired_sessions:
            del self.session_cache[session_id]


class RequestValidationMiddleware(LoggerMixin):
    """
    Middleware for validating requests from Node.js backend.
    
    Validates request format, required headers, and payload structure.
    """
    
    def __init__(self, settings: Settings):
        """
        Initialize request validation middleware.
        
        Args:
            settings: Application settings
        """
        self.settings = settings
        
        # Required headers for different endpoints
        self.required_headers = {
            "chat": ["Content-Type"],
            "stream": ["Content-Type"],
            "health": []
        }
        
        self.logger.info("Request validation middleware initialized")
    
    async def validate_request(
        self,
        request: Request,
        endpoint_type: str = "chat"
    ) -> Dict[str, Any]:
        """
        Validate incoming request.
        
        Args:
            request: FastAPI request object
            endpoint_type: Type of endpoint being accessed
            
        Returns:
            Dict[str, Any]: Validation context
            
        Raises:
            ValidationError: If validation fails
        """
        validation_context = {
            "valid": False,
            "endpoint_type": endpoint_type,
            "content_type": None,
            "content_length": 0,
            "user_agent": None
        }
        
        # Extract request metadata
        content_type = request.headers.get("Content-Type", "")
        content_length = int(request.headers.get("Content-Length", "0"))
        user_agent = request.headers.get("User-Agent", "")
        
        validation_context.update({
            "content_type": content_type,
            "content_length": content_length,
            "user_agent": user_agent
        })
        
        # Validate required headers
        required = self.required_headers.get(endpoint_type, [])
        missing_headers = []
        
        for header in required:
            if not request.headers.get(header):
                missing_headers.append(header)
        
        if missing_headers:
            raise ValidationError(
                f"Missing required headers: {', '.join(missing_headers)}",
                details={
                    "missing_headers": missing_headers,
                    "endpoint_type": endpoint_type
                }
            )
        
        # Validate content type for POST requests
        if request.method == "POST" and endpoint_type in ["chat", "stream"]:
            if not content_type.startswith("application/json"):
                raise ValidationError(
                    "Content-Type must be application/json",
                    details={
                        "received_content_type": content_type,
                        "expected_content_type": "application/json"
                    }
                )
        
        # Validate content length
        max_content_length = 1024 * 1024  # 1MB
        if content_length > max_content_length:
            raise ValidationError(
                f"Request too large: {content_length} bytes",
                details={
                    "content_length": content_length,
                    "max_allowed": max_content_length
                }
            )
        
        validation_context["valid"] = True
        
        self.log_method_call(
            "validate_request",
            endpoint_type=endpoint_type,
            content_length=content_length,
            user_agent=user_agent[:50] if user_agent else None
        )
        
        return validation_context


class ServiceCommunicationMiddleware(LoggerMixin):
    """
    Combined middleware for service communication.
    
    Handles authentication, validation, and request processing
    for communication between Node.js backend and FastAPI service.
    """
    
    def __init__(self, settings: Settings):
        """
        Initialize service communication middleware.
        
        Args:
            settings: Application settings
        """
        self.settings = settings
        self.auth_middleware = ServiceAuthenticationMiddleware(settings)
        self.validation_middleware = RequestValidationMiddleware(settings)
        
        # Request tracking
        self.active_requests = {}
        
        self.logger.info("Service communication middleware initialized")
    
    async def process_request(
        self,
        request: Request,
        endpoint_type: str = "chat"
    ) -> Dict[str, Any]:
        """
        Process incoming request with authentication and validation.
        
        Args:
            request: FastAPI request object
            endpoint_type: Type of endpoint being accessed
            
        Returns:
            Dict[str, Any]: Request processing context
            
        Raises:
            HTTPException: If processing fails
        """
        start_time = time.time()
        request_id = str(uuid.uuid4())
        
        try:
            # Authenticate request
            auth_context = await self.auth_middleware.authenticate_request(request)
            
            # Validate request
            validation_context = await self.validation_middleware.validate_request(
                request, endpoint_type
            )
            
            # Create processing context
            processing_context = {
                "request_id": request_id,
                "start_time": start_time,
                "endpoint_type": endpoint_type,
                "auth": auth_context,
                "validation": validation_context,
                "metadata": {
                    "method": request.method,
                    "url": str(request.url),
                    "client_ip": request.client.host if request.client else None,
                    "user_agent": request.headers.get("User-Agent", "")
                }
            }
            
            # Track active request
            self.active_requests[request_id] = processing_context
            
            # Add to request state
            request.state.request_id = request_id
            request.state.auth_context = auth_context
            request.state.processing_context = processing_context
            
            self.log_method_call(
                "process_request",
                request_id=request_id,
                endpoint_type=endpoint_type,
                auth_method=auth_context["auth_method"],
                correlation_id=auth_context["correlation_id"]
            )
            
            return processing_context
            
        except (AuthenticationError, ValidationError) as e:
            processing_time = round((time.time() - start_time) * 1000, 2)
            
            self.log_error(e, "process_request",
                          request_id=request_id,
                          endpoint_type=endpoint_type,
                          processing_time_ms=processing_time)
            
            # Convert to HTTP exception
            if isinstance(e, AuthenticationError):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "error": str(e),
                        "code": "AUTHENTICATION_FAILED",
                        "request_id": request_id
                    }
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "error": str(e),
                        "code": "VALIDATION_FAILED",
                        "request_id": request_id
                    }
                )
    
    def complete_request(self, request_id: str) -> Dict[str, Any]:
        """
        Complete request processing and return metrics.
        
        Args:
            request_id: Request identifier
            
        Returns:
            Dict[str, Any]: Request completion metrics
        """
        if request_id not in self.active_requests:
            return {}
        
        context = self.active_requests.pop(request_id)
        processing_time = round((time.time() - context["start_time"]) * 1000, 2)
        
        metrics = {
            "request_id": request_id,
            "processing_time_ms": processing_time,
            "endpoint_type": context["endpoint_type"],
            "auth_method": context["auth"]["auth_method"],
            "correlation_id": context["auth"]["correlation_id"]
        }
        
        self.log_method_result(
            "complete_request",
            **metrics
        )
        
        return metrics
    
    def get_active_requests_count(self) -> int:
        """Get count of active requests."""
        return len(self.active_requests)
    
    def get_request_metrics(self) -> Dict[str, Any]:
        """Get request processing metrics."""
        now = time.time()
        active_requests = []
        
        for request_id, context in self.active_requests.items():
            duration = round((now - context["start_time"]) * 1000, 2)
            active_requests.append({
                "request_id": request_id,
                "duration_ms": duration,
                "endpoint_type": context["endpoint_type"],
                "auth_method": context["auth"]["auth_method"]
            })
        
        return {
            "active_requests_count": len(active_requests),
            "active_requests": active_requests[:10]  # Limit to 10 for performance
        }
