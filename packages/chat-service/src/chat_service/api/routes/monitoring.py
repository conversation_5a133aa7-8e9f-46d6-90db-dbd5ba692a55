"""
Health Monitoring API Routes

This module provides API endpoints for health monitoring, service discovery,
metrics collection, and alerting functionality.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

import structlog
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from chat_service.core.service_discovery import (
    get_service_registry,
    ServiceRegistry,
    ServiceStatus
)
from chat_service.core.health_monitor import HealthMonitor, AlertLevel
from chat_service.core.config import get_settings


router = APIRouter()
logger = structlog.get_logger("api.monitoring")


class ServiceRegistrationRequest(BaseModel):
    """Service registration request model."""
    name: str = Field(..., description="Service name")
    url: str = Field(..., description="Service URL")
    version: str = Field(default="1.0.0", description="Service version")
    tags: List[str] = Field(default_factory=list, description="Service tags")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Service metadata")


class ServiceDiscoveryResponse(BaseModel):
    """Service discovery response model."""
    services: List[Dict[str, Any]] = Field(..., description="List of discovered services")
    total_count: int = Field(..., description="Total number of services")
    healthy_count: int = Field(..., description="Number of healthy services")


class HealthStatusResponse(BaseModel):
    """Health status response model."""
    overall_status: str = Field(..., description="Overall system health status")
    services: List[Dict[str, Any]] = Field(..., description="Individual service statuses")
    metrics_summary: Dict[str, Any] = Field(..., description="Metrics summary")
    active_alerts: int = Field(..., description="Number of active alerts")
    critical_alerts: int = Field(..., description="Number of critical alerts")
    last_updated: str = Field(..., description="Last update timestamp")


class AlertResponse(BaseModel):
    """Alert response model."""
    alerts: List[Dict[str, Any]] = Field(..., description="List of alerts")
    total_count: int = Field(..., description="Total number of alerts")


# Global health monitor instance
_health_monitor: Optional[HealthMonitor] = None


async def get_health_monitor() -> HealthMonitor:
    """Get or create health monitor instance."""
    global _health_monitor
    
    if _health_monitor is None:
        settings = get_settings()
        service_registry = get_service_registry(settings)
        _health_monitor = HealthMonitor(service_registry, settings)
        await _health_monitor.start_monitoring()
    
    return _health_monitor


@router.get("/health", response_model=HealthStatusResponse)
async def get_health_status(
    use_cache: bool = Query(default=True, description="Use cached status"),
    health_monitor: HealthMonitor = Depends(get_health_monitor)
) -> HealthStatusResponse:
    """
    Get overall system health status.
    
    Args:
        use_cache: Whether to use cached status
        health_monitor: Health monitor dependency
        
    Returns:
        HealthStatusResponse: System health status
    """
    try:
        health_status = await health_monitor.get_health_status(use_cache=use_cache)
        
        logger.info(
            "Health status retrieved",
            overall_status=health_status["overall_status"],
            active_alerts=health_status["active_alerts"],
            use_cache=use_cache
        )
        
        return HealthStatusResponse(**health_status)
        
    except Exception as e:
        logger.error("Failed to get health status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve health status")


@router.get("/services", response_model=ServiceDiscoveryResponse)
async def discover_services(
    tag: Optional[str] = Query(default=None, description="Filter by service tag"),
    status: Optional[str] = Query(default=None, description="Filter by service status"),
    use_cache: bool = Query(default=True, description="Use cached results"),
    service_registry: ServiceRegistry = Depends(get_service_registry)
) -> ServiceDiscoveryResponse:
    """
    Discover services based on criteria.
    
    Args:
        tag: Filter by service tag
        status: Filter by service status
        use_cache: Whether to use cached results
        service_registry: Service registry dependency
        
    Returns:
        ServiceDiscoveryResponse: Discovered services
    """
    try:
        # Parse status filter
        status_filter = None
        if status:
            try:
                status_filter = ServiceStatus(status.lower())
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid status filter: {status}. Valid values: healthy, unhealthy, degraded, unknown"
                )
        
        # Discover services
        services = await service_registry.discover_services(
            tag=tag,
            status=status_filter,
            use_cache=use_cache
        )
        
        # Convert to response format
        service_data = []
        healthy_count = 0
        
        for service in services:
            service_info = {
                "name": service.name,
                "url": service.url,
                "version": service.version,
                "status": service.status.value,
                "response_time_ms": service.response_time_ms,
                "consecutive_failures": service.consecutive_failures,
                "error_count": service.error_count,
                "last_check": service.last_check.isoformat() if service.last_check else None,
                "tags": list(service.tags),
                "metadata": service.metadata
            }
            service_data.append(service_info)
            
            if service.status == ServiceStatus.HEALTHY:
                healthy_count += 1
        
        logger.info(
            "Services discovered",
            total_count=len(services),
            healthy_count=healthy_count,
            tag_filter=tag,
            status_filter=status
        )
        
        return ServiceDiscoveryResponse(
            services=service_data,
            total_count=len(services),
            healthy_count=healthy_count
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to discover services", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to discover services")


@router.post("/services/register")
async def register_service(
    request: ServiceRegistrationRequest,
    service_registry: ServiceRegistry = Depends(get_service_registry)
) -> Dict[str, str]:
    """
    Register a new service.
    
    Args:
        request: Service registration request
        service_registry: Service registry dependency
        
    Returns:
        Dict[str, str]: Registration confirmation
    """
    try:
        await service_registry.register_service(
            name=request.name,
            url=request.url,
            version=request.version,
            metadata=request.metadata,
            tags=set(request.tags)
        )
        
        logger.info(
            "Service registered",
            service_name=request.name,
            service_url=request.url,
            version=request.version
        )
        
        return {
            "message": f"Service '{request.name}' registered successfully",
            "service_name": request.name,
            "status": "registered"
        }
        
    except Exception as e:
        logger.error("Failed to register service", error=str(e), service_name=request.name)
        raise HTTPException(status_code=500, detail="Failed to register service")


@router.delete("/services/{service_name}")
async def deregister_service(
    service_name: str,
    service_registry: ServiceRegistry = Depends(get_service_registry)
) -> Dict[str, str]:
    """
    Deregister a service.
    
    Args:
        service_name: Name of service to deregister
        service_registry: Service registry dependency
        
    Returns:
        Dict[str, str]: Deregistration confirmation
    """
    try:
        success = await service_registry.deregister_service(service_name)
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Service '{service_name}' not found")
        
        logger.info("Service deregistered", service_name=service_name)
        
        return {
            "message": f"Service '{service_name}' deregistered successfully",
            "service_name": service_name,
            "status": "deregistered"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to deregister service", error=str(e), service_name=service_name)
        raise HTTPException(status_code=500, detail="Failed to deregister service")


@router.get("/services/{service_name}")
async def get_service_info(
    service_name: str,
    service_registry: ServiceRegistry = Depends(get_service_registry)
) -> Dict[str, Any]:
    """
    Get information about a specific service.
    
    Args:
        service_name: Name of service to get info for
        service_registry: Service registry dependency
        
    Returns:
        Dict[str, Any]: Service information
    """
    try:
        service = await service_registry.get_service(service_name)
        
        if not service:
            raise HTTPException(status_code=404, detail=f"Service '{service_name}' not found")
        
        return {
            "name": service.name,
            "url": service.url,
            "version": service.version,
            "status": service.status.value,
            "response_time_ms": service.response_time_ms,
            "consecutive_failures": service.consecutive_failures,
            "error_count": service.error_count,
            "last_check": service.last_check.isoformat() if service.last_check else None,
            "tags": list(service.tags),
            "metadata": service.metadata
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get service info", error=str(e), service_name=service_name)
        raise HTTPException(status_code=500, detail="Failed to get service information")


@router.get("/alerts", response_model=AlertResponse)
async def get_alerts(
    resolved: Optional[bool] = Query(default=None, description="Filter by resolved status"),
    level: Optional[str] = Query(default=None, description="Filter by alert level"),
    service_name: Optional[str] = Query(default=None, description="Filter by service name"),
    limit: int = Query(default=100, ge=1, le=1000, description="Maximum number of alerts"),
    health_monitor: HealthMonitor = Depends(get_health_monitor)
) -> AlertResponse:
    """
    Get alerts with filtering.
    
    Args:
        resolved: Filter by resolved status
        level: Filter by alert level
        service_name: Filter by service name
        limit: Maximum number of alerts to return
        health_monitor: Health monitor dependency
        
    Returns:
        AlertResponse: Filtered alerts
    """
    try:
        # Parse level filter
        level_filter = None
        if level:
            try:
                level_filter = AlertLevel(level.lower())
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid level filter: {level}. Valid values: info, warning, error, critical"
                )
        
        # Get alerts
        alerts = await health_monitor.get_alerts(
            resolved=resolved,
            level=level_filter,
            service_name=service_name,
            limit=limit
        )
        
        logger.info(
            "Alerts retrieved",
            total_count=len(alerts),
            resolved_filter=resolved,
            level_filter=level,
            service_filter=service_name
        )
        
        return AlertResponse(
            alerts=alerts,
            total_count=len(alerts)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get alerts", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve alerts")


@router.get("/metrics")
async def get_metrics(
    metric_name: Optional[str] = Query(default=None, description="Filter by metric name"),
    service_name: Optional[str] = Query(default=None, description="Filter by service name"),
    hours: int = Query(default=1, ge=1, le=24, description="Hours of data to retrieve"),
    health_monitor: HealthMonitor = Depends(get_health_monitor)
) -> Dict[str, Any]:
    """
    Get metrics data.
    
    Args:
        metric_name: Filter by metric name
        service_name: Filter by service name
        hours: Hours of data to retrieve
        health_monitor: Health monitor dependency
        
    Returns:
        Dict[str, Any]: Metrics data
    """
    try:
        # Get metrics from health monitor
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        filtered_metrics = {}
        
        for name, metrics_list in health_monitor.metrics.items():
            # Filter by metric name
            if metric_name and name != metric_name:
                continue
            
            # Filter by time
            recent_metrics = [
                m for m in metrics_list
                if m.timestamp >= cutoff_time
            ]
            
            # Filter by service name
            if service_name:
                recent_metrics = [
                    m for m in recent_metrics
                    if m.labels.get("service") == service_name
                ]
            
            if recent_metrics:
                filtered_metrics[name] = [
                    {
                        "value": m.value,
                        "timestamp": m.timestamp.isoformat(),
                        "labels": m.labels,
                        "unit": m.unit
                    }
                    for m in recent_metrics
                ]
        
        logger.info(
            "Metrics retrieved",
            metric_count=len(filtered_metrics),
            metric_filter=metric_name,
            service_filter=service_name,
            hours=hours
        )
        
        return {
            "metrics": filtered_metrics,
            "time_range_hours": hours,
            "filters": {
                "metric_name": metric_name,
                "service_name": service_name
            },
            "retrieved_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve metrics")


@router.get("/registry/status")
async def get_registry_status(
    service_registry: ServiceRegistry = Depends(get_service_registry)
) -> Dict[str, Any]:
    """
    Get service registry status.
    
    Args:
        service_registry: Service registry dependency
        
    Returns:
        Dict[str, Any]: Registry status information
    """
    try:
        status = await service_registry.get_registry_status()
        
        logger.info(
            "Registry status retrieved",
            total_services=status["total_services"],
            healthy_services=status["healthy_services"]
        )
        
        return status
        
    except Exception as e:
        logger.error("Failed to get registry status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get registry status")
