"""
Circuit Breaker Monitoring Routes

This module provides API endpoints for monitoring and managing
circuit breakers in the chat service.
"""

from typing import Dict, List, Any

import structlog
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from chat_service.core.circuit_breaker import get_registry


router = APIRouter()
logger = structlog.get_logger("api.circuit_breaker")


class CircuitBreakerStatus(BaseModel):
    """Circuit breaker status response model."""
    name: str
    state: str
    total_calls: int
    successful_calls: int
    failed_calls: int
    success_rate: float
    consecutive_failures: int
    consecutive_successes: int
    last_failure_time: float = None
    last_success_time: float = None
    state_changes: int
    state_changed_at: float
    config: Dict[str, Any]


class CircuitBreakerSummary(BaseModel):
    """Circuit breaker summary response model."""
    total_breakers: int
    healthy_breakers: int
    open_breakers: int
    half_open_breakers: int
    breakers: List[CircuitBreakerStatus]


@router.get("/", response_model=CircuitBreakerSummary)
async def get_circuit_breaker_status() -> CircuitBreakerSummary:
    """
    Get status of all circuit breakers.
    
    Returns:
        CircuitBreakerSummary: Summary of all circuit breakers
    """
    registry = get_registry()
    all_stats = registry.get_all_stats()
    
    breakers = []
    healthy_count = 0
    open_count = 0
    half_open_count = 0
    
    for name, stats in all_stats.items():
        breaker_status = CircuitBreakerStatus(**stats)
        breakers.append(breaker_status)
        
        if stats["state"] == "closed":
            healthy_count += 1
        elif stats["state"] == "open":
            open_count += 1
        elif stats["state"] == "half_open":
            half_open_count += 1
    
    logger.info(
        "Circuit breaker status requested",
        total_breakers=len(breakers),
        healthy=healthy_count,
        open=open_count,
        half_open=half_open_count
    )
    
    return CircuitBreakerSummary(
        total_breakers=len(breakers),
        healthy_breakers=healthy_count,
        open_breakers=open_count,
        half_open_breakers=half_open_count,
        breakers=breakers
    )


@router.get("/{breaker_name}", response_model=CircuitBreakerStatus)
async def get_circuit_breaker_details(breaker_name: str) -> CircuitBreakerStatus:
    """
    Get details of a specific circuit breaker.
    
    Args:
        breaker_name: Name of the circuit breaker
        
    Returns:
        CircuitBreakerStatus: Circuit breaker details
        
    Raises:
        HTTPException: If circuit breaker not found
    """
    registry = get_registry()
    breaker = registry.get_breaker(breaker_name)
    
    if not breaker:
        raise HTTPException(
            status_code=404,
            detail=f"Circuit breaker '{breaker_name}' not found"
        )
    
    stats = breaker.get_stats()
    
    logger.info(
        "Circuit breaker details requested",
        breaker_name=breaker_name,
        state=stats["state"]
    )
    
    return CircuitBreakerStatus(**stats)


@router.post("/{breaker_name}/reset")
async def reset_circuit_breaker(breaker_name: str) -> Dict[str, str]:
    """
    Reset a circuit breaker to closed state.
    
    Args:
        breaker_name: Name of the circuit breaker
        
    Returns:
        Dict[str, str]: Reset confirmation
        
    Raises:
        HTTPException: If circuit breaker not found
    """
    registry = get_registry()
    breaker = registry.get_breaker(breaker_name)
    
    if not breaker:
        raise HTTPException(
            status_code=404,
            detail=f"Circuit breaker '{breaker_name}' not found"
        )
    
    await breaker.reset()
    
    logger.info(
        "Circuit breaker reset",
        breaker_name=breaker_name
    )
    
    return {"message": f"Circuit breaker '{breaker_name}' has been reset"}


@router.post("/{breaker_name}/force-open")
async def force_open_circuit_breaker(breaker_name: str) -> Dict[str, str]:
    """
    Force a circuit breaker to open state.
    
    Args:
        breaker_name: Name of the circuit breaker
        
    Returns:
        Dict[str, str]: Force open confirmation
        
    Raises:
        HTTPException: If circuit breaker not found
    """
    registry = get_registry()
    breaker = registry.get_breaker(breaker_name)
    
    if not breaker:
        raise HTTPException(
            status_code=404,
            detail=f"Circuit breaker '{breaker_name}' not found"
        )
    
    await breaker.force_open()
    
    logger.warning(
        "Circuit breaker forced open",
        breaker_name=breaker_name
    )
    
    return {"message": f"Circuit breaker '{breaker_name}' has been forced open"}


@router.post("/{breaker_name}/force-close")
async def force_close_circuit_breaker(breaker_name: str) -> Dict[str, str]:
    """
    Force a circuit breaker to closed state.
    
    Args:
        breaker_name: Name of the circuit breaker
        
    Returns:
        Dict[str, str]: Force close confirmation
        
    Raises:
        HTTPException: If circuit breaker not found
    """
    registry = get_registry()
    breaker = registry.get_breaker(breaker_name)
    
    if not breaker:
        raise HTTPException(
            status_code=404,
            detail=f"Circuit breaker '{breaker_name}' not found"
        )
    
    await breaker.force_close()
    
    logger.info(
        "Circuit breaker forced closed",
        breaker_name=breaker_name
    )
    
    return {"message": f"Circuit breaker '{breaker_name}' has been forced closed"}


@router.post("/reset-all")
async def reset_all_circuit_breakers() -> Dict[str, Any]:
    """
    Reset all circuit breakers to closed state.
    
    Returns:
        Dict[str, Any]: Reset confirmation with count
    """
    registry = get_registry()
    breaker_names = registry.list_breakers()
    
    await registry.reset_all()
    
    logger.info(
        "All circuit breakers reset",
        total_breakers=len(breaker_names),
        breaker_names=breaker_names
    )
    
    return {
        "message": "All circuit breakers have been reset",
        "total_breakers": len(breaker_names),
        "breaker_names": breaker_names
    }


@router.get("/metrics/summary")
async def get_circuit_breaker_metrics() -> Dict[str, Any]:
    """
    Get circuit breaker metrics summary.
    
    Returns:
        Dict[str, Any]: Metrics summary
    """
    registry = get_registry()
    all_stats = registry.get_all_stats()
    
    total_calls = sum(stats["total_calls"] for stats in all_stats.values())
    total_successful = sum(stats["successful_calls"] for stats in all_stats.values())
    total_failed = sum(stats["failed_calls"] for stats in all_stats.values())
    
    state_counts = {}
    for stats in all_stats.values():
        state = stats["state"]
        state_counts[state] = state_counts.get(state, 0) + 1
    
    overall_success_rate = (
        total_successful / total_calls if total_calls > 0 else 0.0
    )
    
    return {
        "total_breakers": len(all_stats),
        "total_calls": total_calls,
        "total_successful_calls": total_successful,
        "total_failed_calls": total_failed,
        "overall_success_rate": round(overall_success_rate, 4),
        "state_distribution": state_counts,
        "breaker_names": list(all_stats.keys())
    }
