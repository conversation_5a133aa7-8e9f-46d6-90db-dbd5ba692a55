"""
Knowledge Graph API Routes

This module provides REST API endpoints for knowledge graph operations,
including search, entity retrieval, and relationship queries.
"""

from typing import List, Optional, Dict, Any

import structlog
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from chat_service.core.exceptions import ValidationError, NotFoundError
from chat_service.services.dependencies import get_kg_service
from chat_service.services.kg_query_service import KnowledgeGraphQueryService
from chat_service.services.rag_pipeline_service import RAGPipelineService
from chat_service.services.knowledge_graph_service import KnowledgeGraphService


router = APIRouter()
logger = structlog.get_logger("api.knowledge_graph")


class SearchRequest(BaseModel):
    """Knowledge graph search request model."""
    query: str = Field(..., min_length=1, max_length=1000, description="Search query")
    limit: int = Field(default=10, ge=1, le=100, description="Maximum results")
    threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Similarity threshold")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="Search filters")


class Entity(BaseModel):
    """Knowledge graph entity model."""
    id: str = Field(..., description="Entity identifier")
    type: str = Field(..., description="Entity type")
    properties: Dict[str, Any] = Field(..., description="Entity properties")
    labels: List[str] = Field(default=[], description="Entity labels")


class Relationship(BaseModel):
    """Knowledge graph relationship model."""
    id: str = Field(..., description="Relationship identifier")
    type: str = Field(..., description="Relationship type")
    source_id: str = Field(..., description="Source entity ID")
    target_id: str = Field(..., description="Target entity ID")
    properties: Dict[str, Any] = Field(default={}, description="Relationship properties")


class SearchResult(BaseModel):
    """Search result model."""
    entity: Entity = Field(..., description="Found entity")
    score: float = Field(..., description="Similarity score")
    context: Optional[str] = Field(default=None, description="Context information")


class SearchResponse(BaseModel):
    """Search response model."""
    query: str = Field(..., description="Original search query")
    results: List[SearchResult] = Field(..., description="Search results")
    total_count: int = Field(..., description="Total number of results")
    execution_time_ms: float = Field(..., description="Query execution time")


class GraphContext(BaseModel):
    """Graph context model."""
    entities: List[Entity] = Field(..., description="Context entities")
    relationships: List[Relationship] = Field(..., description="Context relationships")
    metadata: Dict[str, Any] = Field(default={}, description="Context metadata")


@router.post("/search", response_model=SearchResponse)
async def search_knowledge_graph(
    request: SearchRequest,
    kg_service: KnowledgeGraphService = Depends(get_kg_service)
) -> SearchResponse:
    """
    Search the knowledge graph.
    
    Args:
        request: Search request parameters
        kg_service: Knowledge graph service dependency
        
    Returns:
        SearchResponse: Search results
        
    Raises:
        HTTPException: If search fails
    """
    logger.info(
        "Searching knowledge graph",
        query=request.query,
        limit=request.limit,
        threshold=request.threshold
    )
    
    try:
        results = await kg_service.search(
            query=request.query,
            limit=request.limit,
            threshold=request.threshold,
            filters=request.filters or {}
        )
        
        logger.info(
            "Knowledge graph search completed",
            query=request.query,
            results_count=len(results["results"]),
            execution_time_ms=results["execution_time_ms"]
        )
        
        return SearchResponse(**results)
        
    except ValidationError as e:
        logger.warning(
            "Knowledge graph search validation failed",
            query=request.query,
            error=str(e)
        )
        raise HTTPException(status_code=400, detail=str(e))
        
    except Exception as e:
        logger.error(
            "Knowledge graph search failed",
            query=request.query,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Search failed")


@router.get("/entities/{entity_id}", response_model=Entity)
async def get_entity(
    entity_id: str,
    kg_service: KnowledgeGraphService = Depends(get_kg_service)
) -> Entity:
    """
    Get entity by ID.
    
    Args:
        entity_id: Entity identifier
        kg_service: Knowledge graph service dependency
        
    Returns:
        Entity: Entity information
        
    Raises:
        HTTPException: If entity not found
    """
    try:
        entity = await kg_service.get_entity(entity_id)
        
        if not entity:
            raise NotFoundError(
                message="Entity not found",
                resource_type="entity",
                resource_id=entity_id
            )
        
        return Entity(**entity)
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
        
    except Exception as e:
        logger.error(
            "Failed to get entity",
            entity_id=entity_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to get entity")


@router.get("/entities/{entity_id}/relationships", response_model=List[Relationship])
async def get_entity_relationships(
    entity_id: str,
    direction: str = Query(default="both", regex="^(incoming|outgoing|both)$"),
    relationship_type: Optional[str] = Query(default=None),
    limit: int = Query(default=50, ge=1, le=200),
    kg_service: KnowledgeGraphService = Depends(get_kg_service)
) -> List[Relationship]:
    """
    Get entity relationships.
    
    Args:
        entity_id: Entity identifier
        direction: Relationship direction (incoming, outgoing, both)
        relationship_type: Filter by relationship type
        limit: Maximum number of relationships
        kg_service: Knowledge graph service dependency
        
    Returns:
        List[Relationship]: Entity relationships
        
    Raises:
        HTTPException: If entity not found or query fails
    """
    try:
        relationships = await kg_service.get_entity_relationships(
            entity_id=entity_id,
            direction=direction,
            relationship_type=relationship_type,
            limit=limit
        )
        
        return [Relationship(**rel) for rel in relationships]
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
        
    except Exception as e:
        logger.error(
            "Failed to get entity relationships",
            entity_id=entity_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to get entity relationships")


@router.get("/context", response_model=GraphContext)
async def get_graph_context(
    entity_ids: List[str] = Query(..., description="Entity IDs for context"),
    depth: int = Query(default=1, ge=1, le=3, description="Context depth"),
    kg_service: KnowledgeGraphService = Depends(get_kg_service)
) -> GraphContext:
    """
    Get graph context for entities.
    
    Args:
        entity_ids: List of entity identifiers
        depth: Context depth (number of hops)
        kg_service: Knowledge graph service dependency
        
    Returns:
        GraphContext: Graph context information
        
    Raises:
        HTTPException: If context retrieval fails
    """
    logger.info(
        "Getting graph context",
        entity_ids=entity_ids,
        depth=depth
    )
    
    try:
        context = await kg_service.get_context(
            entity_ids=entity_ids,
            depth=depth
        )
        
        logger.info(
            "Graph context retrieved",
            entity_ids=entity_ids,
            entities_count=len(context["entities"]),
            relationships_count=len(context["relationships"])
        )
        
        return GraphContext(**context)
        
    except ValidationError as e:
        logger.warning(
            "Graph context validation failed",
            entity_ids=entity_ids,
            error=str(e)
        )
        raise HTTPException(status_code=400, detail=str(e))
        
    except Exception as e:
        logger.error(
            "Failed to get graph context",
            entity_ids=entity_ids,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to get graph context")


@router.get("/stats")
async def get_graph_stats(
    kg_service: KnowledgeGraphService = Depends(get_kg_service)
) -> Dict[str, Any]:
    """
    Get knowledge graph statistics.
    
    Args:
        kg_service: Knowledge graph service dependency
        
    Returns:
        Dict[str, Any]: Graph statistics
        
    Raises:
        HTTPException: If stats retrieval fails
    """
    try:
        stats = await kg_service.get_stats()
        
        return stats
        
    except Exception as e:
        logger.error(
            "Failed to get graph stats",
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to get graph statistics")


class CypherQueryRequest(BaseModel):
    """Cypher query request model."""
    query: str = Field(..., min_length=1, description="Cypher query")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Query parameters")


@router.post("/cypher")
async def execute_cypher_query(
    request: CypherQueryRequest,
    kg_service: KnowledgeGraphService = Depends(get_kg_service)
) -> Dict[str, Any]:
    """
    Execute a Cypher query.
    
    Args:
        query: Cypher query string
        parameters: Query parameters
        kg_service: Knowledge graph service dependency
        
    Returns:
        Dict[str, Any]: Query results
        
    Raises:
        HTTPException: If query execution fails
    """
    logger.info(
        "Executing Cypher query",
        query_length=len(request.query),
        has_parameters=request.parameters is not None
    )

    try:
        results = await kg_service.execute_cypher(
            query=request.query,
            parameters=request.parameters or {}
        )
        
        logger.info(
            "Cypher query executed",
            results_count=len(results.get("records", [])),
            execution_time_ms=results.get("execution_time_ms", 0)
        )
        
        return results
        
    except ValidationError as e:
        logger.warning(
            "Cypher query validation failed",
            error=str(e)
        )
        raise HTTPException(status_code=400, detail=str(e))
        
    except Exception as e:
        logger.error(
            "Cypher query execution failed",
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Query execution failed")


class RAGContextRequest(BaseModel):
    """RAG context request model."""
    query: str = Field(..., min_length=1, description="Search query")
    max_context_length: Optional[int] = Field(default=4000, description="Maximum context length")
    group_id: Optional[str] = Field(default=None, description="Group ID filter")
    limit: Optional[int] = Field(default=10, description="Maximum results")


@router.post("/rag/context")
async def get_rag_context(
    request: RAGContextRequest,
    kg_service: KnowledgeGraphService = Depends(get_kg_service)
) -> Dict[str, Any]:
    """
    Get RAG-formatted context for a query.

    Args:
        request: RAG context request
        kg_service: Knowledge graph service dependency

    Returns:
        Dict[str, Any]: Formatted context for LLM consumption

    Raises:
        HTTPException: If context retrieval fails
    """
    logger.info(
        "Getting RAG context",
        query=request.query,
        max_context_length=request.max_context_length
    )

    try:
        # Check if enhanced service is available
        if hasattr(kg_service, 'get_context_for_llm'):
            # Use enhanced service
            context_result = await kg_service.get_context_for_llm(
                query=request.query,
                max_context_length=request.max_context_length
            )

            return {
                "context": context_result["context"],
                "citations": context_result["citations"],
                "metadata": {
                    "entities_found": context_result["entities_found"],
                    "search_query": context_result["search_query"],
                    "truncated": context_result.get("truncated", False),
                    "service_type": "enhanced"
                }
            }
        else:
            # Use basic service with search
            search_results = await kg_service.search(
                query=request.query,
                limit=request.limit,
                filters={"group_id": request.group_id} if request.group_id else None
            )

            # Format basic context
            context_parts = []
            citations = []

            for i, result in enumerate(search_results["results"][:5], 1):
                entity = result.get("entity", result)
                name = entity.get("properties", {}).get("name", "Unknown")
                entity_type = entity.get("type", "Entity")

                context_parts.append(f"{i}. {name} ({entity_type})")
                citations.append({
                    "id": entity.get("id", ""),
                    "name": name,
                    "type": entity_type
                })

            context = "**Knowledge Graph Results:**\n\n" + "\n".join(context_parts) if context_parts else "No relevant information found."

            return {
                "context": context,
                "citations": citations,
                "metadata": {
                    "entities_found": len(search_results["results"]),
                    "search_query": request.query,
                    "truncated": False,
                    "service_type": "basic"
                }
            }

    except Exception as e:
        logger.error(
            "Failed to get RAG context",
            query=request.query,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to get RAG context")


@router.get("/statistics")
async def get_kg_statistics(
    kg_service: KnowledgeGraphService = Depends(get_kg_service)
) -> Dict[str, Any]:
    """
    Get knowledge graph service statistics.

    Args:
        kg_service: Knowledge graph service dependency

    Returns:
        Dict[str, Any]: Service statistics and performance metrics

    Raises:
        HTTPException: If statistics retrieval fails
    """
    logger.info("Getting knowledge graph statistics")

    try:
        # Check if enhanced service with statistics is available
        if hasattr(kg_service, 'get_query_statistics'):
            stats = await kg_service.get_query_statistics()
            return {
                "service_type": "enhanced",
                "statistics": stats
            }
        else:
            # Basic service statistics
            return {
                "service_type": "basic",
                "statistics": {
                    "message": "Basic service - limited statistics available"
                }
            }

    except Exception as e:
        logger.error(
            "Failed to get statistics",
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to get statistics")


@router.get("/initial-graph")
async def get_initial_graph_data(
    limit: int = Query(default=100, ge=1, le=1000, description="Maximum relationships"),
    kg_service: KnowledgeGraphService = Depends(get_kg_service)
) -> Dict[str, Any]:
    """
    Get initial graph data for visualization.

    Args:
        limit: Maximum number of relationships to return
        kg_service: Knowledge graph service dependency

    Returns:
        Dict[str, Any]: Graph data with nodes and relationships

    Raises:
        HTTPException: If graph data retrieval fails
    """
    logger.info(
        "Getting initial graph data",
        limit=limit
    )

    try:
        # Check if enhanced query service is available
        if hasattr(kg_service, 'get_initial_graph'):
            graph_data = await kg_service.get_initial_graph(limit=limit)
            return graph_data
        else:
            # Fallback to basic search
            search_results = await kg_service.search(
                query="",  # Empty query to get general results
                limit=limit
            )

            # Convert search results to graph format
            nodes = []
            relationships = []

            for result in search_results["results"]:
                entity = result.get("entity", result)
                nodes.append({
                    "id": entity.get("id", ""),
                    "labels": entity.get("labels", []),
                    "properties": entity.get("properties", {})
                })

            return {
                "nodes": nodes,
                "relationships": relationships,
                "summary": {
                    "node_count": len(nodes),
                    "relationship_count": len(relationships)
                }
            }

    except Exception as e:
        logger.error(
            "Failed to get initial graph data",
            limit=limit,
            error=str(e),
            error_type=type(e).__name__
        )
        raise HTTPException(status_code=500, detail="Failed to get initial graph data")
