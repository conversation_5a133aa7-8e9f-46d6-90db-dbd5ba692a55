"""
Health Check Routes

This module provides health check endpoints for monitoring
the chat service and its dependencies.
"""

import asyncio
import time
from typing import Dict, Any

import structlog
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from chat_service.core.config import get_settings
from chat_service.services.dependencies import get_chat_service, get_kg_service


router = APIRouter()
logger = structlog.get_logger("api.health")


class HealthStatus(BaseModel):
    """Health status response model."""
    status: str
    timestamp: str
    version: str
    environment: str
    uptime_seconds: float


class DetailedHealthStatus(BaseModel):
    """Detailed health status response model."""
    status: str
    timestamp: str
    version: str
    environment: str
    uptime_seconds: float
    services: Dict[str, Dict[str, Any]]
    system: Dict[str, Any]


# Track service start time
_start_time = time.time()


@router.get("/", response_model=HealthStatus)
async def health_check() -> HealthStatus:
    """
    Basic health check endpoint.
    
    Returns basic service health information without checking dependencies.
    This endpoint should be fast and always available for load balancer checks.
    
    Returns:
        HealthStatus: Basic health status information
    """
    settings = get_settings()
    current_time = time.time()
    
    return HealthStatus(
        status="healthy",
        timestamp=time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime(current_time)),
        version="1.0.0",
        environment=settings.environment,
        uptime_seconds=round(current_time - _start_time, 2)
    )


@router.get("/ready", response_model=DetailedHealthStatus)
async def readiness_check(
    chat_service=Depends(get_chat_service),
    kg_service=Depends(get_kg_service)
) -> DetailedHealthStatus:
    """
    Readiness check endpoint.
    
    Checks if the service is ready to handle requests by verifying
    all dependencies are available and healthy.
    
    Args:
        chat_service: Chat service dependency
        kg_service: Knowledge graph service dependency
        
    Returns:
        DetailedHealthStatus: Detailed health status information
        
    Raises:
        HTTPException: If service is not ready
    """
    settings = get_settings()
    current_time = time.time()
    
    # Check service dependencies
    services_status = {}
    overall_status = "healthy"
    
    # Check chat service
    try:
        chat_health = await chat_service.health_check()
        services_status["chat_service"] = {
            "status": "healthy",
            "response_time_ms": chat_health.get("response_time_ms", 0),
            "details": chat_health
        }
    except Exception as e:
        logger.error("Chat service health check failed", error=str(e))
        services_status["chat_service"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        overall_status = "unhealthy"
    
    # Check knowledge graph service
    try:
        kg_health = await kg_service.health_check()
        services_status["knowledge_graph"] = {
            "status": "healthy",
            "response_time_ms": kg_health.get("response_time_ms", 0),
            "details": kg_health
        }
    except Exception as e:
        logger.error("Knowledge graph service health check failed", error=str(e))
        services_status["knowledge_graph"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        overall_status = "unhealthy"
    
    # System information
    system_info = {
        "python_version": "3.9+",
        "memory_usage_mb": _get_memory_usage(),
        "cpu_count": _get_cpu_count(),
    }
    
    response = DetailedHealthStatus(
        status=overall_status,
        timestamp=time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime(current_time)),
        version="1.0.0",
        environment=settings.environment,
        uptime_seconds=round(current_time - _start_time, 2),
        services=services_status,
        system=system_info
    )
    
    # Return 503 if not healthy
    if overall_status != "healthy":
        raise HTTPException(status_code=503, detail=response.dict())
    
    return response


@router.get("/live", response_model=HealthStatus)
async def liveness_check() -> HealthStatus:
    """
    Liveness check endpoint.
    
    Checks if the service is alive and responding. This is a simple
    check that doesn't verify dependencies.
    
    Returns:
        HealthStatus: Basic health status information
    """
    settings = get_settings()
    current_time = time.time()
    
    return HealthStatus(
        status="alive",
        timestamp=time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime(current_time)),
        version="1.0.0",
        environment=settings.environment,
        uptime_seconds=round(current_time - _start_time, 2)
    )


@router.get("/dependencies")
async def dependencies_check(
    chat_service=Depends(get_chat_service),
    kg_service=Depends(get_kg_service)
) -> Dict[str, Any]:
    """
    Check individual service dependencies.
    
    Provides detailed information about each service dependency
    including response times and specific health metrics.
    
    Args:
        chat_service: Chat service dependency
        kg_service: Knowledge graph service dependency
        
    Returns:
        Dict[str, Any]: Dependency health information
    """
    dependencies = {}
    
    # Check each dependency with timeout
    async def check_dependency(name: str, service, method_name: str = "health_check"):
        """Check individual dependency with timeout."""
        try:
            start_time = time.time()
            method = getattr(service, method_name)
            result = await asyncio.wait_for(method(), timeout=10.0)
            response_time = round((time.time() - start_time) * 1000, 2)
            
            return {
                "status": "healthy",
                "response_time_ms": response_time,
                "details": result
            }
        except asyncio.TimeoutError:
            return {
                "status": "timeout",
                "error": "Health check timed out after 10 seconds"
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    # Check all dependencies concurrently
    results = await asyncio.gather(
        check_dependency("chat_service", chat_service),
        check_dependency("knowledge_graph", kg_service),
        return_exceptions=True
    )
    
    dependencies["chat_service"] = results[0] if not isinstance(results[0], Exception) else {
        "status": "error",
        "error": str(results[0])
    }
    
    dependencies["knowledge_graph"] = results[1] if not isinstance(results[1], Exception) else {
        "status": "error", 
        "error": str(results[1])
    }
    
    return {
        "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
        "dependencies": dependencies
    }


def _get_memory_usage() -> float:
    """
    Get current memory usage in MB.
    
    Returns:
        float: Memory usage in megabytes
    """
    try:
        import psutil
        process = psutil.Process()
        return round(process.memory_info().rss / 1024 / 1024, 2)
    except ImportError:
        return 0.0


def _get_cpu_count() -> int:
    """
    Get CPU count.
    
    Returns:
        int: Number of CPU cores
    """
    try:
        import os
        return os.cpu_count() or 1
    except Exception:
        return 1
