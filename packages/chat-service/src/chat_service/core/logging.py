"""
Logging Configuration

This module configures structured logging for the chat service using structlog.
Provides consistent logging format and context across the application.
"""

import logging
import sys
from typing import Any, Dict

import structlog
from structlog.types import EventDict, Processor


def add_app_context(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """
    Add application context to log events.
    
    Args:
        logger: Logger instance
        method_name: Method name
        event_dict: Event dictionary
        
    Returns:
        EventDict: Enhanced event dictionary
    """
    event_dict["service"] = "chat-service"
    event_dict["version"] = "1.0.0"
    return event_dict


def add_correlation_id(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """
    Add correlation ID to log events if available.
    
    Args:
        logger: Logger instance
        method_name: Method name
        event_dict: Event dictionary
        
    Returns:
        EventDict: Enhanced event dictionary
    """
    # Try to get correlation ID from context
    # This would be set by middleware
    correlation_id = getattr(logger, "_correlation_id", None)
    if correlation_id:
        event_dict["correlation_id"] = correlation_id
    return event_dict


def filter_sensitive_data(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """
    Filter sensitive data from log events.
    
    Args:
        logger: Logger instance
        method_name: Method name
        event_dict: Event dictionary
        
    Returns:
        EventDict: Filtered event dictionary
    """
    sensitive_keys = {
        "password", "token", "api_key", "secret", "authorization",
        "cookie", "session", "private_key", "access_token"
    }
    
    def filter_dict(data: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively filter sensitive data from dictionary."""
        if not isinstance(data, dict):
            return data
            
        filtered = {}
        for key, value in data.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                filtered[key] = "***REDACTED***"
            elif isinstance(value, dict):
                filtered[key] = filter_dict(value)
            elif isinstance(value, list):
                filtered[key] = [
                    filter_dict(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                filtered[key] = value
        return filtered
    
    # Filter the entire event dict
    return filter_dict(event_dict)


def configure_logging(log_level: str = "INFO", environment: str = "development") -> None:
    """
    Configure structured logging for the application.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        environment: Environment name (development, staging, production)
    """
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level.upper()),
    )
    
    # Configure processors based on environment
    processors: list[Processor] = [
        structlog.contextvars.merge_contextvars,
        add_app_context,
        add_correlation_id,
        filter_sensitive_data,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
    ]
    
    if environment == "development":
        # Development: Human-readable console output
        processors.extend([
            structlog.dev.set_exc_info,
            structlog.processors.TimeStamper(fmt="ISO"),
            structlog.dev.ConsoleRenderer(colors=True)
        ])
    else:
        # Production: JSON output for log aggregation
        processors.extend([
            structlog.processors.format_exc_info,
            structlog.processors.TimeStamper(fmt="ISO"),
            structlog.processors.JSONRenderer()
        ])
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, log_level.upper())
        ),
        logger_factory=structlog.WriteLoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Set log levels for third-party libraries
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("neo4j").setLevel(logging.WARNING)
    
    # Create a test log entry
    logger = structlog.get_logger()
    logger.info(
        "Logging configured",
        log_level=log_level,
        environment=environment,
        processors_count=len(processors)
    )


def get_logger(name: str = None) -> structlog.BoundLogger:
    """
    Get a configured logger instance.
    
    Args:
        name: Logger name (optional)
        
    Returns:
        structlog.BoundLogger: Configured logger instance
    """
    if name:
        return structlog.get_logger(name)
    return structlog.get_logger()


def bind_correlation_id(logger: structlog.BoundLogger, correlation_id: str) -> structlog.BoundLogger:
    """
    Bind correlation ID to logger.
    
    Args:
        logger: Logger instance
        correlation_id: Correlation ID
        
    Returns:
        structlog.BoundLogger: Logger with bound correlation ID
    """
    return logger.bind(correlation_id=correlation_id)


def bind_user_context(
    logger: structlog.BoundLogger,
    user_id: str = None,
    session_id: str = None
) -> structlog.BoundLogger:
    """
    Bind user context to logger.
    
    Args:
        logger: Logger instance
        user_id: User ID (optional)
        session_id: Session ID (optional)
        
    Returns:
        structlog.BoundLogger: Logger with bound user context
    """
    context = {}
    if user_id:
        context["user_id"] = user_id
    if session_id:
        context["session_id"] = session_id
    
    return logger.bind(**context)


def bind_request_context(
    logger: structlog.BoundLogger,
    method: str,
    path: str,
    remote_addr: str = None
) -> structlog.BoundLogger:
    """
    Bind request context to logger.
    
    Args:
        logger: Logger instance
        method: HTTP method
        path: Request path
        remote_addr: Remote address (optional)
        
    Returns:
        structlog.BoundLogger: Logger with bound request context
    """
    context = {
        "http_method": method,
        "http_path": path
    }
    if remote_addr:
        context["remote_addr"] = remote_addr
    
    return logger.bind(**context)


class LoggerMixin:
    """
    Mixin class to add logging capabilities to other classes.
    """
    
    @property
    def logger(self) -> structlog.BoundLogger:
        """Get logger instance for this class."""
        return get_logger(self.__class__.__name__)
    
    def log_method_call(self, method_name: str, **kwargs) -> None:
        """
        Log method call with parameters.
        
        Args:
            method_name: Name of the method being called
            **kwargs: Method parameters to log
        """
        self.logger.debug(
            f"Calling {method_name}",
            method=method_name,
            parameters=kwargs
        )
    
    def log_method_result(self, method_name: str, result: Any = None, **kwargs) -> None:
        """
        Log method result.
        
        Args:
            method_name: Name of the method
            result: Method result (optional)
            **kwargs: Additional context to log
        """
        context = {"method": method_name}
        if result is not None:
            context["result_type"] = type(result).__name__
        context.update(kwargs)
        
        self.logger.debug(f"Completed {method_name}", **context)
    
    def log_error(self, error: Exception, context: str = None, **kwargs) -> None:
        """
        Log error with context.
        
        Args:
            error: Exception that occurred
            context: Error context (optional)
            **kwargs: Additional context to log
        """
        log_context = {
            "error_type": type(error).__name__,
            "error_message": str(error)
        }
        if context:
            log_context["context"] = context
        log_context.update(kwargs)
        
        self.logger.error("Error occurred", **log_context)
