"""
Service Discovery and Health Monitoring

This module provides service discovery, health monitoring, and service
registry functionality for the microservices architecture.
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

import httpx
import structlog

from chat_service.core.config import Settings
from chat_service.core.exceptions import ServiceDiscoveryError
from chat_service.core.logging import LoggerMixin


class ServiceStatus(Enum):
    """Service health status enumeration."""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"
    UNKNOWN = "unknown"


@dataclass
class ServiceInfo:
    """Service information and metadata."""
    name: str
    url: str
    version: str
    status: ServiceStatus = ServiceStatus.UNKNOWN
    last_check: Optional[datetime] = None
    response_time_ms: float = 0.0
    error_count: int = 0
    consecutive_failures: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: Set[str] = field(default_factory=set)


@dataclass
class HealthCheckConfig:
    """Health check configuration."""
    endpoint: str = "/health"
    interval_seconds: int = 30
    timeout_seconds: int = 5
    failure_threshold: int = 3
    recovery_threshold: int = 2


class ServiceRegistry(LoggerMixin):
    """
    Service registry for managing service discovery and health monitoring.
    
    Provides centralized service registration, health checking, and
    service discovery functionality for microservices architecture.
    """
    
    def __init__(self, settings: Optional[Settings] = None):
        """
        Initialize service registry.
        
        Args:
            settings: Application settings
        """
        self.settings = settings
        self.services: Dict[str, ServiceInfo] = {}
        self.health_check_config = HealthCheckConfig()
        self.http_client = httpx.AsyncClient(timeout=self.health_check_config.timeout_seconds)
        
        # Health check task
        self._health_check_task: Optional[asyncio.Task] = None
        self._running = False
        
        # Service discovery cache
        self._discovery_cache: Dict[str, List[ServiceInfo]] = {}
        self._cache_ttl = timedelta(minutes=5)
        self._last_cache_update = datetime.utcnow()
        
        self.logger.info(
            "Service registry initialized",
            health_check_interval=self.health_check_config.interval_seconds,
            timeout=self.health_check_config.timeout_seconds
        )
    
    async def register_service(
        self,
        name: str,
        url: str,
        version: str = "1.0.0",
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[Set[str]] = None
    ) -> None:
        """
        Register a service in the registry.
        
        Args:
            name: Service name
            url: Service URL
            version: Service version
            metadata: Additional service metadata
            tags: Service tags for categorization
        """
        service_info = ServiceInfo(
            name=name,
            url=url.rstrip("/"),
            version=version,
            metadata=metadata or {},
            tags=tags or set()
        )
        
        self.services[name] = service_info
        
        # Perform initial health check
        await self._check_service_health(service_info)
        
        self.log_method_call(
            "register_service",
            service_name=name,
            service_url=url,
            version=version,
            status=service_info.status.value
        )
    
    async def deregister_service(self, name: str) -> bool:
        """
        Deregister a service from the registry.
        
        Args:
            name: Service name
            
        Returns:
            bool: True if service was deregistered, False if not found
        """
        if name in self.services:
            del self.services[name]
            self.log_method_call("deregister_service", service_name=name)
            return True
        return False
    
    async def discover_services(
        self,
        tag: Optional[str] = None,
        status: Optional[ServiceStatus] = None,
        use_cache: bool = True
    ) -> List[ServiceInfo]:
        """
        Discover services based on criteria.
        
        Args:
            tag: Filter by service tag
            status: Filter by service status
            use_cache: Whether to use cached results
            
        Returns:
            List[ServiceInfo]: List of matching services
        """
        cache_key = f"{tag}:{status.value if status else 'all'}"
        
        # Check cache first
        if use_cache and self._is_cache_valid():
            cached_result = self._discovery_cache.get(cache_key)
            if cached_result:
                return cached_result
        
        # Filter services
        filtered_services = []
        
        for service in self.services.values():
            # Filter by tag
            if tag and tag not in service.tags:
                continue
            
            # Filter by status
            if status and service.status != status:
                continue
            
            filtered_services.append(service)
        
        # Update cache
        if use_cache:
            self._discovery_cache[cache_key] = filtered_services
            self._last_cache_update = datetime.utcnow()
        
        self.log_method_call(
            "discover_services",
            tag=tag,
            status=status.value if status else None,
            found_count=len(filtered_services)
        )
        
        return filtered_services
    
    async def get_service(self, name: str) -> Optional[ServiceInfo]:
        """
        Get service information by name.
        
        Args:
            name: Service name
            
        Returns:
            Optional[ServiceInfo]: Service information or None if not found
        """
        return self.services.get(name)
    
    async def get_healthy_service(self, name: str) -> Optional[ServiceInfo]:
        """
        Get a healthy service instance by name.
        
        Args:
            name: Service name
            
        Returns:
            Optional[ServiceInfo]: Healthy service or None if unavailable
        """
        service = self.services.get(name)
        if service and service.status == ServiceStatus.HEALTHY:
            return service
        return None
    
    async def start_health_monitoring(self) -> None:
        """Start background health monitoring."""
        if self._running:
            return
        
        self._running = True
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        
        self.logger.info("Health monitoring started")
    
    async def stop_health_monitoring(self) -> None:
        """Stop background health monitoring."""
        self._running = False
        
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
            self._health_check_task = None
        
        self.logger.info("Health monitoring stopped")
    
    async def _health_check_loop(self) -> None:
        """Background health check loop."""
        while self._running:
            try:
                # Check all registered services
                for service in self.services.values():
                    await self._check_service_health(service)
                
                # Wait for next check interval
                await asyncio.sleep(self.health_check_config.interval_seconds)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log_error(e, "_health_check_loop")
                await asyncio.sleep(5)  # Brief pause on error
    
    async def _check_service_health(self, service: ServiceInfo) -> None:
        """
        Check health of a specific service.
        
        Args:
            service: Service to check
        """
        start_time = time.time()
        
        try:
            # Make health check request
            health_url = f"{service.url}{self.health_check_config.endpoint}"
            response = await self.http_client.get(health_url)
            
            response_time = round((time.time() - start_time) * 1000, 2)
            service.response_time_ms = response_time
            service.last_check = datetime.utcnow()
            
            # Determine status based on response
            if response.status_code == 200:
                # Parse health response
                try:
                    health_data = response.json()
                    reported_status = health_data.get("status", "healthy").lower()
                    
                    if reported_status == "healthy":
                        service.status = ServiceStatus.HEALTHY
                        service.consecutive_failures = 0
                    elif reported_status == "degraded":
                        service.status = ServiceStatus.DEGRADED
                        service.consecutive_failures = 0
                    else:
                        service.status = ServiceStatus.UNHEALTHY
                        service.consecutive_failures += 1
                        
                except Exception:
                    # If we can't parse response, consider it healthy if 200
                    service.status = ServiceStatus.HEALTHY
                    service.consecutive_failures = 0
            else:
                # Non-200 response
                service.status = ServiceStatus.UNHEALTHY
                service.consecutive_failures += 1
                service.error_count += 1
            
            self.logger.debug(
                "Health check completed",
                service_name=service.name,
                status=service.status.value,
                response_time_ms=response_time,
                consecutive_failures=service.consecutive_failures
            )
            
        except Exception as e:
            # Health check failed
            response_time = round((time.time() - start_time) * 1000, 2)
            service.response_time_ms = response_time
            service.last_check = datetime.utcnow()
            service.status = ServiceStatus.UNHEALTHY
            service.consecutive_failures += 1
            service.error_count += 1
            
            self.logger.warning(
                "Health check failed",
                service_name=service.name,
                error=str(e),
                consecutive_failures=service.consecutive_failures,
                response_time_ms=response_time
            )
    
    def _is_cache_valid(self) -> bool:
        """Check if discovery cache is still valid."""
        return datetime.utcnow() - self._last_cache_update < self._cache_ttl
    
    async def get_registry_status(self) -> Dict[str, Any]:
        """
        Get overall registry status and statistics.
        
        Returns:
            Dict[str, Any]: Registry status information
        """
        total_services = len(self.services)
        healthy_services = sum(1 for s in self.services.values() if s.status == ServiceStatus.HEALTHY)
        degraded_services = sum(1 for s in self.services.values() if s.status == ServiceStatus.DEGRADED)
        unhealthy_services = sum(1 for s in self.services.values() if s.status == ServiceStatus.UNHEALTHY)
        
        # Calculate average response time
        response_times = [s.response_time_ms for s in self.services.values() if s.response_time_ms > 0]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        return {
            "total_services": total_services,
            "healthy_services": healthy_services,
            "degraded_services": degraded_services,
            "unhealthy_services": unhealthy_services,
            "health_percentage": round((healthy_services / total_services * 100) if total_services > 0 else 0, 2),
            "average_response_time_ms": round(avg_response_time, 2),
            "monitoring_active": self._running,
            "last_cache_update": self._last_cache_update.isoformat(),
            "services": [
                {
                    "name": service.name,
                    "url": service.url,
                    "status": service.status.value,
                    "version": service.version,
                    "response_time_ms": service.response_time_ms,
                    "consecutive_failures": service.consecutive_failures,
                    "last_check": service.last_check.isoformat() if service.last_check else None,
                    "tags": list(service.tags)
                }
                for service in self.services.values()
            ]
        }
    
    async def cleanup(self) -> None:
        """Cleanup registry resources."""
        await self.stop_health_monitoring()
        await self.http_client.aclose()
        self.logger.info("Service registry cleaned up")


# Global service registry instance
_service_registry: Optional[ServiceRegistry] = None


def get_service_registry(settings: Optional[Settings] = None) -> ServiceRegistry:
    """
    Get or create global service registry instance.
    
    Args:
        settings: Application settings
        
    Returns:
        ServiceRegistry: Global service registry instance
    """
    global _service_registry
    
    if _service_registry is None:
        _service_registry = ServiceRegistry(settings)
    
    return _service_registry


async def initialize_service_discovery(settings: Settings) -> ServiceRegistry:
    """
    Initialize service discovery with default services.
    
    Args:
        settings: Application settings
        
    Returns:
        ServiceRegistry: Initialized service registry
    """
    registry = get_service_registry(settings)
    
    # Register default services
    await registry.register_service(
        name="chat-service",
        url=settings.fastapi_url or "http://localhost:8000",
        version="1.0.0",
        tags={"chat", "api", "fastapi"},
        metadata={
            "description": "FastAPI chat service",
            "environment": settings.environment
        }
    )
    
    # Register Node.js backend if URL is available
    if hasattr(settings, 'nodejs_backend_url') and settings.nodejs_backend_url:
        await registry.register_service(
            name="nodejs-backend",
            url=settings.nodejs_backend_url,
            version="1.0.0",
            tags={"backend", "api", "nodejs"},
            metadata={
                "description": "Node.js backend service",
                "environment": settings.environment
            }
        )
    
    # Start health monitoring
    await registry.start_health_monitoring()
    
    return registry
