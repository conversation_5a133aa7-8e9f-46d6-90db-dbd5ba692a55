"""
Health Monitoring System

This module provides comprehensive health monitoring, metrics collection,
and alerting for the microservices architecture.
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

import structlog

from chat_service.core.config import Settings
from chat_service.core.logging import LoggerMixin
from chat_service.core.service_discovery import ServiceRegistry, ServiceStatus


class AlertLevel(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class HealthMetric:
    """Health metric data point."""
    name: str
    value: float
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)
    unit: str = ""


@dataclass
class Alert:
    """Health monitoring alert."""
    id: str
    level: AlertLevel
    title: str
    description: str
    timestamp: datetime
    service_name: Optional[str] = None
    metric_name: Optional[str] = None
    threshold_value: Optional[float] = None
    current_value: Optional[float] = None
    resolved: bool = False
    resolved_at: Optional[datetime] = None


@dataclass
class HealthThreshold:
    """Health monitoring threshold configuration."""
    metric_name: str
    warning_threshold: Optional[float] = None
    error_threshold: Optional[float] = None
    critical_threshold: Optional[float] = None
    comparison: str = "greater_than"  # greater_than, less_than, equals
    duration_seconds: int = 60  # How long threshold must be exceeded


class HealthMonitor(LoggerMixin):
    """
    Comprehensive health monitoring system.
    
    Monitors service health, collects metrics, generates alerts,
    and provides health dashboards for microservices architecture.
    """
    
    def __init__(
        self,
        service_registry: ServiceRegistry,
        settings: Optional[Settings] = None
    ):
        """
        Initialize health monitor.
        
        Args:
            service_registry: Service registry instance
            settings: Application settings
        """
        self.service_registry = service_registry
        self.settings = settings
        
        # Metrics storage
        self.metrics: Dict[str, List[HealthMetric]] = {}
        self.metrics_retention_hours = 24
        
        # Alerts
        self.alerts: List[Alert] = []
        self.alert_handlers: List[Callable[[Alert], None]] = []
        
        # Thresholds
        self.thresholds: Dict[str, HealthThreshold] = {}
        self._setup_default_thresholds()
        
        # Monitoring state
        self._monitoring_task: Optional[asyncio.Task] = None
        self._running = False
        self.monitoring_interval = 30  # seconds
        
        # Health status cache
        self._health_cache: Dict[str, Any] = {}
        self._cache_ttl = timedelta(seconds=30)
        self._last_cache_update = datetime.utcnow()
        
        self.logger.info(
            "Health monitor initialized",
            monitoring_interval=self.monitoring_interval,
            metrics_retention_hours=self.metrics_retention_hours
        )
    
    def _setup_default_thresholds(self) -> None:
        """Set up default health monitoring thresholds."""
        self.thresholds.update({
            "response_time_ms": HealthThreshold(
                metric_name="response_time_ms",
                warning_threshold=1000.0,
                error_threshold=3000.0,
                critical_threshold=5000.0,
                comparison="greater_than",
                duration_seconds=60
            ),
            "error_rate": HealthThreshold(
                metric_name="error_rate",
                warning_threshold=0.05,  # 5%
                error_threshold=0.10,    # 10%
                critical_threshold=0.25, # 25%
                comparison="greater_than",
                duration_seconds=120
            ),
            "consecutive_failures": HealthThreshold(
                metric_name="consecutive_failures",
                warning_threshold=3.0,
                error_threshold=5.0,
                critical_threshold=10.0,
                comparison="greater_than",
                duration_seconds=0  # Immediate
            ),
            "service_availability": HealthThreshold(
                metric_name="service_availability",
                warning_threshold=0.95,  # 95%
                error_threshold=0.90,    # 90%
                critical_threshold=0.80, # 80%
                comparison="less_than",
                duration_seconds=300
            )
        })
    
    async def start_monitoring(self) -> None:
        """Start health monitoring."""
        if self._running:
            return
        
        self._running = True
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        self.logger.info("Health monitoring started")
    
    async def stop_monitoring(self) -> None:
        """Stop health monitoring."""
        self._running = False
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
            self._monitoring_task = None
        
        self.logger.info("Health monitoring stopped")
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while self._running:
            try:
                # Collect metrics from all services
                await self._collect_service_metrics()
                
                # Check thresholds and generate alerts
                await self._check_thresholds()
                
                # Clean up old metrics
                self._cleanup_old_metrics()
                
                # Update health cache
                await self._update_health_cache()
                
                # Wait for next monitoring cycle
                await asyncio.sleep(self.monitoring_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log_error(e, "_monitoring_loop")
                await asyncio.sleep(5)  # Brief pause on error
    
    async def _collect_service_metrics(self) -> None:
        """Collect metrics from all registered services."""
        registry_status = await self.service_registry.get_registry_status()
        
        # Overall system metrics
        self._record_metric(
            "total_services",
            registry_status["total_services"],
            {"category": "system"}
        )
        
        self._record_metric(
            "healthy_services",
            registry_status["healthy_services"],
            {"category": "system"}
        )
        
        self._record_metric(
            "service_availability",
            registry_status["health_percentage"] / 100.0,
            {"category": "system"}
        )
        
        # Individual service metrics
        for service_data in registry_status["services"]:
            service_name = service_data["name"]
            labels = {"service": service_name}
            
            # Response time
            self._record_metric(
                "response_time_ms",
                service_data["response_time_ms"],
                labels
            )
            
            # Consecutive failures
            self._record_metric(
                "consecutive_failures",
                service_data["consecutive_failures"],
                labels
            )
            
            # Service status (as numeric)
            status_value = {
                "healthy": 1.0,
                "degraded": 0.5,
                "unhealthy": 0.0,
                "unknown": -1.0
            }.get(service_data["status"], -1.0)
            
            self._record_metric(
                "service_status",
                status_value,
                labels
            )
    
    def _record_metric(
        self,
        name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ) -> None:
        """Record a metric data point."""
        metric = HealthMetric(
            name=name,
            value=value,
            timestamp=datetime.utcnow(),
            labels=labels or {}
        )
        
        if name not in self.metrics:
            self.metrics[name] = []
        
        self.metrics[name].append(metric)
    
    async def _check_thresholds(self) -> None:
        """Check all thresholds and generate alerts."""
        for threshold in self.thresholds.values():
            await self._check_metric_threshold(threshold)
    
    async def _check_metric_threshold(self, threshold: HealthThreshold) -> None:
        """Check a specific metric threshold."""
        metric_name = threshold.metric_name
        
        if metric_name not in self.metrics:
            return
        
        # Get recent metrics
        cutoff_time = datetime.utcnow() - timedelta(seconds=threshold.duration_seconds)
        recent_metrics = [
            m for m in self.metrics[metric_name]
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return
        
        # Check each service separately if metrics have service labels
        services_to_check = set()
        for metric in recent_metrics:
            service = metric.labels.get("service")
            if service:
                services_to_check.add(service)
            else:
                services_to_check.add("system")
        
        for service in services_to_check:
            service_metrics = [
                m for m in recent_metrics
                if m.labels.get("service", "system") == service
            ]
            
            if service_metrics:
                avg_value = sum(m.value for m in service_metrics) / len(service_metrics)
                await self._evaluate_threshold(threshold, avg_value, service)
    
    async def _evaluate_threshold(
        self,
        threshold: HealthThreshold,
        value: float,
        service_name: str
    ) -> None:
        """Evaluate a threshold and generate alerts if needed."""
        alert_level = None
        threshold_value = None
        
        # Determine alert level
        if threshold.critical_threshold is not None:
            if self._compare_value(value, threshold.critical_threshold, threshold.comparison):
                alert_level = AlertLevel.CRITICAL
                threshold_value = threshold.critical_threshold
        
        if alert_level is None and threshold.error_threshold is not None:
            if self._compare_value(value, threshold.error_threshold, threshold.comparison):
                alert_level = AlertLevel.ERROR
                threshold_value = threshold.error_threshold
        
        if alert_level is None and threshold.warning_threshold is not None:
            if self._compare_value(value, threshold.warning_threshold, threshold.comparison):
                alert_level = AlertLevel.WARNING
                threshold_value = threshold.warning_threshold
        
        # Generate alert if threshold exceeded
        if alert_level:
            await self._generate_alert(
                level=alert_level,
                title=f"{threshold.metric_name} threshold exceeded",
                description=f"Service {service_name} {threshold.metric_name} is {value:.2f}, exceeding {alert_level.value} threshold of {threshold_value:.2f}",
                service_name=service_name,
                metric_name=threshold.metric_name,
                threshold_value=threshold_value,
                current_value=value
            )
    
    def _compare_value(self, value: float, threshold: float, comparison: str) -> bool:
        """Compare value against threshold."""
        if comparison == "greater_than":
            return value > threshold
        elif comparison == "less_than":
            return value < threshold
        elif comparison == "equals":
            return abs(value - threshold) < 0.001
        return False
    
    async def _generate_alert(
        self,
        level: AlertLevel,
        title: str,
        description: str,
        service_name: Optional[str] = None,
        metric_name: Optional[str] = None,
        threshold_value: Optional[float] = None,
        current_value: Optional[float] = None
    ) -> None:
        """Generate a health monitoring alert."""
        alert_id = f"{service_name or 'system'}_{metric_name or 'unknown'}_{int(time.time())}"
        
        # Check if similar alert already exists
        existing_alert = next(
            (a for a in self.alerts
             if not a.resolved
             and a.service_name == service_name
             and a.metric_name == metric_name
             and a.level == level),
            None
        )
        
        if existing_alert:
            # Update existing alert
            existing_alert.current_value = current_value
            existing_alert.timestamp = datetime.utcnow()
            return
        
        # Create new alert
        alert = Alert(
            id=alert_id,
            level=level,
            title=title,
            description=description,
            timestamp=datetime.utcnow(),
            service_name=service_name,
            metric_name=metric_name,
            threshold_value=threshold_value,
            current_value=current_value
        )
        
        self.alerts.append(alert)
        
        # Notify alert handlers
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                self.log_error(e, "_generate_alert", alert_id=alert_id)
        
        self.logger.warning(
            "Health alert generated",
            alert_id=alert_id,
            level=level.value,
            service_name=service_name,
            metric_name=metric_name,
            current_value=current_value,
            threshold_value=threshold_value
        )
    
    def _cleanup_old_metrics(self) -> None:
        """Remove old metrics beyond retention period."""
        cutoff_time = datetime.utcnow() - timedelta(hours=self.metrics_retention_hours)
        
        for metric_name in self.metrics:
            self.metrics[metric_name] = [
                m for m in self.metrics[metric_name]
                if m.timestamp >= cutoff_time
            ]
    
    async def _update_health_cache(self) -> None:
        """Update health status cache."""
        registry_status = await self.service_registry.get_registry_status()
        
        # Get recent alerts
        recent_alerts = [
            a for a in self.alerts
            if not a.resolved and a.timestamp >= datetime.utcnow() - timedelta(hours=1)
        ]
        
        self._health_cache = {
            "overall_status": self._determine_overall_status(registry_status, recent_alerts),
            "services": registry_status["services"],
            "metrics_summary": self._get_metrics_summary(),
            "active_alerts": len([a for a in recent_alerts if not a.resolved]),
            "critical_alerts": len([a for a in recent_alerts if a.level == AlertLevel.CRITICAL and not a.resolved]),
            "last_updated": datetime.utcnow().isoformat()
        }
        
        self._last_cache_update = datetime.utcnow()
    
    def _determine_overall_status(
        self,
        registry_status: Dict[str, Any],
        recent_alerts: List[Alert]
    ) -> str:
        """Determine overall system health status."""
        # Check for critical alerts
        critical_alerts = [a for a in recent_alerts if a.level == AlertLevel.CRITICAL and not a.resolved]
        if critical_alerts:
            return "critical"
        
        # Check service availability
        health_percentage = registry_status.get("health_percentage", 0)
        if health_percentage < 80:
            return "critical"
        elif health_percentage < 95:
            return "degraded"
        
        # Check for error alerts
        error_alerts = [a for a in recent_alerts if a.level == AlertLevel.ERROR and not a.resolved]
        if error_alerts:
            return "degraded"
        
        # Check for warning alerts
        warning_alerts = [a for a in recent_alerts if a.level == AlertLevel.WARNING and not a.resolved]
        if warning_alerts:
            return "warning"
        
        return "healthy"
    
    def _get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of recent metrics."""
        summary = {}
        
        for metric_name, metrics_list in self.metrics.items():
            if not metrics_list:
                continue
            
            # Get metrics from last hour
            recent_metrics = [
                m for m in metrics_list
                if m.timestamp >= datetime.utcnow() - timedelta(hours=1)
            ]
            
            if recent_metrics:
                values = [m.value for m in recent_metrics]
                summary[metric_name] = {
                    "current": values[-1] if values else 0,
                    "average": sum(values) / len(values),
                    "min": min(values),
                    "max": max(values),
                    "count": len(values)
                }
        
        return summary
    
    def add_alert_handler(self, handler: Callable[[Alert], None]) -> None:
        """Add an alert handler function."""
        self.alert_handlers.append(handler)
    
    async def get_health_status(self, use_cache: bool = True) -> Dict[str, Any]:
        """
        Get current health status.
        
        Args:
            use_cache: Whether to use cached status
            
        Returns:
            Dict[str, Any]: Health status information
        """
        if use_cache and self._is_cache_valid():
            return self._health_cache
        
        await self._update_health_cache()
        return self._health_cache
    
    def _is_cache_valid(self) -> bool:
        """Check if health cache is still valid."""
        return datetime.utcnow() - self._last_cache_update < self._cache_ttl
    
    async def get_alerts(
        self,
        resolved: Optional[bool] = None,
        level: Optional[AlertLevel] = None,
        service_name: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get alerts with filtering.
        
        Args:
            resolved: Filter by resolved status
            level: Filter by alert level
            service_name: Filter by service name
            limit: Maximum number of alerts to return
            
        Returns:
            List[Dict[str, Any]]: Filtered alerts
        """
        filtered_alerts = self.alerts
        
        # Apply filters
        if resolved is not None:
            filtered_alerts = [a for a in filtered_alerts if a.resolved == resolved]
        
        if level is not None:
            filtered_alerts = [a for a in filtered_alerts if a.level == level]
        
        if service_name is not None:
            filtered_alerts = [a for a in filtered_alerts if a.service_name == service_name]
        
        # Sort by timestamp (newest first) and limit
        filtered_alerts.sort(key=lambda a: a.timestamp, reverse=True)
        filtered_alerts = filtered_alerts[:limit]
        
        # Convert to dict format
        return [
            {
                "id": alert.id,
                "level": alert.level.value,
                "title": alert.title,
                "description": alert.description,
                "timestamp": alert.timestamp.isoformat(),
                "service_name": alert.service_name,
                "metric_name": alert.metric_name,
                "threshold_value": alert.threshold_value,
                "current_value": alert.current_value,
                "resolved": alert.resolved,
                "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None
            }
            for alert in filtered_alerts
        ]
    
    async def cleanup(self) -> None:
        """Cleanup health monitor resources."""
        await self.stop_monitoring()
        self.logger.info("Health monitor cleaned up")
