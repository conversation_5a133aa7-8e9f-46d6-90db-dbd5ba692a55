"""
Circuit Breaker Pattern Implementation

This module provides a circuit breaker pattern for resilient service calls
with automatic failover and recovery mechanisms.
"""

import asyncio
import time
from enum import Enum
from typing import Any, Callable, Optional, Dict, Union
from dataclasses import dataclass, field

import structlog

from chat_service.core.exceptions import CircuitBreakerError
from chat_service.core.logging import LoggerMixin


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit is open, calls fail fast
    HALF_OPEN = "half_open"  # Testing if service has recovered


@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration."""
    failure_threshold: int = 5
    recovery_timeout: float = 60.0
    expected_exception: Union[type, tuple] = Exception
    success_threshold: int = 3  # For half-open state
    timeout: float = 30.0
    name: str = "default"


@dataclass
class CircuitBreakerStats:
    """Circuit breaker statistics."""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    consecutive_failures: int = 0
    consecutive_successes: int = 0
    last_failure_time: Optional[float] = None
    last_success_time: Optional[float] = None
    state_changes: int = 0
    current_state: CircuitState = CircuitState.CLOSED
    state_changed_at: float = field(default_factory=time.time)


class CircuitBreaker(LoggerMixin):
    """
    Circuit breaker implementation for resilient service calls.
    
    The circuit breaker monitors calls to a service and opens the circuit
    when failures exceed a threshold, preventing further calls and allowing
    the service to recover.
    """
    
    def __init__(self, config: CircuitBreakerConfig) -> None:
        """
        Initialize circuit breaker.
        
        Args:
            config: Circuit breaker configuration
        """
        self.config = config
        self.stats = CircuitBreakerStats()
        self._lock = asyncio.Lock()
        
        self.logger.info(
            "Circuit breaker initialized",
            name=config.name,
            failure_threshold=config.failure_threshold,
            recovery_timeout=config.recovery_timeout
        )
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute a function call through the circuit breaker.
        
        Args:
            func: Function to call
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Any: Function result
            
        Raises:
            CircuitBreakerError: If circuit is open
            Exception: Original function exceptions
        """
        async with self._lock:
            await self._check_state()
            
            if self.stats.current_state == CircuitState.OPEN:
                self.logger.warning(
                    "Circuit breaker is open, failing fast",
                    name=self.config.name,
                    consecutive_failures=self.stats.consecutive_failures
                )
                raise CircuitBreakerError(
                    f"Circuit breaker '{self.config.name}' is open",
                    service_name=self.config.name,
                    details={
                        "state": self.stats.current_state.value,
                        "consecutive_failures": self.stats.consecutive_failures,
                        "last_failure_time": self.stats.last_failure_time
                    }
                )
        
        # Execute the function call
        start_time = time.time()
        self.stats.total_calls += 1
        
        try:
            # Add timeout to the call
            if asyncio.iscoroutinefunction(func):
                result = await asyncio.wait_for(
                    func(*args, **kwargs),
                    timeout=self.config.timeout
                )
            else:
                result = func(*args, **kwargs)
            
            # Record success
            await self._record_success()
            
            execution_time = time.time() - start_time
            self.logger.debug(
                "Circuit breaker call succeeded",
                name=self.config.name,
                execution_time_ms=round(execution_time * 1000, 2)
            )
            
            return result
            
        except self.config.expected_exception as e:
            # Record failure
            await self._record_failure(e)
            
            execution_time = time.time() - start_time
            self.logger.warning(
                "Circuit breaker call failed",
                name=self.config.name,
                error=str(e),
                execution_time_ms=round(execution_time * 1000, 2),
                consecutive_failures=self.stats.consecutive_failures
            )
            
            raise
        
        except asyncio.TimeoutError as e:
            # Record timeout as failure
            await self._record_failure(e)
            
            self.logger.warning(
                "Circuit breaker call timed out",
                name=self.config.name,
                timeout_seconds=self.config.timeout,
                consecutive_failures=self.stats.consecutive_failures
            )
            
            raise
    
    async def _check_state(self) -> None:
        """Check and update circuit breaker state."""
        current_time = time.time()
        
        if self.stats.current_state == CircuitState.OPEN:
            # Check if recovery timeout has passed
            if (self.stats.last_failure_time and 
                current_time - self.stats.last_failure_time >= self.config.recovery_timeout):
                await self._change_state(CircuitState.HALF_OPEN)
        
        elif self.stats.current_state == CircuitState.HALF_OPEN:
            # In half-open state, we allow limited calls to test recovery
            pass
    
    async def _record_success(self) -> None:
        """Record a successful call."""
        async with self._lock:
            self.stats.successful_calls += 1
            self.stats.consecutive_successes += 1
            self.stats.consecutive_failures = 0
            self.stats.last_success_time = time.time()
            
            # Check if we should close the circuit from half-open
            if (self.stats.current_state == CircuitState.HALF_OPEN and
                self.stats.consecutive_successes >= self.config.success_threshold):
                await self._change_state(CircuitState.CLOSED)
    
    async def _record_failure(self, exception: Exception) -> None:
        """Record a failed call."""
        async with self._lock:
            self.stats.failed_calls += 1
            self.stats.consecutive_failures += 1
            self.stats.consecutive_successes = 0
            self.stats.last_failure_time = time.time()
            
            # Check if we should open the circuit
            if (self.stats.current_state in [CircuitState.CLOSED, CircuitState.HALF_OPEN] and
                self.stats.consecutive_failures >= self.config.failure_threshold):
                await self._change_state(CircuitState.OPEN)
    
    async def _change_state(self, new_state: CircuitState) -> None:
        """Change circuit breaker state."""
        old_state = self.stats.current_state
        self.stats.current_state = new_state
        self.stats.state_changed_at = time.time()
        self.stats.state_changes += 1
        
        self.logger.info(
            "Circuit breaker state changed",
            name=self.config.name,
            old_state=old_state.value,
            new_state=new_state.value,
            consecutive_failures=self.stats.consecutive_failures,
            consecutive_successes=self.stats.consecutive_successes
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get circuit breaker statistics.
        
        Returns:
            Dict[str, Any]: Current statistics
        """
        return {
            "name": self.config.name,
            "state": self.stats.current_state.value,
            "total_calls": self.stats.total_calls,
            "successful_calls": self.stats.successful_calls,
            "failed_calls": self.stats.failed_calls,
            "success_rate": (
                self.stats.successful_calls / self.stats.total_calls
                if self.stats.total_calls > 0 else 0.0
            ),
            "consecutive_failures": self.stats.consecutive_failures,
            "consecutive_successes": self.stats.consecutive_successes,
            "last_failure_time": self.stats.last_failure_time,
            "last_success_time": self.stats.last_success_time,
            "state_changes": self.stats.state_changes,
            "state_changed_at": self.stats.state_changed_at,
            "config": {
                "failure_threshold": self.config.failure_threshold,
                "recovery_timeout": self.config.recovery_timeout,
                "success_threshold": self.config.success_threshold,
                "timeout": self.config.timeout
            }
        }
    
    async def reset(self) -> None:
        """Reset circuit breaker to closed state."""
        async with self._lock:
            old_stats = self.get_stats()
            self.stats = CircuitBreakerStats()
            
            self.logger.info(
                "Circuit breaker reset",
                name=self.config.name,
                previous_stats=old_stats
            )
    
    async def force_open(self) -> None:
        """Force circuit breaker to open state."""
        async with self._lock:
            await self._change_state(CircuitState.OPEN)
            
            self.logger.warning(
                "Circuit breaker forced open",
                name=self.config.name
            )
    
    async def force_close(self) -> None:
        """Force circuit breaker to closed state."""
        async with self._lock:
            await self._change_state(CircuitState.CLOSED)
            self.stats.consecutive_failures = 0
            self.stats.consecutive_successes = 0
            
            self.logger.info(
                "Circuit breaker forced closed",
                name=self.config.name
            )


class CircuitBreakerRegistry:
    """
    Registry for managing multiple circuit breakers.
    """
    
    def __init__(self) -> None:
        """Initialize circuit breaker registry."""
        self._breakers: Dict[str, CircuitBreaker] = {}
        self.logger = structlog.get_logger("circuit_breaker_registry")
    
    def create_breaker(
        self,
        name: str,
        config: Optional[CircuitBreakerConfig] = None
    ) -> CircuitBreaker:
        """
        Create or get a circuit breaker.
        
        Args:
            name: Circuit breaker name
            config: Optional configuration (uses defaults if not provided)
            
        Returns:
            CircuitBreaker: Circuit breaker instance
        """
        if name in self._breakers:
            return self._breakers[name]
        
        if config is None:
            config = CircuitBreakerConfig(name=name)
        else:
            config.name = name
        
        breaker = CircuitBreaker(config)
        self._breakers[name] = breaker
        
        self.logger.info(
            "Circuit breaker created",
            name=name,
            total_breakers=len(self._breakers)
        )
        
        return breaker
    
    def get_breaker(self, name: str) -> Optional[CircuitBreaker]:
        """
        Get a circuit breaker by name.
        
        Args:
            name: Circuit breaker name
            
        Returns:
            Optional[CircuitBreaker]: Circuit breaker or None
        """
        return self._breakers.get(name)
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        Get statistics for all circuit breakers.
        
        Returns:
            Dict[str, Dict[str, Any]]: Statistics for all breakers
        """
        return {
            name: breaker.get_stats()
            for name, breaker in self._breakers.items()
        }
    
    async def reset_all(self) -> None:
        """Reset all circuit breakers."""
        for breaker in self._breakers.values():
            await breaker.reset()
        
        self.logger.info(
            "All circuit breakers reset",
            total_breakers=len(self._breakers)
        )
    
    def list_breakers(self) -> list[str]:
        """
        List all circuit breaker names.
        
        Returns:
            list[str]: List of circuit breaker names
        """
        return list(self._breakers.keys())


# Global registry instance
_registry = CircuitBreakerRegistry()


def get_circuit_breaker(
    name: str,
    config: Optional[CircuitBreakerConfig] = None
) -> CircuitBreaker:
    """
    Get or create a circuit breaker from the global registry.
    
    Args:
        name: Circuit breaker name
        config: Optional configuration
        
    Returns:
        CircuitBreaker: Circuit breaker instance
    """
    return _registry.create_breaker(name, config)


def get_registry() -> CircuitBreakerRegistry:
    """
    Get the global circuit breaker registry.
    
    Returns:
        CircuitBreakerRegistry: Global registry instance
    """
    return _registry
