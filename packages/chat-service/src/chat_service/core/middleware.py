"""
Middleware Components

This module contains custom middleware for the FastAPI application,
including logging, metrics, rate limiting, and request tracking.
"""

import time
import uuid
from typing import Callable

import structlog
from fastapi import Request, Response
from prometheus_client import Counter, Histogram
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import <PERSON><PERSON><PERSON>pp

from chat_service.core.exceptions import RateLimitError


# Prometheus metrics
REQUEST_COUNT = Counter(
    "http_requests_total",
    "Total HTTP requests",
    ["method", "endpoint", "status_code"]
)

REQUEST_DURATION = Histogram(
    "http_request_duration_seconds",
    "HTTP request duration in seconds",
    ["method", "endpoint"]
)

ACTIVE_REQUESTS = Counter(
    "http_requests_active",
    "Active HTTP requests"
)


class RequestIDMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add unique request IDs to all requests.
    
    Adds a unique request ID to each request for tracing and correlation.
    The request ID is available in the request state and response headers.
    """
    
    def __init__(self, app: ASGIApp) -> None:
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request and add request ID.
        
        Args:
            request: FastAPI request object
            call_next: Next middleware/handler in chain
            
        Returns:
            Response: HTTP response with request ID header
        """
        # Generate unique request ID
        request_id = str(uuid.uuid4())
        
        # Add to request state
        request.state.request_id = request_id
        
        # Process request
        response = await call_next(request)
        
        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id
        
        return response


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for structured request/response logging.
    
    Logs all HTTP requests and responses with timing information,
    status codes, and other relevant metadata.
    """
    
    def __init__(self, app: ASGIApp) -> None:
        super().__init__(app)
        self.logger = structlog.get_logger("middleware.logging")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request with logging.
        
        Args:
            request: FastAPI request object
            call_next: Next middleware/handler in chain
            
        Returns:
            Response: HTTP response
        """
        start_time = time.time()
        request_id = getattr(request.state, "request_id", "unknown")
        
        # Log request
        self.logger.info(
            "Request started",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            path=request.url.path,
            query_params=dict(request.query_params),
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log response
            self.logger.info(
                "Request completed",
                request_id=request_id,
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                duration_seconds=round(duration, 4),
                response_size=response.headers.get("content-length"),
            )
            
            return response
            
        except Exception as exc:
            # Calculate duration
            duration = time.time() - start_time
            
            # Log error
            self.logger.error(
                "Request failed",
                request_id=request_id,
                method=request.method,
                path=request.url.path,
                duration_seconds=round(duration, 4),
                error=str(exc),
                error_type=type(exc).__name__,
            )
            
            raise


class MetricsMiddleware(BaseHTTPMiddleware):
    """
    Middleware for Prometheus metrics collection.
    
    Collects HTTP request metrics including request count,
    duration, and active request tracking.
    """
    
    def __init__(self, app: ASGIApp) -> None:
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request with metrics collection.
        
        Args:
            request: FastAPI request object
            call_next: Next middleware/handler in chain
            
        Returns:
            Response: HTTP response
        """
        # Skip metrics for metrics endpoint
        if request.url.path == "/metrics":
            return await call_next(request)
        
        start_time = time.time()
        method = request.method
        path = request.url.path
        
        # Increment active requests
        ACTIVE_REQUESTS.inc()
        
        try:
            # Process request
            response = await call_next(request)
            
            # Record metrics
            duration = time.time() - start_time
            status_code = str(response.status_code)
            
            REQUEST_COUNT.labels(
                method=method,
                endpoint=path,
                status_code=status_code
            ).inc()
            
            REQUEST_DURATION.labels(
                method=method,
                endpoint=path
            ).observe(duration)
            
            return response
            
        except Exception as exc:
            # Record error metrics
            duration = time.time() - start_time
            status_code = "500"  # Default to 500 for unhandled exceptions
            
            REQUEST_COUNT.labels(
                method=method,
                endpoint=path,
                status_code=status_code
            ).inc()
            
            REQUEST_DURATION.labels(
                method=method,
                endpoint=path
            ).observe(duration)
            
            raise
            
        finally:
            # Decrement active requests
            ACTIVE_REQUESTS.dec()


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Middleware for rate limiting requests.
    
    Implements simple in-memory rate limiting based on client IP.
    For production use, consider using Redis-based rate limiting.
    """
    
    def __init__(
        self,
        app: ASGIApp,
        calls: int = 100,
        period: int = 60
    ) -> None:
        """
        Initialize rate limit middleware.
        
        Args:
            app: ASGI application
            calls: Number of calls allowed per period
            period: Time period in seconds
        """
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients = {}  # In-memory storage (use Redis in production)
        self.logger = structlog.get_logger("middleware.rate_limit")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request with rate limiting.
        
        Args:
            request: FastAPI request object
            call_next: Next middleware/handler in chain
            
        Returns:
            Response: HTTP response
            
        Raises:
            RateLimitError: If rate limit is exceeded
        """
        # Skip rate limiting for health checks
        if request.url.path.startswith("/health"):
            return await call_next(request)
        
        # Get client identifier
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # Clean up old entries
        self._cleanup_old_entries(current_time)
        
        # Check rate limit
        if self._is_rate_limited(client_ip, current_time):
            self.logger.warning(
                "Rate limit exceeded",
                client_ip=client_ip,
                calls_limit=self.calls,
                period_seconds=self.period
            )
            
            raise RateLimitError(
                message=f"Rate limit exceeded: {self.calls} calls per {self.period} seconds",
                retry_after=self.period,
                details={
                    "client_ip": client_ip,
                    "limit": self.calls,
                    "period": self.period
                }
            )
        
        # Record request
        self._record_request(client_ip, current_time)
        
        # Process request
        return await call_next(request)
    
    def _cleanup_old_entries(self, current_time: float) -> None:
        """
        Clean up old rate limit entries.
        
        Args:
            current_time: Current timestamp
        """
        cutoff_time = current_time - self.period
        
        for client_ip in list(self.clients.keys()):
            self.clients[client_ip] = [
                timestamp for timestamp in self.clients[client_ip]
                if timestamp > cutoff_time
            ]
            
            # Remove empty entries
            if not self.clients[client_ip]:
                del self.clients[client_ip]
    
    def _is_rate_limited(self, client_ip: str, current_time: float) -> bool:
        """
        Check if client is rate limited.
        
        Args:
            client_ip: Client IP address
            current_time: Current timestamp
            
        Returns:
            bool: True if rate limited
        """
        if client_ip not in self.clients:
            return False
        
        # Count requests in current period
        cutoff_time = current_time - self.period
        recent_requests = [
            timestamp for timestamp in self.clients[client_ip]
            if timestamp > cutoff_time
        ]
        
        return len(recent_requests) >= self.calls
    
    def _record_request(self, client_ip: str, current_time: float) -> None:
        """
        Record a request for rate limiting.
        
        Args:
            client_ip: Client IP address
            current_time: Current timestamp
        """
        if client_ip not in self.clients:
            self.clients[client_ip] = []
        
        self.clients[client_ip].append(current_time)
