"""
FastAPI Chat Service Main Application

This module contains the main FastAPI application for the Knowledge Graph
Visualizer chat service. It provides endpoints for chat interactions,
knowledge graph queries, and LLM integrations.
"""

import logging
import sys
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import structlog
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import make_asgi_app

from chat_service.api.routes import chat, health, knowledge_graph, circuit_breaker, monitoring
from chat_service.core.config import get_settings
from chat_service.core.exceptions import ChatServiceException
from chat_service.core.logging import configure_logging
from chat_service.core.middleware import (
    LoggingMiddleware,
    MetricsMiddleware,
    RateLimitMiddleware,
    RequestIDMiddleware,
)
from chat_service.services.dependencies import get_chat_service, get_kg_service


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager.
    
    Handles startup and shutdown events for the FastAPI application.
    """
    settings = get_settings()
    logger = structlog.get_logger()
    
    # Startup
    logger.info("Starting chat service", version="1.0.0", environment=settings.environment)
    
    try:
        # Initialize services
        chat_service = get_chat_service()
        kg_service = get_kg_service()
        
        # Test connections
        await chat_service.health_check()
        await kg_service.health_check()
        
        logger.info("Chat service started successfully")
        
        yield
        
    except Exception as e:
        logger.error("Failed to start chat service", error=str(e))
        sys.exit(1)
    
    # Shutdown
    logger.info("Shutting down chat service")
    
    try:
        # Cleanup services
        chat_service = get_chat_service()
        kg_service = get_kg_service()
        
        await chat_service.cleanup()
        await kg_service.cleanup()
        
        logger.info("Chat service shutdown complete")
        
    except Exception as e:
        logger.error("Error during shutdown", error=str(e))


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        FastAPI: Configured FastAPI application instance
    """
    settings = get_settings()
    
    # Configure logging
    configure_logging(settings.log_level, settings.environment)
    
    # Create FastAPI app
    app = FastAPI(
        title="Knowledge Graph Chat Service",
        description="FastAPI-based chat service for Knowledge Graph Visualizer",
        version="1.0.0",
        docs_url="/docs" if settings.environment != "production" else None,
        redoc_url="/redoc" if settings.environment != "production" else None,
        openapi_url="/openapi.json" if settings.environment != "production" else None,
        lifespan=lifespan,
    )
    
    # Add middleware
    configure_middleware(app, settings)
    
    # Add routes
    configure_routes(app)
    
    # Add exception handlers
    configure_exception_handlers(app)
    
    return app


def configure_middleware(app: FastAPI, settings) -> None:
    """
    Configure middleware for the FastAPI application.
    
    Args:
        app: FastAPI application instance
        settings: Application settings
    """
    # Security middleware
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.allowed_hosts
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )
    
    # Custom middleware
    app.add_middleware(RequestIDMiddleware)
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(MetricsMiddleware)
    
    if settings.enable_rate_limiting:
        app.add_middleware(
            RateLimitMiddleware,
            calls=settings.rate_limit_calls,
            period=settings.rate_limit_period
        )


def configure_routes(app: FastAPI) -> None:
    """
    Configure API routes for the FastAPI application.
    
    Args:
        app: FastAPI application instance
    """
    # Health check routes
    app.include_router(
        health.router,
        prefix="/health",
        tags=["health"]
    )
    
    # Chat routes
    app.include_router(
        chat.router,
        prefix="/api/v1/chat",
        tags=["chat"]
    )
    
    # Knowledge graph routes
    app.include_router(
        knowledge_graph.router,
        prefix="/api/v1/kg",
        tags=["knowledge-graph"]
    )

    # Monitoring and service discovery routes
    app.include_router(
        monitoring.router,
        prefix="/api/v1/monitoring",
        tags=["monitoring"]
    )

    # Circuit breaker monitoring routes
    app.include_router(
        circuit_breaker.router,
        prefix="/api/v1/circuit-breakers",
        tags=["circuit-breakers"]
    )

    # Metrics endpoint
    metrics_app = make_asgi_app()
    app.mount("/metrics", metrics_app)


def configure_exception_handlers(app: FastAPI) -> None:
    """
    Configure exception handlers for the FastAPI application.
    
    Args:
        app: FastAPI application instance
    """
    @app.exception_handler(ChatServiceException)
    async def chat_service_exception_handler(
        request: Request, exc: ChatServiceException
    ) -> JSONResponse:
        """Handle chat service specific exceptions."""
        logger = structlog.get_logger()
        logger.error(
            "Chat service exception",
            error=str(exc),
            error_code=exc.error_code,
            path=request.url.path,
            method=request.method
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "code": exc.error_code,
                    "message": exc.message,
                    "details": exc.details
                }
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(
        request: Request, exc: Exception
    ) -> JSONResponse:
        """Handle general exceptions."""
        logger = structlog.get_logger()
        logger.error(
            "Unhandled exception",
            error=str(exc),
            error_type=type(exc).__name__,
            path=request.url.path,
            method=request.method
        )
        
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "INTERNAL_SERVER_ERROR",
                    "message": "An internal server error occurred",
                    "details": None
                }
            }
        )


# Create the application instance
app = create_app()


if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "chat_service.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.environment == "development",
        log_level=settings.log_level.lower(),
        access_log=True,
        server_header=False,
        date_header=False,
    )
