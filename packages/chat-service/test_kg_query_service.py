#!/usr/bin/env python3
"""
Knowledge Graph Query Service Test

This script tests the comprehensive knowledge graph query service,
demonstrating the migration of Neo4j query patterns and RAG pipeline
functionality from the Node.js backend.
"""

import asyncio
import sys
import os
import time

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from chat_service.services.kg_query_service import KnowledgeGraphQueryService
from chat_service.services.rag_pipeline_service import RAGPipelineService
from chat_service.services.enhanced_kg_service import EnhancedKnowledgeGraphService
from chat_service.core.config import get_settings


async def test_kg_query_service():
    """Test the knowledge graph query service functionality."""
    print("🔍 Testing Knowledge Graph Query Service")
    print("-" * 50)
    
    settings = get_settings()
    
    try:
        # Initialize the service
        kg_query_service = KnowledgeGraphQueryService(
            neo4j_uri=settings.neo4j_uri,
            neo4j_user=settings.neo4j_user,
            neo4j_password=settings.neo4j_password,
            neo4j_database=settings.neo4j_database,
            settings=settings
        )
        
        print(f"✅ Service initialized")
        
        # Test connection
        connection_test = await kg_query_service.test_connection()
        print(f"  Connection: {'✅' if connection_test['connected'] else '❌'}")
        if connection_test['connected']:
            print(f"  Response time: {connection_test['response_time_ms']:.2f}ms")
        else:
            print(f"  Error: {connection_test['error']}")
        
        if not connection_test['connected']:
            print("  ⚠️  Skipping query tests due to connection failure")
            return False
        
        # Test basic read query
        print("\n  Testing basic read query...")
        try:
            result = await kg_query_service.execute_read_query(
                "MATCH (n) RETURN count(n) as node_count LIMIT 1"
            )
            node_count = result["records"][0]["node_count"] if result["records"] else 0
            print(f"  ✅ Found {node_count} nodes in database")
            print(f"  ⏱️  Query time: {result['summary']['execution_time_ms']:.2f}ms")
        except Exception as e:
            print(f"  ❌ Read query failed: {str(e)}")
        
        # Test initial graph retrieval
        print("\n  Testing initial graph retrieval...")
        try:
            graph_data = await kg_query_service.get_initial_graph(limit=5)
            print(f"  ✅ Retrieved {len(graph_data['nodes'])} nodes, {len(graph_data['relationships'])} relationships")
        except Exception as e:
            print(f"  ❌ Graph retrieval failed: {str(e)}")
        
        # Test context retrieval
        if graph_data and graph_data['nodes']:
            print("\n  Testing context retrieval...")
            try:
                # Get context for first few nodes
                entity_ids = [node['id'] for node in graph_data['nodes'][:3]]
                context = await kg_query_service.get_graph_context(entity_ids, depth=1)
                print(f"  ✅ Retrieved context: {len(context['entities'])} entities, {len(context['relationships'])} relationships")
            except Exception as e:
                print(f"  ❌ Context retrieval failed: {str(e)}")
        
        # Test statistics
        print("\n  Testing query statistics...")
        try:
            stats = await kg_query_service.get_query_statistics()
            print(f"  ✅ Total queries: {stats['total_queries']}")
            print(f"  ✅ Average duration: {stats['average_duration_ms']:.2f}ms")
            print(f"  ✅ Error count: {stats['error_count']}")
        except Exception as e:
            print(f"  ❌ Statistics failed: {str(e)}")
        
        # Cleanup
        await kg_query_service.cleanup()
        print(f"\n✅ Knowledge Graph Query Service test completed")
        return True
        
    except Exception as e:
        print(f"❌ Service test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_rag_pipeline_service():
    """Test the RAG pipeline service functionality."""
    print("\n🧠 Testing RAG Pipeline Service")
    print("-" * 50)
    
    settings = get_settings()
    
    try:
        # Initialize services
        kg_query_service = KnowledgeGraphQueryService(
            neo4j_uri=settings.neo4j_uri,
            neo4j_user=settings.neo4j_user,
            neo4j_password=settings.neo4j_password,
            neo4j_database=settings.neo4j_database,
            settings=settings
        )
        
        # Try to initialize enhanced service
        enhanced_kg_service = None
        try:
            enhanced_kg_service = EnhancedKnowledgeGraphService(
                neo4j_uri=settings.neo4j_uri,
                neo4j_user=settings.neo4j_user,
                neo4j_password=settings.neo4j_password,
                neo4j_database=settings.neo4j_database,
                openai_api_key=settings.openai_api_key,
                settings=settings
            )
        except Exception as e:
            print(f"  ⚠️  Enhanced service not available: {str(e)}")
        
        # Initialize RAG pipeline
        rag_pipeline = RAGPipelineService(
            kg_query_service=kg_query_service,
            enhanced_kg_service=enhanced_kg_service,
            settings=settings
        )
        
        print(f"✅ RAG Pipeline initialized")
        print(f"  Enhanced service: {'Yes' if enhanced_kg_service else 'No'}")
        
        # Test health check
        health = await rag_pipeline.health_check()
        print(f"  Health: {'✅' if health['status'] == 'healthy' else '❌'}")
        if health['status'] == 'healthy':
            print(f"  Response time: {health['response_time_ms']:.2f}ms")
        
        # Test context retrieval and formatting
        test_queries = [
            "knowledge graph",
            "user documentation",
            "API reference",
            "tutorial examples"
        ]
        
        print(f"\n  Testing context retrieval for {len(test_queries)} queries...")
        
        successful_retrievals = 0
        total_context_length = 0
        
        for i, query in enumerate(test_queries, 1):
            try:
                start_time = time.time()
                context_result = await rag_pipeline.retrieve_and_format_context(
                    query=query,
                    options={"max_context_length": 500, "limit": 3}
                )
                duration = time.time() - start_time
                
                context_length = len(context_result["context"])
                entities_found = context_result["metadata"]["entities_found"]
                
                print(f"    {i}. '{query}': {entities_found} entities, {context_length} chars ({duration:.2f}s)")
                
                if entities_found > 0:
                    successful_retrievals += 1
                    total_context_length += context_length
                
            except Exception as e:
                print(f"    {i}. '{query}': ❌ {str(e)}")
        
        print(f"\n  📊 Results: {successful_retrievals}/{len(test_queries)} successful retrievals")
        if successful_retrievals > 0:
            avg_context_length = total_context_length / successful_retrievals
            print(f"  📊 Average context length: {avg_context_length:.1f} characters")
        
        # Test prompt building
        print(f"\n  Testing RAG prompt building...")
        try:
            test_context = "**Knowledge Graph Context:**\n\n1. Knowledge Graph (Concept): A graph database that represents knowledge in a structured format.\n2. Neo4j (Database): A popular graph database management system."
            
            prompt = await rag_pipeline.build_rag_prompt(
                query="What is a knowledge graph?",
                context=test_context
            )
            
            print(f"  ✅ Prompt built: {len(prompt)} characters")
            print(f"  📝 Preview: {prompt[:100]}...")
            
        except Exception as e:
            print(f"  ❌ Prompt building failed: {str(e)}")
        
        # Test pipeline statistics
        print(f"\n  Testing pipeline statistics...")
        try:
            stats = await rag_pipeline.get_pipeline_statistics()
            print(f"  ✅ Total requests: {stats['total_requests']}")
            print(f"  ✅ Success rate: {stats['success_rate']:.3f}")
            print(f"  ✅ Average context length: {stats['average_context_length']:.1f}")
            print(f"  ✅ Average retrieval time: {stats['average_retrieval_time_ms']:.2f}ms")
        except Exception as e:
            print(f"  ❌ Statistics failed: {str(e)}")
        
        # Cleanup
        await kg_query_service.cleanup()
        if enhanced_kg_service:
            await enhanced_kg_service.cleanup()
        
        print(f"\n✅ RAG Pipeline Service test completed")
        return True
        
    except Exception as e:
        print(f"❌ RAG Pipeline test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_api_endpoints():
    """Test the API endpoints for knowledge graph queries."""
    print("\n🌐 Testing API Endpoints")
    print("-" * 50)
    
    try:
        # Import FastAPI test client
        from fastapi.testclient import TestClient
        from chat_service.api.app import app
        
        client = TestClient(app)
        
        print("✅ Test client initialized")
        
        # Test health endpoint
        print("\n  Testing health endpoint...")
        response = client.get("/api/v1/kg/health")
        if response.status_code == 200:
            health_data = response.json()
            print(f"  ✅ Health check: {health_data.get('status', 'unknown')}")
        else:
            print(f"  ❌ Health check failed: {response.status_code}")
        
        # Test search endpoint
        print("\n  Testing search endpoint...")
        search_data = {
            "query": "knowledge graph",
            "limit": 5,
            "threshold": 0.7
        }
        response = client.post("/api/v1/kg/search", json=search_data)
        if response.status_code == 200:
            search_results = response.json()
            print(f"  ✅ Search: {len(search_results.get('results', []))} results")
        else:
            print(f"  ❌ Search failed: {response.status_code}")
        
        # Test RAG context endpoint
        print("\n  Testing RAG context endpoint...")
        rag_data = {
            "query": "user documentation",
            "max_context_length": 1000,
            "limit": 3
        }
        response = client.post("/api/v1/kg/rag/context", json=rag_data)
        if response.status_code == 200:
            rag_results = response.json()
            context_length = len(rag_results.get('context', ''))
            print(f"  ✅ RAG context: {context_length} characters")
            print(f"  📊 Service type: {rag_results.get('metadata', {}).get('service_type', 'unknown')}")
        else:
            print(f"  ❌ RAG context failed: {response.status_code}")
        
        # Test statistics endpoint
        print("\n  Testing statistics endpoint...")
        response = client.get("/api/v1/kg/statistics")
        if response.status_code == 200:
            stats_data = response.json()
            print(f"  ✅ Statistics: {stats_data.get('service_type', 'unknown')} service")
        else:
            print(f"  ❌ Statistics failed: {response.status_code}")
        
        print(f"\n✅ API Endpoints test completed")
        return True
        
    except Exception as e:
        print(f"❌ API test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all knowledge graph query service tests."""
    print("🚀 Knowledge Graph Query Service Test Suite")
    print("🔧 Comprehensive Migration Testing")
    print("=" * 60)
    
    # Show configuration
    settings = get_settings()
    print(f"Environment: {settings.environment}")
    print(f"Neo4j URI: {settings.neo4j_uri}")
    print(f"Database: {settings.neo4j_database}")
    
    # Run tests
    tests = [
        ("Knowledge Graph Query Service", test_kg_query_service),
        ("RAG Pipeline Service", test_rag_pipeline_service),
        ("API Endpoints", test_api_endpoints),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ Test '{test_name}' crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Knowledge Graph Query Service Test Results")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Knowledge graph query service is ready.")
    elif passed >= total * 0.8:
        print("⚠️  Most tests passed. Minor issues may need attention.")
    else:
        print("🚨 Several tests failed. Service needs more work.")
    
    return passed / total


if __name__ == "__main__":
    asyncio.run(main())
