"""
Health Check Tests

Tests for the health check endpoints to verify service functionality.
"""

import pytest
from fastapi.testclient import TestClient

from chat_service.main import app


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


def test_basic_health_check(client):
    """Test basic health check endpoint."""
    response = client.get("/health/")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "version" in data
    assert "environment" in data
    assert "uptime_seconds" in data


def test_liveness_check(client):
    """Test liveness check endpoint."""
    response = client.get("/health/live")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "alive"
    assert "timestamp" in data
    assert "version" in data
    assert "uptime_seconds" in data


def test_health_check_response_format(client):
    """Test health check response format."""
    response = client.get("/health/")
    
    assert response.status_code == 200
    assert response.headers["content-type"] == "application/json"
    
    data = response.json()
    
    # Verify required fields
    required_fields = ["status", "timestamp", "version", "environment", "uptime_seconds"]
    for field in required_fields:
        assert field in data
    
    # Verify data types
    assert isinstance(data["status"], str)
    assert isinstance(data["timestamp"], str)
    assert isinstance(data["version"], str)
    assert isinstance(data["environment"], str)
    assert isinstance(data["uptime_seconds"], (int, float))


def test_health_check_uptime_increases(client):
    """Test that uptime increases between calls."""
    import time
    
    response1 = client.get("/health/")
    time.sleep(0.1)  # Small delay
    response2 = client.get("/health/")
    
    assert response1.status_code == 200
    assert response2.status_code == 200
    
    uptime1 = response1.json()["uptime_seconds"]
    uptime2 = response2.json()["uptime_seconds"]
    
    assert uptime2 > uptime1


def test_cors_headers(client):
    """Test CORS headers are present."""
    response = client.options("/health/")
    
    # Should allow CORS preflight
    assert response.status_code in [200, 204]


def test_request_id_header(client):
    """Test that request ID header is added."""
    response = client.get("/health/")
    
    assert response.status_code == 200
    assert "X-Request-ID" in response.headers
    
    # Request ID should be a valid UUID format
    request_id = response.headers["X-Request-ID"]
    assert len(request_id) == 36  # UUID length
    assert request_id.count("-") == 4  # UUID format
