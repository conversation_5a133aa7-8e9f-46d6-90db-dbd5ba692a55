"""
Circuit Breaker Tests

Tests for the circuit breaker pattern implementation.
"""

import asyncio
import pytest

from chat_service.core.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitState,
    CircuitBreakerError,
    get_circuit_breaker,
    get_registry
)


class TestException(Exception):
    """Test exception for circuit breaker tests."""
    pass


@pytest.fixture
def circuit_breaker():
    """Create a test circuit breaker."""
    config = CircuitBreakerConfig(
        failure_threshold=3,
        recovery_timeout=1.0,
        expected_exception=TestException,
        name="test_breaker"
    )
    return CircuitBreaker(config)


@pytest.mark.asyncio
async def test_circuit_breaker_closed_state(circuit_breaker):
    """Test circuit breaker in closed state."""
    # Initially closed
    assert circuit_breaker.stats.current_state == CircuitState.CLOSED
    
    # Successful call
    async def success_func():
        return "success"
    
    result = await circuit_breaker.call(success_func)
    assert result == "success"
    assert circuit_breaker.stats.successful_calls == 1
    assert circuit_breaker.stats.failed_calls == 0


@pytest.mark.asyncio
async def test_circuit_breaker_failure_tracking(circuit_breaker):
    """Test circuit breaker failure tracking."""
    async def failing_func():
        raise TestException("Test failure")
    
    # First failure
    with pytest.raises(TestException):
        await circuit_breaker.call(failing_func)
    
    assert circuit_breaker.stats.failed_calls == 1
    assert circuit_breaker.stats.consecutive_failures == 1
    assert circuit_breaker.stats.current_state == CircuitState.CLOSED


@pytest.mark.asyncio
async def test_circuit_breaker_opens_after_threshold(circuit_breaker):
    """Test circuit breaker opens after failure threshold."""
    async def failing_func():
        raise TestException("Test failure")
    
    # Fail enough times to open circuit
    for i in range(3):
        with pytest.raises(TestException):
            await circuit_breaker.call(failing_func)
    
    # Circuit should now be open
    assert circuit_breaker.stats.current_state == CircuitState.OPEN
    assert circuit_breaker.stats.consecutive_failures == 3


@pytest.mark.asyncio
async def test_circuit_breaker_fails_fast_when_open(circuit_breaker):
    """Test circuit breaker fails fast when open."""
    async def failing_func():
        raise TestException("Test failure")
    
    # Open the circuit
    for i in range(3):
        with pytest.raises(TestException):
            await circuit_breaker.call(failing_func)
    
    # Now calls should fail fast with CircuitBreakerError
    with pytest.raises(CircuitBreakerError):
        await circuit_breaker.call(failing_func)


@pytest.mark.asyncio
async def test_circuit_breaker_half_open_recovery(circuit_breaker):
    """Test circuit breaker recovery through half-open state."""
    async def failing_func():
        raise TestException("Test failure")
    
    async def success_func():
        return "success"
    
    # Open the circuit
    for i in range(3):
        with pytest.raises(TestException):
            await circuit_breaker.call(failing_func)
    
    assert circuit_breaker.stats.current_state == CircuitState.OPEN
    
    # Wait for recovery timeout
    await asyncio.sleep(1.1)
    
    # Next call should transition to half-open
    result = await circuit_breaker.call(success_func)
    assert result == "success"
    
    # After enough successes, should close
    for i in range(2):  # Need 3 total successes (success_threshold)
        await circuit_breaker.call(success_func)
    
    assert circuit_breaker.stats.current_state == CircuitState.CLOSED


@pytest.mark.asyncio
async def test_circuit_breaker_timeout():
    """Test circuit breaker timeout functionality."""
    config = CircuitBreakerConfig(
        timeout=0.1,  # Very short timeout
        name="timeout_test"
    )
    breaker = CircuitBreaker(config)
    
    async def slow_func():
        await asyncio.sleep(0.2)  # Longer than timeout
        return "success"
    
    with pytest.raises(asyncio.TimeoutError):
        await breaker.call(slow_func)
    
    # Should record as failure
    assert breaker.stats.failed_calls == 1


@pytest.mark.asyncio
async def test_circuit_breaker_stats():
    """Test circuit breaker statistics."""
    config = CircuitBreakerConfig(name="stats_test")
    breaker = CircuitBreaker(config)
    
    async def success_func():
        return "success"
    
    async def failing_func():
        raise TestException("Test failure")
    
    # Some successful calls
    for i in range(5):
        await breaker.call(success_func)
    
    # Some failed calls
    for i in range(2):
        with pytest.raises(TestException):
            await breaker.call(failing_func)
    
    stats = breaker.get_stats()
    
    assert stats["total_calls"] == 7
    assert stats["successful_calls"] == 5
    assert stats["failed_calls"] == 2
    assert stats["success_rate"] == 5/7
    assert stats["name"] == "stats_test"
    assert stats["state"] == CircuitState.CLOSED.value


@pytest.mark.asyncio
async def test_circuit_breaker_reset():
    """Test circuit breaker reset functionality."""
    config = CircuitBreakerConfig(
        failure_threshold=2,
        name="reset_test"
    )
    breaker = CircuitBreaker(config)
    
    async def failing_func():
        raise TestException("Test failure")
    
    # Open the circuit
    for i in range(2):
        with pytest.raises(TestException):
            await breaker.call(failing_func)
    
    assert breaker.stats.current_state == CircuitState.OPEN
    
    # Reset the circuit
    await breaker.reset()
    
    assert breaker.stats.current_state == CircuitState.CLOSED
    assert breaker.stats.total_calls == 0
    assert breaker.stats.failed_calls == 0


@pytest.mark.asyncio
async def test_circuit_breaker_force_operations():
    """Test circuit breaker force open/close operations."""
    config = CircuitBreakerConfig(name="force_test")
    breaker = CircuitBreaker(config)
    
    # Force open
    await breaker.force_open()
    assert breaker.stats.current_state == CircuitState.OPEN
    
    # Force close
    await breaker.force_close()
    assert breaker.stats.current_state == CircuitState.CLOSED


def test_circuit_breaker_registry():
    """Test circuit breaker registry functionality."""
    registry = get_registry()
    
    # Create a breaker
    breaker1 = registry.create_breaker("test1")
    assert breaker1.config.name == "test1"
    
    # Get the same breaker
    breaker2 = registry.get_breaker("test1")
    assert breaker1 is breaker2
    
    # Create another breaker
    config = CircuitBreakerConfig(failure_threshold=10)
    breaker3 = registry.create_breaker("test2", config)
    assert breaker3.config.failure_threshold == 10
    
    # List breakers
    breaker_names = registry.list_breakers()
    assert "test1" in breaker_names
    assert "test2" in breaker_names
    
    # Get all stats
    all_stats = registry.get_all_stats()
    assert "test1" in all_stats
    assert "test2" in all_stats


def test_get_circuit_breaker_function():
    """Test the global get_circuit_breaker function."""
    breaker1 = get_circuit_breaker("global_test")
    breaker2 = get_circuit_breaker("global_test")
    
    # Should return the same instance
    assert breaker1 is breaker2
    assert breaker1.config.name == "global_test"


@pytest.mark.asyncio
async def test_circuit_breaker_with_sync_function():
    """Test circuit breaker with synchronous functions."""
    config = CircuitBreakerConfig(name="sync_test")
    breaker = CircuitBreaker(config)
    
    def sync_success():
        return "sync_success"
    
    def sync_failure():
        raise TestException("Sync failure")
    
    # Test successful sync call
    result = await breaker.call(sync_success)
    assert result == "sync_success"
    
    # Test failed sync call
    with pytest.raises(TestException):
        await breaker.call(sync_failure)
