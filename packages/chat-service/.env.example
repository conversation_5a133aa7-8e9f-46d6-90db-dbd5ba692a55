# Chat Service Configuration
# Copy this file to .env and update with your settings

# Server Settings
CHAT_SERVICE_HOST=127.0.0.1
CHAT_SERVICE_PORT=8001
CHAT_SERVICE_ENVIRONMENT=development
CHAT_SERVICE_DEBUG=true

# Logging Settings
CHAT_SERVICE_LOG_LEVEL=INFO
CHAT_SERVICE_LOG_FORMAT=console

# Security Settings
CHAT_SERVICE_SECRET_KEY=your-secret-key-here
CHAT_SERVICE_ALGORITHM=HS256
CHAT_SERVICE_ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
CHAT_SERVICE_CORS_ORIGINS=["http://localhost:3000", "http://localhost:3001"]
CHAT_SERVICE_ALLOWED_HOSTS=["localhost", "127.0.0.1"]

# Rate Limiting
CHAT_SERVICE_ENABLE_RATE_LIMITING=true
CHAT_SERVICE_RATE_LIMIT_CALLS=100
CHAT_SERVICE_RATE_LIMIT_PERIOD=60

# Database Settings
CHAT_SERVICE_NEO4J_URI=bolt://localhost:7687
CHAT_SERVICE_NEO4J_USER=neo4j
CHAT_SERVICE_NEO4J_PASSWORD=password
CHAT_SERVICE_NEO4J_DATABASE=neo4j

# Redis Settings (for caching and rate limiting)
CHAT_SERVICE_REDIS_URL=redis://localhost:6379
CHAT_SERVICE_REDIS_PASSWORD=
CHAT_SERVICE_REDIS_DB=0

# LLM Provider Settings
CHAT_SERVICE_DEFAULT_LLM_PROVIDER=openai

# OpenAI Settings
CHAT_SERVICE_OPENAI_API_KEY=your-openai-api-key
CHAT_SERVICE_OPENAI_MODEL=gpt-3.5-turbo
CHAT_SERVICE_OPENAI_MAX_TOKENS=1000
CHAT_SERVICE_OPENAI_TEMPERATURE=0.7

# Groq Settings
CHAT_SERVICE_GROQ_API_KEY=your-groq-api-key
CHAT_SERVICE_GROQ_MODEL=mixtral-8x7b-32768
CHAT_SERVICE_GROQ_MAX_TOKENS=1000
CHAT_SERVICE_GROQ_TEMPERATURE=0.7

# Google Settings
CHAT_SERVICE_GOOGLE_API_KEY=your-google-api-key
CHAT_SERVICE_GOOGLE_MODEL=gemini-pro
CHAT_SERVICE_GOOGLE_MAX_TOKENS=1000
CHAT_SERVICE_GOOGLE_TEMPERATURE=0.7

# Embedding Settings
CHAT_SERVICE_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
CHAT_SERVICE_EMBEDDING_DIMENSION=384

# Chat Settings
CHAT_SERVICE_MAX_CONVERSATION_LENGTH=50
CHAT_SERVICE_MAX_CONTEXT_TOKENS=4000
CHAT_SERVICE_ENABLE_STREAMING=true

# Knowledge Graph Settings
CHAT_SERVICE_MAX_SEARCH_RESULTS=10
CHAT_SERVICE_SIMILARITY_THRESHOLD=0.7
CHAT_SERVICE_ENABLE_GRAPH_CONTEXT=true

# Circuit Breaker Settings
CHAT_SERVICE_CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CHAT_SERVICE_CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60
CHAT_SERVICE_CIRCUIT_BREAKER_EXPECTED_EXCEPTION=Exception

# Monitoring Settings
CHAT_SERVICE_ENABLE_METRICS=true
CHAT_SERVICE_METRICS_PORT=8002

# Health Check Settings
CHAT_SERVICE_HEALTH_CHECK_TIMEOUT=30
