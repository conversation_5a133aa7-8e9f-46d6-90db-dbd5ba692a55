#!/usr/bin/env python3
"""
Service Communication Test

This script tests the HTTP communication between Node.js backend
and FastAPI chat service, including authentication, request validation,
and proper error handling.
"""

import asyncio
import sys
import os
import time
import json

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from fastapi.testclient import TestClient
from chat_service.main import create_app
from chat_service.core.config import get_settings


async def test_service_authentication():
    """Test service authentication middleware."""
    print("🔐 Testing Service Authentication")
    print("-" * 40)
    
    try:
        # Create test app
        app = create_app()
        client = TestClient(app)
        
        # Test without authentication (should fail in production)
        print("  Testing unauthenticated request...")
        response = client.post("/api/v1/chat/message", json={
            "message": "Hello, world!"
        })
        
        if response.status_code == 401:
            print("  ✅ Unauthenticated request properly rejected")
        elif response.status_code == 200:
            print("  ⚠️  Unauthenticated request allowed (development mode)")
        else:
            print(f"  ❌ Unexpected status code: {response.status_code}")
        
        # Test with API key
        print("  Testing API key authentication...")
        headers = {"X-API-Key": "test-api-key"}
        response = client.post("/api/v1/chat/message", 
                              json={"message": "Hello with API key!"},
                              headers=headers)
        
        if response.status_code in [200, 401]:
            print(f"  ✅ API key authentication handled (status: {response.status_code})")
        else:
            print(f"  ❌ Unexpected status code: {response.status_code}")
        
        # Test with session ID
        print("  Testing session authentication...")
        headers = {"X-Session-ID": "test-session-12345"}
        response = client.post("/api/v1/chat/message",
                              json={"message": "Hello with session!"},
                              headers=headers)
        
        if response.status_code in [200, 401]:
            print(f"  ✅ Session authentication handled (status: {response.status_code})")
        else:
            print(f"  ❌ Unexpected status code: {response.status_code}")
        
        # Test with Bearer token
        print("  Testing Bearer token authentication...")
        headers = {"Authorization": "Bearer test-token-12345"}
        response = client.post("/api/v1/chat/message",
                              json={"message": "Hello with token!"},
                              headers=headers)
        
        if response.status_code in [200, 401]:
            print(f"  ✅ Bearer token authentication handled (status: {response.status_code})")
        else:
            print(f"  ❌ Unexpected status code: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Authentication test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_request_validation():
    """Test request validation middleware."""
    print("\n✅ Testing Request Validation")
    print("-" * 40)
    
    try:
        # Create test app
        app = create_app()
        client = TestClient(app)
        
        # Test missing message
        print("  Testing missing message...")
        response = client.post("/api/v1/chat/message", json={})
        
        if response.status_code == 422:  # Validation error
            print("  ✅ Missing message properly rejected")
        else:
            print(f"  ❌ Unexpected status code: {response.status_code}")
        
        # Test empty message
        print("  Testing empty message...")
        response = client.post("/api/v1/chat/message", json={"message": ""})
        
        if response.status_code in [400, 422]:
            print("  ✅ Empty message properly rejected")
        else:
            print(f"  ❌ Unexpected status code: {response.status_code}")
        
        # Test invalid content type
        print("  Testing invalid content type...")
        response = client.post("/api/v1/chat/message",
                              data="invalid data",
                              headers={"Content-Type": "text/plain"})
        
        if response.status_code in [400, 422]:
            print("  ✅ Invalid content type properly rejected")
        else:
            print(f"  ❌ Unexpected status code: {response.status_code}")
        
        # Test valid request
        print("  Testing valid request...")
        response = client.post("/api/v1/chat/message", json={
            "message": "This is a valid test message"
        })
        
        if response.status_code in [200, 401]:  # 401 if auth required
            print(f"  ✅ Valid request handled (status: {response.status_code})")
        else:
            print(f"  ❌ Unexpected status code: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Validation test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_correlation_tracking():
    """Test correlation ID tracking."""
    print("\n🔗 Testing Correlation Tracking")
    print("-" * 40)
    
    try:
        # Create test app
        app = create_app()
        client = TestClient(app)
        
        # Test with correlation ID
        print("  Testing with correlation ID...")
        correlation_id = f"test-{int(time.time())}"
        headers = {"X-Correlation-ID": correlation_id}
        
        response = client.post("/api/v1/chat/message",
                              json={"message": "Test with correlation ID"},
                              headers=headers)
        
        if response.status_code in [200, 401]:
            print(f"  ✅ Request with correlation ID handled (status: {response.status_code})")
            
            # Check if correlation ID is preserved in response
            if hasattr(response, 'json') and response.status_code == 200:
                response_data = response.json()
                if "metadata" in response_data:
                    print("  ✅ Response includes metadata")
                else:
                    print("  ⚠️  Response missing metadata")
        else:
            print(f"  ❌ Unexpected status code: {response.status_code}")
        
        # Test without correlation ID (should generate one)
        print("  Testing without correlation ID...")
        response = client.post("/api/v1/chat/message",
                              json={"message": "Test without correlation ID"})
        
        if response.status_code in [200, 401]:
            print(f"  ✅ Request without correlation ID handled (status: {response.status_code})")
        else:
            print(f"  ❌ Unexpected status code: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Correlation tracking test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_health_endpoint():
    """Test health check endpoint."""
    print("\n🏥 Testing Health Endpoint")
    print("-" * 40)
    
    try:
        # Create test app
        app = create_app()
        client = TestClient(app)
        
        # Test health endpoint
        print("  Testing health endpoint...")
        response = client.get("/api/v1/health")
        
        if response.status_code == 200:
            print("  ✅ Health endpoint accessible")
            
            try:
                health_data = response.json()
                print(f"  📊 Health status: {health_data.get('status', 'unknown')}")
                print(f"  📊 Service: {health_data.get('service', 'unknown')}")
                
                if "components" in health_data:
                    print(f"  📊 Components: {len(health_data['components'])}")
                
            except json.JSONDecodeError:
                print("  ⚠️  Health response not valid JSON")
        else:
            print(f"  ❌ Health endpoint failed: {response.status_code}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"  ❌ Health endpoint test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_streaming_endpoint():
    """Test streaming endpoint."""
    print("\n🌊 Testing Streaming Endpoint")
    print("-" * 40)
    
    try:
        # Create test app
        app = create_app()
        client = TestClient(app)
        
        # Test streaming endpoint
        print("  Testing streaming endpoint...")
        response = client.post("/api/v1/chat/stream",
                              json={"message": "Test streaming message"})
        
        if response.status_code in [200, 401]:
            print(f"  ✅ Streaming endpoint accessible (status: {response.status_code})")
            
            if response.status_code == 200:
                # Check if response is streaming
                content_type = response.headers.get("content-type", "")
                if "text/plain" in content_type or "text/event-stream" in content_type:
                    print("  ✅ Streaming response format detected")
                else:
                    print(f"  ⚠️  Unexpected content type: {content_type}")
        else:
            print(f"  ❌ Streaming endpoint failed: {response.status_code}")
        
        return response.status_code in [200, 401]
        
    except Exception as e:
        print(f"  ❌ Streaming endpoint test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_error_handling():
    """Test error handling."""
    print("\n🚨 Testing Error Handling")
    print("-" * 40)
    
    try:
        # Create test app
        app = create_app()
        client = TestClient(app)
        
        # Test 404 endpoint
        print("  Testing 404 endpoint...")
        response = client.get("/api/v1/nonexistent")
        
        if response.status_code == 404:
            print("  ✅ 404 error properly handled")
        else:
            print(f"  ❌ Unexpected status code: {response.status_code}")
        
        # Test malformed JSON
        print("  Testing malformed JSON...")
        response = client.post("/api/v1/chat/message",
                              data='{"invalid": json}',
                              headers={"Content-Type": "application/json"})
        
        if response.status_code in [400, 422]:
            print("  ✅ Malformed JSON properly rejected")
        else:
            print(f"  ❌ Unexpected status code: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error handling test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all service communication tests."""
    print("🚀 Service Communication Test Suite")
    print("🔧 Node.js ↔ FastAPI Communication")
    print("=" * 60)
    
    # Show configuration
    settings = get_settings()
    print(f"Environment: {settings.environment}")
    print(f"Service API Key: {'Set' if settings.service_api_key else 'Not set'}")
    print(f"Rate Limiting: {'Enabled' if settings.enable_rate_limiting else 'Disabled'}")
    
    # Run tests
    tests = [
        ("Service Authentication", test_service_authentication),
        ("Request Validation", test_request_validation),
        ("Correlation Tracking", test_correlation_tracking),
        ("Health Endpoint", test_health_endpoint),
        ("Streaming Endpoint", test_streaming_endpoint),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ Test '{test_name}' crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Service Communication Test Results")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All service communication tests passed!")
    elif passed >= total * 0.8:
        print("⚠️  Most tests passed. Minor issues may need attention.")
    else:
        print("🚨 Several tests failed. Service communication needs work.")
    
    return passed / total


if __name__ == "__main__":
    asyncio.run(main())
