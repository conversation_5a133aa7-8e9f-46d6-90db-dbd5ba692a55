{"name": "@kg-visualizer/chat-service", "version": "1.0.0", "description": "Chat service for Knowledge Graph Visualizer", "main": "dist/main.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "nodemon --exec ts-node src/main.ts", "start": "node dist/main.js", "start:production": "NODE_ENV=production node dist/main.js", "clean": "rm -rf dist", "clean:build": "npm run clean && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "migration:legacy": "cd ../.. && python real_llm_kg_script.py"}, "dependencies": {"@kg-visualizer/shared": "^1.0.0", "express": "^4.18.0", "cors": "^2.8.5", "helmet": "^7.0.0", "openai": "^4.0.0", "neo4j-driver": "^5.12.0", "axios": "^1.4.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "jest": "^29.5.0", "nodemon": "^3.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.1.0"}, "files": ["dist", "src"]}