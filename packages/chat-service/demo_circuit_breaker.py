#!/usr/bin/env python3
"""
Circuit Breaker Demonstration

This script demonstrates the circuit breaker pattern in action,
showing how the service handles failures and recovers automatically.
"""

import asyncio
import sys
import os
import time

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from chat_service.core.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitState,
    get_circuit_breaker
)
from chat_service.services.dependencies import get_chat_service


class FailingService:
    """A service that fails predictably for demonstration."""
    
    def __init__(self):
        self.call_count = 0
        self.failure_pattern = [True, True, False, False, False, True, True, True, False, False]
    
    async def call(self):
        """Make a call that may succeed or fail based on pattern."""
        should_fail = self.failure_pattern[self.call_count % len(self.failure_pattern)]
        self.call_count += 1
        
        if should_fail:
            raise Exception(f"Service failure #{self.call_count}")
        
        return f"Success #{self.call_count}"


async def demonstrate_basic_circuit_breaker():
    """Demonstrate basic circuit breaker functionality."""
    print("🔧 Circuit Breaker Basic Demonstration")
    print("=" * 50)
    
    # Create a circuit breaker with low thresholds for quick demonstration
    config = CircuitBreakerConfig(
        failure_threshold=3,
        recovery_timeout=2.0,
        name="demo_breaker"
    )
    breaker = CircuitBreaker(config)
    service = FailingService()
    
    print(f"Initial state: {breaker.stats.current_state.value}")
    print()
    
    # Make several calls to demonstrate state transitions
    for i in range(15):
        try:
            result = await breaker.call(service.call)
            print(f"Call {i+1}: ✅ {result} (State: {breaker.stats.current_state.value})")
        except Exception as e:
            print(f"Call {i+1}: ❌ {str(e)} (State: {breaker.stats.current_state.value})")
        
        # Show stats periodically
        if (i + 1) % 5 == 0:
            stats = breaker.get_stats()
            print(f"  📊 Stats: {stats['successful_calls']}/{stats['total_calls']} success rate: {stats['success_rate']:.2f}")
            print(f"  🔄 Consecutive failures: {stats['consecutive_failures']}")
            print()
        
        await asyncio.sleep(0.5)
    
    print("\n🎯 Final Statistics:")
    final_stats = breaker.get_stats()
    for key, value in final_stats.items():
        if key != 'config':
            print(f"  {key}: {value}")


async def demonstrate_resilient_chat_service():
    """Demonstrate the resilient chat service with circuit breakers."""
    print("\n🚀 Resilient Chat Service Demonstration")
    print("=" * 50)
    
    try:
        # Get the resilient chat service
        chat_service = get_chat_service()
        print(f"Service type: {type(chat_service).__name__}")
        
        # Test health check
        health = await chat_service.health_check()
        print(f"Health status: {health['status']}")
        print(f"Service mode: {health.get('mode', 'unknown')}")
        
        # Test several messages
        test_messages = [
            "Hello, how are you?",
            "What's the weather like?",
            "Can you help me with a problem?",
            "Tell me a joke",
            "What time is it?"
        ]
        
        print("\n📨 Processing test messages:")
        for i, message in enumerate(test_messages, 1):
            try:
                start_time = time.time()
                response = await chat_service.process_message(message)
                duration = time.time() - start_time
                
                service_mode = response['metadata'].get('service_mode', 'unknown')
                content_preview = response['message']['content'][:60] + "..."
                
                print(f"  {i}. ✅ {service_mode.upper()} ({duration:.2f}s): {content_preview}")
                
            except Exception as e:
                print(f"  {i}. ❌ Error: {str(e)}")
            
            await asyncio.sleep(0.5)
        
        # Show circuit breaker statistics
        print("\n🔧 Circuit Breaker Statistics:")
        stats = chat_service.get_statistics()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()


async def demonstrate_circuit_breaker_recovery():
    """Demonstrate circuit breaker recovery after failures."""
    print("\n🔄 Circuit Breaker Recovery Demonstration")
    print("=" * 50)
    
    # Create a breaker that opens quickly
    config = CircuitBreakerConfig(
        failure_threshold=2,
        recovery_timeout=3.0,
        name="recovery_demo"
    )
    breaker = CircuitBreaker(config)
    
    async def always_fail():
        raise Exception("Always fails")
    
    async def always_succeed():
        return "Success!"
    
    print("Phase 1: Causing failures to open circuit")
    # Cause failures to open the circuit
    for i in range(3):
        try:
            await breaker.call(always_fail)
        except Exception as e:
            print(f"  Failure {i+1}: {str(e)} (State: {breaker.stats.current_state.value})")
    
    print(f"\nCircuit is now: {breaker.stats.current_state.value}")
    
    # Try to call while circuit is open
    print("\nPhase 2: Attempting calls while circuit is open")
    try:
        await breaker.call(always_succeed)
    except Exception as e:
        print(f"  Call blocked: {str(e)}")
    
    print(f"\nWaiting {config.recovery_timeout} seconds for recovery timeout...")
    await asyncio.sleep(config.recovery_timeout + 0.1)
    
    print("\nPhase 3: Recovery - circuit should be half-open")
    # Now the circuit should allow test calls
    try:
        result = await breaker.call(always_succeed)
        print(f"  Recovery call 1: ✅ {result} (State: {breaker.stats.current_state.value})")
        
        result = await breaker.call(always_succeed)
        print(f"  Recovery call 2: ✅ {result} (State: {breaker.stats.current_state.value})")
        
        result = await breaker.call(always_succeed)
        print(f"  Recovery call 3: ✅ {result} (State: {breaker.stats.current_state.value})")
        
    except Exception as e:
        print(f"  Recovery failed: {str(e)}")
    
    print(f"\nFinal circuit state: {breaker.stats.current_state.value}")


async def main():
    """Run all demonstrations."""
    print("🎪 Circuit Breaker Pattern Demonstration")
    print("🔧 Knowledge Graph Chat Service")
    print("=" * 60)
    
    try:
        await demonstrate_basic_circuit_breaker()
        await demonstrate_resilient_chat_service()
        await demonstrate_circuit_breaker_recovery()
        
        print("\n✅ All demonstrations completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demonstration interrupted by user")
    except Exception as e:
        print(f"\n❌ Demonstration failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
