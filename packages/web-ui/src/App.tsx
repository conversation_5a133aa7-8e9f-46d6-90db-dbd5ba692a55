/**
 * Main App Component
 *
 * Root component that sets up the feature-slice architecture
 * with migration adapter pattern for seamless transitions.
 */

import React from 'react';
import { GraphVisualizationProvider, GraphCanvas } from './features/graph-visualization';
import { featureBus } from './shared/infrastructure/FeatureBus';
import {
  MigrationProvider,
  ComponentAdapter,
  MigrationControlPanel,
  useMigrationControlPanel,
  MigrationMode
} from './shared/adapters';

// Legacy graph component (placeholder)
const LegacyGraphComponent: React.FC<{ width: number; height: number }> = ({ width, height }) => (
  <div
    style={{
      width,
      height,
      border: '2px dashed #ccc',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: '#f8f9fa',
      color: '#6c757d'
    }}
  >
    <div style={{ textAlign: 'center' }}>
      <h3>Legacy Graph Visualization</h3>
      <p>Original implementation</p>
      <small>This would be your existing graph component</small>
    </div>
  </div>
);

// Modern graph wrapper
const ModernGraphComponent: React.FC<{ width: number; height: number }> = ({ width, height }) => (
  <GraphVisualizationProvider>
    <GraphCanvas
      width={width}
      height={height}
      onNodeClick={(node, event) => {
        console.log('Node clicked:', node, event);
      }}
      onCanvasClick={(event) => {
        console.log('Canvas clicked:', event);
      }}
    />
  </GraphVisualizationProvider>
);

// Main app content
const AppContent: React.FC = () => {
  const { isOpen, toggle, close } = useMigrationControlPanel();

  React.useEffect(() => {
    // Initialize feature bus and register features
    console.log('Initializing Knowledge Graph Visualizer with Migration Adapter Pattern');

    // Log feature bus health
    const health = featureBus.getFeatureHealth();
    console.log('Feature Bus Health:', health);

    return () => {
      // Cleanup on unmount
      featureBus.cleanup();
    };
  }, []);

  return (
    <div className="app">
      <header className="app-header">
        <h1>Knowledge Graph Visualizer</h1>
        <p>Migration Adapter Pattern Demo</p>
        <button
          className="migration-toggle-btn"
          onClick={toggle}
          style={{
            position: 'absolute',
            top: '1rem',
            right: '1rem',
            padding: '8px 16px',
            background: 'rgba(255,255,255,0.2)',
            border: '1px solid rgba(255,255,255,0.3)',
            borderRadius: '4px',
            color: 'white',
            cursor: 'pointer'
          }}
        >
          Migration Control
        </button>
      </header>

      <main className="app-main">
        <div className="graph-container">
          <ComponentAdapter
            featureName="graph-visualization"
            legacyComponent={LegacyGraphComponent}
            modernComponent={ModernGraphComponent}
            props={{ width: 800, height: 600 }}
            enableLazyLoading={true}
            performanceThreshold={100}
          />
        </div>
      </main>

      <footer className="app-footer">
        <p>Powered by Feature-Slice Architecture with Migration Adapter Pattern</p>
      </footer>

      <MigrationControlPanel
        isOpen={isOpen}
        onClose={close}
        showAdvanced={true}
      />
    </div>
  );
};

export const App: React.FC = () => {
  return (
    <MigrationProvider
      initialConfig={{
        mode: MigrationMode.HYBRID,
        enabledFeatures: ['graph-visualization'],
        fallbackToLegacy: true,
        userCanToggle: true,
        debugMode: process.env.NODE_ENV === 'development',
        migrationProgress: 25
      }}
    >
      <AppContent />
    </MigrationProvider>
  );
};
