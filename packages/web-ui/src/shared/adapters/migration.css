/**
 * Migration Adapter Styles
 * 
 * Styles for migration control panel and component adapters.
 */

/* Migration Control Panel */
.migration-control-panel {
  position: fixed;
  width: 400px;
  max-height: 600px;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  overflow: hidden;
}

.migration-control-panel.top-right {
  top: 20px;
  right: 20px;
}

.migration-control-panel.top-left {
  top: 20px;
  left: 20px;
}

.migration-control-panel.bottom-right {
  bottom: 20px;
  right: 20px;
}

.migration-control-panel.bottom-left {
  bottom: 20px;
  left: 20px;
}

/* Panel Header */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-button:hover {
  background: #e9ecef;
  color: #495057;
}

/* Panel Tabs */
.panel-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.panel-tabs button {
  flex: 1;
  padding: 12px 8px;
  background: none;
  border: none;
  font-size: 13px;
  font-weight: 500;
  color: #6c757d;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.panel-tabs button:hover {
  color: #495057;
  background: #e9ecef;
}

.panel-tabs button.active {
  color: #4A90E2;
  border-bottom-color: #4A90E2;
  background: white;
}

/* Panel Content */
.panel-content {
  padding: 20px;
  max-height: 480px;
  overflow-y: auto;
}

/* Overview Tab */
.overview-tab {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.migration-progress h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4A90E2, #5cb85c);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6c757d;
}

/* Mode Selector */
.mode-selector h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.mode-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mode-option {
  display: flex;
  flex-direction: column;
  padding: 12px;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mode-option:hover {
  border-color: #4A90E2;
  background: #f8f9ff;
}

.mode-option.active {
  border-color: #4A90E2;
  background: #f8f9ff;
}

.mode-option input[type="radio"] {
  margin-right: 8px;
}

.mode-label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.mode-description {
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
}

/* Status Summary */
.status-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
}

.status-item:last-child {
  border-bottom: none;
}

.status-item .label {
  font-size: 13px;
  color: #6c757d;
}

.status-item .value {
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
}

/* Features Tab */
.features-tab h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  padding: 12px;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  background: #fafbfc;
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.feature-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.feature-toggle input[type="checkbox"] {
  margin-right: 8px;
}

.feature-name {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
}

.feature-details {
  display: flex;
  gap: 8px;
}

.status-badge,
.enabled-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.enabled-badge.enabled {
  background: #d1ecf1;
  color: #0c5460;
}

.enabled-badge.disabled {
  background: #f8d7da;
  color: #721c24;
}

/* Performance Tab */
.performance-tab h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.metric-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  text-align: center;
}

.metric-label {
  display: block;
  font-size: 11px;
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 500;
  margin-bottom: 4px;
}

.metric-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.error-section {
  padding: 12px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
}

.error-section h5 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #721c24;
}

.error-details p {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #721c24;
}

.error-details pre {
  font-size: 10px;
  background: #fff;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 100px;
}

/* Settings Tab */
.settings-tab h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #2c3e50;
  cursor: pointer;
}

.setting-item input[type="checkbox"] {
  margin-right: 8px;
}

.setting-item input[type="range"] {
  margin: 0 8px;
  flex: 1;
}

/* Migration Status Indicator */
.migration-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #6c757d;
}

.status-dot.completed {
  background: #28a745;
}

.status-dot.pending {
  background: #ffc107;
}

.mode-badge {
  padding: 2px 6px;
  background: #e9ecef;
  color: #495057;
  font-size: 10px;
  border-radius: 3px;
  text-transform: uppercase;
  font-weight: 500;
}

/* Component Adapter */
.component-adapter-debug {
  position: relative;
}

.debug-overlay {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  font-size: 10px;
  border-radius: 0 0 0 4px;
  z-index: 10;
  pointer-events: none;
}

.component-adapter-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #6c757d;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.component-adapter-error {
  padding: 16px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  color: #721c24;
}

.component-adapter-error h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.component-adapter-error button {
  margin-top: 8px;
  padding: 6px 12px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.component-adapter-fallback {
  padding: 16px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  color: #856404;
  text-align: center;
}
