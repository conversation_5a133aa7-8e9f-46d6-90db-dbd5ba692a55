/**
 * Migration Control Panel
 * 
 * User interface for controlling migration settings, toggling features,
 * and monitoring migration progress.
 */

import React, { useState, useCallback } from 'react';
import { useMigration, useMigrationMode, MigrationMode } from './MigrationAdapter';
import { MigrationStatusIndicator } from './ComponentAdapter';

export interface MigrationControlPanelProps {
  isOpen: boolean;
  onClose: () => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  showAdvanced?: boolean;
}

export const MigrationControlPanel: React.FC<MigrationControlPanelProps> = ({
  isOpen,
  onClose,
  position = 'top-right',
  showAdvanced = false
}) => {
  const { 
    state, 
    setMode, 
    toggleFeature, 
    getMigrationStatus 
  } = useMigration();
  
  const { mode, isTransitioning } = useMigrationMode();
  const [activeTab, setActiveTab] = useState<'overview' | 'features' | 'performance' | 'settings'>('overview');
  
  const migrationStatus = getMigrationStatus();
  const allFeatures = ['graph-visualization', 'chat-interface', 'analysis-tools', 'search-filter'];

  // Handle mode change
  const handleModeChange = useCallback((newMode: MigrationMode) => {
    if (isTransitioning) return;
    setMode(newMode);
  }, [setMode, isTransitioning]);

  // Handle feature toggle
  const handleFeatureToggle = useCallback((featureName: string, enabled: boolean) => {
    toggleFeature(featureName, enabled);
  }, [toggleFeature]);

  // Calculate progress percentage
  const progressPercentage = (migrationStatus.completedFeatures.length / allFeatures.length) * 100;

  if (!isOpen) return null;

  return (
    <div className={`migration-control-panel ${position}`}>
      <div className="panel-header">
        <h3>Migration Control</h3>
        <button className="close-button" onClick={onClose}>×</button>
      </div>

      <div className="panel-tabs">
        <button 
          className={activeTab === 'overview' ? 'active' : ''}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button 
          className={activeTab === 'features' ? 'active' : ''}
          onClick={() => setActiveTab('features')}
        >
          Features
        </button>
        <button 
          className={activeTab === 'performance' ? 'active' : ''}
          onClick={() => setActiveTab('performance')}
        >
          Performance
        </button>
        {showAdvanced && (
          <button 
            className={activeTab === 'settings' ? 'active' : ''}
            onClick={() => setActiveTab('settings')}
          >
            Settings
          </button>
        )}
      </div>

      <div className="panel-content">
        {activeTab === 'overview' && (
          <OverviewTab
            mode={mode}
            isTransitioning={isTransitioning}
            migrationStatus={migrationStatus}
            progressPercentage={progressPercentage}
            onModeChange={handleModeChange}
          />
        )}

        {activeTab === 'features' && (
          <FeaturesTab
            allFeatures={allFeatures}
            enabledFeatures={state.config.enabledFeatures}
            migrationStatus={migrationStatus}
            onFeatureToggle={handleFeatureToggle}
          />
        )}

        {activeTab === 'performance' && (
          <PerformanceTab
            performanceMetrics={state.performanceMetrics}
            lastError={state.lastError}
          />
        )}

        {activeTab === 'settings' && showAdvanced && (
          <SettingsTab
            config={state.config}
            onConfigChange={(updates) => {
              // Handle config updates
              console.log('Config updates:', updates);
            }}
          />
        )}
      </div>
    </div>
  );
};

// Overview tab component
const OverviewTab: React.FC<{
  mode: MigrationMode;
  isTransitioning: boolean;
  migrationStatus: any;
  progressPercentage: number;
  onModeChange: (mode: MigrationMode) => void;
}> = ({ mode, isTransitioning, migrationStatus, progressPercentage, onModeChange }) => (
  <div className="overview-tab">
    <div className="migration-progress">
      <h4>Migration Progress</h4>
      <div className="progress-bar">
        <div 
          className="progress-fill" 
          style={{ width: `${progressPercentage}%` }}
        />
      </div>
      <span className="progress-text">{progressPercentage.toFixed(0)}% Complete</span>
    </div>

    <div className="mode-selector">
      <h4>Architecture Mode</h4>
      <div className="mode-options">
        <label className={`mode-option ${mode === MigrationMode.LEGACY ? 'active' : ''}`}>
          <input
            type="radio"
            name="migration-mode"
            value={MigrationMode.LEGACY}
            checked={mode === MigrationMode.LEGACY}
            onChange={() => onModeChange(MigrationMode.LEGACY)}
            disabled={isTransitioning}
          />
          <span className="mode-label">Legacy</span>
          <span className="mode-description">Use original architecture</span>
        </label>

        <label className={`mode-option ${mode === MigrationMode.HYBRID ? 'active' : ''}`}>
          <input
            type="radio"
            name="migration-mode"
            value={MigrationMode.HYBRID}
            checked={mode === MigrationMode.HYBRID}
            onChange={() => onModeChange(MigrationMode.HYBRID)}
            disabled={isTransitioning}
          />
          <span className="mode-label">Hybrid</span>
          <span className="mode-description">Mix of old and new features</span>
        </label>

        <label className={`mode-option ${mode === MigrationMode.MODERN ? 'active' : ''}`}>
          <input
            type="radio"
            name="migration-mode"
            value={MigrationMode.MODERN}
            checked={mode === MigrationMode.MODERN}
            onChange={() => onModeChange(MigrationMode.MODERN)}
            disabled={isTransitioning}
          />
          <span className="mode-label">Modern</span>
          <span className="mode-description">Use new architecture</span>
        </label>
      </div>
    </div>

    <div className="status-summary">
      <div className="status-item">
        <span className="label">Completed Features:</span>
        <span className="value">{migrationStatus.completedFeatures.length}</span>
      </div>
      <div className="status-item">
        <span className="label">Pending Features:</span>
        <span className="value">{migrationStatus.pendingFeatures.length}</span>
      </div>
      <div className="status-item">
        <span className="label">User Satisfaction:</span>
        <span className="value">{migrationStatus.userFeedback.satisfaction}/5</span>
      </div>
    </div>
  </div>
);

// Features tab component
const FeaturesTab: React.FC<{
  allFeatures: string[];
  enabledFeatures: string[];
  migrationStatus: any;
  onFeatureToggle: (feature: string, enabled: boolean) => void;
}> = ({ allFeatures, enabledFeatures, migrationStatus, onFeatureToggle }) => (
  <div className="features-tab">
    <h4>Feature Status</h4>
    <div className="features-list">
      {allFeatures.map(feature => {
        const isEnabled = enabledFeatures.includes(feature);
        const isCompleted = migrationStatus.completedFeatures.includes(feature);
        
        return (
          <div key={feature} className="feature-item">
            <div className="feature-header">
              <label className="feature-toggle">
                <input
                  type="checkbox"
                  checked={isEnabled}
                  onChange={(e) => onFeatureToggle(feature, e.target.checked)}
                />
                <span className="feature-name">{feature}</span>
              </label>
              <MigrationStatusIndicator featureName={feature} />
            </div>
            
            <div className="feature-details">
              <span className={`status-badge ${isCompleted ? 'completed' : 'pending'}`}>
                {isCompleted ? 'Completed' : 'Pending'}
              </span>
              <span className={`enabled-badge ${isEnabled ? 'enabled' : 'disabled'}`}>
                {isEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
        );
      })}
    </div>
  </div>
);

// Performance tab component
const PerformanceTab: React.FC<{
  performanceMetrics: any;
  lastError: Error | null;
}> = ({ performanceMetrics, lastError }) => (
  <div className="performance-tab">
    <h4>Performance Metrics</h4>
    
    <div className="metrics-grid">
      <div className="metric-item">
        <span className="metric-label">Legacy Render Time</span>
        <span className="metric-value">{performanceMetrics.legacyRenderTime.toFixed(2)}ms</span>
      </div>
      
      <div className="metric-item">
        <span className="metric-label">Modern Render Time</span>
        <span className="metric-value">{performanceMetrics.modernRenderTime.toFixed(2)}ms</span>
      </div>
      
      <div className="metric-item">
        <span className="metric-label">Error Count</span>
        <span className="metric-value">{performanceMetrics.errorCount}</span>
      </div>
      
      <div className="metric-item">
        <span className="metric-label">User Satisfaction</span>
        <span className="metric-value">{performanceMetrics.userSatisfaction}/5</span>
      </div>
    </div>

    {lastError && (
      <div className="error-section">
        <h5>Last Error</h5>
        <div className="error-details">
          <p><strong>Message:</strong> {lastError.message}</p>
          <details>
            <summary>Stack Trace</summary>
            <pre>{lastError.stack}</pre>
          </details>
        </div>
      </div>
    )}
  </div>
);

// Settings tab component
const SettingsTab: React.FC<{
  config: any;
  onConfigChange: (updates: any) => void;
}> = ({ config, onConfigChange }) => (
  <div className="settings-tab">
    <h4>Advanced Settings</h4>
    
    <div className="setting-item">
      <label>
        <input
          type="checkbox"
          checked={config.fallbackToLegacy}
          onChange={(e) => onConfigChange({ fallbackToLegacy: e.target.checked })}
        />
        Auto-fallback to legacy on errors
      </label>
    </div>
    
    <div className="setting-item">
      <label>
        <input
          type="checkbox"
          checked={config.userCanToggle}
          onChange={(e) => onConfigChange({ userCanToggle: e.target.checked })}
        />
        Allow user to toggle features
      </label>
    </div>
    
    <div className="setting-item">
      <label>
        <input
          type="checkbox"
          checked={config.debugMode}
          onChange={(e) => onConfigChange({ debugMode: e.target.checked })}
        />
        Enable debug mode
      </label>
    </div>
    
    <div className="setting-item">
      <label>
        Migration Progress:
        <input
          type="range"
          min="0"
          max="100"
          value={config.migrationProgress}
          onChange={(e) => onConfigChange({ migrationProgress: parseInt(e.target.value) })}
        />
        <span>{config.migrationProgress}%</span>
      </label>
    </div>
  </div>
);

// Hook for control panel state
export const useMigrationControlPanel = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  const toggle = useCallback(() => setIsOpen(prev => !prev), []);
  const open = useCallback(() => setIsOpen(true), []);
  const close = useCallback(() => setIsOpen(false), []);
  
  return { isOpen, toggle, open, close };
};
