/**
 * Migration Adapter
 * 
 * Provides seamless transition between legacy and modern frontend architectures.
 * Allows users to toggle between old and new patterns during migration.
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { featureBus } from '../infrastructure/FeatureBus';

// Migration modes
export enum MigrationMode {
  LEGACY = 'legacy',
  HYBRID = 'hybrid',
  MODERN = 'modern'
}

// Migration configuration
export interface MigrationConfig {
  mode: MigrationMode;
  enabledFeatures: string[];
  fallbackToLegacy: boolean;
  userCanToggle: boolean;
  debugMode: boolean;
  migrationProgress: number; // 0-100
}

// Migration state
export interface MigrationState {
  config: MigrationConfig;
  isTransitioning: boolean;
  lastError: Error | null;
  performanceMetrics: {
    legacyRenderTime: number;
    modernRenderTime: number;
    errorCount: number;
    userSatisfaction: number;
  };
}

// Migration context
interface MigrationContextType {
  state: MigrationState;
  setMode: (mode: MigrationMode) => void;
  toggleFeature: (featureName: string, enabled: boolean) => void;
  reportError: (error: Error, component: string) => void;
  reportPerformance: (component: string, renderTime: number, mode: MigrationMode) => void;
  canUseModernFeature: (featureName: string) => boolean;
  getMigrationStatus: () => MigrationStatus;
}

// Migration status
export interface MigrationStatus {
  overallProgress: number;
  completedFeatures: string[];
  pendingFeatures: string[];
  blockedFeatures: string[];
  userFeedback: {
    satisfaction: number;
    issues: string[];
    preferences: Record<string, any>;
  };
}

// Default configuration
const defaultConfig: MigrationConfig = {
  mode: MigrationMode.HYBRID,
  enabledFeatures: ['graph-visualization'],
  fallbackToLegacy: true,
  userCanToggle: true,
  debugMode: process.env.NODE_ENV === 'development',
  migrationProgress: 25
};

// Migration context
const MigrationContext = createContext<MigrationContextType | null>(null);

// Migration provider component
export const MigrationProvider: React.FC<{
  children: React.ReactNode;
  initialConfig?: Partial<MigrationConfig>;
}> = ({ children, initialConfig = {} }) => {
  const [state, setState] = useState<MigrationState>({
    config: { ...defaultConfig, ...initialConfig },
    isTransitioning: false,
    lastError: null,
    performanceMetrics: {
      legacyRenderTime: 0,
      modernRenderTime: 0,
      errorCount: 0,
      userSatisfaction: 0
    }
  });

  // Set migration mode
  const setMode = useCallback((mode: MigrationMode) => {
    setState(prev => ({
      ...prev,
      isTransitioning: true,
      config: { ...prev.config, mode }
    }));

    // Publish migration mode change event
    featureBus.communicate.publish({
      type: 'migration:mode-changed',
      payload: { mode, timestamp: new Date() },
      timestamp: new Date(),
      source: 'migration-adapter'
    });

    // Simulate transition time
    setTimeout(() => {
      setState(prev => ({ ...prev, isTransitioning: false }));
    }, 500);
  }, []);

  // Toggle feature
  const toggleFeature = useCallback((featureName: string, enabled: boolean) => {
    setState(prev => {
      const enabledFeatures = enabled
        ? [...prev.config.enabledFeatures, featureName]
        : prev.config.enabledFeatures.filter(f => f !== featureName);

      return {
        ...prev,
        config: {
          ...prev.config,
          enabledFeatures: [...new Set(enabledFeatures)]
        }
      };
    });

    // Publish feature toggle event
    featureBus.communicate.publish({
      type: 'migration:feature-toggled',
      payload: { featureName, enabled, timestamp: new Date() },
      timestamp: new Date(),
      source: 'migration-adapter'
    });
  }, []);

  // Report error
  const reportError = useCallback((error: Error, component: string) => {
    setState(prev => ({
      ...prev,
      lastError: error,
      performanceMetrics: {
        ...prev.performanceMetrics,
        errorCount: prev.performanceMetrics.errorCount + 1
      }
    }));

    // Publish error event
    featureBus.communicate.publish({
      type: 'migration:error',
      payload: { 
        error: error.message, 
        component, 
        stack: error.stack,
        timestamp: new Date() 
      },
      timestamp: new Date(),
      source: 'migration-adapter'
    });

    // Auto-fallback to legacy if too many errors
    if (state.performanceMetrics.errorCount > 5 && state.config.fallbackToLegacy) {
      console.warn('Too many errors detected, falling back to legacy mode');
      setMode(MigrationMode.LEGACY);
    }
  }, [state.performanceMetrics.errorCount, state.config.fallbackToLegacy, setMode]);

  // Report performance
  const reportPerformance = useCallback((component: string, renderTime: number, mode: MigrationMode) => {
    setState(prev => ({
      ...prev,
      performanceMetrics: {
        ...prev.performanceMetrics,
        [mode === MigrationMode.LEGACY ? 'legacyRenderTime' : 'modernRenderTime']: renderTime
      }
    }));

    // Publish performance event
    featureBus.communicate.publish({
      type: 'migration:performance',
      payload: { component, renderTime, mode, timestamp: new Date() },
      timestamp: new Date(),
      source: 'migration-adapter'
    });
  }, []);

  // Check if modern feature can be used
  const canUseModernFeature = useCallback((featureName: string) => {
    const { mode, enabledFeatures } = state.config;
    
    if (mode === MigrationMode.LEGACY) return false;
    if (mode === MigrationMode.MODERN) return true;
    
    // Hybrid mode - check if feature is enabled
    return enabledFeatures.includes(featureName);
  }, [state.config]);

  // Get migration status
  const getMigrationStatus = useCallback((): MigrationStatus => {
    const allFeatures = ['graph-visualization', 'chat-interface', 'analysis-tools', 'search-filter'];
    const { enabledFeatures, migrationProgress } = state.config;
    
    return {
      overallProgress: migrationProgress,
      completedFeatures: enabledFeatures,
      pendingFeatures: allFeatures.filter(f => !enabledFeatures.includes(f)),
      blockedFeatures: [], // Features blocked by errors or dependencies
      userFeedback: {
        satisfaction: state.performanceMetrics.userSatisfaction,
        issues: state.lastError ? [state.lastError.message] : [],
        preferences: {
          preferredMode: state.config.mode,
          enabledFeatures: state.config.enabledFeatures
        }
      }
    };
  }, [state]);

  // Load migration preferences from localStorage
  useEffect(() => {
    const savedPreferences = localStorage.getItem('migration-preferences');
    if (savedPreferences) {
      try {
        const preferences = JSON.parse(savedPreferences);
        setState(prev => ({
          ...prev,
          config: { ...prev.config, ...preferences }
        }));
      } catch (error) {
        console.warn('Failed to load migration preferences:', error);
      }
    }
  }, []);

  // Save migration preferences to localStorage
  useEffect(() => {
    localStorage.setItem('migration-preferences', JSON.stringify({
      mode: state.config.mode,
      enabledFeatures: state.config.enabledFeatures
    }));
  }, [state.config.mode, state.config.enabledFeatures]);

  // Context value
  const contextValue: MigrationContextType = {
    state,
    setMode,
    toggleFeature,
    reportError,
    reportPerformance,
    canUseModernFeature,
    getMigrationStatus
  };

  return (
    <MigrationContext.Provider value={contextValue}>
      {children}
    </MigrationContext.Provider>
  );
};

// Hook to use migration context
export const useMigration = (): MigrationContextType => {
  const context = useContext(MigrationContext);
  if (!context) {
    throw new Error('useMigration must be used within MigrationProvider');
  }
  return context;
};

// Migration mode detector hook
export const useMigrationMode = () => {
  const { state, canUseModernFeature } = useMigration();
  
  return {
    mode: state.config.mode,
    isLegacy: state.config.mode === MigrationMode.LEGACY,
    isHybrid: state.config.mode === MigrationMode.HYBRID,
    isModern: state.config.mode === MigrationMode.MODERN,
    isTransitioning: state.isTransitioning,
    canUseFeature: canUseModernFeature
  };
};

// Performance monitoring hook
export const useMigrationPerformance = (componentName: string) => {
  const { reportPerformance, state } = useMigration();
  const startTime = React.useRef<number>(0);

  const startMeasurement = useCallback(() => {
    startTime.current = performance.now();
  }, []);

  const endMeasurement = useCallback(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTime.current;
    reportPerformance(componentName, renderTime, state.config.mode);
    return renderTime;
  }, [componentName, reportPerformance, state.config.mode]);

  return { startMeasurement, endMeasurement };
};

// Error boundary for migration
export class MigrationErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Migration Error Boundary caught an error:', error, errorInfo);
    
    // Report error to migration adapter if available
    try {
      featureBus.communicate.publish({
        type: 'migration:boundary-error',
        payload: { 
          error: error.message, 
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          timestamp: new Date() 
        },
        timestamp: new Date(),
        source: 'migration-error-boundary'
      });
    } catch (e) {
      console.error('Failed to report error to migration adapter:', e);
    }
  }

  override render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      if (FallbackComponent && this.state.error) {
        return <FallbackComponent error={this.state.error} />;
      }
      
      return (
        <div style={{ 
          padding: '20px', 
          border: '1px solid #ff6b6b', 
          borderRadius: '4px',
          backgroundColor: '#ffe0e0',
          color: '#d63031'
        }}>
          <h3>Migration Error</h3>
          <p>Something went wrong during the architecture migration.</p>
          <details>
            <summary>Error Details</summary>
            <pre>{this.state.error?.stack}</pre>
          </details>
          <button 
            onClick={() => this.setState({ hasError: false, error: null })}
            style={{
              marginTop: '10px',
              padding: '8px 16px',
              backgroundColor: '#d63031',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
