/**
 * Component Adapter
 * 
 * Adaptive component that can render either legacy or modern implementations
 * based on migration configuration and feature flags.
 */

import React, { Suspense, lazy, useCallback, useEffect } from 'react';
import { useMigration, useMigrationMode, useMigrationPerformance, MigrationErrorBoundary } from './MigrationAdapter';

// Component adapter props
export interface ComponentAdapterProps {
  featureName: string;
  legacyComponent?: React.ComponentType<any>;
  modernComponent?: React.ComponentType<any>;
  fallbackComponent?: React.ComponentType<any>;
  loadingComponent?: React.ComponentType;
  props?: Record<string, any>;
  enableLazyLoading?: boolean;
  performanceThreshold?: number; // ms
  children?: React.ReactNode;
}

// Performance comparison result
interface PerformanceComparison {
  legacyTime: number;
  modernTime: number;
  recommendation: 'legacy' | 'modern' | 'neutral';
  confidence: number;
}

// Component adapter implementation
export const ComponentAdapter: React.FC<ComponentAdapterProps> = ({
  featureName,
  legacyComponent: LegacyComponent,
  modernComponent: ModernComponent,
  fallbackComponent: FallbackComponent,
  loadingComponent: LoadingComponent = DefaultLoadingComponent,
  props = {},
  enableLazyLoading = true,
  performanceThreshold = 100,
  children
}) => {
  const { reportError, state } = useMigration();
  const { mode, canUseFeature, isTransitioning } = useMigrationMode();
  const { startMeasurement, endMeasurement } = useMigrationPerformance(featureName);

  // Determine which component to render
  const shouldUseModern = canUseFeature(featureName);
  const hasModernComponent = !!ModernComponent;
  const hasLegacyComponent = !!LegacyComponent;

  // Performance monitoring wrapper
  const withPerformanceMonitoring = useCallback((
    Component: React.ComponentType<any>,
    componentType: 'legacy' | 'modern'
  ) => {
    return React.forwardRef<any, any>((componentProps, ref) => {
      useEffect(() => {
        startMeasurement();
        return () => {
          const renderTime = endMeasurement();
          
          // Log performance if in debug mode
          if (state.config.debugMode) {
            console.log(`${featureName} (${componentType}) rendered in ${renderTime.toFixed(2)}ms`);
          }
          
          // Report performance issues
          if (renderTime > performanceThreshold) {
            console.warn(`${featureName} (${componentType}) exceeded performance threshold: ${renderTime.toFixed(2)}ms`);
          }
        };
      });

      return <Component {...componentProps} ref={ref} />;
    });
  }, [startMeasurement, endMeasurement, featureName, state.config.debugMode, performanceThreshold]);

  // Error handling wrapper
  const withErrorHandling = useCallback((
    Component: React.ComponentType<any>,
    componentType: 'legacy' | 'modern'
  ) => {
    return React.forwardRef<any, any>((componentProps, ref) => {
      const handleError = useCallback((error: Error) => {
        reportError(error, `${featureName}-${componentType}`);
      }, [reportError]);

      return (
        <MigrationErrorBoundary fallback={({ error }) => (
          <ErrorFallback 
            error={error} 
            featureName={featureName} 
            componentType={componentType}
            onRetry={() => window.location.reload()}
          />
        )}>
          <Component {...componentProps} ref={ref} onError={handleError} />
        </MigrationErrorBoundary>
      );
    });
  }, [reportError, featureName]);

  // Lazy loading wrapper
  const withLazyLoading = useCallback((Component: React.ComponentType<any>) => {
    if (!enableLazyLoading) return Component;
    
    return lazy(() => Promise.resolve({ default: Component }));
  }, [enableLazyLoading]);

  // Create enhanced components
  const createEnhancedComponent = useCallback((
    Component: React.ComponentType<any>,
    componentType: 'legacy' | 'modern'
  ) => {
    let EnhancedComponent = Component;
    
    // Add performance monitoring
    EnhancedComponent = withPerformanceMonitoring(EnhancedComponent, componentType);
    
    // Add error handling
    EnhancedComponent = withErrorHandling(EnhancedComponent, componentType);
    
    // Add lazy loading if enabled
    if (enableLazyLoading) {
      EnhancedComponent = withLazyLoading(EnhancedComponent);
    }
    
    return EnhancedComponent;
  }, [withPerformanceMonitoring, withErrorHandling, withLazyLoading, enableLazyLoading]);

  // Render loading state
  if (isTransitioning) {
    return <LoadingComponent />;
  }

  // Determine which component to render
  let ComponentToRender: React.ComponentType<any> | null = null;
  let componentType: 'legacy' | 'modern' = 'legacy';

  if (shouldUseModern && hasModernComponent) {
    ComponentToRender = createEnhancedComponent(ModernComponent!, 'modern');
    componentType = 'modern';
  } else if (hasLegacyComponent) {
    ComponentToRender = createEnhancedComponent(LegacyComponent!, 'legacy');
    componentType = 'legacy';
  } else if (FallbackComponent) {
    ComponentToRender = FallbackComponent;
  }

  // Render fallback if no component available
  if (!ComponentToRender) {
    return (
      <div className="component-adapter-fallback">
        <p>Component not available for feature: {featureName}</p>
        <p>Mode: {mode}, Modern available: {hasModernComponent ? 'Yes' : 'No'}</p>
      </div>
    );
  }

  // Render with suspense if lazy loading is enabled
  const content = enableLazyLoading ? (
    <Suspense fallback={<LoadingComponent />}>
      <ComponentToRender {...props}>
        {children}
      </ComponentToRender>
    </Suspense>
  ) : (
    <ComponentToRender {...props}>
      {children}
    </ComponentToRender>
  );

  // Add debug overlay in development
  if (state.config.debugMode && process.env.NODE_ENV === 'development') {
    return (
      <div className="component-adapter-debug">
        <div className="debug-overlay">
          <small>
            {featureName} ({componentType}) - Mode: {mode}
          </small>
        </div>
        {content}
      </div>
    );
  }

  return content;
};

// Default loading component
const DefaultLoadingComponent: React.FC = () => (
  <div className="component-adapter-loading">
    <div className="loading-spinner" />
    <span>Loading component...</span>
  </div>
);

// Error fallback component
const ErrorFallback: React.FC<{
  error: Error;
  featureName: string;
  componentType: string;
  onRetry: () => void;
}> = ({ error, featureName, componentType, onRetry }) => (
  <div className="component-adapter-error">
    <h4>Component Error</h4>
    <p>
      Failed to render {featureName} ({componentType})
    </p>
    <details>
      <summary>Error Details</summary>
      <pre>{error.message}</pre>
    </details>
    <button onClick={onRetry}>Retry</button>
  </div>
);

// Hook for component comparison
export const useComponentComparison = (_featureName: string) => {
  const { state } = useMigration();
  
  const comparePerformance = useCallback((): PerformanceComparison => {
    const { legacyRenderTime, modernRenderTime } = state.performanceMetrics;
    
    if (legacyRenderTime === 0 || modernRenderTime === 0) {
      return {
        legacyTime: legacyRenderTime,
        modernTime: modernRenderTime,
        recommendation: 'neutral',
        confidence: 0
      };
    }
    
    const difference = Math.abs(legacyRenderTime - modernRenderTime);
    const faster = legacyRenderTime < modernRenderTime ? 'legacy' : 'modern';
    const confidence = Math.min(difference / Math.max(legacyRenderTime, modernRenderTime), 1);
    
    return {
      legacyTime: legacyRenderTime,
      modernTime: modernRenderTime,
      recommendation: difference > 10 ? faster : 'neutral',
      confidence
    };
  }, [state.performanceMetrics]);
  
  return { comparePerformance };
};

// Higher-order component for migration adaptation
export const withMigrationAdapter = <P extends object>(
  featureName: string,
  options: Partial<ComponentAdapterProps> = {}
) => {
  return (Component: React.ComponentType<P>) => {
    const AdaptedComponent = React.forwardRef<any, P>((props, ref) => (
      <ComponentAdapter
        featureName={featureName}
        modernComponent={Component}
        {...options}
        props={{ ...props, ref }}
      />
    ));
    
    AdaptedComponent.displayName = `withMigrationAdapter(${Component.displayName || Component.name})`;
    
    return AdaptedComponent;
  };
};

// Migration status indicator component
export const MigrationStatusIndicator: React.FC<{
  featureName: string;
  showDetails?: boolean;
}> = ({ featureName, showDetails = false }) => {
  const { getMigrationStatus, state } = useMigration();
  const { mode, canUseFeature } = useMigrationMode();
  
  const status = getMigrationStatus();
  const isEnabled = canUseFeature(featureName);
  const isCompleted = status.completedFeatures.includes(featureName);
  
  return (
    <div className={`migration-status ${isEnabled ? 'enabled' : 'disabled'}`}>
      <div className="status-indicator">
        <span className={`status-dot ${isCompleted ? 'completed' : 'pending'}`} />
        <span className="feature-name">{featureName}</span>
        <span className="mode-badge">{mode}</span>
      </div>
      
      {showDetails && (
        <div className="status-details">
          <p>Enabled: {isEnabled ? 'Yes' : 'No'}</p>
          <p>Completed: {isCompleted ? 'Yes' : 'No'}</p>
          <p>Errors: {state.performanceMetrics.errorCount}</p>
        </div>
      )}
    </div>
  );
};
