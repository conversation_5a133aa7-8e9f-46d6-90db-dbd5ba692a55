/**
 * Migration Adapter Tests
 * 
 * Tests for the migration adapter pattern and related components.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { 
  MigrationProvider, 
  useMigration, 
  useMigrationMode,
  MigrationMode,
  MigrationErrorBoundary
} from '../MigrationAdapter';
import { ComponentAdapter } from '../ComponentAdapter';
import { MigrationControlPanel, useMigrationControlPanel } from '../MigrationControlPanel';

// Test components
const TestLegacyComponent: React.FC<{ message: string }> = ({ message }) => (
  <div data-testid="legacy-component">Legacy: {message}</div>
);

const TestModernComponent: React.FC<{ message: string }> = ({ message }) => (
  <div data-testid="modern-component">Modern: {message}</div>
);

const TestMigrationConsumer: React.FC = () => {
  const { state, setMode, toggleFeature, getMigrationStatus } = useMigration();
  const { mode, canUseFeature } = useMigrationMode();
  
  return (
    <div data-testid="migration-consumer">
      <div data-testid="current-mode">{mode}</div>
      <div data-testid="can-use-graph">{canUseFeature('graph-visualization').toString()}</div>
      <div data-testid="enabled-features">{state.config.enabledFeatures.join(',')}</div>
      <button 
        data-testid="set-modern-mode"
        onClick={() => setMode(MigrationMode.MODERN)}
      >
        Set Modern
      </button>
      <button 
        data-testid="toggle-chat"
        onClick={() => toggleFeature('chat-interface', true)}
      >
        Enable Chat
      </button>
      <div data-testid="migration-progress">
        {getMigrationStatus().overallProgress}
      </div>
    </div>
  );
};

const TestControlPanelConsumer: React.FC = () => {
  const { isOpen, toggle, close } = useMigrationControlPanel();
  
  return (
    <div>
      <button data-testid="toggle-panel" onClick={toggle}>
        Toggle Panel
      </button>
      <MigrationControlPanel 
        isOpen={isOpen} 
        onClose={close}
        showAdvanced={true}
      />
    </div>
  );
};

const ErrorThrowingComponent: React.FC = () => {
  throw new Error('Test error');
};

describe('Migration Adapter', () => {
  describe('MigrationProvider', () => {
    it('should provide migration context', () => {
      render(
        <MigrationProvider>
          <TestMigrationConsumer />
        </MigrationProvider>
      );
      
      expect(screen.getByTestId('migration-consumer')).toBeInTheDocument();
      expect(screen.getByTestId('current-mode')).toHaveTextContent('hybrid');
      expect(screen.getByTestId('can-use-graph')).toHaveTextContent('true');
    });

    it('should handle mode changes', async () => {
      render(
        <MigrationProvider>
          <TestMigrationConsumer />
        </MigrationProvider>
      );
      
      const setModernButton = screen.getByTestId('set-modern-mode');
      fireEvent.click(setModernButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('current-mode')).toHaveTextContent('modern');
      });
    });

    it('should handle feature toggles', () => {
      render(
        <MigrationProvider>
          <TestMigrationConsumer />
        </MigrationProvider>
      );
      
      const toggleChatButton = screen.getByTestId('toggle-chat');
      fireEvent.click(toggleChatButton);
      
      expect(screen.getByTestId('enabled-features')).toHaveTextContent('graph-visualization,chat-interface');
    });

    it('should persist preferences to localStorage', () => {
      const mockLocalStorage = {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn()
      };
      
      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true
      });

      render(
        <MigrationProvider>
          <TestMigrationConsumer />
        </MigrationProvider>
      );
      
      const setModernButton = screen.getByTestId('set-modern-mode');
      fireEvent.click(setModernButton);
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'migration-preferences',
        expect.stringContaining('modern')
      );
    });
  });

  describe('ComponentAdapter', () => {
    it('should render legacy component in legacy mode', () => {
      render(
        <MigrationProvider initialConfig={{ mode: MigrationMode.LEGACY }}>
          <ComponentAdapter
            featureName="test-feature"
            legacyComponent={TestLegacyComponent}
            modernComponent={TestModernComponent}
            props={{ message: 'Hello' }}
            enableLazyLoading={false}
          />
        </MigrationProvider>
      );

      expect(screen.getByTestId('legacy-component')).toBeInTheDocument();
      expect(screen.getByText('Legacy: Hello')).toBeInTheDocument();
    });

    it('should render modern component in modern mode', () => {
      render(
        <MigrationProvider initialConfig={{ mode: MigrationMode.MODERN }}>
          <ComponentAdapter
            featureName="test-feature"
            legacyComponent={TestLegacyComponent}
            modernComponent={TestModernComponent}
            props={{ message: 'Hello' }}
            enableLazyLoading={false}
          />
        </MigrationProvider>
      );

      expect(screen.getByTestId('modern-component')).toBeInTheDocument();
      expect(screen.getByText('Modern: Hello')).toBeInTheDocument();
    });

    it('should render modern component for enabled features in hybrid mode', () => {
      render(
        <MigrationProvider initialConfig={{
          mode: MigrationMode.HYBRID,
          enabledFeatures: ['test-feature']
        }}>
          <ComponentAdapter
            featureName="test-feature"
            legacyComponent={TestLegacyComponent}
            modernComponent={TestModernComponent}
            props={{ message: 'Hello' }}
            enableLazyLoading={false}
          />
        </MigrationProvider>
      );

      expect(screen.getByTestId('modern-component')).toBeInTheDocument();
    });

    it('should render legacy component for disabled features in hybrid mode', () => {
      render(
        <MigrationProvider initialConfig={{
          mode: MigrationMode.HYBRID,
          enabledFeatures: []
        }}>
          <ComponentAdapter
            featureName="test-feature"
            legacyComponent={TestLegacyComponent}
            modernComponent={TestModernComponent}
            props={{ message: 'Hello' }}
            enableLazyLoading={false}
          />
        </MigrationProvider>
      );

      expect(screen.getByTestId('legacy-component')).toBeInTheDocument();
    });

    it('should show fallback when no component is available', () => {
      render(
        <MigrationProvider initialConfig={{ mode: MigrationMode.MODERN }}>
          <ComponentAdapter
            featureName="test-feature"
            props={{ message: 'Hello' }}
          />
        </MigrationProvider>
      );
      
      expect(screen.getByText(/Component not available for feature: test-feature/)).toBeInTheDocument();
    });

    it('should show debug overlay in development mode', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      render(
        <MigrationProvider initialConfig={{
          mode: MigrationMode.MODERN,
          debugMode: true
        }}>
          <ComponentAdapter
            featureName="test-feature"
            modernComponent={TestModernComponent}
            props={{ message: 'Hello' }}
            enableLazyLoading={false}
          />
        </MigrationProvider>
      );

      expect(screen.getByText(/test-feature \(modern\) - Mode: modern/)).toBeInTheDocument();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('MigrationErrorBoundary', () => {
    // Suppress console.error for this test
    const originalError = console.error;
    beforeAll(() => {
      console.error = jest.fn();
    });

    afterAll(() => {
      console.error = originalError;
    });

    it('should catch and display errors', () => {
      // Use a wrapper to ensure error boundary catches the error properly
      const TestWrapper = () => (
        <MigrationErrorBoundary>
          <ErrorThrowingComponent />
        </MigrationErrorBoundary>
      );

      render(<TestWrapper />);

      expect(screen.getByText('Migration Error')).toBeInTheDocument();
      expect(screen.getByText('Something went wrong during the architecture migration.')).toBeInTheDocument();
    });

    it('should allow retry after error', () => {
      const TestWrapper = () => (
        <MigrationErrorBoundary>
          <ErrorThrowingComponent />
        </MigrationErrorBoundary>
      );

      const { rerender } = render(<TestWrapper />);

      const retryButton = screen.getByText('Try Again');
      fireEvent.click(retryButton);

      // Component should attempt to render again
      expect(screen.getByText('Migration Error')).toBeInTheDocument();
    });

    it('should render custom fallback component', () => {
      const CustomFallback: React.FC<{ error: Error }> = ({ error }) => (
        <div data-testid="custom-fallback">Custom Error: {error.message}</div>
      );

      const TestWrapper = () => (
        <MigrationErrorBoundary fallback={CustomFallback}>
          <ErrorThrowingComponent />
        </MigrationErrorBoundary>
      );

      render(<TestWrapper />);

      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
      expect(screen.getByText('Custom Error: Test error')).toBeInTheDocument();
    });
  });

  describe('MigrationControlPanel', () => {
    it('should render when open', () => {
      render(
        <MigrationProvider>
          <TestControlPanelConsumer />
        </MigrationProvider>
      );
      
      const toggleButton = screen.getByTestId('toggle-panel');
      fireEvent.click(toggleButton);
      
      expect(screen.getByText('Migration Control')).toBeInTheDocument();
    });

    it('should not render when closed', () => {
      render(
        <MigrationProvider>
          <MigrationControlPanel isOpen={false} onClose={() => {}} />
        </MigrationProvider>
      );
      
      expect(screen.queryByText('Migration Control')).not.toBeInTheDocument();
    });

    it('should switch between tabs', () => {
      render(
        <MigrationProvider>
          <MigrationControlPanel isOpen={true} onClose={() => {}} showAdvanced={true} />
        </MigrationProvider>
      );
      
      // Should start with Overview tab
      expect(screen.getByText('Migration Progress')).toBeInTheDocument();
      
      // Switch to Features tab
      const featuresTab = screen.getByText('Features');
      fireEvent.click(featuresTab);
      
      expect(screen.getByText('Feature Status')).toBeInTheDocument();
      
      // Switch to Performance tab
      const performanceTab = screen.getByText('Performance');
      fireEvent.click(performanceTab);
      
      expect(screen.getByText('Performance Metrics')).toBeInTheDocument();
      
      // Switch to Settings tab
      const settingsTab = screen.getByText('Settings');
      fireEvent.click(settingsTab);
      
      expect(screen.getByText('Advanced Settings')).toBeInTheDocument();
    });

    it('should handle mode changes from control panel', async () => {
      render(
        <MigrationProvider>
          <MigrationControlPanel isOpen={true} onClose={() => {}} />
        </MigrationProvider>
      );
      
      const modernRadio = screen.getByDisplayValue('modern');
      fireEvent.click(modernRadio);
      
      await waitFor(() => {
        expect(modernRadio).toBeChecked();
      });
    });

    it('should close when close button is clicked', () => {
      const onClose = jest.fn();
      
      render(
        <MigrationProvider>
          <MigrationControlPanel isOpen={true} onClose={onClose} />
        </MigrationProvider>
      );
      
      const closeButton = screen.getByText('×');
      fireEvent.click(closeButton);
      
      expect(onClose).toHaveBeenCalled();
    });
  });

  describe('Migration Hooks', () => {
    it('should throw error when used outside provider', () => {
      // Suppress console.error for this test
      const originalError = console.error;
      console.error = jest.fn();
      
      expect(() => {
        render(<TestMigrationConsumer />);
      }).toThrow('useMigration must be used within MigrationProvider');
      
      console.error = originalError;
    });
  });
});
