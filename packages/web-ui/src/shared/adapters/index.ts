/**
 * Migration Adapters Index
 * 
 * Central export point for all migration adapter functionality.
 */

// Core migration adapter
export {
  MigrationProvider,
  useMigration,
  useMigrationMode,
  useMigrationPerformance,
  MigrationErrorBoundary,
  MigrationMode
} from './MigrationAdapter';

export type {
  MigrationConfig,
  MigrationState,
  MigrationStatus
} from './MigrationAdapter';

// Component adapter
export {
  ComponentAdapter,
  useComponentComparison,
  withMigrationAdapter,
  MigrationStatusIndicator
} from './ComponentAdapter';

export type {
  ComponentAdapterProps
} from './ComponentAdapter';

// Control panel
export {
  MigrationControlPanel,
  useMigrationControlPanel
} from './MigrationControlPanel';

export type {
  MigrationControlPanelProps
} from './MigrationControlPanel';

// Styles
import './migration.css';
