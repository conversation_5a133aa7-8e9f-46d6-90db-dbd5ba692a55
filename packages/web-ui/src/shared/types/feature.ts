/**
 * Shared Feature Types
 * 
 * Common types used across all features in the feature-slice architecture.
 */

// Base feature state interface
export interface FeatureState {
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

// Feature configuration interface
export interface FeatureConfig {
  enabled: boolean;
  settings: Record<string, any>;
  permissions: string[];
}

// Feature provider props
export interface FeatureProviderProps {
  children: React.ReactNode;
  config?: Partial<FeatureConfig>;
  onError?: (error: Error) => void;
}

// Feature hook return type
export interface FeatureHookReturn<T = any> {
  state: T & FeatureState;
  actions: Record<string, (...args: any[]) => any>;
  selectors: Record<string, (...args: any[]) => any>;
}

// Feature service interface
export interface FeatureService {
  initialize(): Promise<void>;
  cleanup(): Promise<void>;
  getState(): any;
  setState(state: any): void;
}

// Feature component props
export interface FeatureComponentProps {
  className?: string;
  style?: React.CSSProperties;
  testId?: string;
  onError?: (error: Error) => void;
}

// Feature event types
export type FeatureEvent = {
  type: string;
  payload?: any;
  timestamp: Date;
  source: string;
};

// Feature communication interface
export interface FeatureCommunication {
  subscribe(eventType: string, handler: (event: FeatureEvent) => void): () => void;
  publish(event: FeatureEvent): void;
  request<T>(target: string, action: string, payload?: any): Promise<T>;
}

// Feature isolation levels
export enum FeatureIsolationLevel {
  NONE = 'none',           // No isolation, full access to global state
  PARTIAL = 'partial',     // Limited access to specific global state slices
  FULL = 'full'           // Complete isolation, no global state access
}

// Feature dependency interface
export interface FeatureDependency {
  name: string;
  version: string;
  required: boolean;
  isolationLevel: FeatureIsolationLevel;
}

// Feature metadata
export interface FeatureMetadata {
  name: string;
  version: string;
  description: string;
  dependencies: FeatureDependency[];
  isolationLevel: FeatureIsolationLevel;
  permissions: string[];
  tags: string[];
}

// Feature registry entry
export interface FeatureRegistryEntry {
  metadata: FeatureMetadata;
  provider: React.ComponentType<FeatureProviderProps>;
  hooks: Record<string, () => FeatureHookReturn>;
  services: Record<string, FeatureService>;
  components: Record<string, React.ComponentType<any>>;
}

// Feature communication bus
export interface FeatureBus {
  register(feature: FeatureRegistryEntry): void;
  unregister(featureName: string): void;
  getFeature(name: string): FeatureRegistryEntry | null;
  listFeatures(): FeatureRegistryEntry[];
  communicate: FeatureCommunication;
}

// Feature slice state
export interface FeatureSliceState<T = any> {
  data: T;
  meta: {
    isLoading: boolean;
    error: string | null;
    lastFetch: Date | null;
    version: number;
  };
}

// Feature action
export interface FeatureAction<T = any> {
  type: string;
  payload?: T;
  meta?: {
    timestamp: Date;
    source: string;
    correlationId?: string;
  };
}

// Feature reducer
export type FeatureReducer<T> = (
  state: FeatureSliceState<T>,
  action: FeatureAction
) => FeatureSliceState<T>;

// Feature selector
export type FeatureSelector<T, R = any> = (state: FeatureSliceState<T>) => R;

// Feature middleware
export type FeatureMiddleware = (
  action: FeatureAction,
  state: any,
  next: (action: FeatureAction) => any
) => any;

// Feature store interface
export interface FeatureStore<T = any> {
  getState(): FeatureSliceState<T>;
  dispatch(action: FeatureAction): void;
  subscribe(listener: (state: FeatureSliceState<T>) => void): () => void;
  select<R>(selector: FeatureSelector<T, R>): R;
}

// Feature isolation context
export interface FeatureIsolationContext {
  featureName: string;
  isolationLevel: FeatureIsolationLevel;
  allowedGlobalAccess: string[];
  communicationBus: FeatureCommunication;
}

// Feature performance metrics
export interface FeaturePerformanceMetrics {
  renderTime: number;
  stateUpdateTime: number;
  apiCallTime: number;
  memoryUsage: number;
  errorCount: number;
}

// Feature analytics
export interface FeatureAnalytics {
  usage: {
    componentRenders: number;
    hookCalls: number;
    serviceInvocations: number;
  };
  performance: FeaturePerformanceMetrics;
  errors: Array<{
    error: Error;
    timestamp: Date;
    context: string;
  }>;
}

// Feature health status
export enum FeatureHealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  UNKNOWN = 'unknown'
}

// Feature health check
export interface FeatureHealthCheck {
  status: FeatureHealthStatus;
  lastCheck: Date;
  checks: Array<{
    name: string;
    status: FeatureHealthStatus;
    message?: string;
    duration: number;
  }>;
  analytics: FeatureAnalytics;
}
