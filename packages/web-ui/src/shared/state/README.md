# Modern State Management System

A comprehensive, modern state management system for React applications with advanced features like optimistic updates, state synchronization, persistence, and performance monitoring.

## Features

- **Centralized App State**: Global application state with TypeScript safety
- **Optimistic Updates**: Immediate UI updates with automatic rollback on failure
- **State Synchronization**: Real-time sync between local and remote state
- **Persistence**: Multiple storage backends with compression and encryption
- **Performance Monitoring**: Built-in performance tracking and debugging
- **DevTools Integration**: Redux DevTools support for debugging
- **Middleware System**: Extensible middleware for logging, validation, and side effects
- **Offline Support**: Automatic conflict resolution and offline queue management

## Core Components

### AppStateProvider

The main state provider that manages global application state.

```tsx
import { AppStateProvider } from '@/shared/state';

function App() {
  return (
    <AppStateProvider
      enablePersistence={true}
      enableDevtools={process.env.NODE_ENV === 'development'}
    >
      <YourApp />
    </AppStateProvider>
  );
}
```

### State Hooks

```tsx
import { useAppState, useAppActions, useAppSelectors } from '@/shared/state';

function MyComponent() {
  const { state } = useAppState();
  const actions = useAppActions();
  const selectors = useAppSelectors();

  // Access state
  const theme = state.user.preferences.theme;
  const isAuthenticated = selectors.isAuthenticated();

  // Update state
  const handleThemeChange = () => {
    actions.setUserPreferences({ theme: 'dark' });
  };

  // Add notification
  const showNotification = () => {
    actions.addNotification({
      type: 'success',
      title: 'Success',
      message: 'Operation completed',
      persistent: false
    });
  };

  return (
    <div>
      <p>Current theme: {theme}</p>
      <p>Authenticated: {isAuthenticated.toString()}</p>
      <button onClick={handleThemeChange}>Toggle Theme</button>
      <button onClick={showNotification}>Show Notification</button>
    </div>
  );
}
```

## Advanced Hooks

### Optimistic Updates

```tsx
import { useOptimisticState } from '@/shared/state';

function TodoList() {
  const { state, optimisticUpdate, isPending } = useOptimisticState({
    initialState: [],
    applyOptimisticUpdate: (todos, newTodo) => [...todos, newTodo],
    performUpdate: async (newTodo) => {
      const response = await fetch('/api/todos', {
        method: 'POST',
        body: JSON.stringify(newTodo)
      });
      return response.json();
    }
  });

  const addTodo = async (text: string) => {
    try {
      await optimisticUpdate({
        id: Date.now(),
        text,
        completed: false
      });
    } catch (error) {
      console.error('Failed to add todo:', error);
    }
  };

  return (
    <div>
      {state.map(todo => (
        <div key={todo.id}>{todo.text}</div>
      ))}
      {isPending && <div>Adding todo...</div>}
    </div>
  );
}
```

### State Synchronization

```tsx
import { useStateSync } from '@/shared/state';

function UserProfile() {
  const { state, setState, syncStatus } = useStateSync({
    key: 'user-profile',
    initialState: { name: '', email: '' },
    fetchRemoteState: () => fetch('/api/profile').then(r => r.json()),
    pushLocalState: (profile) => fetch('/api/profile', {
      method: 'PUT',
      body: JSON.stringify(profile)
    }),
    resolveConflict: (local, remote) => ({
      ...remote,
      ...local,
      lastModified: new Date().toISOString()
    })
  });

  return (
    <div>
      <input
        value={state.name}
        onChange={(e) => setState({ ...state, name: e.target.value })}
      />
      <div>
        Status: {syncStatus.isSyncing ? 'Syncing...' : 'Synced'}
        {syncStatus.hasConflicts && <span> (Conflicts detected)</span>}
      </div>
    </div>
  );
}
```

### Persisted State

```tsx
import { usePersistedState } from '@/shared/state';

function Settings() {
  const { state, setState, status } = usePersistedState({
    key: 'app-settings',
    initialState: { theme: 'light', language: 'en' },
    storage: 'localStorage',
    compress: true,
    syncAcrossTabs: true
  });

  return (
    <div>
      <select
        value={state.theme}
        onChange={(e) => setState({ ...state, theme: e.target.value })}
      >
        <option value="light">Light</option>
        <option value="dark">Dark</option>
      </select>
      {status.isSaving && <span>Saving...</span>}
    </div>
  );
}
```

## State Structure

The global state is organized into logical sections:

```typescript
interface AppState {
  user: {
    preferences: UserPreferences;
    session: UserSession;
  };
  ui: {
    layout: LayoutState;
    notifications: Notification[];
    modals: ModalState;
    loading: LoadingState;
  };
  features: {
    enabled: Record<string, boolean>;
    config: Record<string, any>;
  };
  performance: {
    metrics: PerformanceMetrics;
    monitoring: MonitoringConfig;
  };
  sync: {
    isOnline: boolean;
    pendingChanges: PendingChange[];
    conflicts: SyncConflict[];
  };
}
```

## Middleware System

Create custom middleware for cross-cutting concerns:

```tsx
import { createLoggingMiddleware, createValidationMiddleware } from '@/shared/state';

// Logging middleware
const loggingMiddleware = createLoggingMiddleware({
  enabled: process.env.NODE_ENV === 'development',
  filter: (action) => !action.type.startsWith('@@')
});

// Validation middleware
const validationMiddleware = createValidationMiddleware((state) => {
  const errors = [];
  
  if (!state.user.preferences.theme) {
    errors.push('Theme is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
});

// Use with state provider
const middlewareManager = new StateMiddlewareManager();
middlewareManager.add(loggingMiddleware);
middlewareManager.add(validationMiddleware);
```

## Performance Monitoring

Built-in performance monitoring and debugging:

```tsx
import { useStatePerformanceMonitor, statePerformanceMonitor } from '@/shared/state';

function MyComponent() {
  const { measure, getMetrics } = useStatePerformanceMonitor('MyComponent');

  const expensiveOperation = () => {
    return measure(() => {
      // Expensive computation
      return heavyCalculation();
    });
  };

  // View performance metrics
  console.log(statePerformanceMonitor.generateReport());
}
```

## DevTools Integration

Automatic integration with Redux DevTools:

```tsx
// Development tools are automatically available
// Access via browser extension or window.__STATE_DEV_TOOLS__

// Manual DevTools usage
import { useDevTools } from '@/shared/state';

function MyComponent() {
  const [state, setState] = useState(initialState);
  const { send } = useDevTools(state, 'MyComponent');

  const updateState = (newState) => {
    setState(newState);
    send({ type: 'UPDATE_STATE', payload: newState });
  };
}
```

## Best Practices

### 1. State Organization
- Keep state flat and normalized
- Use selectors for computed values
- Separate UI state from business logic

### 2. Performance
- Use selectors to prevent unnecessary re-renders
- Implement proper memoization
- Monitor state update performance

### 3. Error Handling
- Always handle optimistic update failures
- Implement proper conflict resolution
- Use error boundaries for state errors

### 4. Testing
- Test state logic separately from components
- Mock external dependencies
- Test error scenarios and edge cases

### 5. TypeScript
- Define strict types for all state
- Use discriminated unions for actions
- Leverage type inference where possible

## Migration Guide

### From useState to Global State

```tsx
// Before
function MyComponent() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);
}

// After
function MyComponent() {
  const { state, actions } = useAppState();
  const user = state.user.session;
  const loading = state.ui.loading.global;
}
```

### From Context to AppState

```tsx
// Before
const UserContext = createContext();

// After
// Use AppStateProvider with user state section
```

### From Redux to AppState

```tsx
// Before
const store = createStore(reducer);

// After
<AppStateProvider>
  <App />
</AppStateProvider>
```

## API Reference

### Hooks
- `useAppState()` - Access global state and actions
- `useAppActions()` - Access state actions only
- `useAppSelectors()` - Access state selectors only
- `useOptimisticState()` - Optimistic updates
- `useStateSync()` - State synchronization
- `usePersistedState()` - State persistence

### Utilities
- `StateMiddlewareManager` - Middleware management
- `StateLogger` - State change logging
- `StatePerformanceMonitor` - Performance tracking
- `StateSnapshot` - State snapshots for debugging

### Middleware
- `createLoggingMiddleware()` - Action logging
- `createPerformanceMiddleware()` - Performance monitoring
- `createValidationMiddleware()` - State validation
- `createPersistenceMiddleware()` - Auto-persistence
- `createUndoRedoMiddleware()` - Undo/redo functionality
