/**
 * App State Provider Tests
 * 
 * Tests for the centralized application state management.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AppStateProvider, useAppState, useAppActions, useAppSelectors } from '../AppStateProvider';

// Mock feature bus
jest.mock('../../infrastructure/FeatureBus', () => ({
  featureBus: {
    communicate: {
      subscribe: jest.fn(() => jest.fn()),
      publish: jest.fn()
    }
  }
}));

// Test component that uses app state
const TestComponent: React.FC = () => {
  const { state } = useAppState();
  const actions = useAppActions();
  const selectors = useAppSelectors();

  return (
    <div>
      <div data-testid="theme">{state.user.preferences.theme}</div>
      <div data-testid="authenticated">{selectors.isAuthenticated().toString()}</div>
      <div data-testid="notifications-count">{state.ui.notifications.length}</div>
      <div data-testid="global-loading">{selectors.isGlobalLoading().toString()}</div>
      
      <button 
        data-testid="set-theme-dark" 
        onClick={() => actions.setUserPreferences({ theme: 'dark' })}
      >
        Set Dark Theme
      </button>
      
      <button 
        data-testid="add-notification" 
        onClick={() => actions.addNotification({
          type: 'info',
          title: 'Test',
          message: 'Test notification',
          persistent: false
        })}
      >
        Add Notification
      </button>
      
      <button 
        data-testid="set-loading" 
        onClick={() => actions.setGlobalLoading(true)}
      >
        Set Loading
      </button>
      
      <button 
        data-testid="authenticate" 
        onClick={() => actions.setUserSession({ isAuthenticated: true, userId: 'test-user' })}
      >
        Authenticate
      </button>
    </div>
  );
};

describe('AppStateProvider', () => {
  const renderWithProvider = (props = {}) => {
    return render(
      <AppStateProvider enablePersistence={false} enableDevtools={false} {...props}>
        <TestComponent />
      </AppStateProvider>
    );
  };

  beforeEach(() => {
    // Clear localStorage
    localStorage.clear();
    jest.clearAllMocks();
  });

  it('should provide initial state', () => {
    renderWithProvider();
    
    expect(screen.getByTestId('theme')).toHaveTextContent('auto');
    expect(screen.getByTestId('authenticated')).toHaveTextContent('false');
    expect(screen.getByTestId('notifications-count')).toHaveTextContent('0');
    expect(screen.getByTestId('global-loading')).toHaveTextContent('false');
  });

  it('should update user preferences', () => {
    renderWithProvider();
    
    fireEvent.click(screen.getByTestId('set-theme-dark'));
    
    expect(screen.getByTestId('theme')).toHaveTextContent('dark');
  });

  it('should handle authentication', () => {
    renderWithProvider();
    
    fireEvent.click(screen.getByTestId('authenticate'));
    
    expect(screen.getByTestId('authenticated')).toHaveTextContent('true');
  });

  it('should manage notifications', () => {
    renderWithProvider();
    
    fireEvent.click(screen.getByTestId('add-notification'));
    
    expect(screen.getByTestId('notifications-count')).toHaveTextContent('1');
  });

  it('should handle loading states', () => {
    renderWithProvider();
    
    fireEvent.click(screen.getByTestId('set-loading'));
    
    expect(screen.getByTestId('global-loading')).toHaveTextContent('true');
  });

  it('should accept initial state', () => {
    const initialState = {
      user: {
        preferences: {
          theme: 'dark' as const,
          language: 'en',
          timezone: 'UTC',
          accessibility: {
            reducedMotion: false,
            highContrast: false,
            fontSize: 'medium' as const
          }
        }
      }
    };

    render(
      <AppStateProvider 
        initialState={initialState}
        enablePersistence={false} 
        enableDevtools={false}
      >
        <TestComponent />
      </AppStateProvider>
    );
    
    expect(screen.getByTestId('theme')).toHaveTextContent('dark');
  });

  it('should handle multiple notifications', () => {
    renderWithProvider();
    
    // Add multiple notifications
    fireEvent.click(screen.getByTestId('add-notification'));
    fireEvent.click(screen.getByTestId('add-notification'));
    fireEvent.click(screen.getByTestId('add-notification'));
    
    expect(screen.getByTestId('notifications-count')).toHaveTextContent('3');
  });

  it('should provide selectors', () => {
    const SelectorTestComponent: React.FC = () => {
      const selectors = useAppSelectors();
      
      return (
        <div>
          <div data-testid="enabled-features">
            {selectors.getEnabledFeatures().join(',')}
          </div>
          <div data-testid="is-feature-enabled">
            {selectors.isFeatureEnabled('graph-visualization').toString()}
          </div>
          <div data-testid="is-online">
            {selectors.isOnline().toString()}
          </div>
        </div>
      );
    };

    render(
      <AppStateProvider enablePersistence={false} enableDevtools={false}>
        <SelectorTestComponent />
      </AppStateProvider>
    );
    
    expect(screen.getByTestId('enabled-features')).toHaveTextContent(
      'graph-visualization,chat-interface,analysis-tools,search-filter'
    );
    expect(screen.getByTestId('is-feature-enabled')).toHaveTextContent('true');
    expect(screen.getByTestId('is-online')).toHaveTextContent('true');
  });

  it('should handle feature management', () => {
    const FeatureTestComponent: React.FC = () => {
      const actions = useAppActions();
      const selectors = useAppSelectors();
      
      return (
        <div>
          <div data-testid="feature-enabled">
            {selectors.isFeatureEnabled('test-feature').toString()}
          </div>
          <button 
            data-testid="enable-feature"
            onClick={() => actions.enableFeature('test-feature', true)}
          >
            Enable Feature
          </button>
          <button 
            data-testid="disable-feature"
            onClick={() => actions.enableFeature('test-feature', false)}
          >
            Disable Feature
          </button>
        </div>
      );
    };

    render(
      <AppStateProvider enablePersistence={false} enableDevtools={false}>
        <FeatureTestComponent />
      </AppStateProvider>
    );
    
    // Initially disabled
    expect(screen.getByTestId('feature-enabled')).toHaveTextContent('false');
    
    // Enable feature
    fireEvent.click(screen.getByTestId('enable-feature'));
    expect(screen.getByTestId('feature-enabled')).toHaveTextContent('true');
    
    // Disable feature
    fireEvent.click(screen.getByTestId('disable-feature'));
    expect(screen.getByTestId('feature-enabled')).toHaveTextContent('false');
  });

  it('should handle modal management', () => {
    const ModalTestComponent: React.FC = () => {
      const actions = useAppActions();
      const selectors = useAppSelectors();
      
      return (
        <div>
          <div data-testid="active-modal">
            {selectors.getActiveModal() || 'none'}
          </div>
          <button 
            data-testid="push-modal"
            onClick={() => actions.pushModal('test-modal', { data: 'test' })}
          >
            Push Modal
          </button>
          <button 
            data-testid="pop-modal"
            onClick={() => actions.popModal()}
          >
            Pop Modal
          </button>
        </div>
      );
    };

    render(
      <AppStateProvider enablePersistence={false} enableDevtools={false}>
        <ModalTestComponent />
      </AppStateProvider>
    );
    
    // Initially no modal
    expect(screen.getByTestId('active-modal')).toHaveTextContent('none');
    
    // Push modal
    fireEvent.click(screen.getByTestId('push-modal'));
    expect(screen.getByTestId('active-modal')).toHaveTextContent('test-modal');
    
    // Pop modal
    fireEvent.click(screen.getByTestId('pop-modal'));
    expect(screen.getByTestId('active-modal')).toHaveTextContent('none');
  });

  it('should handle operation loading states', () => {
    const LoadingTestComponent: React.FC = () => {
      const actions = useAppActions();
      const selectors = useAppSelectors();
      
      return (
        <div>
          <div data-testid="operation-loading">
            {selectors.isOperationLoading('test-operation').toString()}
          </div>
          <button 
            data-testid="start-operation"
            onClick={() => actions.setOperationLoading('test-operation', true)}
          >
            Start Operation
          </button>
          <button 
            data-testid="stop-operation"
            onClick={() => actions.setOperationLoading('test-operation', false)}
          >
            Stop Operation
          </button>
        </div>
      );
    };

    render(
      <AppStateProvider enablePersistence={false} enableDevtools={false}>
        <LoadingTestComponent />
      </AppStateProvider>
    );
    
    // Initially not loading
    expect(screen.getByTestId('operation-loading')).toHaveTextContent('false');
    
    // Start operation
    fireEvent.click(screen.getByTestId('start-operation'));
    expect(screen.getByTestId('operation-loading')).toHaveTextContent('true');
    
    // Stop operation
    fireEvent.click(screen.getByTestId('stop-operation'));
    expect(screen.getByTestId('operation-loading')).toHaveTextContent('false');
  });

  it('should throw error when used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useAppState must be used within an AppStateProvider');
    
    consoleSpy.mockRestore();
  });
});
