/**
 * State Management Index
 * 
 * Central export point for all state management utilities and providers.
 */

// Core state provider
export {
  AppStateProvider,
  useAppState,
  useAppActions,
  useAppSelectors
} from './AppStateProvider';

export type {
  AppState,
  AppAction,
  AppStateActions,
  AppStateSelectors,
  AppStateProviderProps
} from './AppStateProvider';

// State management hooks
export {
  useOptimisticState,
  useSimpleOptimisticState,
  useOptimisticArray
} from './hooks/useOptimisticState';

export type {
  OptimisticStateOptions,
  OptimisticStateReturn
} from './hooks/useOptimisticState';

export {
  useStateSync,
  useObjectStateSync
} from './hooks/useStateSync';

export type {
  StateSyncOptions,
  StateSyncReturn
} from './hooks/useStateSync';

export {
  usePersistedState,
  useLocalStorageState,
  useSessionStorageState,
  useIndexedDBState
} from './hooks/usePersistedState';

export type {
  PersistedStateOptions,
  PersistedStateReturn,
  StorageBackend
} from './hooks/usePersistedState';

// State utilities
export * from './utils/stateUtils';
export * from './utils/stateDevtools';
export * from './utils/stateMiddleware';
