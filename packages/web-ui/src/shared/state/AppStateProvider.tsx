/**
 * App State Provider
 * 
 * Centralized application state management with modern React patterns.
 * Provides global state coordination, persistence, and synchronization.
 */

import React, { createContext, useContext, useReducer, useEffect, useMemo } from 'react';
import { featureBus } from '../infrastructure/FeatureBus';

// App-level state interface
export interface AppState {
  // User preferences and settings
  user: {
    preferences: {
      theme: 'light' | 'dark' | 'auto';
      language: string;
      timezone: string;
      accessibility: {
        reducedMotion: boolean;
        highContrast: boolean;
        fontSize: 'small' | 'medium' | 'large';
      };
    };
    session: {
      isAuthenticated: boolean;
      userId?: string;
      permissions: string[];
      lastActivity: Date | null;
    };
  };

  // Application-wide UI state
  ui: {
    layout: {
      sidebarCollapsed: boolean;
      panelSizes: Record<string, number>;
      activePanel: string | null;
    };
    notifications: Array<{
      id: string;
      type: 'info' | 'success' | 'warning' | 'error';
      title: string;
      message: string;
      timestamp: Date;
      persistent: boolean;
      actions?: Array<{
        label: string;
        action: () => void;
      }>;
    }>;
    modals: {
      stack: string[];
      data: Record<string, any>;
    };
    loading: {
      global: boolean;
      operations: Record<string, boolean>;
    };
  };

  // Feature coordination state
  features: {
    enabled: Record<string, boolean>;
    config: Record<string, any>;
    communication: {
      subscriptions: Record<string, string[]>;
      pendingRequests: Record<string, any>;
    };
  };

  // Performance and monitoring
  performance: {
    metrics: {
      renderTime: number;
      memoryUsage: number;
      errorCount: number;
      lastUpdate: Date;
    };
    monitoring: {
      enabled: boolean;
      level: 'basic' | 'detailed' | 'debug';
    };
  };

  // Offline and sync state
  sync: {
    isOnline: boolean;
    lastSync: Date | null;
    pendingChanges: Array<{
      id: string;
      type: string;
      data: any;
      timestamp: Date;
    }>;
    conflicts: Array<{
      id: string;
      local: any;
      remote: any;
      timestamp: Date;
    }>;
  };
}

// Action types
export type AppAction =
  | { type: 'SET_USER_PREFERENCES'; payload: Partial<AppState['user']['preferences']> }
  | { type: 'SET_USER_SESSION'; payload: Partial<AppState['user']['session']> }
  | { type: 'UPDATE_LAYOUT'; payload: Partial<AppState['ui']['layout']> }
  | { type: 'ADD_NOTIFICATION'; payload: Omit<AppState['ui']['notifications'][0], 'id' | 'timestamp'> }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'CLEAR_NOTIFICATIONS' }
  | { type: 'PUSH_MODAL'; payload: { id: string; data?: any } }
  | { type: 'POP_MODAL' }
  | { type: 'CLEAR_MODALS' }
  | { type: 'SET_GLOBAL_LOADING'; payload: boolean }
  | { type: 'SET_OPERATION_LOADING'; payload: { operation: string; loading: boolean } }
  | { type: 'ENABLE_FEATURE'; payload: { feature: string; enabled: boolean } }
  | { type: 'UPDATE_FEATURE_CONFIG'; payload: { feature: string; config: any } }
  | { type: 'UPDATE_PERFORMANCE_METRICS'; payload: Partial<AppState['performance']['metrics']> }
  | { type: 'SET_ONLINE_STATUS'; payload: boolean }
  | { type: 'ADD_PENDING_CHANGE'; payload: Omit<AppState['sync']['pendingChanges'][0], 'id' | 'timestamp'> }
  | { type: 'REMOVE_PENDING_CHANGE'; payload: string }
  | { type: 'ADD_SYNC_CONFLICT'; payload: Omit<AppState['sync']['conflicts'][0], 'id' | 'timestamp'> }
  | { type: 'RESOLVE_SYNC_CONFLICT'; payload: string }
  | { type: 'SET_LAST_SYNC'; payload: Date }
  | { type: 'HYDRATE_STATE'; payload: Partial<AppState> };

// Initial state
const initialState: AppState = {
  user: {
    preferences: {
      theme: 'auto',
      language: 'en',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      accessibility: {
        reducedMotion: false,
        highContrast: false,
        fontSize: 'medium'
      }
    },
    session: {
      isAuthenticated: false,
      permissions: [],
      lastActivity: null
    }
  },
  ui: {
    layout: {
      sidebarCollapsed: false,
      panelSizes: {},
      activePanel: null
    },
    notifications: [],
    modals: {
      stack: [],
      data: {}
    },
    loading: {
      global: false,
      operations: {}
    }
  },
  features: {
    enabled: {
      'graph-visualization': true,
      'chat-interface': true,
      'analysis-tools': true,
      'search-filter': true
    },
    config: {},
    communication: {
      subscriptions: {},
      pendingRequests: {}
    }
  },
  performance: {
    metrics: {
      renderTime: 0,
      memoryUsage: 0,
      errorCount: 0,
      lastUpdate: new Date()
    },
    monitoring: {
      enabled: process.env.NODE_ENV === 'development',
      level: 'basic'
    }
  },
  sync: {
    isOnline: navigator.onLine,
    lastSync: null,
    pendingChanges: [],
    conflicts: []
  }
};

// Reducer
const appStateReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_USER_PREFERENCES':
      return {
        ...state,
        user: {
          ...state.user,
          preferences: { ...state.user.preferences, ...action.payload }
        }
      };

    case 'SET_USER_SESSION':
      return {
        ...state,
        user: {
          ...state.user,
          session: { ...state.user.session, ...action.payload }
        }
      };

    case 'UPDATE_LAYOUT':
      return {
        ...state,
        ui: {
          ...state.ui,
          layout: { ...state.ui.layout, ...action.payload }
        }
      };

    case 'ADD_NOTIFICATION':
      return {
        ...state,
        ui: {
          ...state.ui,
          notifications: [
            ...state.ui.notifications,
            {
              ...action.payload,
              id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              timestamp: new Date()
            }
          ]
        }
      };

    case 'REMOVE_NOTIFICATION':
      return {
        ...state,
        ui: {
          ...state.ui,
          notifications: state.ui.notifications.filter(n => n.id !== action.payload)
        }
      };

    case 'CLEAR_NOTIFICATIONS':
      return {
        ...state,
        ui: {
          ...state.ui,
          notifications: []
        }
      };

    case 'PUSH_MODAL':
      return {
        ...state,
        ui: {
          ...state.ui,
          modals: {
            stack: [...state.ui.modals.stack, action.payload.id],
            data: {
              ...state.ui.modals.data,
              [action.payload.id]: action.payload.data || {}
            }
          }
        }
      };

    case 'POP_MODAL':
      const newStack = [...state.ui.modals.stack];
      const removedModal = newStack.pop();
      const newData = { ...state.ui.modals.data };
      if (removedModal) {
        delete newData[removedModal];
      }
      return {
        ...state,
        ui: {
          ...state.ui,
          modals: {
            stack: newStack,
            data: newData
          }
        }
      };

    case 'CLEAR_MODALS':
      return {
        ...state,
        ui: {
          ...state.ui,
          modals: {
            stack: [],
            data: {}
          }
        }
      };

    case 'SET_GLOBAL_LOADING':
      return {
        ...state,
        ui: {
          ...state.ui,
          loading: {
            ...state.ui.loading,
            global: action.payload
          }
        }
      };

    case 'SET_OPERATION_LOADING':
      return {
        ...state,
        ui: {
          ...state.ui,
          loading: {
            ...state.ui.loading,
            operations: {
              ...state.ui.loading.operations,
              [action.payload.operation]: action.payload.loading
            }
          }
        }
      };

    case 'ENABLE_FEATURE':
      return {
        ...state,
        features: {
          ...state.features,
          enabled: {
            ...state.features.enabled,
            [action.payload.feature]: action.payload.enabled
          }
        }
      };

    case 'UPDATE_FEATURE_CONFIG':
      return {
        ...state,
        features: {
          ...state.features,
          config: {
            ...state.features.config,
            [action.payload.feature]: action.payload.config
          }
        }
      };

    case 'UPDATE_PERFORMANCE_METRICS':
      return {
        ...state,
        performance: {
          ...state.performance,
          metrics: {
            ...state.performance.metrics,
            ...action.payload,
            lastUpdate: new Date()
          }
        }
      };

    case 'SET_ONLINE_STATUS':
      return {
        ...state,
        sync: {
          ...state.sync,
          isOnline: action.payload
        }
      };

    case 'ADD_PENDING_CHANGE':
      return {
        ...state,
        sync: {
          ...state.sync,
          pendingChanges: [
            ...state.sync.pendingChanges,
            {
              ...action.payload,
              id: `change-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              timestamp: new Date()
            }
          ]
        }
      };

    case 'REMOVE_PENDING_CHANGE':
      return {
        ...state,
        sync: {
          ...state.sync,
          pendingChanges: state.sync.pendingChanges.filter(c => c.id !== action.payload)
        }
      };

    case 'ADD_SYNC_CONFLICT':
      return {
        ...state,
        sync: {
          ...state.sync,
          conflicts: [
            ...state.sync.conflicts,
            {
              ...action.payload,
              id: `conflict-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              timestamp: new Date()
            }
          ]
        }
      };

    case 'RESOLVE_SYNC_CONFLICT':
      return {
        ...state,
        sync: {
          ...state.sync,
          conflicts: state.sync.conflicts.filter(c => c.id !== action.payload)
        }
      };

    case 'SET_LAST_SYNC':
      return {
        ...state,
        sync: {
          ...state.sync,
          lastSync: action.payload
        }
      };

    case 'HYDRATE_STATE':
      return {
        ...state,
        ...action.payload
      };

    default:
      return state;
  }
};

// Context
interface AppStateContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  actions: AppStateActions;
  selectors: AppStateSelectors;
}

const AppStateContext = createContext<AppStateContextType | null>(null);

// Actions interface
export interface AppStateActions {
  // User actions
  setUserPreferences: (preferences: Partial<AppState['user']['preferences']>) => void;
  setUserSession: (session: Partial<AppState['user']['session']>) => void;
  
  // UI actions
  updateLayout: (layout: Partial<AppState['ui']['layout']>) => void;
  addNotification: (notification: Omit<AppState['ui']['notifications'][0], 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  pushModal: (id: string, data?: any) => void;
  popModal: () => void;
  clearModals: () => void;
  setGlobalLoading: (loading: boolean) => void;
  setOperationLoading: (operation: string, loading: boolean) => void;
  
  // Feature actions
  enableFeature: (feature: string, enabled: boolean) => void;
  updateFeatureConfig: (feature: string, config: any) => void;
  
  // Performance actions
  updatePerformanceMetrics: (metrics: Partial<AppState['performance']['metrics']>) => void;
  
  // Sync actions
  setOnlineStatus: (online: boolean) => void;
  addPendingChange: (change: Omit<AppState['sync']['pendingChanges'][0], 'id' | 'timestamp'>) => void;
  removePendingChange: (id: string) => void;
  addSyncConflict: (conflict: Omit<AppState['sync']['conflicts'][0], 'id' | 'timestamp'>) => void;
  resolveSyncConflict: (id: string) => void;
  setLastSync: (date: Date) => void;
  
  // State management actions
  hydrateState: (state: Partial<AppState>) => void;
  persistState: () => Promise<void>;
  resetState: () => void;
}

// Selectors interface
export interface AppStateSelectors {
  // User selectors
  getUserPreferences: () => AppState['user']['preferences'];
  getUserSession: () => AppState['user']['session'];
  isAuthenticated: () => boolean;
  
  // UI selectors
  getLayout: () => AppState['ui']['layout'];
  getNotifications: () => AppState['ui']['notifications'];
  getActiveModal: () => string | null;
  getModalData: (id: string) => any;
  isGlobalLoading: () => boolean;
  isOperationLoading: (operation: string) => boolean;
  
  // Feature selectors
  isFeatureEnabled: (feature: string) => boolean;
  getFeatureConfig: (feature: string) => any;
  getEnabledFeatures: () => string[];
  
  // Performance selectors
  getPerformanceMetrics: () => AppState['performance']['metrics'];
  
  // Sync selectors
  isOnline: () => boolean;
  getPendingChanges: () => AppState['sync']['pendingChanges'];
  getSyncConflicts: () => AppState['sync']['conflicts'];
  getLastSync: () => Date | null;
  hasPendingChanges: () => boolean;
  hasSyncConflicts: () => boolean;
}

// Provider component
export interface AppStateProviderProps {
  children: React.ReactNode;
  initialState?: Partial<AppState>;
  enablePersistence?: boolean;
  enableDevtools?: boolean;
}

export const AppStateProvider: React.FC<AppStateProviderProps> = ({
  children,
  initialState: providedInitialState,
  enablePersistence = true,
  enableDevtools = process.env.NODE_ENV === 'development'
}) => {
  // Deep merge initial state with provided initial state
  const mergedInitialState = useMemo(() => {
    if (!providedInitialState) return initialState;

    return {
      ...initialState,
      user: {
        ...initialState.user,
        ...providedInitialState.user,
        preferences: {
          ...initialState.user.preferences,
          ...providedInitialState.user?.preferences
        },
        session: {
          ...initialState.user.session,
          ...providedInitialState.user?.session
        }
      },
      ui: {
        ...initialState.ui,
        ...providedInitialState.ui,
        layout: {
          ...initialState.ui.layout,
          ...providedInitialState.ui?.layout
        },
        notifications: providedInitialState.ui?.notifications || initialState.ui.notifications,
        modals: {
          ...initialState.ui.modals,
          ...providedInitialState.ui?.modals
        },
        loading: {
          ...initialState.ui.loading,
          ...providedInitialState.ui?.loading
        }
      },
      features: {
        ...initialState.features,
        ...providedInitialState.features,
        enabled: {
          ...initialState.features.enabled,
          ...providedInitialState.features?.enabled
        },
        config: {
          ...initialState.features.config,
          ...providedInitialState.features?.config
        },
        communication: {
          ...initialState.features.communication,
          ...providedInitialState.features?.communication
        }
      },
      performance: {
        ...initialState.performance,
        ...providedInitialState.performance,
        metrics: {
          ...initialState.performance.metrics,
          ...providedInitialState.performance?.metrics
        },
        monitoring: {
          ...initialState.performance.monitoring,
          ...providedInitialState.performance?.monitoring
        }
      },
      sync: {
        ...initialState.sync,
        ...providedInitialState.sync,
        pendingChanges: providedInitialState.sync?.pendingChanges || initialState.sync.pendingChanges,
        conflicts: providedInitialState.sync?.conflicts || initialState.sync.conflicts
      }
    };
  }, [providedInitialState]);

  const [state, dispatch] = useReducer(appStateReducer, mergedInitialState);

  // Actions implementation
  const actions = useMemo<AppStateActions>(() => ({
    setUserPreferences: (preferences) => 
      dispatch({ type: 'SET_USER_PREFERENCES', payload: preferences }),
    
    setUserSession: (session) => 
      dispatch({ type: 'SET_USER_SESSION', payload: session }),
    
    updateLayout: (layout) => 
      dispatch({ type: 'UPDATE_LAYOUT', payload: layout }),
    
    addNotification: (notification) => 
      dispatch({ type: 'ADD_NOTIFICATION', payload: notification }),
    
    removeNotification: (id) => 
      dispatch({ type: 'REMOVE_NOTIFICATION', payload: id }),
    
    clearNotifications: () => 
      dispatch({ type: 'CLEAR_NOTIFICATIONS' }),
    
    pushModal: (id, data) => 
      dispatch({ type: 'PUSH_MODAL', payload: { id, data } }),
    
    popModal: () => 
      dispatch({ type: 'POP_MODAL' }),
    
    clearModals: () => 
      dispatch({ type: 'CLEAR_MODALS' }),
    
    setGlobalLoading: (loading) => 
      dispatch({ type: 'SET_GLOBAL_LOADING', payload: loading }),
    
    setOperationLoading: (operation, loading) => 
      dispatch({ type: 'SET_OPERATION_LOADING', payload: { operation, loading } }),
    
    enableFeature: (feature, enabled) => 
      dispatch({ type: 'ENABLE_FEATURE', payload: { feature, enabled } }),
    
    updateFeatureConfig: (feature, config) => 
      dispatch({ type: 'UPDATE_FEATURE_CONFIG', payload: { feature, config } }),
    
    updatePerformanceMetrics: (metrics) => 
      dispatch({ type: 'UPDATE_PERFORMANCE_METRICS', payload: metrics }),
    
    setOnlineStatus: (online) => 
      dispatch({ type: 'SET_ONLINE_STATUS', payload: online }),
    
    addPendingChange: (change) => 
      dispatch({ type: 'ADD_PENDING_CHANGE', payload: change }),
    
    removePendingChange: (id) => 
      dispatch({ type: 'REMOVE_PENDING_CHANGE', payload: id }),
    
    addSyncConflict: (conflict) => 
      dispatch({ type: 'ADD_SYNC_CONFLICT', payload: conflict }),
    
    resolveSyncConflict: (id) => 
      dispatch({ type: 'RESOLVE_SYNC_CONFLICT', payload: id }),
    
    setLastSync: (date) => 
      dispatch({ type: 'SET_LAST_SYNC', payload: date }),
    
    hydrateState: (newState) => 
      dispatch({ type: 'HYDRATE_STATE', payload: newState }),
    
    persistState: async () => {
      if (enablePersistence) {
        try {
          const persistableState = {
            user: state.user,
            ui: {
              layout: state.ui.layout
            },
            features: state.features
          };
          localStorage.setItem('app-state', JSON.stringify(persistableState));
        } catch (error) {
          console.error('Failed to persist state:', error);
        }
      }
    },
    
    resetState: () => 
      dispatch({ type: 'HYDRATE_STATE', payload: initialState })
  }), [state, enablePersistence]);

  // Selectors implementation
  const selectors = useMemo<AppStateSelectors>(() => ({
    getUserPreferences: () => state.user.preferences,
    getUserSession: () => state.user.session,
    isAuthenticated: () => state.user.session.isAuthenticated,
    
    getLayout: () => state.ui.layout,
    getNotifications: () => state.ui.notifications,
    getActiveModal: () => state.ui.modals.stack[state.ui.modals.stack.length - 1] || null,
    getModalData: (id) => state.ui.modals.data[id],
    isGlobalLoading: () => state.ui.loading.global,
    isOperationLoading: (operation) => state.ui.loading.operations[operation] || false,
    
    isFeatureEnabled: (feature) => state.features.enabled[feature] || false,
    getFeatureConfig: (feature) => state.features.config[feature],
    getEnabledFeatures: () => Object.keys(state.features.enabled).filter(f => state.features.enabled[f]),
    
    getPerformanceMetrics: () => state.performance.metrics,
    
    isOnline: () => state.sync.isOnline,
    getPendingChanges: () => state.sync.pendingChanges,
    getSyncConflicts: () => state.sync.conflicts,
    getLastSync: () => state.sync.lastSync,
    hasPendingChanges: () => state.sync.pendingChanges.length > 0,
    hasSyncConflicts: () => state.sync.conflicts.length > 0
  }), [state]);

  // Load persisted state on mount
  useEffect(() => {
    if (enablePersistence) {
      try {
        const persistedState = localStorage.getItem('app-state');
        if (persistedState) {
          const parsed = JSON.parse(persistedState);
          dispatch({ type: 'HYDRATE_STATE', payload: parsed });
        }
      } catch (error) {
        console.error('Failed to load persisted state:', error);
      }
    }
  }, [enablePersistence]);

  // Online/offline detection
  useEffect(() => {
    const handleOnline = () => actions.setOnlineStatus(true);
    const handleOffline = () => actions.setOnlineStatus(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [actions]);

  // Auto-persist state changes
  useEffect(() => {
    if (enablePersistence) {
      const timeoutId = setTimeout(() => {
        actions.persistState();
      }, 1000); // Debounce persistence

      return () => clearTimeout(timeoutId);
    }
    return undefined;
  }, [state, actions, enablePersistence]);

  // Feature bus integration
  useEffect(() => {
    const unsubscribe = featureBus.communicate.subscribe('*', (event) => {
      // Handle feature communication events
      if (event.type.startsWith('feature:')) {
        // Update feature state based on events
        console.log('Feature event received:', event);
      }
    });

    return unsubscribe;
  }, []);

  // DevTools integration
  useEffect(() => {
    if (enableDevtools && typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION__) {
      const devTools = (window as any).__REDUX_DEVTOOLS_EXTENSION__.connect({
        name: 'App State'
      });

      devTools.init(state);

      return () => {
        devTools.disconnect();
      };
    }
    return undefined;
  }, [state, enableDevtools]);

  const contextValue = useMemo(() => ({
    state,
    dispatch,
    actions,
    selectors
  }), [state, actions, selectors]);

  return (
    <AppStateContext.Provider value={contextValue}>
      {children}
    </AppStateContext.Provider>
  );
};

// Hook to use app state
export const useAppState = () => {
  const context = useContext(AppStateContext);
  if (!context) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  return context;
};

// Convenience hooks
export const useAppActions = () => useAppState().actions;
export const useAppSelectors = () => useAppState().selectors;
