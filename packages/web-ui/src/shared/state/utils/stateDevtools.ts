/**
 * State DevTools Integration
 * 
 * Integration with Redux DevTools and custom state debugging tools.
 */

import { useEffect, useRef } from 'react';

// DevTools extension interface
interface DevToolsExtension {
  connect(options?: DevToolsOptions): DevToolsConnection;
}

interface DevToolsConnection {
  init(state: any): void;
  send(action: any, state: any): void;
  subscribe(listener: (message: any) => void): () => void;
  disconnect(): void;
}

interface DevToolsOptions {
  name?: string;
  maxAge?: number;
  trace?: boolean;
  traceLimit?: number;
  actionSanitizer?: (action: any) => any;
  stateSanitizer?: (state: any) => any;
  predicate?: (state: any, action: any) => boolean;
}

// Check if DevTools extension is available
export function isDevToolsAvailable(): boolean {
  return typeof window !== 'undefined' && 
         !!(window as any).__REDUX_DEVTOOLS_EXTENSION__;
}

// Create DevTools connection
export function createDevToolsConnection(options: DevToolsOptions = {}): DevToolsConnection | null {
  if (!isDevToolsAvailable()) {
    return null;
  }

  const extension = (window as any).__REDUX_DEVTOOLS_EXTENSION__ as DevToolsExtension;
  return extension.connect({
    name: 'App State',
    maxAge: 50,
    trace: true,
    traceLimit: 25,
    ...options
  });
}

// DevTools hook for state management
export function useDevTools<T>(
  state: T,
  name: string = 'State',
  options: DevToolsOptions = {}
): {
  send: (action: any) => void;
  isConnected: boolean;
} {
  const connectionRef = useRef<DevToolsConnection | null>(null);
  const isConnectedRef = useRef(false);

  // Initialize connection
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      connectionRef.current = createDevToolsConnection({
        name,
        ...options
      });
      
      if (connectionRef.current) {
        isConnectedRef.current = true;
        connectionRef.current.init(state);
      }
    }

    return () => {
      if (connectionRef.current) {
        connectionRef.current.disconnect();
        isConnectedRef.current = false;
      }
    };
  }, [name]);

  // Send state updates
  useEffect(() => {
    if (connectionRef.current && isConnectedRef.current) {
      connectionRef.current.send({ type: 'STATE_UPDATE' }, state);
    }
  }, [state]);

  const send = (action: any) => {
    if (connectionRef.current && isConnectedRef.current) {
      connectionRef.current.send(action, state);
    }
  };

  return {
    send,
    isConnected: isConnectedRef.current
  };
}

// State logger for debugging
export class StateLogger {
  private logs: Array<{
    timestamp: Date;
    action: any;
    prevState: any;
    nextState: any;
    duration: number;
  }> = [];

  private maxLogs = 100;

  log(action: any, prevState: any, nextState: any, duration: number = 0): void {
    if (process.env.NODE_ENV === 'development') {
      this.logs.push({
        timestamp: new Date(),
        action,
        prevState,
        nextState,
        duration
      });

      // Keep only recent logs
      if (this.logs.length > this.maxLogs) {
        this.logs = this.logs.slice(-this.maxLogs);
      }

      // Console output
      console.group(`🔄 ${action.type || 'State Change'}`);
      console.log('⏰ Timestamp:', new Date().toISOString());
      console.log('📋 Action:', action);
      console.log('📊 Previous State:', prevState);
      console.log('📈 Next State:', nextState);
      if (duration > 0) {
        console.log('⚡ Duration:', `${duration}ms`);
      }
      console.groupEnd();
    }
  }

  getLogs(): typeof this.logs {
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
  }

  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  importLogs(logsJson: string): void {
    try {
      this.logs = JSON.parse(logsJson);
    } catch (error) {
      console.error('Failed to import logs:', error);
    }
  }
}

// Global state logger instance
export const stateLogger = new StateLogger();

// Performance monitor for state updates
export class StatePerformanceMonitor {
  private metrics: Array<{
    name: string;
    duration: number;
    timestamp: Date;
    memoryUsage?: number;
  }> = [];

  private maxMetrics = 1000;

  measure<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const startMemory = (performance as any).memory?.usedJSHeapSize;
    
    const result = fn();
    
    const end = performance.now();
    const endMemory = (performance as any).memory?.usedJSHeapSize;
    const duration = end - start;
    
    this.metrics.push({
      name,
      duration,
      timestamp: new Date(),
      memoryUsage: endMemory ? endMemory - startMemory : undefined
    });

    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log slow operations
    if (duration > 16) { // Slower than 60fps
      console.warn(`🐌 Slow state operation: ${name} took ${duration.toFixed(2)}ms`);
    }

    return result;
  }

  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const startMemory = (performance as any).memory?.usedJSHeapSize;
    
    const result = await fn();
    
    const end = performance.now();
    const endMemory = (performance as any).memory?.usedJSHeapSize;
    const duration = end - start;
    
    this.metrics.push({
      name,
      duration,
      timestamp: new Date(),
      memoryUsage: endMemory ? endMemory - startMemory : undefined
    });

    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    return result;
  }

  getMetrics(): typeof this.metrics {
    return [...this.metrics];
  }

  getAverageTime(name: string): number {
    const nameMetrics = this.metrics.filter(m => m.name === name);
    if (nameMetrics.length === 0) return 0;
    
    const total = nameMetrics.reduce((sum, m) => sum + m.duration, 0);
    return total / nameMetrics.length;
  }

  getSlowOperations(threshold: number = 16): typeof this.metrics {
    return this.metrics.filter(m => m.duration > threshold);
  }

  clearMetrics(): void {
    this.metrics = [];
  }

  generateReport(): string {
    const report = {
      totalOperations: this.metrics.length,
      averageTime: this.metrics.reduce((sum, m) => sum + m.duration, 0) / this.metrics.length,
      slowOperations: this.getSlowOperations().length,
      operationsByName: this.metrics.reduce((acc, m) => {
        if (!acc[m.name]) {
          acc[m.name] = { count: 0, totalTime: 0, averageTime: 0 };
        }
        acc[m.name].count++;
        acc[m.name].totalTime += m.duration;
        acc[m.name].averageTime = acc[m.name].totalTime / acc[m.name].count;
        return acc;
      }, {} as Record<string, { count: number; totalTime: number; averageTime: number }>)
    };

    return JSON.stringify(report, null, 2);
  }
}

// Global performance monitor instance
export const statePerformanceMonitor = new StatePerformanceMonitor();

// Hook for performance monitoring
export function useStatePerformanceMonitor(name: string) {
  return {
    measure: <T>(fn: () => T) => statePerformanceMonitor.measure(name, fn),
    measureAsync: <T>(fn: () => Promise<T>) => statePerformanceMonitor.measureAsync(name, fn),
    getMetrics: () => statePerformanceMonitor.getMetrics(),
    getAverageTime: () => statePerformanceMonitor.getAverageTime(name)
  };
}

// State diff utility
export function createStateDiff(prevState: any, nextState: any): {
  added: Record<string, any>;
  removed: Record<string, any>;
  changed: Record<string, { from: any; to: any }>;
} {
  const added: Record<string, any> = {};
  const removed: Record<string, any> = {};
  const changed: Record<string, { from: any; to: any }> = {};

  // Find added and changed properties
  for (const key in nextState) {
    if (!(key in prevState)) {
      added[key] = nextState[key];
    } else if (prevState[key] !== nextState[key]) {
      changed[key] = { from: prevState[key], to: nextState[key] };
    }
  }

  // Find removed properties
  for (const key in prevState) {
    if (!(key in nextState)) {
      removed[key] = prevState[key];
    }
  }

  return { added, removed, changed };
}

// State snapshot utility
export class StateSnapshot {
  private snapshots: Map<string, any> = new Map();

  capture(name: string, state: any): void {
    this.snapshots.set(name, JSON.parse(JSON.stringify(state)));
  }

  restore(name: string): any | null {
    return this.snapshots.get(name) || null;
  }

  compare(name1: string, name2: string): ReturnType<typeof createStateDiff> | null {
    const state1 = this.snapshots.get(name1);
    const state2 = this.snapshots.get(name2);
    
    if (!state1 || !state2) return null;
    
    return createStateDiff(state1, state2);
  }

  list(): string[] {
    return Array.from(this.snapshots.keys());
  }

  clear(): void {
    this.snapshots.clear();
  }

  export(): string {
    const data = Object.fromEntries(this.snapshots);
    return JSON.stringify(data, null, 2);
  }

  import(data: string): void {
    try {
      const parsed = JSON.parse(data);
      this.snapshots = new Map(Object.entries(parsed));
    } catch (error) {
      console.error('Failed to import snapshots:', error);
    }
  }
}

// Global state snapshot instance
export const stateSnapshot = new StateSnapshot();

// Development tools panel
export function createDevPanel(): void {
  if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
    (window as any).__STATE_DEV_TOOLS__ = {
      logger: stateLogger,
      performanceMonitor: statePerformanceMonitor,
      snapshot: stateSnapshot,
      createDiff: createStateDiff
    };

    console.log('🛠️ State development tools available at window.__STATE_DEV_TOOLS__');
  }
}
