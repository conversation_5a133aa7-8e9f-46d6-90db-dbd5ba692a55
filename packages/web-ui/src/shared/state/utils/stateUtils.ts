/**
 * State Utilities
 * 
 * Utility functions for state management operations.
 */

import { useCallback, useRef, useEffect } from 'react';

// Deep equality check
export function deepEqual(a: any, b: any): boolean {
  if (a === b) return true;
  
  if (a == null || b == null) return false;
  
  if (typeof a !== typeof b) return false;
  
  if (typeof a !== 'object') return false;
  
  if (Array.isArray(a) !== Array.isArray(b)) return false;
  
  const keysA = Object.keys(a);
  const keysB = Object.keys(b);
  
  if (keysA.length !== keysB.length) return false;
  
  for (const key of keysA) {
    if (!keysB.includes(key)) return false;
    if (!deepEqual(a[key], b[key])) return false;
  }
  
  return true;
}

// Shallow equality check
export function shallowEqual(a: any, b: any): boolean {
  if (a === b) return true;
  
  if (a == null || b == null) return false;
  
  if (typeof a !== 'object' || typeof b !== 'object') return false;
  
  const keysA = Object.keys(a);
  const keysB = Object.keys(b);
  
  if (keysA.length !== keysB.length) return false;
  
  for (const key of keysA) {
    if (!keysB.includes(key) || a[key] !== b[key]) return false;
  }
  
  return true;
}

// Deep clone utility
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj;
  
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

// Immutable update utilities
export function updateIn<T>(obj: T, path: string[], value: any): T {
  if (path.length === 0) return value;
  
  const [head, ...tail] = path;
  const current = (obj as any)[head];
  
  return {
    ...obj as any,
    [head]: tail.length === 0 ? value : updateIn(current || {}, tail, value)
  };
}

export function setIn<T>(obj: T, path: string[], value: any): T {
  return updateIn(obj, path, value);
}

export function deleteIn<T>(obj: T, path: string[]): T {
  if (path.length === 0) return obj;
  
  const [head, ...tail] = path;
  
  if (tail.length === 0) {
    const { [head]: deleted, ...rest } = obj as any;
    return rest as T;
  }
  
  const current = (obj as any)[head];
  if (!current) return obj;
  
  return {
    ...obj as any,
    [head]: deleteIn(current, tail)
  };
}

// Array utilities
export function arrayInsert<T>(array: T[], index: number, item: T): T[] {
  return [...array.slice(0, index), item, ...array.slice(index)];
}

export function arrayRemove<T>(array: T[], index: number): T[] {
  return [...array.slice(0, index), ...array.slice(index + 1)];
}

export function arrayUpdate<T>(array: T[], index: number, item: T): T[] {
  return array.map((existing, i) => i === index ? item : existing);
}

export function arrayMove<T>(array: T[], fromIndex: number, toIndex: number): T[] {
  const item = array[fromIndex];
  const withoutItem = arrayRemove(array, fromIndex);
  return arrayInsert(withoutItem, toIndex, item);
}

// State selector utilities
export function createSelector<T, R>(
  selector: (state: T) => R,
  equalityFn: (a: R, b: R) => boolean = shallowEqual
) {
  let lastResult: R;
  let lastArgs: T;
  
  return (state: T): R => {
    if (lastArgs !== state) {
      const result = selector(state);
      if (!equalityFn(result, lastResult)) {
        lastResult = result;
      }
      lastArgs = state;
    }
    return lastResult;
  };
}

export function createStructuredSelector<T, R extends Record<string, any>>(
  selectors: { [K in keyof R]: (state: T) => R[K] }
): (state: T) => R {
  const keys = Object.keys(selectors) as (keyof R)[];
  
  return createSelector(
    (state: T) => {
      const result = {} as R;
      for (const key of keys) {
        result[key] = selectors[key](state);
      }
      return result;
    }
  );
}

// Debounce hook
export function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout>();
  
  const debouncedCallback = useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]) as T;
  
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return debouncedCallback;
}

// Throttle hook
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastCallRef = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout>();
  
  const throttledCallback = useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    const timeSinceLastCall = now - lastCallRef.current;
    
    if (timeSinceLastCall >= delay) {
      lastCallRef.current = now;
      callback(...args);
    } else {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        lastCallRef.current = Date.now();
        callback(...args);
      }, delay - timeSinceLastCall);
    }
  }, [callback, delay]) as T;
  
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return throttledCallback;
}

// Previous value hook
export function usePrevious<T>(value: T): T | undefined {
  const ref = useRef<T>();
  
  useEffect(() => {
    ref.current = value;
  });
  
  return ref.current;
}

// State comparison hook
export function useStateComparison<T>(
  state: T,
  equalityFn: (a: T, b: T) => boolean = shallowEqual
): {
  hasChanged: boolean;
  previous: T | undefined;
} {
  const previous = usePrevious(state);
  const hasChanged = previous !== undefined && !equalityFn(state, previous);
  
  return { hasChanged, previous };
}

// Batch updates utility
export function batchUpdates<T>(
  updates: Array<(state: T) => T>
): (state: T) => T {
  return (state: T) => {
    return updates.reduce((currentState, update) => update(currentState), state);
  };
}

// State validation utilities
export function validateState<T>(
  state: T,
  schema: Record<string, (value: any) => boolean>
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  for (const [key, validator] of Object.entries(schema)) {
    const value = (state as any)[key];
    if (!validator(value)) {
      errors.push(`Invalid value for ${key}: ${value}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// State normalization utilities
export function normalizeArray<T extends { id: string }>(
  array: T[]
): { byId: Record<string, T>; allIds: string[] } {
  const byId: Record<string, T> = {};
  const allIds: string[] = [];
  
  for (const item of array) {
    byId[item.id] = item;
    allIds.push(item.id);
  }
  
  return { byId, allIds };
}

export function denormalizeArray<T>(
  byId: Record<string, T>,
  allIds: string[]
): T[] {
  return allIds.map(id => byId[id]).filter(Boolean);
}

// State migration utilities
export function migrateState<T>(
  state: any,
  migrations: Record<number, (state: any) => any>,
  currentVersion: number
): T {
  const stateVersion = state.version || 0;
  
  if (stateVersion >= currentVersion) {
    return state;
  }
  
  let migratedState = { ...state };
  
  for (let version = stateVersion + 1; version <= currentVersion; version++) {
    const migration = migrations[version];
    if (migration) {
      migratedState = migration(migratedState);
      migratedState.version = version;
    }
  }
  
  return migratedState;
}

// Performance monitoring utilities
export function measureStateUpdate<T>(
  name: string,
  updateFn: () => T
): T {
  const start = performance.now();
  const result = updateFn();
  const end = performance.now();
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`State update "${name}" took ${end - start}ms`);
  }
  
  return result;
}

// State debugging utilities
export function logStateChange<T>(
  name: string,
  prevState: T,
  nextState: T,
  action?: any
): void {
  if (process.env.NODE_ENV === 'development') {
    console.group(`State change: ${name}`);
    console.log('Previous state:', prevState);
    console.log('Next state:', nextState);
    if (action) {
      console.log('Action:', action);
    }
    console.groupEnd();
  }
}
