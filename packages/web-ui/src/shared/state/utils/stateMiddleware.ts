/**
 * State Middleware
 * 
 * Middleware system for state management with logging, validation, and side effects.
 */

import { stateLogger, statePerformanceMonitor } from './stateDevtools';
import { deepEqual } from './stateUtils';

// Middleware function type
export type StateMiddleware<T> = (
  action: any,
  prevState: T,
  nextState: T,
  dispatch: (action: any) => void
) => T | Promise<T>;

// Middleware manager
export class StateMiddlewareManager<T> {
  private middlewares: StateMiddleware<T>[] = [];

  add(middleware: StateMiddleware<T>): void {
    this.middlewares.push(middleware);
  }

  remove(middleware: StateMiddleware<T>): void {
    const index = this.middlewares.indexOf(middleware);
    if (index > -1) {
      this.middlewares.splice(index, 1);
    }
  }

  clear(): void {
    this.middlewares = [];
  }

  async apply(
    action: any,
    prevState: T,
    nextState: T,
    dispatch: (action: any) => void
  ): Promise<T> {
    let currentState = nextState;

    for (const middleware of this.middlewares) {
      try {
        const result = await middleware(action, prevState, currentState, dispatch);
        if (result !== undefined) {
          currentState = result;
        }
      } catch (error) {
        console.error('Middleware error:', error);
      }
    }

    return currentState;
  }
}

// Built-in middleware

// Logging middleware
export function createLoggingMiddleware<T>(
  options: {
    enabled?: boolean;
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
    filter?: (action: any) => boolean;
  } = {}
): StateMiddleware<T> {
  const { enabled = process.env.NODE_ENV === 'development', filter } = options;

  return (action, prevState, nextState) => {
    if (!enabled) return nextState;
    
    if (filter && !filter(action)) return nextState;

    const hasChanged = !deepEqual(prevState, nextState);
    
    if (hasChanged) {
      stateLogger.log(action, prevState, nextState);
    }

    return nextState;
  };
}

// Performance monitoring middleware
export function createPerformanceMiddleware<T>(
  options: {
    enabled?: boolean;
    threshold?: number;
    logSlowActions?: boolean;
  } = {}
): StateMiddleware<T> {
  const { enabled = process.env.NODE_ENV === 'development', threshold = 16, logSlowActions = true } = options;

  return (action, _prevState, nextState) => {
    if (!enabled) return nextState;

    return statePerformanceMonitor.measure(`Action: ${action.type || 'Unknown'}`, () => {
      if (logSlowActions) {
        const start = performance.now();
        const result = nextState;
        const duration = performance.now() - start;
        
        if (duration > threshold) {
          console.warn(`Slow action: ${action.type} took ${duration.toFixed(2)}ms`);
        }
        
        return result;
      }
      
      return nextState;
    });
  };
}

// Validation middleware
export function createValidationMiddleware<T>(
  validator: (state: T) => { isValid: boolean; errors: string[] }
): StateMiddleware<T> {
  return (action, prevState, nextState) => {
    const validation = validator(nextState);
    
    if (!validation.isValid) {
      console.error('State validation failed:', validation.errors);
      console.error('Action:', action);
      console.error('Invalid state:', nextState);
      
      // Return previous state to prevent invalid state
      return prevState;
    }
    
    return nextState;
  };
}

// Persistence middleware
export function createPersistenceMiddleware<T>(
  options: {
    key: string;
    storage?: Storage;
    serialize?: (state: T) => string;
    deserialize?: (data: string) => T;
    debounceMs?: number;
    filter?: (action: any) => boolean;
  }
): StateMiddleware<T> {
  const {
    key,
    storage = localStorage,
    serialize = JSON.stringify,
    debounceMs = 1000,
    filter
  } = options;

  let timeoutId: NodeJS.Timeout | null = null;

  return (action, _prevState, nextState) => {
    if (filter && !filter(action)) return nextState;

    // Debounce persistence
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      try {
        const serialized = serialize(nextState);
        storage.setItem(key, serialized);
      } catch (error) {
        console.error('Failed to persist state:', error);
      }
    }, debounceMs);

    return nextState;
  };
}

// Undo/Redo middleware
export function createUndoRedoMiddleware<T>(
  options: {
    maxHistorySize?: number;
    filter?: (action: any) => boolean;
    groupBy?: (action: any) => string | null;
  } = {}
): {
  middleware: StateMiddleware<T>;
  undo: () => void;
  redo: () => void;
  canUndo: () => boolean;
  canRedo: () => boolean;
  clearHistory: () => void;
} {
  const { maxHistorySize = 50, filter, groupBy } = options;

  let history: T[] = [];
  let currentIndex = -1;
  let dispatch: ((action: any) => void) | null = null;

  const middleware: StateMiddleware<T> = (action, prevState, nextState, dispatchFn) => {
    dispatch = dispatchFn;

    // Skip undo/redo actions
    if (action.type === '@@UNDO' || action.type === '@@REDO') {
      return nextState;
    }

    if (filter && !filter(action)) return nextState;

    // Group actions if groupBy is provided
    const group = groupBy?.(action);
    const shouldAddToHistory = !group || history.length === 0 || 
      groupBy?.(history[currentIndex]) !== group;

    if (shouldAddToHistory) {
      // Remove any future history if we're not at the end
      if (currentIndex < history.length - 1) {
        history = history.slice(0, currentIndex + 1);
      }

      // Add new state to history
      history.push(prevState);
      currentIndex = history.length - 1;

      // Limit history size
      if (history.length > maxHistorySize) {
        history = history.slice(-maxHistorySize);
        currentIndex = history.length - 1;
      }
    }

    return nextState;
  };

  const undo = () => {
    if (canUndo() && dispatch) {
      currentIndex--;
      const previousState = history[currentIndex];
      dispatch({ type: '@@UNDO', payload: previousState });
    }
  };

  const redo = () => {
    if (canRedo() && dispatch) {
      currentIndex++;
      const nextState = history[currentIndex];
      dispatch({ type: '@@REDO', payload: nextState });
    }
  };

  const canUndo = () => currentIndex > 0;
  const canRedo = () => currentIndex < history.length - 1;

  const clearHistory = () => {
    history = [];
    currentIndex = -1;
  };

  return {
    middleware,
    undo,
    redo,
    canUndo,
    canRedo,
    clearHistory
  };
}

// Side effects middleware
export function createSideEffectsMiddleware<T>(
  effects: Record<string, (action: any, state: T, dispatch: (action: any) => void) => void | Promise<void>>
): StateMiddleware<T> {
  return async (action, _prevState, nextState, dispatch) => {
    const effect = effects[action.type];
    
    if (effect) {
      try {
        await effect(action, nextState, dispatch);
      } catch (error) {
        console.error(`Side effect error for action ${action.type}:`, error);
      }
    }
    
    return nextState;
  };
}

// Batching middleware
export function createBatchingMiddleware<T>(
  options: {
    batchSize?: number;
    flushInterval?: number;
  } = {}
): StateMiddleware<T> {
  const { batchSize = 10, flushInterval = 100 } = options;

  let batch: Array<{ action: any; prevState: T; nextState: T }> = [];
  let timeoutId: NodeJS.Timeout | null = null;

  const flushBatch = () => {
    if (batch.length > 0) {
      console.log(`Flushing batch of ${batch.length} actions:`, batch);
      batch = [];
    }
    timeoutId = null;
  };

  return (action, prevState, nextState) => {
    batch.push({ action, prevState, nextState });

    // Flush if batch is full
    if (batch.length >= batchSize) {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      flushBatch();
    } else if (!timeoutId) {
      // Schedule flush
      timeoutId = setTimeout(flushBatch, flushInterval);
    }

    return nextState;
  };
}

// Error handling middleware
export function createErrorHandlingMiddleware<T>(
  errorHandler: (error: Error, action: any, state: T) => T | void
): StateMiddleware<T> {
  return (action, prevState, nextState) => {
    try {
      return nextState;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Unknown error');
      console.error('State update error:', err);
      
      const recoveredState = errorHandler(err, action, prevState);
      return recoveredState !== undefined ? recoveredState : prevState;
    }
  };
}

// Immutability check middleware
export function createImmutabilityMiddleware<T>(): StateMiddleware<T> {
  return (action, prevState, nextState) => {
    if (process.env.NODE_ENV === 'development') {
      // Check if state was mutated
      try {
        const serializedPrev = JSON.stringify(prevState);
        const serializedNext = JSON.stringify(nextState);
        
        // If states are different but reference is the same, mutation occurred
        if (prevState === nextState && serializedPrev !== serializedNext) {
          console.error('State mutation detected!', {
            action,
            prevState,
            nextState
          });
        }
      } catch (error) {
        // Ignore serialization errors
      }
    }
    
    return nextState;
  };
}

// Compose multiple middlewares
export function composeMiddleware<T>(...middlewares: StateMiddleware<T>[]): StateMiddleware<T> {
  return async (action, prevState, nextState, dispatch) => {
    let currentState = nextState;
    
    for (const middleware of middlewares) {
      currentState = await middleware(action, prevState, currentState, dispatch);
    }
    
    return currentState;
  };
}
