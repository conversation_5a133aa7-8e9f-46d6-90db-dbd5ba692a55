/**
 * State Synchronization Hook
 * 
 * Provides state synchronization between local and remote state,
 * with conflict resolution and offline support.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAppState } from '../AppStateProvider';

export interface StateSyncOptions<T> {
  // Unique key for this state
  key: string;
  
  // Initial state
  initialState: T;
  
  // Function to fetch remote state
  fetchRemoteState: () => Promise<T>;
  
  // Function to push local state to remote
  pushLocalState: (state: T) => Promise<void>;
  
  // Function to merge conflicting states
  resolveConflict: (local: T, remote: T) => T;
  
  // Sync interval in milliseconds
  syncInterval?: number;
  
  // Whether to sync automatically
  autoSync?: boolean;
  
  // Whether to sync on window focus
  syncOnFocus?: boolean;
  
  // Whether to sync on network reconnection
  syncOnReconnect?: boolean;
  
  // Debounce delay for local changes
  debounceDelay?: number;
  
  // Custom equality function
  isEqual?: (a: T, b: T) => boolean;
}

export interface StateSyncReturn<T> {
  // Current state
  state: T;
  
  // Update local state
  setState: (newState: T | ((prev: T) => T)) => void;
  
  // Sync status
  syncStatus: {
    isOnline: boolean;
    isSyncing: boolean;
    lastSync: Date | null;
    hasConflicts: boolean;
    hasPendingChanges: boolean;
  };
  
  // Manual sync operations
  sync: () => Promise<void>;
  forceSync: () => Promise<void>;
  
  // Conflict resolution
  resolveConflicts: () => Promise<void>;
  
  // Reset to remote state
  resetToRemote: () => Promise<void>;
}

const defaultIsEqual = <T>(a: T, b: T): boolean => {
  return JSON.stringify(a) === JSON.stringify(b);
};

export function useStateSync<T>(options: StateSyncOptions<T>): StateSyncReturn<T> {
  const {
    key,
    initialState,
    fetchRemoteState,
    pushLocalState,
    resolveConflict,
    syncInterval = 30000, // 30 seconds
    autoSync = true,
    syncOnFocus = true,
    syncOnReconnect = true,
    debounceDelay = 1000,
    isEqual = defaultIsEqual
  } = options;

  const { actions, selectors } = useAppState();
  
  // Local state
  const [localState, setLocalState] = useState<T>(initialState);
  const [, setRemoteState] = useState<T>(initialState);
  const [isSyncing, setIsSyncing] = useState(false);
  const [hasConflicts, setHasConflicts] = useState(false);
  
  // Refs for tracking
  const lastSyncRef = useRef<Date | null>(null);
  const pendingChangesRef = useRef(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const syncIntervalRef = useRef<NodeJS.Timeout>();

  // App state integration
  const isOnline = selectors.isOnline();
  const lastSync = lastSyncRef.current;
  const hasPendingChanges = pendingChangesRef.current;

  // Update local state
  const setState = useCallback((newState: T | ((prev: T) => T)) => {
    setLocalState(prev => {
      const nextState = typeof newState === 'function' ? (newState as (prev: T) => T)(prev) : newState;
      
      // Mark as having pending changes
      pendingChangesRef.current = true;
      
      // Add to app state pending changes
      actions.addPendingChange({
        type: 'state-sync',
        data: { key, state: nextState }
      });
      
      // Debounce sync
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      
      debounceTimeoutRef.current = setTimeout(() => {
        if (isOnline && autoSync) {
          sync();
        }
      }, debounceDelay);
      
      return nextState;
    });
  }, [key, isOnline, autoSync, debounceDelay, actions]);

  // Fetch remote state
  const fetchRemote = useCallback(async (): Promise<T> => {
    try {
      const remote = await fetchRemoteState();
      setRemoteState(remote);
      return remote;
    } catch (error) {
      console.error('Failed to fetch remote state:', error);
      throw error;
    }
  }, [fetchRemoteState]);

  // Push local state to remote
  const pushLocal = useCallback(async (state: T): Promise<void> => {
    try {
      await pushLocalState(state);
      pendingChangesRef.current = false;
      
      // Remove from app state pending changes
      const pendingChanges = selectors.getPendingChanges();
      const changeToRemove = pendingChanges.find(c => 
        c.type === 'state-sync' && c.data.key === key
      );
      if (changeToRemove) {
        actions.removePendingChange(changeToRemove.id);
      }
    } catch (error) {
      console.error('Failed to push local state:', error);
      throw error;
    }
  }, [pushLocalState, key, selectors, actions]);

  // Sync operation
  const sync = useCallback(async (): Promise<void> => {
    if (!isOnline || isSyncing) {
      return;
    }

    setIsSyncing(true);
    
    try {
      // Fetch remote state
      const remote = await fetchRemote();
      
      // Check for conflicts
      if (!isEqual(localState, remote) && pendingChangesRef.current) {
        // Conflict detected
        setHasConflicts(true);
        
        // Add conflict to app state
        actions.addSyncConflict({
          local: localState,
          remote: remote
        });
        
        // Auto-resolve if possible
        try {
          const resolved = resolveConflict(localState, remote);
          setLocalState(resolved);
          await pushLocal(resolved);
          setHasConflicts(false);
        } catch (resolveError) {
          console.error('Failed to auto-resolve conflict:', resolveError);
        }
      } else if (pendingChangesRef.current) {
        // No conflict, push local changes
        await pushLocal(localState);
      } else if (!isEqual(localState, remote)) {
        // No local changes, update to remote
        setLocalState(remote);
      }
      
      lastSyncRef.current = new Date();
      actions.setLastSync(lastSyncRef.current);
      
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      setIsSyncing(false);
    }
  }, [isOnline, isSyncing, localState, isEqual, fetchRemote, resolveConflict, pushLocal, actions]);

  // Force sync (ignores conflicts)
  const forceSync = useCallback(async (): Promise<void> => {
    if (!isOnline) {
      return;
    }

    setIsSyncing(true);
    setHasConflicts(false);
    
    try {
      if (pendingChangesRef.current) {
        await pushLocal(localState);
      } else {
        const remote = await fetchRemote();
        setLocalState(remote);
      }
      
      lastSyncRef.current = new Date();
      actions.setLastSync(lastSyncRef.current);
      
    } catch (error) {
      console.error('Force sync failed:', error);
    } finally {
      setIsSyncing(false);
    }
  }, [isOnline, localState, pushLocal, fetchRemote, actions]);

  // Resolve conflicts manually
  const resolveConflicts = useCallback(async (): Promise<void> => {
    if (!hasConflicts || !isOnline) {
      return;
    }

    try {
      const remote = await fetchRemote();
      const resolved = resolveConflict(localState, remote);
      
      setLocalState(resolved);
      await pushLocal(resolved);
      setHasConflicts(false);
      
      // Remove conflict from app state
      const conflicts = selectors.getSyncConflicts();
      conflicts.forEach(conflict => {
        actions.resolveSyncConflict(conflict.id);
      });
      
    } catch (error) {
      console.error('Failed to resolve conflicts:', error);
    }
  }, [hasConflicts, isOnline, localState, fetchRemote, resolveConflict, pushLocal, selectors, actions]);

  // Reset to remote state
  const resetToRemote = useCallback(async (): Promise<void> => {
    if (!isOnline) {
      return;
    }

    try {
      const remote = await fetchRemote();
      setLocalState(remote);
      pendingChangesRef.current = false;
      setHasConflicts(false);
      
      // Clear pending changes
      const pendingChanges = selectors.getPendingChanges();
      const changeToRemove = pendingChanges.find(c => 
        c.type === 'state-sync' && c.data.key === key
      );
      if (changeToRemove) {
        actions.removePendingChange(changeToRemove.id);
      }
      
    } catch (error) {
      console.error('Failed to reset to remote:', error);
    }
  }, [isOnline, key, fetchRemote, selectors, actions]);

  // Auto sync interval
  useEffect(() => {
    if (autoSync && isOnline) {
      syncIntervalRef.current = setInterval(() => {
        sync();
      }, syncInterval);

      return () => {
        if (syncIntervalRef.current) {
          clearInterval(syncIntervalRef.current);
        }
      };
    }
    return undefined;
  }, [autoSync, isOnline, syncInterval, sync]);

  // Sync on reconnection
  useEffect(() => {
    if (isOnline && syncOnReconnect) {
      sync();
    }
  }, [isOnline, syncOnReconnect, sync]);

  // Sync on window focus
  useEffect(() => {
    if (syncOnFocus) {
      const handleFocus = () => {
        if (isOnline) {
          sync();
        }
      };

      window.addEventListener('focus', handleFocus);
      return () => window.removeEventListener('focus', handleFocus);
    }
    return undefined;
  }, [syncOnFocus, isOnline, sync]);

  // Initial sync
  useEffect(() => {
    if (isOnline) {
      fetchRemote().catch(console.error);
    }
  }, [fetchRemote, isOnline]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
      }
    };
  }, []);

  return {
    state: localState,
    setState,
    syncStatus: {
      isOnline,
      isSyncing,
      lastSync,
      hasConflicts,
      hasPendingChanges
    },
    sync,
    forceSync,
    resolveConflicts,
    resetToRemote
  };
}

// Hook for simple object state sync
export function useObjectStateSync<T extends Record<string, any>>(
  key: string,
  initialState: T,
  apiEndpoint: string
) {
  return useStateSync({
    key,
    initialState,
    fetchRemoteState: async () => {
      const response = await fetch(apiEndpoint);
      if (!response.ok) {
        throw new Error(`Failed to fetch: ${response.statusText}`);
      }
      return response.json();
    },
    pushLocalState: async (state) => {
      const response = await fetch(apiEndpoint, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(state)
      });
      if (!response.ok) {
        throw new Error(`Failed to push: ${response.statusText}`);
      }
    },
    resolveConflict: (local, remote) => ({
      ...remote,
      ...local,
      _lastModified: new Date().toISOString()
    })
  });
}
