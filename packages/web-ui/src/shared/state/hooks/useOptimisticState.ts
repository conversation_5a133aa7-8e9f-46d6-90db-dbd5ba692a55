/**
 * Optimistic State Hook
 * 
 * Provides optimistic updates for better user experience.
 * Automatically handles rollback on failure and retry logic.
 */

import { useState, useCallback, useRef, useEffect } from 'react';

export interface OptimisticStateOptions<T> {
  // Initial state value
  initialState: T;
  
  // Function to apply optimistic update
  applyOptimisticUpdate: (currentState: T, update: any) => T;
  
  // Function to perform the actual async operation
  performUpdate: (update: any) => Promise<T>;
  
  // Function to handle rollback on failure
  onRollback?: (error: Error, failedUpdate: any) => void;
  
  // Function to handle successful update
  onSuccess?: (result: T, update: any) => void;
  
  // Maximum number of retry attempts
  maxRetries?: number;
  
  // Retry delay in milliseconds
  retryDelay?: number;
  
  // Whether to automatically retry on failure
  autoRetry?: boolean;
}

export interface OptimisticStateReturn<T> {
  // Current state (including optimistic updates)
  state: T;
  
  // Whether there are pending optimistic updates
  isPending: boolean;
  
  // Number of pending operations
  pendingCount: number;
  
  // Last error that occurred
  lastError: Error | null;
  
  // Apply an optimistic update
  optimisticUpdate: (update: any) => Promise<T>;
  
  // Manually rollback all optimistic updates
  rollback: () => void;
  
  // Get the confirmed state (without optimistic updates)
  getConfirmedState: () => T;
  
  // Retry failed operations
  retryFailed: () => Promise<void>;
}

interface PendingOperation<T> {
  id: string;
  update: any;
  optimisticState: T;
  retryCount: number;
  timestamp: Date;
}

export function useOptimisticState<T>(
  options: OptimisticStateOptions<T>
): OptimisticStateReturn<T> {
  const {
    initialState,
    applyOptimisticUpdate,
    performUpdate,
    onRollback,
    onSuccess,
    maxRetries = 3,
    retryDelay = 1000,
    autoRetry = true
  } = options;

  // Confirmed state (without optimistic updates)
  const [confirmedState, setConfirmedState] = useState<T>(initialState);
  
  // Pending operations
  const [pendingOperations, setPendingOperations] = useState<PendingOperation<T>[]>([]);
  
  // Last error
  const [lastError, setLastError] = useState<Error | null>(null);
  
  // Operation counter for unique IDs
  const operationCounter = useRef(0);

  // Calculate current state with optimistic updates
  const currentState = pendingOperations.reduce(
    (_state, operation) => operation.optimisticState,
    confirmedState
  );

  // Generate unique operation ID
  const generateOperationId = useCallback(() => {
    operationCounter.current += 1;
    return `op-${Date.now()}-${operationCounter.current}`;
  }, []);

  // Remove operation from pending list
  const removeOperation = useCallback((operationId: string) => {
    setPendingOperations(prev => prev.filter(op => op.id !== operationId));
  }, []);

  // Perform update with retry logic
  const performUpdateWithRetry = useCallback(async (
    operation: PendingOperation<T>
  ): Promise<T> => {
    try {
      const result = await performUpdate(operation.update);
      
      // Update confirmed state
      setConfirmedState(result);
      
      // Remove successful operation
      removeOperation(operation.id);
      
      // Clear error
      setLastError(null);
      
      // Call success callback
      onSuccess?.(result, operation.update);
      
      return result;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Update failed');
      
      if (operation.retryCount < maxRetries && autoRetry) {
        // Retry after delay
        setTimeout(() => {
          setPendingOperations(prev => 
            prev.map(op => 
              op.id === operation.id 
                ? { ...op, retryCount: op.retryCount + 1 }
                : op
            )
          );
          
          performUpdateWithRetry({
            ...operation,
            retryCount: operation.retryCount + 1
          });
        }, retryDelay * Math.pow(2, operation.retryCount)); // Exponential backoff
      } else {
        // Max retries reached, rollback
        removeOperation(operation.id);
        setLastError(err);
        onRollback?.(err, operation.update);
      }
      
      throw err;
    }
  }, [performUpdate, maxRetries, autoRetry, retryDelay, removeOperation, onSuccess, onRollback]);

  // Apply optimistic update
  const optimisticUpdate = useCallback(async (update: any): Promise<T> => {
    const operationId = generateOperationId();
    const optimisticState = applyOptimisticUpdate(currentState, update);
    
    const operation: PendingOperation<T> = {
      id: operationId,
      update,
      optimisticState,
      retryCount: 0,
      timestamp: new Date()
    };
    
    // Add to pending operations
    setPendingOperations(prev => [...prev, operation]);
    
    // Perform actual update
    try {
      return await performUpdateWithRetry(operation);
    } catch (error) {
      // Error is already handled in performUpdateWithRetry
      throw error;
    }
  }, [currentState, applyOptimisticUpdate, generateOperationId, performUpdateWithRetry]);

  // Rollback all optimistic updates
  const rollback = useCallback(() => {
    setPendingOperations([]);
    setLastError(null);
  }, []);

  // Get confirmed state
  const getConfirmedState = useCallback(() => confirmedState, [confirmedState]);

  // Retry failed operations
  const retryFailed = useCallback(async () => {
    const failedOperations = pendingOperations.filter(op => op.retryCount >= maxRetries);
    
    for (const operation of failedOperations) {
      try {
        await performUpdateWithRetry({
          ...operation,
          retryCount: 0
        });
      } catch (error) {
        // Error is handled in performUpdateWithRetry
        console.error('Retry failed for operation:', operation.id, error);
      }
    }
  }, [pendingOperations, maxRetries, performUpdateWithRetry]);

  // Clean up old operations
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = new Date();
      const maxAge = 5 * 60 * 1000; // 5 minutes
      
      setPendingOperations(prev => 
        prev.filter(op => now.getTime() - op.timestamp.getTime() < maxAge)
      );
    }, 60000); // Check every minute

    return () => clearInterval(cleanup);
  }, []);

  return {
    state: currentState,
    isPending: pendingOperations.length > 0,
    pendingCount: pendingOperations.length,
    lastError,
    optimisticUpdate,
    rollback,
    getConfirmedState,
    retryFailed
  };
}

// Hook for simple optimistic updates
export function useSimpleOptimisticState<T>(
  initialState: T,
  updateFunction: (update: any) => Promise<T>
) {
  return useOptimisticState({
    initialState,
    applyOptimisticUpdate: (state, update) => ({ ...state, ...update }),
    performUpdate: updateFunction
  });
}

// Hook for array-based optimistic updates
export function useOptimisticArray<T extends { id: string }>(
  initialArray: T[],
  updateFunction: (operation: { type: 'add' | 'update' | 'remove'; item?: T; id?: string }) => Promise<T[]>
) {
  return useOptimisticState({
    initialState: initialArray,
    applyOptimisticUpdate: (array, operation) => {
      switch (operation.type) {
        case 'add':
          return [...array, operation.item];
        case 'update':
          return array.map(item =>
            item.id === operation.item.id ? { ...item, ...operation.item } : item
          );
        case 'remove':
          return array.filter(item => item.id !== operation.id);
        default:
          return array;
      }
    },
    performUpdate: updateFunction
  });
}
