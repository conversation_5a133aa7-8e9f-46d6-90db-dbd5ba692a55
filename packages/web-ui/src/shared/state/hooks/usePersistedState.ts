/**
 * Persisted State Hook
 * 
 * Provides state persistence with multiple storage backends,
 * compression, encryption, and migration support.
 */

import { useState, useEffect, useCallback, useRef } from 'react';

export type StorageBackend = 'localStorage' | 'sessionStorage' | 'indexedDB' | 'memory';

export interface PersistedStateOptions<T> {
  // Storage key
  key: string;
  
  // Initial state
  initialState: T;
  
  // Storage backend
  storage?: StorageBackend;
  
  // Serialization functions
  serialize?: (value: T) => string;
  deserialize?: (value: string) => T;
  
  // Compression (for large states)
  compress?: boolean;
  
  // Encryption key (for sensitive data)
  encryptionKey?: string;
  
  // State migration function
  migrate?: (persistedState: any, version: number) => T;
  
  // Current version for migration
  version?: number;
  
  // Debounce delay for persistence
  debounceDelay?: number;
  
  // Whether to sync across tabs
  syncAcrossTabs?: boolean;
  
  // Custom validation function
  validate?: (value: any) => value is T;
  
  // Error handler
  onError?: (error: Error, operation: 'load' | 'save') => void;
}

export interface PersistedStateReturn<T> {
  // Current state
  state: T;
  
  // Update state
  setState: (newState: T | ((prev: T) => T)) => void;
  
  // Persistence status
  status: {
    isLoading: boolean;
    isSaving: boolean;
    lastSaved: Date | null;
    error: Error | null;
  };
  
  // Manual operations
  save: () => Promise<void>;
  load: () => Promise<void>;
  clear: () => Promise<void>;
  
  // Reset to initial state
  reset: () => void;
}

// Storage adapters
class StorageAdapter {
  static async get(key: string, backend: StorageBackend): Promise<string | null> {
    switch (backend) {
      case 'localStorage':
        return localStorage.getItem(key);
      
      case 'sessionStorage':
        return sessionStorage.getItem(key);
      
      case 'indexedDB':
        return this.getFromIndexedDB(key);
      
      case 'memory':
        return this.memoryStorage.get(key) || null;
      
      default:
        throw new Error(`Unsupported storage backend: ${backend}`);
    }
  }

  static async set(key: string, value: string, backend: StorageBackend): Promise<void> {
    switch (backend) {
      case 'localStorage':
        localStorage.setItem(key, value);
        break;
      
      case 'sessionStorage':
        sessionStorage.setItem(key, value);
        break;
      
      case 'indexedDB':
        await this.setToIndexedDB(key, value);
        break;
      
      case 'memory':
        this.memoryStorage.set(key, value);
        break;
      
      default:
        throw new Error(`Unsupported storage backend: ${backend}`);
    }
  }

  static async remove(key: string, backend: StorageBackend): Promise<void> {
    switch (backend) {
      case 'localStorage':
        localStorage.removeItem(key);
        break;
      
      case 'sessionStorage':
        sessionStorage.removeItem(key);
        break;
      
      case 'indexedDB':
        await this.removeFromIndexedDB(key);
        break;
      
      case 'memory':
        this.memoryStorage.delete(key);
        break;
      
      default:
        throw new Error(`Unsupported storage backend: ${backend}`);
    }
  }

  private static memoryStorage = new Map<string, string>();

  private static async getFromIndexedDB(key: string): Promise<string | null> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('PersistedState', 1);
      
      request.onerror = () => reject(request.error);
      
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['state'], 'readonly');
        const store = transaction.objectStore('state');
        const getRequest = store.get(key);
        
        getRequest.onsuccess = () => {
          resolve(getRequest.result?.value || null);
        };
        
        getRequest.onerror = () => reject(getRequest.error);
      };
      
      request.onupgradeneeded = () => {
        const db = request.result;
        if (!db.objectStoreNames.contains('state')) {
          db.createObjectStore('state', { keyPath: 'key' });
        }
      };
    });
  }

  private static async setToIndexedDB(key: string, value: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('PersistedState', 1);
      
      request.onerror = () => reject(request.error);
      
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['state'], 'readwrite');
        const store = transaction.objectStore('state');
        const putRequest = store.put({ key, value });
        
        putRequest.onsuccess = () => resolve();
        putRequest.onerror = () => reject(putRequest.error);
      };
      
      request.onupgradeneeded = () => {
        const db = request.result;
        if (!db.objectStoreNames.contains('state')) {
          db.createObjectStore('state', { keyPath: 'key' });
        }
      };
    });
  }

  private static async removeFromIndexedDB(key: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('PersistedState', 1);
      
      request.onerror = () => reject(request.error);
      
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['state'], 'readwrite');
        const store = transaction.objectStore('state');
        const deleteRequest = store.delete(key);
        
        deleteRequest.onsuccess = () => resolve();
        deleteRequest.onerror = () => reject(deleteRequest.error);
      };
    });
  }
}

// Compression utilities
const compress = (data: string): string => {
  // Simple compression using LZ-string-like algorithm
  // In a real implementation, you'd use a proper compression library
  return btoa(data);
};

const decompress = (data: string): string => {
  try {
    return atob(data);
  } catch {
    return data; // Fallback for uncompressed data
  }
};

// Encryption utilities (basic implementation)
const encrypt = (data: string, key: string): string => {
  // Simple XOR encryption (use a proper encryption library in production)
  let result = '';
  for (let i = 0; i < data.length; i++) {
    result += String.fromCharCode(
      data.charCodeAt(i) ^ key.charCodeAt(i % key.length)
    );
  }
  return btoa(result);
};

const decrypt = (data: string, key: string): string => {
  try {
    const decoded = atob(data);
    let result = '';
    for (let i = 0; i < decoded.length; i++) {
      result += String.fromCharCode(
        decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length)
      );
    }
    return result;
  } catch {
    return data; // Fallback for unencrypted data
  }
};

export function usePersistedState<T>(
  options: PersistedStateOptions<T>
): PersistedStateReturn<T> {
  const {
    key,
    initialState,
    storage = 'localStorage',
    serialize = JSON.stringify,
    deserialize = JSON.parse,
    compress: shouldCompress = false,
    encryptionKey,
    migrate,
    version = 1,
    debounceDelay = 500,
    syncAcrossTabs = true,
    validate,
    onError
  } = options;

  const [state, setStateInternal] = useState<T>(initialState);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [error, setError] = useState<Error | null>(null);

  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const isInitializedRef = useRef(false);

  // Process data for storage
  const processForStorage = useCallback((data: T): string => {
    let processed = serialize(data);
    
    if (shouldCompress) {
      processed = compress(processed);
    }
    
    if (encryptionKey) {
      processed = encrypt(processed, encryptionKey);
    }
    
    return processed;
  }, [serialize, shouldCompress, encryptionKey]);

  // Process data from storage
  const processFromStorage = useCallback((data: string): T => {
    let processed = data;
    
    if (encryptionKey) {
      processed = decrypt(processed, encryptionKey);
    }
    
    if (shouldCompress) {
      processed = decompress(processed);
    }
    
    const parsed = deserialize(processed);
    
    // Apply migration if needed
    if (migrate && parsed && typeof parsed === 'object' && 'version' in parsed) {
      const persistedVersion = (parsed as any).version || 0;
      if (persistedVersion < version) {
        return migrate(parsed, persistedVersion);
      }
    }
    
    // Validate if validator provided
    if (validate && !validate(parsed)) {
      throw new Error('Persisted state validation failed');
    }
    
    return parsed;
  }, [deserialize, encryptionKey, shouldCompress, migrate, version, validate]);

  // Load state from storage
  const load = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const stored = await StorageAdapter.get(key, storage);
      
      if (stored) {
        const parsed = processFromStorage(stored);
        setStateInternal(parsed);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to load state');
      setError(error);
      onError?.(error, 'load');
    } finally {
      setIsLoading(false);
      isInitializedRef.current = true;
    }
  }, [key, storage, processFromStorage, onError]);

  // Save state to storage
  const save = useCallback(async (): Promise<void> => {
    if (!isInitializedRef.current) return;
    
    try {
      setIsSaving(true);
      setError(null);
      
      const dataToStore = {
        ...state,
        version,
        timestamp: new Date().toISOString()
      };
      
      const processed = processForStorage(dataToStore as T);
      await StorageAdapter.set(key, processed, storage);
      
      setLastSaved(new Date());
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to save state');
      setError(error);
      onError?.(error, 'save');
    } finally {
      setIsSaving(false);
    }
  }, [state, key, storage, version, processForStorage, onError]);

  // Clear state from storage
  const clear = useCallback(async (): Promise<void> => {
    try {
      await StorageAdapter.remove(key, storage);
      setLastSaved(null);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to clear state');
      setError(error);
      onError?.(error, 'save');
    }
  }, [key, storage, onError]);

  // Update state with debounced save
  const setState = useCallback((newState: T | ((prev: T) => T)) => {
    setStateInternal(prev => {
      const nextState = typeof newState === 'function' ? (newState as (prev: T) => T)(prev) : newState;
      
      // Debounce save
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      
      debounceTimeoutRef.current = setTimeout(() => {
        save();
      }, debounceDelay);
      
      return nextState;
    });
  }, [save, debounceDelay]);

  // Reset to initial state
  const reset = useCallback(() => {
    setStateInternal(initialState);
    clear();
  }, [initialState, clear]);

  // Cross-tab synchronization
  useEffect(() => {
    if (syncAcrossTabs && storage === 'localStorage') {
      const handleStorageChange = (e: StorageEvent) => {
        if (e.key === key && e.newValue) {
          try {
            const parsed = processFromStorage(e.newValue);
            setStateInternal(parsed);
          } catch (err) {
            console.error('Failed to sync state across tabs:', err);
          }
        }
      };

      window.addEventListener('storage', handleStorageChange);
      return () => window.removeEventListener('storage', handleStorageChange);
    }
    return undefined;
  }, [key, storage, syncAcrossTabs, processFromStorage]);

  // Load initial state
  useEffect(() => {
    load();
  }, [load]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    state,
    setState,
    status: {
      isLoading,
      isSaving,
      lastSaved,
      error
    },
    save,
    load,
    clear,
    reset
  };
}

// Convenience hooks
export function useLocalStorageState<T>(key: string, initialState: T) {
  return usePersistedState({
    key,
    initialState,
    storage: 'localStorage'
  });
}

export function useSessionStorageState<T>(key: string, initialState: T) {
  return usePersistedState({
    key,
    initialState,
    storage: 'sessionStorage'
  });
}

export function useIndexedDBState<T>(key: string, initialState: T) {
  return usePersistedState({
    key,
    initialState,
    storage: 'indexedDB'
  });
}
