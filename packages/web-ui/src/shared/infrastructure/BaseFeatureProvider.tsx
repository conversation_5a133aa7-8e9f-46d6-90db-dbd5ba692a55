/**
 * Base Feature Provider
 * 
 * Abstract base class for feature providers in the feature-slice architecture.
 * Provides common functionality for state management, error handling, and isolation.
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback, useMemo } from 'react';
import {
  FeatureProviderProps,
  FeatureAction,
  FeatureSliceState,
  FeatureReducer,
  FeatureIsolationContext,
  FeatureIsolationLevel
} from '../types/feature';
import { featureBus } from './FeatureBus';

// Base feature context interface
interface BaseFeatureContext<T = any> {
  state: FeatureSliceState<T>;
  dispatch: React.Dispatch<FeatureAction>;
  isolationContext: FeatureIsolationContext;
  error: Error | null;
  setError: (error: Error | null) => void;
}

// Base feature provider props
interface BaseFeatureProviderProps<T = any> extends FeatureProviderProps {
  featureName: string;
  initialState: T;
  reducer: FeatureReducer<T>;
  isolationLevel?: FeatureIsolationLevel;
  allowedGlobalAccess?: string[];
}

// Create base feature context
const createBaseFeatureContext = <T,>() => {
  return createContext<BaseFeatureContext<T> | null>(null);
};

// Base feature provider component
export function createBaseFeatureProvider<T>(
  featureName: string,
  initialState: T,
  reducer: FeatureReducer<T>,
  isolationLevel: FeatureIsolationLevel = FeatureIsolationLevel.PARTIAL
) {
  const FeatureContext = createBaseFeatureContext<T>();

  const BaseFeatureProvider: React.FC<BaseFeatureProviderProps<T>> = ({
    children,
    onError,
    allowedGlobalAccess = []
  }) => {
    // Initialize feature state
    const [state, dispatch] = useReducer(reducer, {
      data: initialState,
      meta: {
        isLoading: false,
        error: null,
        lastFetch: null,
        version: 0
      }
    });

    // Error state
    const [error, setErrorState] = React.useState<Error | null>(null);

    // Create isolation context
    const isolationContext = useMemo<FeatureIsolationContext>(() => ({
      featureName,
      isolationLevel,
      allowedGlobalAccess,
      communicationBus: featureBus.communicate
    }), [allowedGlobalAccess]);

    // Error handler
    const handleError = useCallback((err: Error | null) => {
      setErrorState(err);
      if (err) onError?.(err);
      
      // Publish error event
      if (err) {
        featureBus.communicate.publish({
          type: 'feature:error',
          payload: {
            featureName,
            error: err.message,
            stack: err.stack
          },
          timestamp: new Date(),
          source: featureName
        });
      }
    }, [onError]);

    // Enhanced dispatch with error handling and isolation
    const enhancedDispatch = useCallback((action: FeatureAction) => {
      try {
        // Add metadata to action
        const enhancedAction: FeatureAction = {
          ...action,
          meta: {
            timestamp: new Date(),
            source: featureName,
            correlationId: action.meta?.correlationId || `${featureName}-${Date.now()}`,
            ...action.meta
          }
        };

        // Check isolation policy for external actions
        if (action.meta?.source && action.meta.source !== featureName) {
          if (!featureBus.checkIsolationPolicy(action.meta.source, featureName)) {
            throw new Error(
              `Isolation policy violation: ${action.meta.source} cannot dispatch to ${featureName}`
            );
          }
        }

        dispatch(enhancedAction);

        // Publish state change event
        featureBus.communicate.publish({
          type: 'feature:state-changed',
          payload: {
            featureName,
            action: enhancedAction.type,
            timestamp: enhancedAction.meta?.timestamp
          },
          timestamp: new Date(),
          source: featureName
        });

      } catch (err) {
        handleError(err as Error);
      }
    }, [handleError]);

    // Subscribe to feature bus events
    useEffect(() => {
      const unsubscribers: Array<() => void> = [];

      // Subscribe to global feature events if allowed
      if (isolationLevel !== FeatureIsolationLevel.FULL) {
        const unsubscribe = featureBus.communicate.subscribe(
          'feature:global-state-changed',
          (event) => {
            // Only process if we have access to the source feature
            if (allowedGlobalAccess.includes(event.source)) {
              enhancedDispatch({
                type: 'EXTERNAL_STATE_CHANGED',
                payload: event.payload,
                meta: {
                  source: event.source,
                  timestamp: event.timestamp
                }
              });
            }
          }
        );
        unsubscribers.push(unsubscribe);
      }

      // Subscribe to feature-specific events
      const unsubscribeFeature = featureBus.communicate.subscribe(
        `feature:${featureName}:*`,
        (event) => {
          enhancedDispatch({
            type: 'FEATURE_EVENT',
            payload: event,
            meta: {
              source: 'feature-bus',
              timestamp: new Date()
            }
          });
        }
      );
      unsubscribers.push(unsubscribeFeature);

      return () => {
        unsubscribers.forEach(unsub => unsub());
      };
    }, [enhancedDispatch, allowedGlobalAccess, isolationLevel]);

    // Provide context value
    const contextValue = useMemo<BaseFeatureContext<T>>(() => ({
      state,
      dispatch: enhancedDispatch,
      isolationContext,
      error,
      setError: handleError
    }), [state, enhancedDispatch, isolationContext, error, handleError]);

    // Error boundary
    const ErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
      return (
        <React.Suspense fallback={<div>Loading {featureName}...</div>}>
          {children}
        </React.Suspense>
      );
    };

    return (
      <FeatureContext.Provider value={contextValue}>
        <ErrorBoundary>
          {children}
        </ErrorBoundary>
      </FeatureContext.Provider>
    );
  };

  // Hook to use the feature context
  const useFeatureContext = (): BaseFeatureContext<T> => {
    const context = useContext(FeatureContext);
    
    if (!context) {
      throw new Error(`useFeatureContext must be used within ${featureName}Provider`);
    }
    
    return context;
  };

  return {
    Provider: BaseFeatureProvider,
    useContext: useFeatureContext,
    Context: FeatureContext
  };
}

// Utility hook for feature isolation
export const useFeatureIsolation = () => {
  const [isolationContext, setIsolationContext] = React.useState<FeatureIsolationContext | null>(null);

  const checkAccess = useCallback((targetFeature: string): boolean => {
    if (!isolationContext) return false;
    
    return featureBus.checkIsolationPolicy(isolationContext.featureName, targetFeature);
  }, [isolationContext]);

  const requestFromFeature = useCallback(async <T,>(
    targetFeature: string,
    action: string,
    payload?: any
  ): Promise<T> => {
    if (!isolationContext) {
      throw new Error('Feature isolation context not available');
    }

    if (!checkAccess(targetFeature)) {
      throw new Error(`Access denied to feature ${targetFeature}`);
    }

    return isolationContext.communicationBus.request<T>(targetFeature, action, payload);
  }, [isolationContext, checkAccess]);

  return {
    isolationContext,
    setIsolationContext,
    checkAccess,
    requestFromFeature
  };
};

// Performance monitoring hook
export const useFeaturePerformance = (featureName: string) => {
  const [metrics, setMetrics] = React.useState({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0
  });

  const startTime = React.useRef<number>(0);

  React.useEffect(() => {
    startTime.current = performance.now();
  });

  React.useEffect(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTime.current;

    setMetrics(prev => ({
      renderCount: prev.renderCount + 1,
      lastRenderTime: renderTime,
      averageRenderTime: (prev.averageRenderTime * prev.renderCount + renderTime) / (prev.renderCount + 1)
    }));

    // Publish performance metrics
    featureBus.communicate.publish({
      type: 'feature:performance',
      payload: {
        featureName,
        renderTime,
        renderCount: metrics.renderCount + 1
      },
      timestamp: new Date(),
      source: featureName
    });
  });

  return metrics;
};
