/**
 * Feature Communication Bus
 * 
 * Central communication system for feature-slice architecture.
 * Enables isolated features to communicate safely without tight coupling.
 */

// Browser-compatible EventEmitter implementation
class BrowserEventEmitter {
  private events: Map<string, Array<(...args: any[]) => void>> = new Map();

  setMaxListeners(_n: number): void {
    // Note: This is for API compatibility, actual enforcement not implemented
  }

  on(event: string, listener: (...args: any[]) => void): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(listener);
  }

  off(event: string, listener: (...args: any[]) => void): void {
    const listeners = this.events.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event: string, ...args: any[]): void {
    const listeners = this.events.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(...args));
    }
  }

  removeAllListeners(event?: string): void {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }
}
import {
  FeatureBus,
  FeatureRegistryEntry,
  FeatureCommunication,
  FeatureEvent,
  FeatureIsolationLevel
} from '../types/feature';

class FeatureCommunicationImpl implements FeatureCommunication {
  private eventEmitter: BrowserEventEmitter;
  private requestHandlers: Map<string, (payload: any) => Promise<any>>;

  constructor() {
    this.eventEmitter = new BrowserEventEmitter();
    this.requestHandlers = new Map();

    // Set max listeners to prevent memory leak warnings
    this.eventEmitter.setMaxListeners(100);
  }

  subscribe(eventType: string, handler: (event: FeatureEvent) => void): () => void {
    this.eventEmitter.on(eventType, handler);
    
    return () => {
      this.eventEmitter.off(eventType, handler);
    };
  }

  publish(event: FeatureEvent): void {
    // Add timestamp if not provided
    if (!event.timestamp) {
      event.timestamp = new Date();
    }

    // Emit the event
    this.eventEmitter.emit(event.type, event);
    
    // Also emit to wildcard listeners
    this.eventEmitter.emit('*', event);
  }

  async request<T>(target: string, action: string, payload?: any): Promise<T> {
    const requestKey = `${target}:${action}`;
    const handler = this.requestHandlers.get(requestKey);
    
    if (!handler) {
      throw new Error(`No handler registered for ${requestKey}`);
    }

    try {
      return await handler(payload);
    } catch (error) {
      throw new Error(`Request to ${requestKey} failed: ${(error as Error).message}`);
    }
  }

  registerRequestHandler(target: string, action: string, handler: (payload: any) => Promise<any>): void {
    const requestKey = `${target}:${action}`;
    this.requestHandlers.set(requestKey, handler);
  }

  unregisterRequestHandler(target: string, action: string): void {
    const requestKey = `${target}:${action}`;
    this.requestHandlers.delete(requestKey);
  }

  cleanup(): void {
    this.eventEmitter.removeAllListeners();
    this.requestHandlers.clear();
  }
}

export class FeatureBusImpl implements FeatureBus {
  private features: Map<string, FeatureRegistryEntry>;
  private _communicate: FeatureCommunicationImpl;
  private isolationPolicies: Map<string, Set<string>>;

  constructor() {
    this.features = new Map();
    this._communicate = new FeatureCommunicationImpl();
    this.isolationPolicies = new Map();
  }

  get communicate(): FeatureCommunication {
    return this._communicate;
  }

  register(feature: FeatureRegistryEntry): void {
    const { name } = feature.metadata;
    
    if (this.features.has(name)) {
      throw new Error(`Feature ${name} is already registered`);
    }

    // Validate dependencies
    this.validateDependencies(feature);
    
    // Set up isolation policies
    this.setupIsolationPolicies(feature);
    
    // Register the feature
    this.features.set(name, feature);
    
    // Set up request handlers for feature services
    this.setupServiceHandlers(feature);
    
    // Publish feature registration event
    this._communicate.publish({
      type: 'feature:registered',
      payload: { name, metadata: feature.metadata },
      timestamp: new Date(),
      source: 'feature-bus'
    });

    console.log(`Feature ${name} registered successfully`);
  }

  unregister(featureName: string): void {
    const feature = this.features.get(featureName);
    
    if (!feature) {
      throw new Error(`Feature ${featureName} is not registered`);
    }

    // Clean up service handlers
    this.cleanupServiceHandlers(feature);
    
    // Remove isolation policies
    this.isolationPolicies.delete(featureName);
    
    // Remove from registry
    this.features.delete(featureName);
    
    // Publish feature unregistration event
    this._communicate.publish({
      type: 'feature:unregistered',
      payload: { name: featureName },
      timestamp: new Date(),
      source: 'feature-bus'
    });

    console.log(`Feature ${featureName} unregistered successfully`);
  }

  getFeature(name: string): FeatureRegistryEntry | null {
    return this.features.get(name) || null;
  }

  listFeatures(): FeatureRegistryEntry[] {
    return Array.from(this.features.values());
  }

  private validateDependencies(feature: FeatureRegistryEntry): void {
    const { dependencies } = feature.metadata;
    
    for (const dependency of dependencies) {
      if (dependency.required && !this.features.has(dependency.name)) {
        throw new Error(
          `Required dependency ${dependency.name} is not available for feature ${feature.metadata.name}`
        );
      }
    }
  }

  private setupIsolationPolicies(feature: FeatureRegistryEntry): void {
    const { name, isolationLevel, dependencies } = feature.metadata;
    const allowedFeatures = new Set<string>();
    
    // Add self
    allowedFeatures.add(name);
    
    // Add dependencies based on isolation level
    if (isolationLevel !== FeatureIsolationLevel.FULL) {
      dependencies.forEach(dep => {
        if (dep.isolationLevel !== FeatureIsolationLevel.FULL) {
          allowedFeatures.add(dep.name);
        }
      });
    }
    
    this.isolationPolicies.set(name, allowedFeatures);
  }

  private setupServiceHandlers(feature: FeatureRegistryEntry): void {
    const { metadata, services } = feature;
    const name = metadata.name;
    
    Object.entries(services).forEach(([serviceName, service]) => {
      // Register common service methods
      this._communicate.registerRequestHandler(
        name,
        `${serviceName}:getState`,
        async () => service.getState()
      );
      
      this._communicate.registerRequestHandler(
        name,
        `${serviceName}:setState`,
        async (state) => service.setState(state)
      );
    });
  }

  private cleanupServiceHandlers(feature: FeatureRegistryEntry): void {
    const { metadata, services } = feature;
    const name = metadata.name;
    
    Object.keys(services).forEach(serviceName => {
      this._communicate.unregisterRequestHandler(name, `${serviceName}:getState`);
      this._communicate.unregisterRequestHandler(name, `${serviceName}:setState`);
    });
  }

  checkIsolationPolicy(sourceFeature: string, targetFeature: string): boolean {
    const allowedFeatures = this.isolationPolicies.get(sourceFeature);
    
    if (!allowedFeatures) {
      return false;
    }
    
    return allowedFeatures.has(targetFeature);
  }

  getFeatureHealth(): Record<string, any> {
    const health: Record<string, any> = {};
    
    this.features.forEach((feature, name) => {
      health[name] = {
        registered: true,
        dependencies: feature.metadata.dependencies.map(dep => ({
          name: dep.name,
          available: this.features.has(dep.name),
          required: dep.required
        })),
        isolationLevel: feature.metadata.isolationLevel,
        serviceCount: Object.keys(feature.services).length,
        componentCount: Object.keys(feature.components).length
      };
    });
    
    return health;
  }

  cleanup(): void {
    // Unregister all features
    const featureNames = Array.from(this.features.keys());
    featureNames.forEach(name => {
      try {
        this.unregister(name);
      } catch (error) {
        console.error(`Error unregistering feature ${name}:`, error);
      }
    });
    
    // Clean up communication
    this._communicate.cleanup();
    
    // Clear isolation policies
    this.isolationPolicies.clear();
  }
}

// Singleton instance
export const featureBus = new FeatureBusImpl();

// React hook for accessing the feature bus
export const useFeatureBus = () => {
  return featureBus;
};

// React hook for feature communication
export const useFeatureCommunication = () => {
  return featureBus.communicate;
};
