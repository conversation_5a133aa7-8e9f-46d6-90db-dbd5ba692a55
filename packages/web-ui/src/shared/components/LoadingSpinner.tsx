/**
 * Loading Spinner Component
 *
 * Reusable loading spinner with different sizes, variants, and accessibility features.
 */

import React from 'react';
import { designTokens } from './design-system/tokens';

interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars';
  color?: string;
  className?: string;
  label?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'spinner',
  color = designTokens.colors.primary[600],
  className = '',
  label = 'Loading...'
}) => {
  const sizeMap = {
    xs: '12px',
    sm: '16px',
    md: '24px',
    lg: '32px',
    xl: '48px'
  };

  const spinnerSize = sizeMap[size];

  const renderSpinner = () => {
    switch (variant) {
      case 'spinner':
        return (
          <div
            style={{
              width: spinnerSize,
              height: spinnerSize,
              border: `2px solid ${color}20`,
              borderTop: `2px solid ${color}`,
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }}
          />
        );

      case 'dots':
        return (
          <div
            style={{
              display: 'flex',
              gap: '4px',
              alignItems: 'center'
            }}
          >
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                style={{
                  width: `calc(${spinnerSize} / 3)`,
                  height: `calc(${spinnerSize} / 3)`,
                  backgroundColor: color,
                  borderRadius: '50%',
                  animation: `pulse 1.4s ease-in-out ${i * 0.16}s infinite both`
                }}
              />
            ))}
          </div>
        );

      case 'pulse':
        return (
          <div
            style={{
              width: spinnerSize,
              height: spinnerSize,
              backgroundColor: color,
              borderRadius: '50%',
              animation: 'pulse-scale 1s ease-in-out infinite'
            }}
          />
        );

      case 'bars':
        return (
          <div
            style={{
              display: 'flex',
              gap: '2px',
              alignItems: 'end',
              height: spinnerSize
            }}
          >
            {[0, 1, 2, 3].map((i) => (
              <div
                key={i}
                style={{
                  width: `calc(${spinnerSize} / 6)`,
                  backgroundColor: color,
                  animation: `bars 1.2s ease-in-out ${i * 0.1}s infinite`
                }}
              />
            ))}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div
      className={`loading-spinner ${className}`}
      role="status"
      aria-label={label}
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      {renderSpinner()}
      <span className="sr-only">{label}</span>

      {/* CSS animations */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
          0%, 80%, 100% {
            transform: scale(0);
            opacity: 0.5;
          }
          40% {
            transform: scale(1);
            opacity: 1;
          }
        }

        @keyframes pulse-scale {
          0%, 100% {
            transform: scale(1);
            opacity: 1;
          }
          50% {
            transform: scale(0.8);
            opacity: 0.5;
          }
        }

        @keyframes bars {
          0%, 40%, 100% {
            height: 20%;
          }
          20% {
            height: 100%;
          }
        }

        .sr-only {
          position: absolute;
          width: 1px;
          height: 1px;
          padding: 0;
          margin: -1px;
          overflow: hidden;
          clip: rect(0, 0, 0, 0);
          white-space: nowrap;
          border: 0;
        }
      `}</style>
    </div>
  );
};
