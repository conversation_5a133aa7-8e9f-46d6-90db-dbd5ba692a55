/**
 * Input Component
 * 
 * Reusable input component with consistent styling and accessibility features.
 */

import React, { forwardRef, useState } from 'react';
import { designTokens } from '../design-system/tokens';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  error?: string;
  helperText?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outline';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isLoading?: boolean;
  fullWidth?: boolean;
}

const getInputStyles = (
  variant: InputProps['variant'] = 'default',
  size: InputProps['size'] = 'md',
  hasError: boolean = false,
  isFocused: boolean = false,
  disabled: boolean = false,
  fullWidth: boolean = false
) => {
  const baseStyles = {
    fontFamily: designTokens.typography.fontFamily.sans.join(', '),
    borderRadius: designTokens.borderRadius.md,
    border: '1px solid',
    transition: designTokens.transition.all,
    outline: 'none',
    width: fullWidth ? '100%' : 'auto',
    backgroundColor: disabled ? designTokens.colors.neutral[100] : 'white'
  };

  // Size variants
  const sizeStyles = {
    sm: {
      fontSize: designTokens.typography.fontSize.sm,
      padding: `${designTokens.spacing[2]} ${designTokens.spacing[3]}`,
      minHeight: '32px'
    },
    md: {
      fontSize: designTokens.typography.fontSize.base,
      padding: `${designTokens.spacing[3]} ${designTokens.spacing[4]}`,
      minHeight: '40px'
    },
    lg: {
      fontSize: designTokens.typography.fontSize.lg,
      padding: `${designTokens.spacing[4]} ${designTokens.spacing[5]}`,
      minHeight: '48px'
    }
  };

  // Variant styles
  const variantStyles = {
    default: {
      borderColor: hasError 
        ? designTokens.colors.error[400]
        : isFocused 
          ? designTokens.colors.primary[400]
          : designTokens.colors.neutral[300],
      boxShadow: isFocused 
        ? hasError
          ? `0 0 0 3px ${designTokens.colors.error[200]}`
          : `0 0 0 3px ${designTokens.colors.primary[200]}`
        : 'none'
    },
    filled: {
      backgroundColor: disabled 
        ? designTokens.colors.neutral[100]
        : designTokens.colors.neutral[50],
      borderColor: hasError 
        ? designTokens.colors.error[400]
        : 'transparent',
      boxShadow: isFocused 
        ? hasError
          ? `0 0 0 3px ${designTokens.colors.error[200]}`
          : `0 0 0 3px ${designTokens.colors.primary[200]}`
        : 'none'
    },
    outline: {
      backgroundColor: 'transparent',
      borderColor: hasError 
        ? designTokens.colors.error[400]
        : isFocused 
          ? designTokens.colors.primary[400]
          : designTokens.colors.neutral[400],
      borderWidth: '2px',
      boxShadow: 'none'
    }
  };

  return {
    ...baseStyles,
    ...sizeStyles[size],
    ...variantStyles[variant]
  };
};

const getLabelStyles = (size: InputProps['size'] = 'md', hasError: boolean = false) => ({
  display: 'block',
  fontSize: size === 'sm' ? designTokens.typography.fontSize.xs : designTokens.typography.fontSize.sm,
  fontWeight: designTokens.typography.fontWeight.medium,
  color: hasError ? designTokens.colors.error[700] : designTokens.colors.neutral[700],
  marginBottom: designTokens.spacing[1]
});

const getHelperTextStyles = (hasError: boolean = false) => ({
  fontSize: designTokens.typography.fontSize.xs,
  color: hasError ? designTokens.colors.error[600] : designTokens.colors.neutral[500],
  marginTop: designTokens.spacing[1]
});

const getIconContainerStyles = (position: 'left' | 'right', size: InputProps['size'] = 'md') => {
  const iconSize = {
    sm: '16px',
    md: '18px',
    lg: '20px'
  };

  const padding = {
    sm: designTokens.spacing[2],
    md: designTokens.spacing[3],
    lg: designTokens.spacing[4]
  };

  return {
    position: 'absolute' as const,
    top: '50%',
    transform: 'translateY(-50%)',
    [position]: padding[size],
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: iconSize[size],
    height: iconSize[size],
    color: designTokens.colors.neutral[500],
    pointerEvents: 'none' as const
  };
};

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      helperText,
      size = 'md',
      variant = 'default',
      leftIcon,
      rightIcon,
      isLoading = false,
      fullWidth = false,
      disabled = false,
      className = '',
      style,
      onFocus,
      onBlur,
      id,
      ...props
    },
    ref
  ) => {
    const [isFocused, setIsFocused] = useState(false);
    const hasError = Boolean(error);
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

    const inputStyles = getInputStyles(variant, size, hasError, isFocused, disabled, fullWidth);
    const labelStyles = getLabelStyles(size, hasError);
    const helperTextStyles = getHelperTextStyles(hasError);

    // Adjust padding for icons
    const paddingAdjustment = {
      sm: '32px',
      md: '36px',
      lg: '40px'
    };

    const inputStylesWithIcons = {
      ...inputStyles,
      paddingLeft: leftIcon ? paddingAdjustment[size] : inputStyles.padding?.split(' ')[1] || designTokens.spacing[3],
      paddingRight: (rightIcon || isLoading) ? paddingAdjustment[size] : inputStyles.padding?.split(' ')[1] || designTokens.spacing[3]
    };

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      onBlur?.(e);
    };

    const LoadingSpinner = () => (
      <div
        style={{
          width: '14px',
          height: '14px',
          border: '2px solid currentColor',
          borderTop: '2px solid transparent',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}
      />
    );

    return (
      <div style={{ width: fullWidth ? '100%' : 'auto' }}>
        {label && (
          <label htmlFor={inputId} style={labelStyles}>
            {label}
          </label>
        )}
        
        <div style={{ position: 'relative', display: 'inline-block', width: fullWidth ? '100%' : 'auto' }}>
          {leftIcon && (
            <div style={getIconContainerStyles('left', size)}>
              {leftIcon}
            </div>
          )}
          
          <input
            ref={ref}
            id={inputId}
            disabled={disabled}
            className={className}
            style={{
              ...inputStylesWithIcons,
              ...style
            }}
            onFocus={handleFocus}
            onBlur={handleBlur}
            aria-invalid={hasError}
            aria-describedby={
              error ? `${inputId}-error` : 
              helperText ? `${inputId}-helper` : 
              undefined
            }
            {...props}
          />
          
          {(rightIcon || isLoading) && (
            <div style={getIconContainerStyles('right', size)}>
              {isLoading ? <LoadingSpinner /> : rightIcon}
            </div>
          )}
        </div>
        
        {(error || helperText) && (
          <div
            id={error ? `${inputId}-error` : `${inputId}-helper`}
            style={helperTextStyles}
            role={error ? 'alert' : undefined}
          >
            {error || helperText}
          </div>
        )}

        {/* CSS for spinner animation */}
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }
);

Input.displayName = 'Input';
