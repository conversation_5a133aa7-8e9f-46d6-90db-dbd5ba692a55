/**
 * Modal Component
 * 
 * Accessible modal component with backdrop, focus management, and keyboard navigation.
 */

import React, { useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { designTokens } from '../design-system/tokens';

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnBackdropClick?: boolean;
  closeOnEscape?: boolean;
  showCloseButton?: boolean;
  className?: string;
  overlayClassName?: string;
  contentClassName?: string;
}

const getModalStyles = (size: ModalProps['size'] = 'md') => {
  const sizeStyles = {
    sm: {
      maxWidth: '400px',
      width: '90vw'
    },
    md: {
      maxWidth: '500px',
      width: '90vw'
    },
    lg: {
      maxWidth: '800px',
      width: '90vw'
    },
    xl: {
      maxWidth: '1200px',
      width: '95vw'
    },
    full: {
      width: '100vw',
      height: '100vh',
      maxWidth: 'none',
      maxHeight: 'none',
      borderRadius: '0'
    }
  };

  const baseStyles = {
    backgroundColor: 'white',
    borderRadius: size === 'full' ? '0' : designTokens.borderRadius.lg,
    boxShadow: designTokens.boxShadow['2xl'],
    maxHeight: size === 'full' ? '100vh' : '90vh',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column' as const,
    position: 'relative' as const
  };

  return {
    ...baseStyles,
    ...sizeStyles[size]
  };
};

const overlayStyles = {
  position: 'fixed' as const,
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  padding: designTokens.spacing[4],
  zIndex: designTokens.zIndex.modal,
  backdropFilter: 'blur(4px)'
};

const headerStyles = {
  padding: `${designTokens.spacing[6]} ${designTokens.spacing[6]} ${designTokens.spacing[4]}`,
  borderBottom: `1px solid ${designTokens.colors.neutral[200]}`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between'
};

const titleStyles = {
  fontSize: designTokens.typography.fontSize.xl,
  fontWeight: designTokens.typography.fontWeight.semibold,
  color: designTokens.colors.neutral[900],
  margin: 0
};

const closeButtonStyles = {
  background: 'none',
  border: 'none',
  fontSize: designTokens.typography.fontSize.xl,
  color: designTokens.colors.neutral[500],
  cursor: 'pointer',
  padding: designTokens.spacing[2],
  borderRadius: designTokens.borderRadius.md,
  transition: designTokens.transition.colors,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '32px',
  height: '32px'
};

const contentStyles = {
  padding: designTokens.spacing[6],
  flex: 1,
  overflow: 'auto'
};

// Focus trap hook
const useFocusTrap = (isOpen: boolean, containerRef: React.RefObject<HTMLElement>) => {
  useEffect(() => {
    if (!isOpen || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }, [isOpen, containerRef]);
};

// Body scroll lock hook
const useBodyScrollLock = (isOpen: boolean) => {
  useEffect(() => {
    if (isOpen) {
      const originalStyle = window.getComputedStyle(document.body).overflow;
      document.body.style.overflow = 'hidden';

      return () => {
        document.body.style.overflow = originalStyle;
      };
    }
    return undefined;
  }, [isOpen]);
};

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  closeOnBackdropClick = true,
  closeOnEscape = true,
  showCloseButton = true,
  className = '',
  overlayClassName = '',
  contentClassName = ''
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);

  // Store the previously focused element
  useEffect(() => {
    if (isOpen) {
      previousActiveElement.current = document.activeElement as HTMLElement;
    }
  }, [isOpen]);

  // Restore focus when modal closes
  useEffect(() => {
    if (!isOpen && previousActiveElement.current) {
      previousActiveElement.current.focus();
      previousActiveElement.current = null;
    }
  }, [isOpen]);

  // Focus trap and body scroll lock
  useFocusTrap(isOpen, modalRef);
  useBodyScrollLock(isOpen);

  // Handle escape key
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onClose]);

  // Handle backdrop click
  const handleBackdropClick = useCallback((e: React.MouseEvent) => {
    if (closeOnBackdropClick && e.target === e.currentTarget) {
      onClose();
    }
  }, [closeOnBackdropClick, onClose]);

  // Handle close button click
  const handleCloseClick = useCallback(() => {
    onClose();
  }, [onClose]);

  if (!isOpen) return null;

  const modalStyles = getModalStyles(size);

  const modalContent = (
    <div
      className={`modal-overlay ${overlayClassName}`}
      style={overlayStyles}
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby={title ? 'modal-title' : undefined}
    >
      <div
        ref={modalRef}
        className={`modal-content ${className}`}
        style={modalStyles}
        onClick={(e) => e.stopPropagation()}
      >
        {(title || showCloseButton) && (
          <div className="modal-header" style={headerStyles}>
            {title && (
              <h2 id="modal-title" style={titleStyles}>
                {title}
              </h2>
            )}
            {showCloseButton && (
              <button
                type="button"
                onClick={handleCloseClick}
                style={closeButtonStyles}
                aria-label="Close modal"
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = designTokens.colors.neutral[100];
                  e.currentTarget.style.color = designTokens.colors.neutral[700];
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = designTokens.colors.neutral[500];
                }}
              >
                ✕
              </button>
            )}
          </div>
        )}
        
        <div className={`modal-body ${contentClassName}`} style={contentStyles}>
          {children}
        </div>
      </div>
    </div>
  );

  // Render modal in portal
  return createPortal(modalContent, document.body);
};

// Modal hook for easier usage
export const useModal = (initialState = false) => {
  const [isOpen, setIsOpen] = React.useState(initialState);

  const open = useCallback(() => setIsOpen(true), []);
  const close = useCallback(() => setIsOpen(false), []);
  const toggle = useCallback(() => setIsOpen(prev => !prev), []);

  return {
    isOpen,
    open,
    close,
    toggle
  };
};
