/**
 * Card Component
 * 
 * Flexible card component for content organization with consistent styling.
 */

import React, { forwardRef } from 'react';
import { designTokens } from '../design-system/tokens';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'outlined' | 'elevated' | 'filled';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  radius?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  interactive?: boolean;
  children: React.ReactNode;
}

export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export interface CardBodyProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

const getCardStyles = (
  variant: CardProps['variant'] = 'default',
  padding: CardProps['padding'] = 'md',
  radius: CardProps['radius'] = 'md',
  shadow: CardProps['shadow'] = 'sm',
  hover: boolean = false,
  interactive: boolean = false,
  isHovered: boolean = false
) => {
  const baseStyles = {
    display: 'block',
    width: '100%',
    transition: designTokens.transition.all,
    cursor: interactive ? 'pointer' : 'default',
    outline: 'none'
  };

  // Padding variants
  const paddingStyles = {
    none: { padding: '0' },
    sm: { padding: designTokens.spacing[3] },
    md: { padding: designTokens.spacing[4] },
    lg: { padding: designTokens.spacing[6] }
  };

  // Border radius variants
  const radiusStyles = {
    none: { borderRadius: '0' },
    sm: { borderRadius: designTokens.borderRadius.sm },
    md: { borderRadius: designTokens.borderRadius.md },
    lg: { borderRadius: designTokens.borderRadius.lg },
    xl: { borderRadius: designTokens.borderRadius.xl }
  };

  // Shadow variants
  const shadowStyles = {
    none: { boxShadow: 'none' },
    sm: { boxShadow: designTokens.boxShadow.sm },
    md: { boxShadow: designTokens.boxShadow.md },
    lg: { boxShadow: designTokens.boxShadow.lg },
    xl: { boxShadow: designTokens.boxShadow.xl }
  };

  // Variant styles
  const variantStyles = {
    default: {
      backgroundColor: 'white',
      border: `1px solid ${designTokens.colors.neutral[200]}`
    },
    outlined: {
      backgroundColor: 'transparent',
      border: `1px solid ${designTokens.colors.neutral[300]}`
    },
    elevated: {
      backgroundColor: 'white',
      border: 'none'
    },
    filled: {
      backgroundColor: designTokens.colors.neutral[50],
      border: 'none'
    }
  };

  // Hover effects
  const hoverStyles = hover && isHovered ? {
    transform: 'translateY(-2px)',
    boxShadow: shadow === 'none' ? designTokens.boxShadow.md : 
               shadow === 'sm' ? designTokens.boxShadow.lg :
               shadow === 'md' ? designTokens.boxShadow.xl :
               designTokens.boxShadow['2xl']
  } : {};

  // Interactive focus styles
  const focusStyles = interactive ? {
    ':focus': {
      outline: `2px solid ${designTokens.colors.primary[500]}`,
      outlineOffset: '2px'
    }
  } : {};

  return {
    ...baseStyles,
    ...paddingStyles[padding],
    ...radiusStyles[radius],
    ...shadowStyles[shadow],
    ...variantStyles[variant],
    ...hoverStyles,
    ...focusStyles
  };
};

const getCardSectionStyles = (section: 'header' | 'body' | 'footer') => {
  const baseStyles = {
    width: '100%'
  };

  const sectionStyles = {
    header: {
      borderBottom: `1px solid ${designTokens.colors.neutral[200]}`,
      paddingBottom: designTokens.spacing[3],
      marginBottom: designTokens.spacing[4]
    },
    body: {
      flex: 1
    },
    footer: {
      borderTop: `1px solid ${designTokens.colors.neutral[200]}`,
      paddingTop: designTokens.spacing[3],
      marginTop: designTokens.spacing[4]
    }
  };

  return {
    ...baseStyles,
    ...sectionStyles[section]
  };
};

export const Card = forwardRef<HTMLDivElement, CardProps>(
  (
    {
      variant = 'default',
      padding = 'md',
      radius = 'md',
      shadow = 'sm',
      hover = false,
      interactive = false,
      children,
      className = '',
      style,
      onMouseEnter,
      onMouseLeave,
      onFocus,
      onBlur,
      tabIndex,
      ...props
    },
    ref
  ) => {
    const [isHovered, setIsHovered] = React.useState(false);
    const [isFocused, setIsFocused] = React.useState(false);

    const cardStyles = getCardStyles(variant, padding, radius, shadow, hover, interactive, isHovered || isFocused);

    const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
      setIsHovered(true);
      onMouseEnter?.(e);
    };

    const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
      setIsHovered(false);
      onMouseLeave?.(e);
    };

    const handleFocus = (e: React.FocusEvent<HTMLDivElement>) => {
      setIsFocused(true);
      onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLDivElement>) => {
      setIsFocused(false);
      onBlur?.(e);
    };

    return (
      <div
        ref={ref}
        className={`card ${className}`}
        style={{
          ...cardStyles,
          ...style
        }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onFocus={interactive ? handleFocus : onFocus}
        onBlur={interactive ? handleBlur : onBlur}
        tabIndex={interactive ? (tabIndex ?? 0) : tabIndex}
        role={interactive ? 'button' : undefined}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

export const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ children, className = '', style, ...props }, ref) => {
    const headerStyles = getCardSectionStyles('header');

    return (
      <div
        ref={ref}
        className={`card-header ${className}`}
        style={{
          ...headerStyles,
          ...style
        }}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardHeader.displayName = 'CardHeader';

export const CardBody = forwardRef<HTMLDivElement, CardBodyProps>(
  ({ children, className = '', style, ...props }, ref) => {
    const bodyStyles = getCardSectionStyles('body');

    return (
      <div
        ref={ref}
        className={`card-body ${className}`}
        style={{
          ...bodyStyles,
          ...style
        }}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardBody.displayName = 'CardBody';

export const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
  ({ children, className = '', style, ...props }, ref) => {
    const footerStyles = getCardSectionStyles('footer');

    return (
      <div
        ref={ref}
        className={`card-footer ${className}`}
        style={{
          ...footerStyles,
          ...style
        }}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardFooter.displayName = 'CardFooter';
