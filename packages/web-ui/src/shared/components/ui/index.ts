/**
 * UI Components Index
 * 
 * Central export point for all reusable UI components.
 */

// Design System
export { designTokens } from '../design-system/tokens';
export type {
  ColorScale,
  ColorToken,
  SpacingToken,
  FontSizeToken,
  FontWeightToken,
  BorderRadiusToken,
  ShadowToken,
  ZIndexToken,
  BreakpointToken,
  TransitionToken
} from '../design-system/tokens';

// Core Components
export { Button } from './Button';
export type { ButtonProps } from './Button';

export { Input } from './Input';
export type { InputProps } from './Input';

export { Modal, useModal } from './Modal';
export type { ModalProps } from './Modal';

export { Card, CardHeader, CardBody, CardFooter } from './Card';
export type { CardProps, CardHeaderProps, CardBodyProps, CardFooterProps } from './Card';

export { Badge, DotBadge, NumberBadge } from './Badge';
export type { BadgeProps, DotBadgeProps, NumberBadgeProps } from './Badge';

// Utility Components
export { LoadingSpinner } from '../LoadingSpinner';
export { ErrorBoundary } from '../ErrorBoundary';
