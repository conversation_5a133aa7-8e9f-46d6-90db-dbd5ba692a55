/**
 * Badge Component
 * 
 * Small status indicators and labels with consistent styling.
 */

import React, { forwardRef } from 'react';
import { designTokens } from '../design-system/tokens';

export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  shape?: 'rounded' | 'pill' | 'square';
  children: React.ReactNode;
  icon?: React.ReactNode;
  removable?: boolean;
  onRemove?: () => void;
}

const getBadgeStyles = (
  variant: BadgeProps['variant'] = 'default',
  size: BadgeProps['size'] = 'md',
  shape: BadgeProps['shape'] = 'rounded'
) => {
  const baseStyles = {
    display: 'inline-flex',
    alignItems: 'center',
    gap: designTokens.spacing[1],
    fontFamily: designTokens.typography.fontFamily.sans.join(', '),
    fontWeight: designTokens.typography.fontWeight.medium,
    lineHeight: designTokens.typography.lineHeight.none,
    border: '1px solid transparent',
    whiteSpace: 'nowrap' as const,
    verticalAlign: 'middle'
  };

  // Size variants
  const sizeStyles = {
    sm: {
      fontSize: designTokens.typography.fontSize.xs,
      padding: `${designTokens.spacing[1]} ${designTokens.spacing[2]}`,
      minHeight: '20px'
    },
    md: {
      fontSize: designTokens.typography.fontSize.sm,
      padding: `${designTokens.spacing[1]} ${designTokens.spacing[3]}`,
      minHeight: '24px'
    },
    lg: {
      fontSize: designTokens.typography.fontSize.base,
      padding: `${designTokens.spacing[2]} ${designTokens.spacing[4]}`,
      minHeight: '32px'
    }
  };

  // Shape variants
  const shapeStyles = {
    rounded: {
      borderRadius: designTokens.borderRadius.md
    },
    pill: {
      borderRadius: designTokens.borderRadius.full
    },
    square: {
      borderRadius: designTokens.borderRadius.sm
    }
  };

  // Color variants
  const variantStyles = {
    default: {
      backgroundColor: designTokens.colors.neutral[100],
      color: designTokens.colors.neutral[800],
      borderColor: designTokens.colors.neutral[200]
    },
    primary: {
      backgroundColor: designTokens.colors.primary[100],
      color: designTokens.colors.primary[800],
      borderColor: designTokens.colors.primary[200]
    },
    secondary: {
      backgroundColor: designTokens.colors.secondary[100],
      color: designTokens.colors.secondary[800],
      borderColor: designTokens.colors.secondary[200]
    },
    success: {
      backgroundColor: designTokens.colors.success[100],
      color: designTokens.colors.success[800],
      borderColor: designTokens.colors.success[200]
    },
    warning: {
      backgroundColor: designTokens.colors.warning[100],
      color: designTokens.colors.warning[800],
      borderColor: designTokens.colors.warning[200]
    },
    error: {
      backgroundColor: designTokens.colors.error[100],
      color: designTokens.colors.error[800],
      borderColor: designTokens.colors.error[200]
    },
    outline: {
      backgroundColor: 'transparent',
      color: designTokens.colors.neutral[700],
      borderColor: designTokens.colors.neutral[300]
    }
  };

  return {
    ...baseStyles,
    ...sizeStyles[size],
    ...shapeStyles[shape],
    ...variantStyles[variant]
  };
};

const getRemoveButtonStyles = (size: BadgeProps['size'] = 'md') => {
  const sizeMap = {
    sm: '14px',
    md: '16px',
    lg: '18px'
  };

  return {
    background: 'none',
    border: 'none',
    color: 'currentColor',
    cursor: 'pointer',
    padding: '0',
    margin: '0',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: sizeMap[size],
    height: sizeMap[size],
    borderRadius: '50%',
    fontSize: '12px',
    opacity: 0.7,
    transition: designTokens.transition.all,
    ':hover': {
      opacity: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.1)'
    }
  };
};

export const Badge = forwardRef<HTMLSpanElement, BadgeProps>(
  (
    {
      variant = 'default',
      size = 'md',
      shape = 'rounded',
      children,
      icon,
      removable = false,
      onRemove,
      className = '',
      style,
      ...props
    },
    ref
  ) => {
    const [isHovered, setIsHovered] = React.useState(false);
    const badgeStyles = getBadgeStyles(variant, size, shape);
    const removeButtonStyles = getRemoveButtonStyles(size);

    const handleRemove = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      onRemove?.();
    };

    const handleRemoveKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        e.stopPropagation();
        onRemove?.();
      }
    };

    return (
      <span
        ref={ref}
        className={`badge ${className}`}
        style={{
          ...badgeStyles,
          ...style
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        {...props}
      >
        {icon && (
          <span className="badge-icon" style={{ display: 'flex', alignItems: 'center' }}>
            {icon}
          </span>
        )}
        
        <span className="badge-content">{children}</span>
        
        {removable && (
          <button
            type="button"
            className="badge-remove"
            style={{
              ...removeButtonStyles,
              ...(isHovered ? removeButtonStyles[':hover'] : {})
            }}
            onClick={handleRemove}
            onKeyDown={handleRemoveKeyDown}
            aria-label="Remove"
            tabIndex={0}
          >
            ✕
          </button>
        )}
      </span>
    );
  }
);

Badge.displayName = 'Badge';

// Dot Badge for simple status indicators
export interface DotBadgeProps extends Omit<BadgeProps, 'children' | 'icon' | 'removable'> {
  status?: 'online' | 'offline' | 'busy' | 'away';
}

export const DotBadge = forwardRef<HTMLSpanElement, DotBadgeProps>(
  (
    {
      variant,
      status = 'online',
      size = 'md',
      className = '',
      style,
      ...props
    },
    ref
  ) => {
    // Map status to variant if variant not provided
    const statusVariantMap = {
      online: 'success',
      offline: 'default',
      busy: 'error',
      away: 'warning'
    } as const;

    const effectiveVariant = variant || statusVariantMap[status];

    const dotSize = {
      sm: '6px',
      md: '8px',
      lg: '10px'
    };

    return (
      <Badge
        ref={ref}
        variant={effectiveVariant}
        size={size}
        shape="pill"
        className={`dot-badge ${className}`}
        style={{
          padding: '0',
          width: dotSize[size],
          height: dotSize[size],
          minHeight: 'auto',
          ...style
        }}
        {...props}
      >
        <span className="sr-only">{status}</span>
      </Badge>
    );
  }
);

DotBadge.displayName = 'DotBadge';

// Number Badge for counts
export interface NumberBadgeProps extends Omit<BadgeProps, 'children'> {
  count: number;
  max?: number;
  showZero?: boolean;
}

export const NumberBadge = forwardRef<HTMLSpanElement, NumberBadgeProps>(
  (
    {
      count,
      max = 99,
      showZero = false,
      variant = 'primary',
      size = 'sm',
      shape = 'pill',
      className = '',
      ...props
    },
    ref
  ) => {
    if (count === 0 && !showZero) {
      return null;
    }

    const displayCount = count > max ? `${max}+` : count.toString();

    return (
      <Badge
        ref={ref}
        variant={variant}
        size={size}
        shape={shape}
        className={`number-badge ${className}`}
        {...props}
      >
        {displayCount}
      </Badge>
    );
  }
);

NumberBadge.displayName = 'NumberBadge';
