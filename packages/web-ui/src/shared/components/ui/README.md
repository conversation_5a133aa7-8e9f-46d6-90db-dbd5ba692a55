# UI Component Library

A comprehensive, accessible, and consistent UI component library for the Knowledge Graph Visualizer application.

## Design System

The component library is built on a robust design system with consistent tokens for:

- **Colors**: Primary, secondary, semantic colors (success, warning, error)
- **Typography**: Font families, sizes, weights, and line heights
- **Spacing**: Consistent spacing scale from 0 to 64
- **Border Radius**: From none to full rounded
- **Shadows**: Elevation system with multiple shadow levels
- **Z-Index**: Layering system for overlays and modals
- **Breakpoints**: Responsive design breakpoints
- **Transitions**: Smooth animations and transitions

## Components

### Button

Versatile button component with multiple variants and states.

```tsx
import { Button } from '@/shared/components/ui';

// Basic usage
<Button>Click me</Button>

// Variants
<Button variant="primary">Primary</Button>
<Button variant="secondary">Secondary</Button>
<Button variant="outline">Outline</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="danger">Danger</Button>

// Sizes
<Button size="sm">Small</Button>
<Button size="md">Medium</Button>
<Button size="lg">Large</Button>

// States
<Button isLoading>Loading...</Button>
<Button disabled>Disabled</Button>

// With icons
<Button leftIcon={<Icon />}>With Left Icon</Button>
<Button rightIcon={<Icon />}>With Right Icon</Button>

// Full width
<Button fullWidth>Full Width</Button>
```

### Input

Form input component with validation and accessibility features.

```tsx
import { Input } from '@/shared/components/ui';

// Basic usage
<Input placeholder="Enter text..." />

// With label and validation
<Input 
  label="Email"
  type="email"
  error="Please enter a valid email"
  helperText="We'll never share your email"
/>

// Variants
<Input variant="default" />
<Input variant="filled" />
<Input variant="outline" />

// Sizes
<Input size="sm" />
<Input size="md" />
<Input size="lg" />

// With icons
<Input leftIcon={<SearchIcon />} placeholder="Search..." />
<Input rightIcon={<EyeIcon />} type="password" />

// Loading state
<Input isLoading />
```

### Modal

Accessible modal component with focus management and keyboard navigation.

```tsx
import { Modal, useModal } from '@/shared/components/ui';

function MyComponent() {
  const { isOpen, open, close } = useModal();

  return (
    <>
      <Button onClick={open}>Open Modal</Button>
      
      <Modal
        isOpen={isOpen}
        onClose={close}
        title="Modal Title"
        size="md"
        closeOnBackdropClick={true}
        closeOnEscape={true}
      >
        <p>Modal content goes here...</p>
      </Modal>
    </>
  );
}
```

### Card

Flexible card component for content organization.

```tsx
import { Card, CardHeader, CardBody, CardFooter } from '@/shared/components/ui';

// Basic usage
<Card>
  <p>Simple card content</p>
</Card>

// With sections
<Card variant="elevated" shadow="lg">
  <CardHeader>
    <h3>Card Title</h3>
  </CardHeader>
  <CardBody>
    <p>Card content goes here...</p>
  </CardBody>
  <CardFooter>
    <Button>Action</Button>
  </CardFooter>
</Card>

// Interactive card
<Card interactive hover onClick={handleClick}>
  <p>Clickable card</p>
</Card>
```

### Badge

Status indicators and labels with consistent styling.

```tsx
import { Badge, DotBadge, NumberBadge } from '@/shared/components/ui';

// Basic badges
<Badge variant="primary">Primary</Badge>
<Badge variant="success">Success</Badge>
<Badge variant="warning">Warning</Badge>
<Badge variant="error">Error</Badge>

// With icons
<Badge icon={<CheckIcon />}>Completed</Badge>

// Removable
<Badge removable onRemove={handleRemove}>
  Removable
</Badge>

// Dot badge for status
<DotBadge status="online" />
<DotBadge status="offline" />
<DotBadge status="busy" />

// Number badge for counts
<NumberBadge count={5} />
<NumberBadge count={99} max={99} />
<NumberBadge count={150} max={99} /> // Shows "99+"
```

### LoadingSpinner

Loading indicators with multiple variants and sizes.

```tsx
import { LoadingSpinner } from '@/shared/components/ui';

// Basic spinner
<LoadingSpinner />

// Different variants
<LoadingSpinner variant="spinner" />
<LoadingSpinner variant="dots" />
<LoadingSpinner variant="pulse" />
<LoadingSpinner variant="bars" />

// Sizes
<LoadingSpinner size="xs" />
<LoadingSpinner size="sm" />
<LoadingSpinner size="md" />
<LoadingSpinner size="lg" />
<LoadingSpinner size="xl" />

// Custom color
<LoadingSpinner color="#ff6b6b" />
```

## Accessibility Features

All components are built with accessibility in mind:

- **Keyboard Navigation**: Full keyboard support with proper focus management
- **Screen Reader Support**: ARIA labels, roles, and descriptions
- **Focus Management**: Proper focus trapping in modals and complex components
- **Color Contrast**: WCAG AA compliant color combinations
- **Semantic HTML**: Proper use of semantic elements and roles

## Theming and Customization

Components use the design token system for consistent theming:

```tsx
import { designTokens } from '@/shared/components/ui';

// Access design tokens
const primaryColor = designTokens.colors.primary[600];
const spacing = designTokens.spacing[4];
const borderRadius = designTokens.borderRadius.md;
```

## Testing

All components include comprehensive tests:

- Unit tests for functionality
- Accessibility tests
- Visual regression tests
- Integration tests

Run tests with:

```bash
npm run test -- --testPathPattern="ui"
```

## Best Practices

1. **Consistent Spacing**: Use design tokens for spacing and sizing
2. **Accessible Labels**: Always provide proper labels and descriptions
3. **Error Handling**: Include proper error states and validation
4. **Loading States**: Show loading indicators for async operations
5. **Responsive Design**: Ensure components work across all screen sizes
6. **Performance**: Use React.memo and proper optimization techniques

## Contributing

When adding new components:

1. Follow the existing design token system
2. Include comprehensive TypeScript types
3. Add accessibility features (ARIA, keyboard navigation)
4. Write comprehensive tests
5. Document usage examples
6. Follow the established file structure

## File Structure

```
src/shared/components/ui/
├── design-system/
│   └── tokens.ts           # Design system tokens
├── __tests__/
│   └── *.test.tsx         # Component tests
├── Button.tsx             # Button component
├── Input.tsx              # Input component
├── Modal.tsx              # Modal component
├── Card.tsx               # Card component
├── Badge.tsx              # Badge component
├── index.ts               # Main exports
└── README.md              # This file
```
