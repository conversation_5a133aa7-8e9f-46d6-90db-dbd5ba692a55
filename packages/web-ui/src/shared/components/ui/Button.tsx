/**
 * Button Component
 * 
 * Reusable button component with consistent styling and accessibility features.
 */

import React, { forwardRef } from 'react';
import { designTokens } from '../design-system/tokens';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
}

const getButtonStyles = (
  variant: ButtonProps['variant'] = 'primary',
  size: ButtonProps['size'] = 'md',
  isLoading: boolean = false,
  fullWidth: boolean = false,
  disabled: boolean = false
) => {
  const baseStyles = {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: designTokens.spacing[2],
    fontFamily: designTokens.typography.fontFamily.sans.join(', '),
    fontWeight: designTokens.typography.fontWeight.medium,
    borderRadius: designTokens.borderRadius.md,
    border: '1px solid transparent',
    cursor: disabled || isLoading ? 'not-allowed' : 'pointer',
    transition: designTokens.transition.all,
    textDecoration: 'none',
    outline: 'none',
    position: 'relative' as const,
    overflow: 'hidden',
    width: fullWidth ? '100%' : 'auto',
    opacity: disabled || isLoading ? 0.6 : 1
  };

  // Size variants
  const sizeStyles = {
    sm: {
      fontSize: designTokens.typography.fontSize.sm,
      padding: `${designTokens.spacing[2]} ${designTokens.spacing[3]}`,
      minHeight: '32px'
    },
    md: {
      fontSize: designTokens.typography.fontSize.base,
      padding: `${designTokens.spacing[3]} ${designTokens.spacing[4]}`,
      minHeight: '40px'
    },
    lg: {
      fontSize: designTokens.typography.fontSize.lg,
      padding: `${designTokens.spacing[4]} ${designTokens.spacing[6]}`,
      minHeight: '48px'
    }
  };

  // Color variants
  const variantStyles = {
    primary: {
      backgroundColor: designTokens.colors.primary[600],
      color: 'white',
      borderColor: designTokens.colors.primary[600],
      ':hover': {
        backgroundColor: designTokens.colors.primary[700],
        borderColor: designTokens.colors.primary[700]
      },
      ':focus': {
        boxShadow: `0 0 0 3px ${designTokens.colors.primary[200]}`
      },
      ':active': {
        backgroundColor: designTokens.colors.primary[800]
      }
    },
    secondary: {
      backgroundColor: designTokens.colors.secondary[100],
      color: designTokens.colors.secondary[900],
      borderColor: designTokens.colors.secondary[200],
      ':hover': {
        backgroundColor: designTokens.colors.secondary[200],
        borderColor: designTokens.colors.secondary[300]
      },
      ':focus': {
        boxShadow: `0 0 0 3px ${designTokens.colors.secondary[200]}`
      },
      ':active': {
        backgroundColor: designTokens.colors.secondary[300]
      }
    },
    outline: {
      backgroundColor: 'transparent',
      color: designTokens.colors.primary[600],
      borderColor: designTokens.colors.primary[300],
      ':hover': {
        backgroundColor: designTokens.colors.primary[50],
        borderColor: designTokens.colors.primary[400]
      },
      ':focus': {
        boxShadow: `0 0 0 3px ${designTokens.colors.primary[200]}`
      },
      ':active': {
        backgroundColor: designTokens.colors.primary[100]
      }
    },
    ghost: {
      backgroundColor: 'transparent',
      color: designTokens.colors.secondary[700],
      borderColor: 'transparent',
      ':hover': {
        backgroundColor: designTokens.colors.secondary[100],
        color: designTokens.colors.secondary[900]
      },
      ':focus': {
        boxShadow: `0 0 0 3px ${designTokens.colors.secondary[200]}`
      },
      ':active': {
        backgroundColor: designTokens.colors.secondary[200]
      }
    },
    danger: {
      backgroundColor: designTokens.colors.error[600],
      color: 'white',
      borderColor: designTokens.colors.error[600],
      ':hover': {
        backgroundColor: designTokens.colors.error[700],
        borderColor: designTokens.colors.error[700]
      },
      ':focus': {
        boxShadow: `0 0 0 3px ${designTokens.colors.error[200]}`
      },
      ':active': {
        backgroundColor: designTokens.colors.error[800]
      }
    }
  };

  return {
    ...baseStyles,
    ...sizeStyles[size],
    ...variantStyles[variant]
  };
};

const LoadingSpinner: React.FC<{ size: ButtonProps['size'] }> = ({ size = 'md' }) => {
  const spinnerSize = {
    sm: '14px',
    md: '16px',
    lg: '18px'
  };

  return (
    <div
      style={{
        width: spinnerSize[size],
        height: spinnerSize[size],
        border: '2px solid currentColor',
        borderTop: '2px solid transparent',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }}
    />
  );
};

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      variant = 'primary',
      size = 'md',
      isLoading = false,
      leftIcon,
      rightIcon,
      fullWidth = false,
      disabled = false,
      className = '',
      style,
      onMouseEnter,
      onMouseLeave,
      onFocus,
      onBlur,
      ...props
    },
    ref
  ) => {
    const [isHovered, setIsHovered] = React.useState(false);
    const [isFocused, setIsFocused] = React.useState(false);
    const [isActive, setIsActive] = React.useState(false);

    const buttonStyles = getButtonStyles(variant, size, isLoading, fullWidth, disabled);

    // Apply hover, focus, and active styles
    let dynamicStyles = { ...buttonStyles };
    
    if (isHovered && !disabled && !isLoading) {
      const hoverStyles = (buttonStyles as any)[':hover'];
      if (hoverStyles) {
        dynamicStyles = { ...dynamicStyles, ...hoverStyles };
      }
    }
    
    if (isFocused && !disabled && !isLoading) {
      const focusStyles = (buttonStyles as any)[':focus'];
      if (focusStyles) {
        dynamicStyles = { ...dynamicStyles, ...focusStyles };
      }
    }
    
    if (isActive && !disabled && !isLoading) {
      const activeStyles = (buttonStyles as any)[':active'];
      if (activeStyles) {
        dynamicStyles = { ...dynamicStyles, ...activeStyles };
      }
    }

    // Remove pseudo-class styles from final styles
    const finalStyles = Object.fromEntries(
      Object.entries(dynamicStyles).filter(([key]) => !key.startsWith(':'))
    );

    const handleMouseEnter = (e: React.MouseEvent<HTMLButtonElement>) => {
      setIsHovered(true);
      onMouseEnter?.(e);
    };

    const handleMouseLeave = (e: React.MouseEvent<HTMLButtonElement>) => {
      setIsHovered(false);
      setIsActive(false);
      onMouseLeave?.(e);
    };

    const handleFocus = (e: React.FocusEvent<HTMLButtonElement>) => {
      setIsFocused(true);
      onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLButtonElement>) => {
      setIsFocused(false);
      onBlur?.(e);
    };

    const handleMouseDown = () => {
      setIsActive(true);
    };

    const handleMouseUp = () => {
      setIsActive(false);
    };

    return (
      <>
        <button
          ref={ref}
          disabled={disabled || isLoading}
          className={className}
          style={{
            ...finalStyles,
            ...style
          }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          aria-disabled={disabled || isLoading}
          aria-busy={isLoading}
          {...props}
        >
          {isLoading && <LoadingSpinner size={size} />}
          {!isLoading && leftIcon && <span>{leftIcon}</span>}
          {children && <span>{children}</span>}
          {!isLoading && rightIcon && <span>{rightIcon}</span>}
        </button>

        {/* CSS for spinner animation */}
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </>
    );
  }
);

Button.displayName = 'Button';
