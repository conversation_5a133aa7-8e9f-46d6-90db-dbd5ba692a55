/**
 * Global Styles
 * 
 * Base styles for the Knowledge Graph Visualizer application.
 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background-color: #4A90E2;
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.app-header h1 {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.app-header p {
  font-size: 0.875rem;
  opacity: 0.9;
}

.app-main {
  flex: 1;
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.graph-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  padding: 1rem;
}

.app-footer {
  background-color: #f8f9fa;
  padding: 1rem 2rem;
  text-align: center;
  border-top: 1px solid #e9ecef;
}

.app-footer p {
  font-size: 0.875rem;
  color: #6c757d;
}

/* Graph Canvas Styles */
.graph-canvas-container {
  position: relative;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.graph-node {
  cursor: pointer;
  transition: all 0.2s ease;
}

.graph-node:hover {
  filter: brightness(1.1);
}

.graph-edge {
  cursor: pointer;
  transition: all 0.2s ease;
}

.graph-edge:hover {
  stroke-width: 3px !important;
}

/* Performance overlay */
.performance-overlay {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  user-select: none;
  pointer-events: none;
}
