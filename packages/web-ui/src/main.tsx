/**
 * Main Entry Point
 * 
 * Entry point for the Knowledge Graph Visualizer web application.
 * Sets up the feature-slice architecture and renders the main app.
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import { App } from './App';
import './index.css';

// Initialize the application
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
