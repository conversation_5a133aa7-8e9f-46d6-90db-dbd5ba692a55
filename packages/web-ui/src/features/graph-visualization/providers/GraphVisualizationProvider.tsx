/**
 * Graph Visualization Provider
 * 
 * Feature provider for graph visualization functionality.
 * Manages graph state, layout, and interactions.
 */

import React, { useMemo } from 'react';
import { createBaseFeatureProvider } from '../../../shared/infrastructure/BaseFeatureProvider';
import {
  VisualizationState,
  GraphData,
  GraphConfig
} from '../types';
import {
  FeatureAction,
  FeatureSliceState,
  FeatureIsolationLevel
} from '../../../shared/types/feature';

// Initial state
const initialVisualizationState: VisualizationState = {
  data: {
    nodes: [],
    links: [],
    metadata: {
      totalNodes: 0,
      totalEdges: 0,
      nodeTypes: [],
      edgeTypes: [],
      lastUpdated: new Date()
    }
  },
  config: {
    layout: {
      algorithm: 'force',
      forceStrength: 0.5,
      linkDistance: 100,
      nodeRepulsion: 300,
      centerForce: 0.1,
      collisionRadius: 10,
      alphaDecay: 0.02,
      velocityDecay: 0.4,
      iterations: 300,
      stabilization: true
    },
    interaction: {
      dragging: true,
      zooming: true,
      panning: true,
      selection: true,
      multiSelect: true,
      hover: true,
      doubleClick: true,
      contextMenu: true,
      keyboard: true
    },
    rendering: {
      nodeSize: { min: 5, max: 50, default: 15 },
      edgeWidth: { min: 1, max: 10, default: 2 },
      colors: {
        nodes: {
          default: '#4A90E2',
          selected: '#FF6B6B',
          highlighted: '#FFD93D'
        },
        edges: {
          default: '#999999',
          selected: '#FF6B6B',
          highlighted: '#FFD93D'
        },
        background: '#FFFFFF',
        selection: '#4A90E2',
        highlight: '#FFD93D'
      },
      shapes: {
        default: 'circle',
        entity: 'circle',
        relationship: 'diamond',
        attribute: 'square'
      },
      labels: {
        nodes: true,
        edges: false,
        fontSize: 12,
        fontFamily: 'Arial, sans-serif',
        color: '#333333'
      },
      animations: {
        enabled: true,
        duration: 300,
        easing: 'ease-in-out'
      }
    },
    performance: {
      maxNodes: 10000,
      maxEdges: 50000,
      levelOfDetail: true,
      clustering: true,
      virtualization: true,
      webWorkers: true,
      batchSize: 1000,
      throttleMs: 16
    }
  },
  viewport: {
    zoom: 1,
    pan: { x: 0, y: 0 },
    bounds: { width: 800, height: 600 }
  },
  selection: {
    nodes: [],
    edges: [],
    mode: 'single'
  },
  interaction: {
    isDragging: false,
    isPanning: false,
    isZooming: false,
    hoveredNode: null,
    hoveredEdge: null
  },
  layout: {
    isRunning: false,
    progress: 0,
    algorithm: 'force',
    iterations: 0
  },
  performance: {
    fps: 60,
    renderTime: 0,
    nodeCount: 0,
    edgeCount: 0,
    visibleNodes: 0,
    visibleEdges: 0
  }
};

// Action types
export const GRAPH_ACTIONS = {
  // Data actions
  SET_GRAPH_DATA: 'SET_GRAPH_DATA',
  ADD_NODES: 'ADD_NODES',
  REMOVE_NODES: 'REMOVE_NODES',
  UPDATE_NODES: 'UPDATE_NODES',
  ADD_EDGES: 'ADD_EDGES',
  REMOVE_EDGES: 'REMOVE_EDGES',
  UPDATE_EDGES: 'UPDATE_EDGES',
  CLEAR_GRAPH: 'CLEAR_GRAPH',
  
  // Configuration actions
  UPDATE_CONFIG: 'UPDATE_CONFIG',
  UPDATE_LAYOUT_CONFIG: 'UPDATE_LAYOUT_CONFIG',
  UPDATE_INTERACTION_CONFIG: 'UPDATE_INTERACTION_CONFIG',
  UPDATE_RENDERING_CONFIG: 'UPDATE_RENDERING_CONFIG',
  
  // Viewport actions
  SET_ZOOM: 'SET_ZOOM',
  SET_PAN: 'SET_PAN',
  SET_VIEWPORT_BOUNDS: 'SET_VIEWPORT_BOUNDS',
  RESET_VIEWPORT: 'RESET_VIEWPORT',
  
  // Selection actions
  SELECT_NODES: 'SELECT_NODES',
  SELECT_EDGES: 'SELECT_EDGES',
  CLEAR_SELECTION: 'CLEAR_SELECTION',
  SET_SELECTION_MODE: 'SET_SELECTION_MODE',
  
  // Interaction actions
  SET_DRAGGING: 'SET_DRAGGING',
  SET_PANNING: 'SET_PANNING',
  SET_ZOOMING: 'SET_ZOOMING',
  SET_HOVERED_NODE: 'SET_HOVERED_NODE',
  SET_HOVERED_EDGE: 'SET_HOVERED_EDGE',
  
  // Layout actions
  START_LAYOUT: 'START_LAYOUT',
  STOP_LAYOUT: 'STOP_LAYOUT',
  UPDATE_LAYOUT_PROGRESS: 'UPDATE_LAYOUT_PROGRESS',
  LAYOUT_COMPLETE: 'LAYOUT_COMPLETE',
  
  // Performance actions
  UPDATE_PERFORMANCE: 'UPDATE_PERFORMANCE'
} as const;

// Reducer
const graphVisualizationReducer = (
  state: FeatureSliceState<VisualizationState>,
  action: FeatureAction
): FeatureSliceState<VisualizationState> => {
  switch (action.type) {
    case GRAPH_ACTIONS.SET_GRAPH_DATA:
      return {
        ...state,
        data: {
          ...state.data,
          data: {
            ...action.payload,
            metadata: {
              ...action.payload.metadata,
              lastUpdated: new Date()
            }
          }
        },
        meta: {
          ...state.meta,
          version: state.meta.version + 1,
          lastFetch: new Date()
        }
      };

    case GRAPH_ACTIONS.UPDATE_CONFIG:
      return {
        ...state,
        data: {
          ...state.data,
          config: {
            ...state.data.config,
            ...action.payload
          }
        },
        meta: {
          ...state.meta,
          version: state.meta.version + 1
        }
      };

    case GRAPH_ACTIONS.SET_ZOOM:
      return {
        ...state,
        data: {
          ...state.data,
          viewport: {
            ...state.data.viewport,
            zoom: action.payload
          }
        }
      };

    case GRAPH_ACTIONS.SET_PAN:
      return {
        ...state,
        data: {
          ...state.data,
          viewport: {
            ...state.data.viewport,
            pan: action.payload
          }
        }
      };

    case GRAPH_ACTIONS.SELECT_NODES:
      return {
        ...state,
        data: {
          ...state.data,
          selection: {
            ...state.data.selection,
            nodes: action.payload
          }
        }
      };

    case GRAPH_ACTIONS.SELECT_EDGES:
      return {
        ...state,
        data: {
          ...state.data,
          selection: {
            ...state.data.selection,
            edges: action.payload
          }
        }
      };

    case GRAPH_ACTIONS.CLEAR_SELECTION:
      return {
        ...state,
        data: {
          ...state.data,
          selection: {
            ...state.data.selection,
            nodes: [],
            edges: []
          }
        }
      };

    case GRAPH_ACTIONS.SET_HOVERED_NODE:
      return {
        ...state,
        data: {
          ...state.data,
          interaction: {
            ...state.data.interaction,
            hoveredNode: action.payload
          }
        }
      };

    case GRAPH_ACTIONS.START_LAYOUT:
      return {
        ...state,
        data: {
          ...state.data,
          layout: {
            ...state.data.layout,
            isRunning: true,
            progress: 0,
            algorithm: action.payload.algorithm,
            iterations: 0
          }
        }
      };

    case GRAPH_ACTIONS.STOP_LAYOUT:
      return {
        ...state,
        data: {
          ...state.data,
          layout: {
            ...state.data.layout,
            isRunning: false,
            progress: 100
          }
        }
      };

    case GRAPH_ACTIONS.UPDATE_PERFORMANCE:
      return {
        ...state,
        data: {
          ...state.data,
          performance: {
            ...state.data.performance,
            ...action.payload
          }
        }
      };

    default:
      return state;
  }
};

// Create the feature provider
const { Provider, useContext } = createBaseFeatureProvider(
  'graph-visualization',
  initialVisualizationState,
  graphVisualizationReducer,
  FeatureIsolationLevel.PARTIAL
);

// Graph Visualization Provider component
export const GraphVisualizationProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  return (
    <Provider
      featureName="graph-visualization"
      initialState={initialVisualizationState}
      reducer={graphVisualizationReducer}
      allowedGlobalAccess={['search-filter', 'analysis-tools']}
    >
      {children}
    </Provider>
  );
};

// Hook to use graph visualization
export const useGraphVisualization = () => {
  const { state, dispatch } = useContext();

  const actions = useMemo(() => ({
    setGraphData: (data: GraphData) => 
      dispatch({ type: GRAPH_ACTIONS.SET_GRAPH_DATA, payload: data }),
    
    updateConfig: (config: Partial<GraphConfig>) =>
      dispatch({ type: GRAPH_ACTIONS.UPDATE_CONFIG, payload: config }),
    
    setZoom: (zoom: number) =>
      dispatch({ type: GRAPH_ACTIONS.SET_ZOOM, payload: zoom }),
    
    setPan: (pan: { x: number; y: number }) =>
      dispatch({ type: GRAPH_ACTIONS.SET_PAN, payload: pan }),
    
    selectNodes: (nodeIds: string[]) =>
      dispatch({ type: GRAPH_ACTIONS.SELECT_NODES, payload: nodeIds }),
    
    selectEdges: (edgeIds: string[]) =>
      dispatch({ type: GRAPH_ACTIONS.SELECT_EDGES, payload: edgeIds }),
    
    clearSelection: () =>
      dispatch({ type: GRAPH_ACTIONS.CLEAR_SELECTION }),
    
    setHoveredNode: (nodeId: string | null) =>
      dispatch({ type: GRAPH_ACTIONS.SET_HOVERED_NODE, payload: nodeId }),
    
    startLayout: (algorithm: string) =>
      dispatch({ type: GRAPH_ACTIONS.START_LAYOUT, payload: { algorithm } }),
    
    stopLayout: () =>
      dispatch({ type: GRAPH_ACTIONS.STOP_LAYOUT }),
    
    updatePerformance: (metrics: Partial<VisualizationState['performance']>) =>
      dispatch({ type: GRAPH_ACTIONS.UPDATE_PERFORMANCE, payload: metrics })
  }), [dispatch]);

  const selectors = useMemo(() => ({
    getGraphData: () => state.data.data,
    getConfig: () => state.data.config,
    getViewport: () => state.data.viewport,
    getSelection: () => state.data.selection,
    getSelectedNodes: () => state.data.selection.nodes,
    getSelectedEdges: () => state.data.selection.edges,
    getHoveredNode: () => state.data.interaction.hoveredNode,
    isLayoutRunning: () => state.data.layout.isRunning,
    getPerformanceMetrics: () => state.data.performance
  }), [state]);

  return {
    state: state.data,
    actions,
    selectors,
    meta: state.meta
  };
};
