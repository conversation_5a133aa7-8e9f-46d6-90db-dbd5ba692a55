/**
 * Graph Data Management Hook
 * 
 * Advanced hook for managing graph data loading, caching, and updates.
 * Provides optimized data fetching with error handling and performance monitoring.
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { GraphData, GraphNode, GraphEdge, GraphFilter } from '../types';
import { useGraphVisualization } from '../providers/GraphVisualizationProvider';
import { featureBus } from '../../../shared/infrastructure/FeatureBus';

export interface GraphDataOptions {
  autoLoad?: boolean;
  cacheTimeout?: number;
  enableRealtime?: boolean;
  batchSize?: number;
  maxRetries?: number;
  retryDelay?: number;
}

export interface GraphDataState {
  data: GraphData;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  lastFetch: Date | null;
  cacheHit: boolean;
  performance: {
    fetchTime: number;
    transformTime: number;
    renderTime: number;
  };
}

export interface GraphDataActions {
  loadInitialData: (limit?: number) => Promise<void>;
  loadMoreData: (offset: number, limit: number) => Promise<void>;
  refreshData: () => Promise<void>;
  searchNodes: (term: string) => Promise<GraphNode[]>;
  expandNode: (nodeId: string) => Promise<void>;
  filterData: (filters: GraphFilter[]) => Promise<void>;
  addNodes: (nodes: GraphNode[]) => void;
  addEdges: (edges: GraphEdge[]) => void;
  removeNodes: (nodeIds: string[]) => void;
  removeEdges: (edgeIds: string[]) => void;
  clearData: () => void;
  exportData: (format: 'json' | 'csv' | 'graphml') => string;
  importData: (data: string, format: 'json' | 'csv' | 'graphml') => Promise<void>;
}

// Cache implementation
class GraphDataCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  set(key: string, data: any, ttl: number = 300000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }
  
  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }
  
  clear(): void {
    this.cache.clear();
  }
  
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }
}

const graphCache = new GraphDataCache();

// Mock API service (replace with actual API calls)
const graphApiService = {
  async getInitialGraph(limit: number = 100): Promise<GraphData> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock data generation
    const nodes: GraphNode[] = Array.from({ length: Math.min(limit, 50) }, (_, i) => ({
      id: `node-${i}`,
      label: `Node ${i}`,
      type: ['Entity', 'Product', 'Feature'][i % 3],
      properties: {
        name: `Node ${i}`,
        category: ['A', 'B', 'C'][i % 3],
        value: Math.random() * 100
      },
      position: {
        x: Math.random() * 800,
        y: Math.random() * 600
      },
      style: {
        size: 10 + Math.random() * 20,
        color: ['#4A90E2', '#FF6B6B', '#50E3C2'][i % 3],
        shape: 'circle'
      }
    }));
    
    const edges: GraphEdge[] = Array.from({ length: Math.min(limit / 2, 25) }, (_, i) => ({
      id: `edge-${i}`,
      source: `node-${i}`,
      target: `node-${(i + 1) % nodes.length}`,
      type: 'RELATES_TO',
      label: `Relation ${i}`,
      properties: {
        weight: Math.random(),
        strength: Math.random() * 10
      },
      style: {
        width: 1 + Math.random() * 3,
        color: '#999999',
        style: 'solid' as const
      }
    }));
    
    return {
      nodes,
      links: edges,
      metadata: {
        totalNodes: nodes.length,
        totalEdges: edges.length,
        nodeTypes: ['Entity', 'Product', 'Feature'],
        edgeTypes: ['RELATES_TO'],
        lastUpdated: new Date()
      }
    };
  },
  
  async searchNodes(term: string): Promise<GraphNode[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // Mock search results
    return Array.from({ length: 5 }, (_, i) => ({
      id: `search-${i}`,
      label: `${term} Result ${i}`,
      type: 'SearchResult',
      properties: {
        name: `${term} Result ${i}`,
        relevance: Math.random()
      },
      position: {
        x: Math.random() * 800,
        y: Math.random() * 600
      },
      style: {
        size: 15,
        color: '#FFD93D',
        shape: 'circle'
      }
    }));
  },
  
  async expandNode(nodeId: string): Promise<GraphData> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Mock expansion data
    const newNodes: GraphNode[] = Array.from({ length: 3 }, (_, i) => ({
      id: `${nodeId}-child-${i}`,
      label: `Child ${i}`,
      type: 'Child',
      properties: {
        name: `Child ${i}`,
        parent: nodeId
      },
      position: {
        x: Math.random() * 800,
        y: Math.random() * 600
      },
      style: {
        size: 12,
        color: '#50E3C2',
        shape: 'circle'
      }
    }));
    
    const newEdges: GraphEdge[] = newNodes.map((node, i) => ({
      id: `${nodeId}-edge-${i}`,
      source: nodeId,
      target: node.id,
      type: 'PARENT_OF',
      label: 'parent of',
      properties: {},
      style: {
        width: 2,
        color: '#999999',
        style: 'solid' as const
      }
    }));
    
    return {
      nodes: newNodes,
      links: newEdges,
      metadata: {
        totalNodes: newNodes.length,
        totalEdges: newEdges.length,
        nodeTypes: ['Child'],
        edgeTypes: ['PARENT_OF'],
        lastUpdated: new Date()
      }
    };
  }
};

export const useGraphData = (options: GraphDataOptions = {}): [GraphDataState, GraphDataActions] => {
  const {
    autoLoad = true,
    cacheTimeout = 300000,
    batchSize = 100,
    maxRetries = 3,
    retryDelay = 1000
  } = options;
  
  const { actions: graphActions } = useGraphVisualization();
  const [state, setState] = useState<GraphDataState>({
    data: { nodes: [], links: [], metadata: undefined },
    isLoading: false,
    isError: false,
    error: null,
    lastFetch: null,
    cacheHit: false,
    performance: {
      fetchTime: 0,
      transformTime: 0,
      renderTime: 0
    }
  });
  
  const abortControllerRef = useRef<AbortController | null>(null);
  
  // Performance monitoring
  const measurePerformance = useCallback(<T,>(
    operation: () => Promise<T>,
    operationType: keyof GraphDataState['performance']
  ): Promise<T> => {
    return new Promise(async (resolve, reject) => {
      const startTime = performance.now();
      
      try {
        const result = await operation();
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        setState(prev => ({
          ...prev,
          performance: {
            ...prev.performance,
            [operationType]: duration
          }
        }));
        
        resolve(result);
      } catch (error) {
        reject(error);
      }
    });
  }, []);
  
  // Error handling with retry logic
  const withRetry = useCallback(async <T,>(
    operation: () => Promise<T>,
    retries: number = maxRetries
  ): Promise<T> => {
    try {
      return await operation();
    } catch (error) {
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return withRetry(operation, retries - 1);
      }
      throw error;
    }
  }, [maxRetries, retryDelay]);
  
  // Load initial data
  const loadInitialData = useCallback(async (limit: number = batchSize) => {
    const cacheKey = `initial-${limit}`;
    
    // Check cache first
    const cachedData = graphCache.get(cacheKey);
    if (cachedData) {
      setState(prev => ({
        ...prev,
        data: cachedData,
        cacheHit: true,
        lastFetch: new Date()
      }));
      graphActions.setGraphData(cachedData);
      return;
    }
    
    setState(prev => ({ ...prev, isLoading: true, isError: false, error: null }));
    
    try {
      const data = await measurePerformance(
        () => withRetry(() => graphApiService.getInitialGraph(limit)),
        'fetchTime'
      );
      
      // Cache the data
      graphCache.set(cacheKey, data, cacheTimeout);
      
      setState(prev => ({
        ...prev,
        data,
        isLoading: false,
        lastFetch: new Date(),
        cacheHit: false
      }));
      
      // Update graph visualization
      graphActions.setGraphData(data);
      
      // Publish data loaded event
      featureBus.communicate.publish({
        type: 'graph:data-loaded',
        payload: { nodeCount: data.nodes.length, edgeCount: data.links.length },
        timestamp: new Date(),
        source: 'graph-visualization'
      });
      
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        isError: true,
        error: error as Error
      }));
      
      // Publish error event
      featureBus.communicate.publish({
        type: 'graph:data-error',
        payload: { error: (error as Error).message },
        timestamp: new Date(),
        source: 'graph-visualization'
      });
    }
  }, [batchSize, cacheTimeout, graphActions, measurePerformance, withRetry]);
  
  // Search nodes
  const searchNodes = useCallback(async (term: string): Promise<GraphNode[]> => {
    if (!term.trim()) return [];
    
    const cacheKey = `search-${term}`;
    const cachedResults = graphCache.get(cacheKey);
    if (cachedResults) return cachedResults;
    
    try {
      const results = await measurePerformance(
        () => withRetry(() => graphApiService.searchNodes(term)),
        'fetchTime'
      );
      
      graphCache.set(cacheKey, results, cacheTimeout / 2); // Shorter cache for search
      return results;
    } catch (error) {
      console.error('Search failed:', error);
      return [];
    }
  }, [cacheTimeout, measurePerformance, withRetry]);
  
  // Expand node
  const expandNode = useCallback(async (nodeId: string) => {
    const cacheKey = `expand-${nodeId}`;
    const cachedData = graphCache.get(cacheKey);
    
    if (cachedData) {
      // Merge with existing data
      setState(prev => {
        const mergedData = {
          nodes: [...prev.data.nodes, ...cachedData.nodes],
          links: [...prev.data.links, ...cachedData.links],
          metadata: {
            totalNodes: prev.data.nodes.length + cachedData.nodes.length,
            totalEdges: prev.data.links.length + cachedData.links.length,
            nodeTypes: prev.data.metadata?.nodeTypes || [],
            edgeTypes: prev.data.metadata?.edgeTypes || [],
            lastUpdated: new Date()
          }
        };
        
        graphActions.setGraphData(mergedData);
        return { ...prev, data: mergedData };
      });
      return;
    }
    
    try {
      const expansionData = await measurePerformance(
        () => withRetry(() => graphApiService.expandNode(nodeId)),
        'fetchTime'
      );
      
      graphCache.set(cacheKey, expansionData, cacheTimeout);
      
      setState(prev => {
        const mergedData = {
          nodes: [...prev.data.nodes, ...expansionData.nodes],
          links: [...prev.data.links, ...expansionData.links],
          metadata: {
            totalNodes: prev.data.nodes.length + expansionData.nodes.length,
            totalEdges: prev.data.links.length + expansionData.links.length,
            nodeTypes: prev.data.metadata?.nodeTypes || [],
            edgeTypes: prev.data.metadata?.edgeTypes || [],
            lastUpdated: new Date()
          }
        };
        
        graphActions.setGraphData(mergedData);
        return { ...prev, data: mergedData };
      });
      
    } catch (error) {
      console.error('Node expansion failed:', error);
    }
  }, [cacheTimeout, graphActions, measurePerformance, withRetry]);
  
  // Auto-load on mount
  useEffect(() => {
    if (autoLoad) {
      loadInitialData();
    }
  }, [autoLoad, loadInitialData]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);
  
  const actions: GraphDataActions = useMemo(() => ({
    loadInitialData,
    loadMoreData: async (offset: number, limit: number) => {
      // Implementation for pagination
      console.log('Load more data:', offset, limit);
    },
    refreshData: () => {
      graphCache.clear();
      return loadInitialData();
    },
    searchNodes,
    expandNode,
    filterData: async (filters: GraphFilter[]) => {
      // Implementation for filtering
      console.log('Filter data:', filters);
    },
    addNodes: (nodes: GraphNode[]) => {
      setState(prev => ({
        ...prev,
        data: {
          ...prev.data,
          nodes: [...prev.data.nodes, ...nodes]
        }
      }));
    },
    addEdges: (edges: GraphEdge[]) => {
      setState(prev => ({
        ...prev,
        data: {
          ...prev.data,
          links: [...prev.data.links, ...edges]
        }
      }));
    },
    removeNodes: (nodeIds: string[]) => {
      setState(prev => ({
        ...prev,
        data: {
          ...prev.data,
          nodes: prev.data.nodes.filter(node => !nodeIds.includes(node.id))
        }
      }));
    },
    removeEdges: (edgeIds: string[]) => {
      setState(prev => ({
        ...prev,
        data: {
          ...prev.data,
          links: prev.data.links.filter(edge => !edgeIds.includes(edge.id))
        }
      }));
    },
    clearData: () => {
      setState(prev => ({
        ...prev,
        data: { nodes: [], links: [], metadata: undefined }
      }));
      graphActions.setGraphData({ nodes: [], links: [], metadata: undefined });
    },
    exportData: (format: 'json' | 'csv' | 'graphml') => {
      // Implementation for data export
      if (format === 'json') {
        return JSON.stringify(state.data, null, 2);
      }
      return '';
    },
    importData: async (data: string, format: 'json' | 'csv' | 'graphml') => {
      // Implementation for data import
      if (format === 'json') {
        try {
          const parsedData = JSON.parse(data);
          setState(prev => ({ ...prev, data: parsedData }));
          graphActions.setGraphData(parsedData);
        } catch (error) {
          console.error('Import failed:', error);
        }
      }
    }
  }), [loadInitialData, searchNodes, expandNode, graphActions, state.data]);
  
  return [state, actions];
};
