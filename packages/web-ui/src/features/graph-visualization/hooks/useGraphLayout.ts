/**
 * Graph Layout Management Hook
 * 
 * Advanced hook for managing graph layout algorithms, animations, and positioning.
 * Supports multiple layout algorithms with smooth transitions and performance optimization.
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { GraphData, GraphNode, GraphEdge, LayoutConfig } from '../types';
import { useGraphVisualization } from '../providers/GraphVisualizationProvider';

export interface LayoutState {
  algorithm: string;
  isRunning: boolean;
  progress: number;
  iterations: number;
  stabilized: boolean;
  performance: {
    fps: number;
    lastFrameTime: number;
    totalTime: number;
  };
}

export interface LayoutActions {
  applyLayout: (algorithm: string, config?: Partial<LayoutConfig>) => Promise<void>;
  stopLayout: () => void;
  pauseLayout: () => void;
  resumeLayout: () => void;
  resetPositions: () => void;
  centerGraph: () => void;
  fitToView: (padding?: number) => void;
  animateToPositions: (positions: Record<string, { x: number; y: number }>) => Promise<void>;
}

// Force-directed layout implementation
class ForceDirectedLayout {
  private nodes: GraphNode[] = [];
  private edges: GraphEdge[] = [];
  private config: LayoutConfig;
  private isRunning = false;
  private animationId: number | null = null;
  private onUpdate: (nodes: GraphNode[]) => void;
  private onProgress: (progress: number, iterations: number) => void;
  
  constructor(
    config: LayoutConfig,
    onUpdate: (nodes: GraphNode[]) => void,
    onProgress: (progress: number, iterations: number) => void
  ) {
    this.config = config;
    this.onUpdate = onUpdate;
    this.onProgress = onProgress;
  }
  
  apply(data: GraphData): void {
    this.nodes = data.nodes.map(node => ({
      ...node,
      position: node.position || {
        x: Math.random() * 800,
        y: Math.random() * 600
      },
      velocity: { x: 0, y: 0 },
      force: { x: 0, y: 0 }
    }));
    
    this.edges = data.links;
    this.isRunning = true;
    this.animate();
  }
  
  private animate = (): void => {
    if (!this.isRunning) return;
    
    const startTime = performance.now();
    
    // Calculate forces
    this.calculateForces();
    
    // Update positions
    this.updatePositions();
    
    // Check for stabilization
    const totalKineticEnergy = this.nodes.reduce((sum, node) => {
      const velocity = node.velocity || { x: 0, y: 0 };
      return sum + velocity.x * velocity.x + velocity.y * velocity.y;
    }, 0);
    
    const isStabilized = totalKineticEnergy < 0.01;
    const iterations = this.config.iterations || 0;
    const progress = Math.min(iterations / 1000, 1);
    
    this.onProgress(progress, iterations);
    this.onUpdate(this.nodes);
    
    if (!isStabilized && iterations < 1000) {
      this.config.iterations = iterations + 1;
      this.animationId = requestAnimationFrame(this.animate);
    } else {
      this.stop();
    }
    
    const endTime = performance.now();
    console.log(`Layout frame time: ${endTime - startTime}ms`);
  };
  
  private calculateForces(): void {
    // Reset forces
    this.nodes.forEach(node => {
      if (node.force) {
        node.force.x = 0;
        node.force.y = 0;
      }
    });
    
    // Repulsion forces between nodes
    for (let i = 0; i < this.nodes.length; i++) {
      for (let j = i + 1; j < this.nodes.length; j++) {
        const nodeA = this.nodes[i];
        const nodeB = this.nodes[j];
        
        if (!nodeA.position || !nodeB.position || !nodeA.force || !nodeB.force) continue;
        
        const dx = nodeA.position.x - nodeB.position.x;
        const dy = nodeA.position.y - nodeB.position.y;
        const distance = Math.sqrt(dx * dx + dy * dy) || 1;
        
        const force = this.config.nodeRepulsion / (distance * distance);
        const fx = (dx / distance) * force;
        const fy = (dy / distance) * force;
        
        nodeA.force.x += fx;
        nodeA.force.y += fy;
        nodeB.force.x -= fx;
        nodeB.force.y -= fy;
      }
    }
    
    // Attraction forces from edges
    this.edges.forEach(edge => {
      const sourceNode = this.nodes.find(n => n.id === edge.source);
      const targetNode = this.nodes.find(n => n.id === edge.target);
      
      if (!sourceNode?.position || !targetNode?.position || !sourceNode.force || !targetNode.force) return;
      
      const dx = targetNode.position.x - sourceNode.position.x;
      const dy = targetNode.position.y - sourceNode.position.y;
      const distance = Math.sqrt(dx * dx + dy * dy) || 1;
      
      const force = (distance - this.config.linkDistance) * this.config.forceStrength;
      const fx = (dx / distance) * force;
      const fy = (dy / distance) * force;
      
      sourceNode.force.x += fx;
      sourceNode.force.y += fy;
      targetNode.force.x -= fx;
      targetNode.force.y -= fy;
    });
    
    // Center force
    const centerX = 400; // Half of typical canvas width
    const centerY = 300; // Half of typical canvas height
    
    this.nodes.forEach(node => {
      if (!node.position || !node.force) return;
      
      const dx = centerX - node.position.x;
      const dy = centerY - node.position.y;
      
      node.force.x += dx * this.config.centerForce;
      node.force.y += dy * this.config.centerForce;
    });
  }
  
  private updatePositions(): void {
    this.nodes.forEach(node => {
      if (!node.position || !node.velocity || !node.force) return;
      
      // Update velocity
      node.velocity.x = (node.velocity.x + node.force.x) * this.config.velocityDecay;
      node.velocity.y = (node.velocity.y + node.force.y) * this.config.velocityDecay;
      
      // Update position
      node.position.x += node.velocity.x;
      node.position.y += node.velocity.y;
      
      // Apply collision detection
      const radius = node.style?.size || 10;
      if (node.position.x < radius) {
        node.position.x = radius;
        node.velocity.x = 0;
      }
      if (node.position.x > 800 - radius) {
        node.position.x = 800 - radius;
        node.velocity.x = 0;
      }
      if (node.position.y < radius) {
        node.position.y = radius;
        node.velocity.y = 0;
      }
      if (node.position.y > 600 - radius) {
        node.position.y = 600 - radius;
        node.velocity.y = 0;
      }
    });
  }
  
  stop(): void {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }
  
  pause(): void {
    this.isRunning = false;
  }
  
  resume(): void {
    if (!this.isRunning) {
      this.isRunning = true;
      this.animate();
    }
  }
}

// Circular layout implementation
class CircularLayout {
  apply(data: GraphData): GraphData {
    const centerX = 400;
    const centerY = 300;
    const radius = Math.min(centerX, centerY) * 0.8;
    
    const nodes = data.nodes.map((node, index) => {
      const angle = (2 * Math.PI * index) / data.nodes.length;
      return {
        ...node,
        position: {
          x: centerX + radius * Math.cos(angle),
          y: centerY + radius * Math.sin(angle)
        }
      };
    });
    
    return { ...data, nodes };
  }
}

// Grid layout implementation
class GridLayout {
  apply(data: GraphData): GraphData {
    const cols = Math.ceil(Math.sqrt(data.nodes.length));
    const cellWidth = 800 / cols;
    const cellHeight = 600 / Math.ceil(data.nodes.length / cols);
    
    const nodes = data.nodes.map((node, index) => {
      const row = Math.floor(index / cols);
      const col = index % cols;
      
      return {
        ...node,
        position: {
          x: col * cellWidth + cellWidth / 2,
          y: row * cellHeight + cellHeight / 2
        }
      };
    });
    
    return { ...data, nodes };
  }
}

export const useGraphLayout = (): [LayoutState, LayoutActions] => {
  const { actions: graphActions, selectors } = useGraphVisualization();
  const [state, setState] = useState<LayoutState>({
    algorithm: 'force',
    isRunning: false,
    progress: 0,
    iterations: 0,
    stabilized: false,
    performance: {
      fps: 60,
      lastFrameTime: 0,
      totalTime: 0
    }
  });
  
  const layoutInstanceRef = useRef<ForceDirectedLayout | null>(null);
  const graphData = selectors.getGraphData();
  const config = selectors.getConfig();
  
  // Update progress callback
  const onProgress = useCallback((progress: number, iterations: number) => {
    setState(prev => ({
      ...prev,
      progress,
      iterations,
      stabilized: progress >= 1
    }));
  }, []);
  
  // Update nodes callback
  const onUpdate = useCallback((nodes: GraphNode[]) => {
    const updatedData = {
      ...graphData,
      nodes
    };
    graphActions.setGraphData(updatedData);
  }, [graphData, graphActions]);
  
  // Apply layout algorithm
  const applyLayout = useCallback(async (
    algorithm: string,
    layoutConfig?: Partial<LayoutConfig>
  ) => {
    if (!graphData.nodes.length) return;
    
    setState(prev => ({
      ...prev,
      algorithm,
      isRunning: true,
      progress: 0,
      iterations: 0,
      stabilized: false
    }));
    
    const mergedConfig = { ...config.layout, ...layoutConfig };
    
    try {
      switch (algorithm) {
        case 'force':
          if (layoutInstanceRef.current) {
            layoutInstanceRef.current.stop();
          }
          layoutInstanceRef.current = new ForceDirectedLayout(
            mergedConfig,
            onUpdate,
            onProgress
          );
          layoutInstanceRef.current.apply(graphData);
          break;
          
        case 'circular':
          const circularLayout = new CircularLayout();
          const circularData = circularLayout.apply(graphData);
          graphActions.setGraphData(circularData);
          setState(prev => ({
            ...prev,
            isRunning: false,
            progress: 1,
            stabilized: true
          }));
          break;
          
        case 'grid':
          const gridLayout = new GridLayout();
          const gridData = gridLayout.apply(graphData);
          graphActions.setGraphData(gridData);
          setState(prev => ({
            ...prev,
            isRunning: false,
            progress: 1,
            stabilized: true
          }));
          break;
          
        default:
          console.warn(`Unknown layout algorithm: ${algorithm}`);
      }
    } catch (error) {
      console.error('Layout application failed:', error);
      setState(prev => ({
        ...prev,
        isRunning: false
      }));
    }
  }, [graphData, config.layout, onUpdate, onProgress, graphActions]);
  
  // Stop layout
  const stopLayout = useCallback(() => {
    if (layoutInstanceRef.current) {
      layoutInstanceRef.current.stop();
    }
    setState(prev => ({
      ...prev,
      isRunning: false
    }));
  }, []);
  
  // Pause layout
  const pauseLayout = useCallback(() => {
    if (layoutInstanceRef.current) {
      layoutInstanceRef.current.pause();
    }
    setState(prev => ({
      ...prev,
      isRunning: false
    }));
  }, []);
  
  // Resume layout
  const resumeLayout = useCallback(() => {
    if (layoutInstanceRef.current) {
      layoutInstanceRef.current.resume();
    }
    setState(prev => ({
      ...prev,
      isRunning: true
    }));
  }, []);
  
  // Reset positions
  const resetPositions = useCallback(() => {
    const resetData = {
      ...graphData,
      nodes: graphData.nodes.map(node => ({
        ...node,
        position: {
          x: Math.random() * 800,
          y: Math.random() * 600
        }
      }))
    };
    graphActions.setGraphData(resetData);
  }, [graphData, graphActions]);
  
  // Center graph
  const centerGraph = useCallback(() => {
    if (!graphData.nodes.length) return;
    
    // Calculate current bounds
    const bounds = graphData.nodes.reduce(
      (acc, node) => {
        if (!node.position) return acc;
        return {
          minX: Math.min(acc.minX, node.position.x),
          maxX: Math.max(acc.maxX, node.position.x),
          minY: Math.min(acc.minY, node.position.y),
          maxY: Math.max(acc.maxY, node.position.y)
        };
      },
      { minX: Infinity, maxX: -Infinity, minY: Infinity, maxY: -Infinity }
    );
    
    const centerX = (bounds.minX + bounds.maxX) / 2;
    const centerY = (bounds.minY + bounds.maxY) / 2;
    const targetX = 400; // Canvas center
    const targetY = 300; // Canvas center
    
    const offsetX = targetX - centerX;
    const offsetY = targetY - centerY;
    
    const centeredData = {
      ...graphData,
      nodes: graphData.nodes.map(node => ({
        ...node,
        position: node.position ? {
          x: node.position.x + offsetX,
          y: node.position.y + offsetY
        } : { x: targetX, y: targetY }
      }))
    };
    
    graphActions.setGraphData(centeredData);
  }, [graphData, graphActions]);
  
  // Fit to view
  const fitToView = useCallback((padding: number = 50) => {
    if (!graphData.nodes.length) return;
    
    // Calculate bounds
    const bounds = graphData.nodes.reduce(
      (acc, node) => {
        if (!node.position) return acc;
        return {
          minX: Math.min(acc.minX, node.position.x),
          maxX: Math.max(acc.maxX, node.position.x),
          minY: Math.min(acc.minY, node.position.y),
          maxY: Math.max(acc.maxY, node.position.y)
        };
      },
      { minX: Infinity, maxX: -Infinity, minY: Infinity, maxY: -Infinity }
    );
    
    const graphWidth = bounds.maxX - bounds.minX;
    const graphHeight = bounds.maxY - bounds.minY;
    const canvasWidth = 800 - 2 * padding;
    const canvasHeight = 600 - 2 * padding;
    
    const scaleX = canvasWidth / graphWidth;
    const scaleY = canvasHeight / graphHeight;
    const scale = Math.min(scaleX, scaleY, 1); // Don't scale up
    
    const centerX = (bounds.minX + bounds.maxX) / 2;
    const centerY = (bounds.minY + bounds.maxY) / 2;
    const targetX = 400; // Canvas center
    const targetY = 300; // Canvas center
    
    const fittedData = {
      ...graphData,
      nodes: graphData.nodes.map(node => ({
        ...node,
        position: node.position ? {
          x: targetX + (node.position.x - centerX) * scale,
          y: targetY + (node.position.y - centerY) * scale
        } : { x: targetX, y: targetY }
      }))
    };
    
    graphActions.setGraphData(fittedData);
  }, [graphData, graphActions]);
  
  // Animate to positions
  const animateToPositions = useCallback(async (
    positions: Record<string, { x: number; y: number }>
  ): Promise<void> => {
    return new Promise((resolve) => {
      const duration = 1000; // 1 second
      const startTime = performance.now();
      const startPositions = graphData.nodes.reduce((acc, node) => {
        acc[node.id] = node.position || { x: 0, y: 0 };
        return acc;
      }, {} as Record<string, { x: number; y: number }>);
      
      const animate = (currentTime: number) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function (ease-out)
        const easeOut = 1 - Math.pow(1 - progress, 3);
        
        const animatedData = {
          ...graphData,
          nodes: graphData.nodes.map(node => {
            const start = startPositions[node.id];
            const end = positions[node.id];
            
            if (!start || !end) return node;
            
            return {
              ...node,
              position: {
                x: start.x + (end.x - start.x) * easeOut,
                y: start.y + (end.y - start.y) * easeOut
              }
            };
          })
        };
        
        graphActions.setGraphData(animatedData);
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          resolve();
        }
      };
      
      requestAnimationFrame(animate);
    });
  }, [graphData, graphActions]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (layoutInstanceRef.current) {
        layoutInstanceRef.current.stop();
      }
    };
  }, []);
  
  const actions: LayoutActions = useMemo(() => ({
    applyLayout,
    stopLayout,
    pauseLayout,
    resumeLayout,
    resetPositions,
    centerGraph,
    fitToView,
    animateToPositions
  }), [
    applyLayout,
    stopLayout,
    pauseLayout,
    resumeLayout,
    resetPositions,
    centerGraph,
    fitToView,
    animateToPositions
  ]);
  
  return [state, actions];
};
