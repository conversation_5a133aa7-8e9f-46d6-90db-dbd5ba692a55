/**
 * Graph Interaction Management Hook
 * 
 * Advanced hook for managing graph interactions including selection, dragging,
 * zooming, panning, and context menus with gesture support.
 */

import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { GraphNode, GraphEdge } from '../types';
import { useGraphVisualization } from '../providers/GraphVisualizationProvider';

export interface InteractionState {
  mode: 'select' | 'pan' | 'zoom' | 'drag' | 'area-select';
  isDragging: boolean;
  isPanning: boolean;
  isZooming: boolean;
  draggedNode: string | null;
  selectionBox: {
    start: { x: number; y: number } | null;
    end: { x: number; y: number } | null;
    active: boolean;
  };
  contextMenu: {
    visible: boolean;
    position: { x: number; y: number };
    target: { type: 'node' | 'edge' | 'canvas'; id?: string } | null;
  };
  gesture: {
    touches: Touch[];
    lastDistance: number;
    lastCenter: { x: number; y: number };
  };
}

export interface InteractionActions {
  setMode: (mode: InteractionState['mode']) => void;
  handleNodeMouseDown: (node: GraphNode, event: MouseEvent) => void;
  handleNodeMouseUp: (node: GraphNode, event: MouseEvent) => void;
  handleNodeClick: (node: GraphNode, event: MouseEvent) => void;
  handleNodeDoubleClick: (node: GraphNode, event: MouseEvent) => void;
  handleNodeContextMenu: (node: GraphNode, event: MouseEvent) => void;
  handleEdgeClick: (edge: GraphEdge, event: MouseEvent) => void;
  handleEdgeContextMenu: (edge: GraphEdge, event: MouseEvent) => void;
  handleCanvasMouseDown: (event: MouseEvent) => void;
  handleCanvasMouseMove: (event: MouseEvent) => void;
  handleCanvasMouseUp: (event: MouseEvent) => void;
  handleCanvasClick: (event: MouseEvent) => void;
  handleCanvasContextMenu: (event: MouseEvent) => void;
  handleWheel: (event: WheelEvent) => void;
  handleTouchStart: (event: TouchEvent) => void;
  handleTouchMove: (event: TouchEvent) => void;
  handleTouchEnd: (event: TouchEvent) => void;
  closeContextMenu: () => void;
  selectArea: (start: { x: number; y: number }, end: { x: number; y: number }) => void;
}

export const useGraphInteraction = (): [InteractionState, InteractionActions] => {
  const { actions: graphActions, selectors } = useGraphVisualization();
  const [state, setState] = useState<InteractionState>({
    mode: 'select',
    isDragging: false,
    isPanning: false,
    isZooming: false,
    draggedNode: null,
    selectionBox: {
      start: null,
      end: null,
      active: false
    },
    contextMenu: {
      visible: false,
      position: { x: 0, y: 0 },
      target: null
    },
    gesture: {
      touches: [],
      lastDistance: 0,
      lastCenter: { x: 0, y: 0 }
    }
  });
  
  const graphData = selectors.getGraphData();
  const config = selectors.getConfig();
  const viewport = selectors.getViewport();
  
  const dragStartRef = useRef<{ x: number; y: number } | null>(null);
  const panStartRef = useRef<{ x: number; y: number; panX: number; panY: number } | null>(null);
  
  // Transform screen coordinates to graph coordinates
  const screenToGraph = useCallback((screenX: number, screenY: number) => {
    return {
      x: (screenX - viewport.pan.x) / viewport.zoom,
      y: (screenY - viewport.pan.y) / viewport.zoom
    };
  }, [viewport]);
  
  // Transform graph coordinates to screen coordinates
  const graphToScreen = useCallback((graphX: number, graphY: number) => {
    return {
      x: graphX * viewport.zoom + viewport.pan.x,
      y: graphY * viewport.zoom + viewport.pan.y
    };
  }, [viewport]);
  
  // Find node at position
  const findNodeAtPosition = useCallback((x: number, y: number): GraphNode | null => {
    const graphPos = screenToGraph(x, y);
    
    return graphData.nodes.find(node => {
      if (!node.position) return false;
      
      const nodeSize = node.style?.size || 10;
      const distance = Math.sqrt(
        Math.pow(node.position.x - graphPos.x, 2) +
        Math.pow(node.position.y - graphPos.y, 2)
      );
      
      return distance <= nodeSize;
    }) || null;
  }, [graphData.nodes, screenToGraph]);
  
  // Find edge at position
  const findEdgeAtPosition = useCallback((x: number, y: number): GraphEdge | null => {
    const graphPos = screenToGraph(x, y);
    const threshold = 5; // pixels
    
    return graphData.links.find(edge => {
      const sourceNode = graphData.nodes.find(n => n.id === edge.source);
      const targetNode = graphData.nodes.find(n => n.id === edge.target);
      
      if (!sourceNode?.position || !targetNode?.position) return false;
      
      // Calculate distance from point to line segment
      const A = graphPos.x - sourceNode.position.x;
      const B = graphPos.y - sourceNode.position.y;
      const C = targetNode.position.x - sourceNode.position.x;
      const D = targetNode.position.y - sourceNode.position.y;
      
      const dot = A * C + B * D;
      const lenSq = C * C + D * D;
      
      if (lenSq === 0) return false;
      
      const param = dot / lenSq;
      
      let xx, yy;
      if (param < 0) {
        xx = sourceNode.position.x;
        yy = sourceNode.position.y;
      } else if (param > 1) {
        xx = targetNode.position.x;
        yy = targetNode.position.y;
      } else {
        xx = sourceNode.position.x + param * C;
        yy = sourceNode.position.y + param * D;
      }
      
      const dx = graphPos.x - xx;
      const dy = graphPos.y - yy;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      return distance <= threshold;
    }) || null;
  }, [graphData.nodes, graphData.links, screenToGraph]);
  
  // Set interaction mode
  const setMode = useCallback((mode: InteractionState['mode']) => {
    setState(prev => ({ ...prev, mode }));
  }, []);
  
  // Node interactions
  const handleNodeMouseDown = useCallback((node: GraphNode, event: MouseEvent) => {
    if (!config.interaction.dragging) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    setState(prev => ({
      ...prev,
      isDragging: true,
      draggedNode: node.id
    }));
    
    dragStartRef.current = { x: event.clientX, y: event.clientY };
  }, [config.interaction.dragging]);
  
  const handleNodeMouseUp = useCallback((_node: GraphNode, _event: MouseEvent) => {
    setState(prev => ({
      ...prev,
      isDragging: false,
      draggedNode: null
    }));

    dragStartRef.current = null;
  }, []);
  
  const handleNodeClick = useCallback((node: GraphNode, event: MouseEvent) => {
    if (!config.interaction.selection) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    const isMultiSelect = event.ctrlKey || event.metaKey;
    const currentSelection = selectors.getSelectedNodes();
    
    if (isMultiSelect) {
      if (currentSelection.includes(node.id)) {
        // Remove from selection
        graphActions.selectNodes(currentSelection.filter(id => id !== node.id));
      } else {
        // Add to selection
        graphActions.selectNodes([...currentSelection, node.id]);
      }
    } else {
      // Single selection
      graphActions.selectNodes([node.id]);
    }
  }, [config.interaction.selection, selectors, graphActions]);
  
  const handleNodeDoubleClick = useCallback((node: GraphNode, event: MouseEvent) => {
    if (!config.interaction.doubleClick) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    // Trigger node expansion or custom action
    console.log('Node double-clicked:', node);
  }, [config.interaction.doubleClick]);
  
  const handleNodeContextMenu = useCallback((node: GraphNode, event: MouseEvent) => {
    if (!config.interaction.contextMenu) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    setState(prev => ({
      ...prev,
      contextMenu: {
        visible: true,
        position: { x: event.clientX, y: event.clientY },
        target: { type: 'node', id: node.id }
      }
    }));
  }, [config.interaction.contextMenu]);
  
  // Edge interactions
  const handleEdgeClick = useCallback((edge: GraphEdge, event: MouseEvent) => {
    if (!config.interaction.selection) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    const isMultiSelect = event.ctrlKey || event.metaKey;
    const currentSelection = selectors.getSelectedEdges();
    
    if (isMultiSelect) {
      if (currentSelection.includes(edge.id)) {
        graphActions.selectEdges(currentSelection.filter(id => id !== edge.id));
      } else {
        graphActions.selectEdges([...currentSelection, edge.id]);
      }
    } else {
      graphActions.selectEdges([edge.id]);
    }
  }, [config.interaction.selection, selectors, graphActions]);
  
  const handleEdgeContextMenu = useCallback((edge: GraphEdge, event: MouseEvent) => {
    if (!config.interaction.contextMenu) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    setState(prev => ({
      ...prev,
      contextMenu: {
        visible: true,
        position: { x: event.clientX, y: event.clientY },
        target: { type: 'edge', id: edge.id }
      }
    }));
  }, [config.interaction.contextMenu]);
  
  // Canvas interactions
  const handleCanvasMouseDown = useCallback((event: MouseEvent) => {
    const clickedNode = findNodeAtPosition(event.clientX, event.clientY);
    const clickedEdge = findEdgeAtPosition(event.clientX, event.clientY);
    
    if (clickedNode) {
      handleNodeMouseDown(clickedNode, event);
      return;
    }
    
    if (clickedEdge) {
      handleEdgeClick(clickedEdge, event);
      return;
    }
    
    // Start panning or area selection
    if (config.interaction.panning && !event.ctrlKey) {
      setState(prev => ({ ...prev, isPanning: true }));
      panStartRef.current = {
        x: event.clientX,
        y: event.clientY,
        panX: viewport.pan.x,
        panY: viewport.pan.y
      };
    } else if (state.mode === 'area-select') {
      setState(prev => ({
        ...prev,
        selectionBox: {
          start: { x: event.clientX, y: event.clientY },
          end: { x: event.clientX, y: event.clientY },
          active: true
        }
      }));
    }
  }, [
    findNodeAtPosition,
    findEdgeAtPosition,
    handleNodeMouseDown,
    handleEdgeClick,
    config.interaction.panning,
    viewport.pan,
    state.mode
  ]);
  
  const handleCanvasMouseMove = useCallback((event: MouseEvent) => {
    // Handle node dragging
    if (state.isDragging && state.draggedNode && dragStartRef.current) {
      const dx = event.clientX - dragStartRef.current.x;
      const dy = event.clientY - dragStartRef.current.y;
      
      const updatedNodes = graphData.nodes.map(node => {
        if (node.id === state.draggedNode && node.position) {
          return {
            ...node,
            position: {
              x: node.position.x + dx / viewport.zoom,
              y: node.position.y + dy / viewport.zoom
            }
          };
        }
        return node;
      });
      
      graphActions.setGraphData({ ...graphData, nodes: updatedNodes });
      dragStartRef.current = { x: event.clientX, y: event.clientY };
      return;
    }
    
    // Handle panning
    if (state.isPanning && panStartRef.current) {
      const dx = event.clientX - panStartRef.current.x;
      const dy = event.clientY - panStartRef.current.y;
      
      graphActions.setPan({
        x: panStartRef.current.panX + dx,
        y: panStartRef.current.panY + dy
      });
      return;
    }
    
    // Handle area selection
    if (state.selectionBox.active && state.selectionBox.start) {
      setState(prev => ({
        ...prev,
        selectionBox: {
          ...prev.selectionBox,
          end: { x: event.clientX, y: event.clientY }
        }
      }));
    }
  }, [
    state.isDragging,
    state.draggedNode,
    state.isPanning,
    state.selectionBox,
    graphData,
    viewport.zoom,
    graphActions
  ]);
  
  const handleCanvasMouseUp = useCallback((_event: MouseEvent) => {
    // End dragging
    if (state.isDragging) {
      setState(prev => ({
        ...prev,
        isDragging: false,
        draggedNode: null
      }));
      dragStartRef.current = null;
    }
    
    // End panning
    if (state.isPanning) {
      setState(prev => ({ ...prev, isPanning: false }));
      panStartRef.current = null;
    }
    
    // End area selection
    if (state.selectionBox.active && state.selectionBox.start && state.selectionBox.end) {
      selectArea(state.selectionBox.start, state.selectionBox.end);
      setState(prev => ({
        ...prev,
        selectionBox: {
          start: null,
          end: null,
          active: false
        }
      }));
    }
  }, [state.isDragging, state.isPanning, state.selectionBox]);
  
  const handleCanvasClick = useCallback((event: MouseEvent) => {
    // Clear selection if clicking on empty space
    if (!event.ctrlKey && !event.metaKey) {
      graphActions.clearSelection();
    }
    
    // Close context menu
    setState(prev => ({
      ...prev,
      contextMenu: { ...prev.contextMenu, visible: false }
    }));
  }, [graphActions]);
  
  const handleCanvasContextMenu = useCallback((event: MouseEvent) => {
    if (!config.interaction.contextMenu) return;
    
    event.preventDefault();
    
    setState(prev => ({
      ...prev,
      contextMenu: {
        visible: true,
        position: { x: event.clientX, y: event.clientY },
        target: { type: 'canvas' }
      }
    }));
  }, [config.interaction.contextMenu]);
  
  // Zoom handling
  const handleWheel = useCallback((event: WheelEvent) => {
    if (!config.interaction.zooming) return;
    
    event.preventDefault();
    
    const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
    const newZoom = Math.max(0.1, Math.min(5, viewport.zoom * zoomFactor));
    
    // Zoom towards mouse position
    const rect = (event.target as Element).getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;
    
    const newPanX = mouseX - (mouseX - viewport.pan.x) * (newZoom / viewport.zoom);
    const newPanY = mouseY - (mouseY - viewport.pan.y) * (newZoom / viewport.zoom);
    
    graphActions.setZoom(newZoom);
    graphActions.setPan({ x: newPanX, y: newPanY });
  }, [config.interaction.zooming, viewport, graphActions]);
  
  // Touch handling for mobile
  const handleTouchStart = useCallback((event: TouchEvent) => {
    const touches = Array.from(event.touches);
    setState(prev => ({
      ...prev,
      gesture: {
        ...prev.gesture,
        touches
      }
    }));
  }, []);
  
  const handleTouchMove = useCallback((event: TouchEvent) => {
    event.preventDefault();
    
    const touches = Array.from(event.touches);
    
    if (touches.length === 2) {
      // Pinch to zoom
      const touch1 = touches[0];
      const touch2 = touches[1];
      
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      
      const center = {
        x: (touch1.clientX + touch2.clientX) / 2,
        y: (touch1.clientY + touch2.clientY) / 2
      };
      
      if (state.gesture.lastDistance > 0) {
        const zoomFactor = distance / state.gesture.lastDistance;
        const newZoom = Math.max(0.1, Math.min(5, viewport.zoom * zoomFactor));
        graphActions.setZoom(newZoom);
      }
      
      setState(prev => ({
        ...prev,
        gesture: {
          ...prev.gesture,
          touches,
          lastDistance: distance,
          lastCenter: center
        }
      }));
    }
  }, [state.gesture.lastDistance, viewport.zoom, graphActions]);
  
  const handleTouchEnd = useCallback((_event: TouchEvent) => {
    setState(prev => ({
      ...prev,
      gesture: {
        touches: [],
        lastDistance: 0,
        lastCenter: { x: 0, y: 0 }
      }
    }));
  }, []);
  
  // Close context menu
  const closeContextMenu = useCallback(() => {
    setState(prev => ({
      ...prev,
      contextMenu: { ...prev.contextMenu, visible: false }
    }));
  }, []);
  
  // Area selection
  const selectArea = useCallback((
    start: { x: number; y: number },
    end: { x: number; y: number }
  ) => {
    const minX = Math.min(start.x, end.x);
    const maxX = Math.max(start.x, end.x);
    const minY = Math.min(start.y, end.y);
    const maxY = Math.max(start.y, end.y);
    
    const selectedNodes = graphData.nodes
      .filter(node => {
        if (!node.position) return false;
        
        const screenPos = graphToScreen(node.position.x, node.position.y);
        return screenPos.x >= minX && screenPos.x <= maxX &&
               screenPos.y >= minY && screenPos.y <= maxY;
      })
      .map(node => node.id);
    
    graphActions.selectNodes(selectedNodes);
  }, [graphData.nodes, graphToScreen, graphActions]);
  
  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!config.interaction.keyboard) return;
      
      switch (event.key) {
        case 'Escape':
          graphActions.clearSelection();
          closeContextMenu();
          break;
        case 'Delete':
        case 'Backspace':
          // Delete selected nodes/edges
          const selectedNodes = selectors.getSelectedNodes();
          const selectedEdges = selectors.getSelectedEdges();
          
          if (selectedNodes.length > 0 || selectedEdges.length > 0) {
            // Implement deletion logic
            console.log('Delete selected items:', { selectedNodes, selectedEdges });
          }
          break;
        case 'a':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            // Select all
            graphActions.selectNodes(graphData.nodes.map(n => n.id));
          }
          break;
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [config.interaction.keyboard, graphActions, selectors, graphData.nodes, closeContextMenu]);
  
  const actions: InteractionActions = useMemo(() => ({
    setMode,
    handleNodeMouseDown,
    handleNodeMouseUp,
    handleNodeClick,
    handleNodeDoubleClick,
    handleNodeContextMenu,
    handleEdgeClick,
    handleEdgeContextMenu,
    handleCanvasMouseDown,
    handleCanvasMouseMove,
    handleCanvasMouseUp,
    handleCanvasClick,
    handleCanvasContextMenu,
    handleWheel,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    closeContextMenu,
    selectArea
  }), [
    setMode,
    handleNodeMouseDown,
    handleNodeMouseUp,
    handleNodeClick,
    handleNodeDoubleClick,
    handleNodeContextMenu,
    handleEdgeClick,
    handleEdgeContextMenu,
    handleCanvasMouseDown,
    handleCanvasMouseMove,
    handleCanvasMouseUp,
    handleCanvasClick,
    handleCanvasContextMenu,
    handleWheel,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    closeContextMenu,
    selectArea
  ]);
  
  return [state, actions];
};
