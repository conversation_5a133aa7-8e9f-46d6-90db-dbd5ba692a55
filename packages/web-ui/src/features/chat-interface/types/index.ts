/**
 * Chat Interface Types
 * 
 * Type definitions for the chat interface feature.
 */

// Import shared chat types
import type {
  ChatMessage,
  ChatMessageMetadata,
  ChatSource,
  ChatSession,
  ChatSessionMetadata,
  ChatContext,
  ChatUserPreferences,
  ChatOptions,
  LLMProvider,
  LLMModel,
  LLMCapabilities,
  LLMProviderConfig,
  ChatStreamChunk,
  ChatStreamResponse,
  ChatConfig
} from '@kg-visualizer/shared/types/chat';

// Re-export for external use
export type {
  ChatMessage,
  ChatMessageMetadata,
  ChatSource,
  ChatSession,
  ChatSessionMetadata,
  ChatContext,
  ChatUserPreferences,
  ChatOptions,
  LLMProvider,
  LLMModel,
  LLMCapabilities,
  LLMProviderConfig,
  ChatStreamChunk,
  ChatStreamResponse,
  ChatConfig
};

// Feature-specific types
export interface ChatState {
  // Current session
  currentSession: ChatSession | null;
  
  // All sessions
  sessions: ChatSession[];
  
  // UI state
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;
  
  // Input state
  inputValue: string;
  
  // Configuration
  config: ChatConfig;
  
  // Performance tracking
  performance: {
    lastMessageTime: number;
    averageResponseTime: number;
    totalMessages: number;
  };
  
  // UI preferences
  uiState: {
    showSources: boolean;
    showThinking: boolean;
    showTimestamps: boolean;
    compactMode: boolean;
  };
}

export interface ChatActions {
  // Session management
  createSession: (title?: string) => Promise<ChatSession>;
  loadSession: (sessionId: string) => Promise<void>;
  deleteSession: (sessionId: string) => Promise<void>;
  updateSessionTitle: (sessionId: string, title: string) => Promise<void>;
  
  // Message handling
  sendMessage: (content: string, options?: ChatOptions) => Promise<void>;
  retryMessage: (messageId: string) => Promise<void>;
  editMessage: (messageId: string, newContent: string) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
  
  // Streaming
  startStreaming: (content: string, options?: ChatOptions) => Promise<void>;
  stopStreaming: () => void;
  
  // Input management
  setInputValue: (value: string) => void;
  clearInput: () => void;
  
  // Configuration
  updateConfig: (config: Partial<ChatConfig>) => void;
  updateUserPreferences: (preferences: Partial<ChatUserPreferences>) => void;
  
  // UI state
  toggleSources: () => void;
  toggleThinking: () => void;
  toggleTimestamps: () => void;
  toggleCompactMode: () => void;
  
  // Error handling
  clearError: () => void;
  setError: (error: string) => void;
}

export interface ChatSelectors {
  // Session selectors
  getCurrentSession: () => ChatSession | null;
  getSessions: () => ChatSession[];
  getSessionById: (id: string) => ChatSession | null;
  
  // Message selectors
  getMessages: () => ChatMessage[];
  getLastMessage: () => ChatMessage | null;
  getMessageById: (id: string) => ChatMessage | null;
  
  // State selectors
  getIsLoading: () => boolean;
  getIsStreaming: () => boolean;
  getError: () => string | null;
  getInputValue: () => string;
  
  // Configuration selectors
  getConfig: () => ChatConfig;
  getUserPreferences: () => ChatUserPreferences;
  
  // UI state selectors
  getShowSources: () => boolean;
  getShowThinking: () => boolean;
  getShowTimestamps: () => boolean;
  getCompactMode: () => boolean;
  
  // Performance selectors
  getPerformanceMetrics: () => ChatState['performance'];
  
  // Computed selectors
  getCanSend: () => boolean;
  getHasMessages: () => boolean;
  getSessionCount: () => number;
}

// Event types
export interface ChatEvent {
  type: string;
  payload?: any;
  timestamp: Date;
}

export interface MessageEvent extends ChatEvent {
  type: 'message:sent' | 'message:received' | 'message:error' | 'message:retry';
  payload: {
    messageId: string;
    sessionId: string;
    content?: string;
    error?: string;
  };
}

export interface SessionEvent extends ChatEvent {
  type: 'session:created' | 'session:loaded' | 'session:deleted' | 'session:updated';
  payload: {
    sessionId: string;
    session?: ChatSession;
  };
}

export interface StreamEvent extends ChatEvent {
  type: 'stream:start' | 'stream:token' | 'stream:complete' | 'stream:error';
  payload: {
    messageId: string;
    token?: string;
    complete?: boolean;
    error?: string;
  };
}

// Component props
export interface ChatInterfaceProps {
  className?: string;
  style?: React.CSSProperties;
  placeholder?: string;
  maxHeight?: number;
  enableStreaming?: boolean;
  enableSources?: boolean;
  enableThinking?: boolean;
  onNodeSelect?: (nodeId: string) => void;
  onError?: (error: Error) => void;
}

export interface MessageListProps {
  messages: ChatMessage[];
  isStreaming?: boolean;
  showSources?: boolean;
  showThinking?: boolean;
  showTimestamps?: boolean;
  compactMode?: boolean;
  onMessageEdit?: (messageId: string, content: string) => void;
  onMessageDelete?: (messageId: string) => void;
  onMessageRetry?: (messageId: string) => void;
  onNodeSelect?: (nodeId: string) => void;
}

export interface MessageInputProps {
  value: string;
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
  maxLength?: number;
  onSend: (message: string) => void;
  onChange: (value: string) => void;
  onKeyDown?: (event: React.KeyboardEvent) => void;
}

export interface SessionListProps {
  sessions: ChatSession[];
  currentSessionId?: string;
  onSessionSelect: (sessionId: string) => void;
  onSessionDelete: (sessionId: string) => void;
  onSessionCreate: () => void;
  onSessionRename: (sessionId: string, title: string) => void;
}

// Hook return types
export interface UseChatReturn {
  state: ChatState;
  actions: ChatActions;
  selectors: ChatSelectors;
}

export interface UseChatSessionReturn {
  session: ChatSession | null;
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (content: string) => Promise<void>;
  loadSession: (sessionId: string) => Promise<void>;
  createSession: (title?: string) => Promise<ChatSession>;
}

export interface UseChatStreamingReturn {
  isStreaming: boolean;
  streamingMessage: ChatMessage | null;
  startStreaming: (content: string) => Promise<void>;
  stopStreaming: () => void;
  error: string | null;
}

// Service interfaces
export interface ChatApiService {
  sendMessage: (content: string, sessionId: string, options?: ChatOptions) => Promise<ChatMessage>;
  getSession: (sessionId: string) => Promise<ChatSession>;
  createSession: (title?: string) => Promise<ChatSession>;
  updateSession: (sessionId: string, updates: Partial<ChatSession>) => Promise<ChatSession>;
  deleteSession: (sessionId: string) => Promise<void>;
  getSessions: () => Promise<ChatSession[]>;
  streamMessage: (content: string, sessionId: string, options?: ChatOptions) => Promise<ReadableStream>;
}

export interface ChatStorageService {
  saveSession: (session: ChatSession) => Promise<void>;
  loadSession: (sessionId: string) => Promise<ChatSession | null>;
  loadSessions: () => Promise<ChatSession[]>;
  deleteSession: (sessionId: string) => Promise<void>;
  saveConfig: (config: ChatConfig) => Promise<void>;
  loadConfig: () => Promise<ChatConfig | null>;
  saveUserPreferences: (preferences: ChatUserPreferences) => Promise<void>;
  loadUserPreferences: () => Promise<ChatUserPreferences | null>;
}
