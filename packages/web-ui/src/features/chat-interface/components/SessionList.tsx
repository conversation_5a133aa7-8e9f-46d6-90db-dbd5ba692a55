/**
 * Session List Component
 * 
 * Displays and manages chat sessions with search and organization.
 */

import React, { useState, useCallback, useMemo } from 'react';
import { SessionListProps, ChatSession } from '../types';

export const SessionList: React.FC<SessionListProps> = ({
  sessions,
  currentSessionId,
  onSessionSelect,
  onSessionDelete,
  onSessionCreate,
  onSessionRename
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState('');

  // Filter sessions based on search query
  const filteredSessions = useMemo(() => {
    if (!searchQuery.trim()) return sessions;
    
    const query = searchQuery.toLowerCase();
    return sessions.filter(session =>
      (session.title || '').toLowerCase().includes(query) ||
      session.messages.some((msg: any) =>
        msg.content.toLowerCase().includes(query)
      )
    );
  }, [sessions, searchQuery]);

  // Sort sessions by last activity
  const sortedSessions = useMemo(() => {
    return [...filteredSessions].sort((a, b) => 
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    );
  }, [filteredSessions]);

  // Handle session selection
  const handleSessionClick = useCallback((sessionId: string) => {
    if (editingSessionId === sessionId) return;
    onSessionSelect(sessionId);
  }, [editingSessionId, onSessionSelect]);

  // Handle session deletion
  const handleDeleteClick = useCallback((event: React.MouseEvent, sessionId: string) => {
    event.stopPropagation();
    if (window.confirm('Are you sure you want to delete this chat session?')) {
      onSessionDelete(sessionId);
    }
  }, [onSessionDelete]);

  // Handle edit start
  const handleEditStart = useCallback((event: React.MouseEvent, session: ChatSession) => {
    event.stopPropagation();
    setEditingSessionId(session.id);
    setEditTitle(session.title || '');
  }, []);

  // Handle edit save
  const handleEditSave = useCallback(() => {
    if (editingSessionId && editTitle.trim() !== '') {
      onSessionRename(editingSessionId, editTitle.trim());
    }
    setEditingSessionId(null);
    setEditTitle('');
  }, [editingSessionId, editTitle, onSessionRename]);

  // Handle edit cancel
  const handleEditCancel = useCallback(() => {
    setEditingSessionId(null);
    setEditTitle('');
  }, []);

  // Handle edit key press
  const handleEditKeyPress = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleEditSave();
    } else if (event.key === 'Escape') {
      handleEditCancel();
    }
  }, [handleEditSave, handleEditCancel]);

  // Format relative time
  const formatRelativeTime = (timestamp: string) => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#f8f9fa'
    }}>
      {/* Header */}
      <div style={{
        padding: '16px',
        borderBottom: '1px solid #e1e5e9'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '12px'
        }}>
          <h3 style={{
            margin: 0,
            fontSize: '16px',
            fontWeight: '600',
            color: '#24292f'
          }}>
            Chat Sessions
          </h3>
          
          <button
            onClick={onSessionCreate}
            style={{
              padding: '6px 12px',
              backgroundColor: '#238636',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '12px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '4px'
            }}
            title="New Chat"
          >
            <span>+</span>
            <span>New</span>
          </button>
        </div>

        {/* Search */}
        <input
          type="text"
          placeholder="Search sessions..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          style={{
            width: '100%',
            padding: '8px 12px',
            border: '1px solid #d0d7de',
            borderRadius: '6px',
            fontSize: '14px',
            backgroundColor: '#ffffff'
          }}
        />
      </div>

      {/* Session List */}
      <div style={{
        flex: 1,
        overflowY: 'auto',
        padding: '8px'
      }}>
        {sortedSessions.length === 0 ? (
          <div style={{
            padding: '20px',
            textAlign: 'center',
            color: '#656d76',
            fontSize: '14px'
          }}>
            {searchQuery ? 'No sessions found' : 'No chat sessions yet'}
          </div>
        ) : (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '4px'
          }}>
            {sortedSessions.map((session) => (
              <div
                key={session.id}
                onClick={() => handleSessionClick(session.id)}
                style={{
                  padding: '12px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  backgroundColor: session.id === currentSessionId ? '#e6f3ff' : '#ffffff',
                  border: session.id === currentSessionId ? '1px solid #0969da' : '1px solid #e1e5e9',
                  transition: 'all 0.2s',
                  position: 'relative'
                }}
                onMouseEnter={(e) => {
                  if (session.id !== currentSessionId) {
                    e.currentTarget.style.backgroundColor = '#f6f8fa';
                  }
                }}
                onMouseLeave={(e) => {
                  if (session.id !== currentSessionId) {
                    e.currentTarget.style.backgroundColor = '#ffffff';
                  }
                }}
              >
                {/* Session Title */}
                {editingSessionId === session.id ? (
                  <input
                    type="text"
                    value={editTitle}
                    onChange={(e) => setEditTitle(e.target.value)}
                    onKeyDown={handleEditKeyPress}
                    onBlur={handleEditSave}
                    style={{
                      width: '100%',
                      padding: '4px 8px',
                      border: '1px solid #0969da',
                      borderRadius: '4px',
                      fontSize: '14px',
                      fontWeight: '600'
                    }}
                    autoFocus
                  />
                ) : (
                  <div style={{
                    fontSize: '14px',
                    fontWeight: '600',
                    color: '#24292f',
                    marginBottom: '4px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {session.title}
                  </div>
                )}

                {/* Session Info */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  fontSize: '12px',
                  color: '#656d76'
                }}>
                  <span>
                    {session.messages.length} messages
                  </span>
                  <span>
                    {formatRelativeTime(session.updatedAt)}
                  </span>
                </div>

                {/* Last Message Preview */}
                {session.messages.length > 0 && (
                  <div style={{
                    marginTop: '6px',
                    fontSize: '12px',
                    color: '#656d76',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {session.messages[session.messages.length - 1].content.slice(0, 60)}
                    {session.messages[session.messages.length - 1].content.length > 60 && '...'}
                  </div>
                )}

                {/* Actions */}
                <div style={{
                  position: 'absolute',
                  top: '8px',
                  right: '8px',
                  display: 'flex',
                  gap: '4px',
                  opacity: 0,
                  transition: 'opacity 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.opacity = '1';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.opacity = '0';
                }}
                >
                  <button
                    onClick={(e) => handleEditStart(e, session)}
                    style={{
                      padding: '4px',
                      backgroundColor: 'transparent',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      color: '#656d76'
                    }}
                    title="Rename session"
                  >
                    ✏️
                  </button>
                  
                  <button
                    onClick={(e) => handleDeleteClick(e, session.id)}
                    style={{
                      padding: '4px',
                      backgroundColor: 'transparent',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      color: '#d73a49'
                    }}
                    title="Delete session"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div style={{
        padding: '12px 16px',
        borderTop: '1px solid #e1e5e9',
        fontSize: '12px',
        color: '#656d76',
        textAlign: 'center'
      }}>
        {sessions.length} session{sessions.length !== 1 ? 's' : ''} total
      </div>
    </div>
  );
};
