/**
 * Chat Settings Component
 * 
 * Settings panel for chat configuration and preferences.
 */

import React, { useState, useCallback } from 'react';
import { useChat } from '../providers/ChatProvider';

interface ChatSettingsProps {
  onClose: () => void;
}

export const ChatSettings: React.FC<ChatSettingsProps> = ({ onClose }) => {
  const { actions, selectors } = useChat();
  const [activeTab, setActiveTab] = useState<'general' | 'appearance' | 'advanced'>('general');

  const config = selectors.getConfig();
  const showSources = selectors.getShowSources();
  const showThinking = selectors.getShowThinking();
  const showTimestamps = selectors.getShowTimestamps();
  const compactMode = selectors.getCompactMode();
  const performanceMetrics = selectors.getPerformanceMetrics();

  // Handle config updates
  const handleConfigUpdate = useCallback((updates: any) => {
    actions.updateConfig(updates);
  }, [actions]);

  const tabs = [
    { id: 'general', label: 'General', icon: '⚙️' },
    { id: 'appearance', label: 'Appearance', icon: '🎨' },
    { id: 'advanced', label: 'Advanced', icon: '🔧' }
  ] as const;

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#f8f9fa'
    }}>
      {/* Header */}
      <div style={{
        padding: '16px',
        borderBottom: '1px solid #e1e5e9',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <h3 style={{
          margin: 0,
          fontSize: '16px',
          fontWeight: '600',
          color: '#24292f'
        }}>
          Chat Settings
        </h3>
        
        <button
          onClick={onClose}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '18px',
            cursor: 'pointer',
            color: '#656d76',
            padding: '4px'
          }}
          title="Close settings"
        >
          ✕
        </button>
      </div>

      {/* Tabs */}
      <div style={{
        display: 'flex',
        borderBottom: '1px solid #e1e5e9'
      }}>
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            style={{
              flex: 1,
              padding: '12px 8px',
              border: 'none',
              backgroundColor: activeTab === tab.id ? '#ffffff' : 'transparent',
              color: activeTab === tab.id ? '#0969da' : '#656d76',
              cursor: 'pointer',
              fontSize: '12px',
              fontWeight: activeTab === tab.id ? '600' : '400',
              borderBottom: activeTab === tab.id ? '2px solid #0969da' : '2px solid transparent',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '4px'
            }}
          >
            <span style={{ fontSize: '16px' }}>{tab.icon}</span>
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      <div style={{
        flex: 1,
        overflowY: 'auto',
        padding: '16px'
      }}>
        {activeTab === 'general' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
            {/* Model Selection */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: '#24292f',
                marginBottom: '8px'
              }}>
                Default Model
              </label>
              <select
                value={config.defaultModel}
                onChange={(e) => handleConfigUpdate({ defaultModel: e.target.value })}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d0d7de',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: '#ffffff'
                }}
              >
                <option value="gpt-4">GPT-4</option>
                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                <option value="claude-3">Claude 3</option>
                <option value="gemini-pro">Gemini Pro</option>
              </select>
            </div>

            {/* Provider Selection */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: '#24292f',
                marginBottom: '8px'
              }}>
                Default Provider
              </label>
              <select
                value={config.defaultProvider}
                onChange={(e) => handleConfigUpdate({ defaultProvider: e.target.value })}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d0d7de',
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: '#ffffff'
                }}
              >
                <option value="openai">OpenAI</option>
                <option value="anthropic">Anthropic</option>
                <option value="google">Google</option>
                <option value="ollama">Ollama (Local)</option>
              </select>
            </div>

            {/* Features */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: '#24292f',
                marginBottom: '12px'
              }}>
                Features
              </label>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer'
                }}>
                  <input
                    type="checkbox"
                    checked={config.enableStreaming}
                    onChange={(e) => handleConfigUpdate({ enableStreaming: e.target.checked })}
                  />
                  <span style={{ fontSize: '14px' }}>Enable streaming responses</span>
                </label>
                
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer'
                }}>
                  <input
                    type="checkbox"
                    checked={config.enableSources}
                    onChange={(e) => handleConfigUpdate({ enableSources: e.target.checked })}
                  />
                  <span style={{ fontSize: '14px' }}>Show sources and references</span>
                </label>
                
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer'
                }}>
                  <input
                    type="checkbox"
                    checked={config.enableReasoning}
                    onChange={(e) => handleConfigUpdate({ enableReasoning: e.target.checked })}
                  />
                  <span style={{ fontSize: '14px' }}>Show reasoning process</span>
                </label>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'appearance' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
            {/* Display Options */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: '#24292f',
                marginBottom: '12px'
              }}>
                Display Options
              </label>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer'
                }}>
                  <input
                    type="checkbox"
                    checked={showSources}
                    onChange={actions.toggleSources}
                  />
                  <span style={{ fontSize: '14px' }}>Show sources</span>
                </label>
                
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer'
                }}>
                  <input
                    type="checkbox"
                    checked={showThinking}
                    onChange={actions.toggleThinking}
                  />
                  <span style={{ fontSize: '14px' }}>Show thinking process</span>
                </label>
                
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer'
                }}>
                  <input
                    type="checkbox"
                    checked={showTimestamps}
                    onChange={actions.toggleTimestamps}
                  />
                  <span style={{ fontSize: '14px' }}>Show timestamps</span>
                </label>
                
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer'
                }}>
                  <input
                    type="checkbox"
                    checked={compactMode}
                    onChange={actions.toggleCompactMode}
                  />
                  <span style={{ fontSize: '14px' }}>Compact mode</span>
                </label>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'advanced' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
            {/* Limits */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: '#24292f',
                marginBottom: '8px'
              }}>
                Message Length Limit
              </label>
              <input
                type="number"
                value={config.maxMessageLength}
                onChange={(e) => handleConfigUpdate({ maxMessageLength: parseInt(e.target.value) })}
                min="100"
                max="10000"
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d0d7de',
                  borderRadius: '6px',
                  fontSize: '14px'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: '#24292f',
                marginBottom: '8px'
              }}>
                Context Window
              </label>
              <input
                type="number"
                value={config.contextWindow}
                onChange={(e) => handleConfigUpdate({ contextWindow: parseInt(e.target.value) })}
                min="1000"
                max="32000"
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d0d7de',
                  borderRadius: '6px',
                  fontSize: '14px'
                }}
              />
            </div>

            {/* Performance Metrics */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: '#24292f',
                marginBottom: '12px'
              }}>
                Performance Metrics
              </label>
              
              <div style={{
                padding: '12px',
                backgroundColor: '#ffffff',
                border: '1px solid #d0d7de',
                borderRadius: '6px',
                fontSize: '12px'
              }}>
                <div style={{ marginBottom: '8px' }}>
                  <strong>Total Messages:</strong> {performanceMetrics.totalMessages}
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>Average Response Time:</strong> {Math.round(performanceMetrics.averageResponseTime)}ms
                </div>
                <div>
                  <strong>Last Response Time:</strong> {Math.round(performanceMetrics.lastMessageTime)}ms
                </div>
              </div>
            </div>

            {/* Reset */}
            <div>
              <button
                onClick={() => {
                  if (window.confirm('Are you sure you want to reset all settings to defaults?')) {
                    // Reset to defaults
                    handleConfigUpdate({
                      defaultModel: 'gpt-4',
                      defaultProvider: 'openai',
                      maxSessionLength: 100,
                      maxMessageLength: 4000,
                      contextWindow: 8000,
                      enableStreaming: true,
                      enableSources: true,
                      enableReasoning: true
                    });
                  }
                }}
                style={{
                  width: '100%',
                  padding: '10px',
                  backgroundColor: '#f85149',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '14px',
                  cursor: 'pointer'
                }}
              >
                Reset to Defaults
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
