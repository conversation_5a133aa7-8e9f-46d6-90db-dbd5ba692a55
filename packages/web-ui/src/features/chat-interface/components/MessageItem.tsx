/**
 * Message Item Component
 * 
 * Individual message component with rich formatting and interactions.
 */

import React, { useState, useCallback } from 'react';
import { ChatMessage } from '../types';

interface MessageItemProps {
  message: ChatMessage;
  isLast: boolean;
  isStreaming: boolean;
  showSources: boolean;
  showThinking: boolean;
  showTimestamps: boolean;
  compactMode: boolean;
  onEdit: (messageId: string, content: string) => void;
  onDelete: (messageId: string) => void;
  onRetry: (messageId: string) => void;
  onNodeSelect: (nodeId: string) => void;
}

export const MessageItem: React.FC<MessageItemProps> = ({
  message,
  isLast,
  isStreaming,
  showSources,
  showThinking,
  showTimestamps,
  compactMode,
  onEdit,
  onDelete,
  onRetry,
  onNodeSelect
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);
  const [showActions, setShowActions] = useState(false);

  const isUser = message.role === 'user';
  const hasError = (message.metadata as any)?.error;
  const sources = message.metadata?.sources || [];
  const reasoning = message.metadata?.reasoning;

  // Handle edit save
  const handleEditSave = useCallback(() => {
    if (editContent.trim() !== message.content) {
      onEdit(message.id, editContent.trim());
    }
    setIsEditing(false);
  }, [editContent, message.content, message.id, onEdit]);

  // Handle edit cancel
  const handleEditCancel = useCallback(() => {
    setEditContent(message.content);
    setIsEditing(false);
  }, [message.content]);

  // Handle node click in content
  const handleContentClick = useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const nodeId = target.getAttribute('data-node-id');
    if (nodeId) {
      event.preventDefault();
      onNodeSelect(nodeId);
    }
  }, [onNodeSelect]);

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Render message content with node links
  const renderContent = (content: string) => {
    // Simple node link detection (you might want to enhance this)
    const nodeRegex = /\[([^\]]+)\]\(node:([^)]+)\)/g;
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = nodeRegex.exec(content)) !== null) {
      // Add text before the match
      if (match.index > lastIndex) {
        parts.push(content.slice(lastIndex, match.index));
      }
      
      // Add the node link
      parts.push(
        <button
          key={match.index}
          data-node-id={match[2]}
          onClick={handleContentClick}
          style={{
            background: 'none',
            border: 'none',
            color: '#0969da',
            textDecoration: 'underline',
            cursor: 'pointer',
            padding: 0,
            font: 'inherit'
          }}
        >
          {match[1]}
        </button>
      );
      
      lastIndex = match.index + match[0].length;
    }
    
    // Add remaining text
    if (lastIndex < content.length) {
      parts.push(content.slice(lastIndex));
    }
    
    return parts.length > 0 ? parts : content;
  };

  return (
    <div
      className={`message-item ${isUser ? 'user' : 'assistant'}`}
      style={{
        display: 'flex',
        flexDirection: isUser ? 'row-reverse' : 'row',
        alignItems: 'flex-start',
        gap: '12px',
        maxWidth: '100%'
      }}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Avatar */}
      <div style={{
        width: compactMode ? '24px' : '32px',
        height: compactMode ? '24px' : '32px',
        borderRadius: '50%',
        backgroundColor: isUser ? '#0969da' : '#6f42c1',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: compactMode ? '12px' : '14px',
        color: 'white',
        flexShrink: 0
      }}>
        {isUser ? '👤' : '🤖'}
      </div>

      {/* Message Content */}
      <div style={{
        flex: 1,
        maxWidth: isUser ? '80%' : '100%'
      }}>
        {/* Message Bubble */}
        <div style={{
          backgroundColor: isUser ? '#0969da' : hasError ? '#fff5f5' : '#f6f8fa',
          color: isUser ? 'white' : hasError ? '#d73a49' : '#24292f',
          padding: compactMode ? '8px 12px' : '12px 16px',
          borderRadius: '12px',
          borderTopLeftRadius: isUser ? '12px' : '4px',
          borderTopRightRadius: isUser ? '4px' : '12px',
          border: hasError ? '1px solid #f85149' : 'none',
          position: 'relative',
          wordBreak: 'break-word'
        }}>
          {isEditing ? (
            <div>
              <textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                style={{
                  width: '100%',
                  minHeight: '60px',
                  border: '1px solid #d0d7de',
                  borderRadius: '6px',
                  padding: '8px',
                  fontSize: '14px',
                  fontFamily: 'inherit',
                  resize: 'vertical'
                }}
                autoFocus
              />
              <div style={{
                display: 'flex',
                gap: '8px',
                marginTop: '8px'
              }}>
                <button
                  onClick={handleEditSave}
                  style={{
                    padding: '4px 12px',
                    backgroundColor: '#238636',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    fontSize: '12px',
                    cursor: 'pointer'
                  }}
                >
                  Save
                </button>
                <button
                  onClick={handleEditCancel}
                  style={{
                    padding: '4px 12px',
                    backgroundColor: '#656d76',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    fontSize: '12px',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div style={{ fontSize: compactMode ? '13px' : '14px', lineHeight: '1.5' }}>
              {renderContent(message.content)}
              
              {/* Streaming indicator */}
              {isStreaming && isLast && (
                <span style={{
                  display: 'inline-block',
                  width: '8px',
                  height: '8px',
                  backgroundColor: 'currentColor',
                  borderRadius: '50%',
                  marginLeft: '4px',
                  animation: 'pulse 1.5s ease-in-out infinite'
                }} />
              )}
            </div>
          )}

          {/* Error indicator */}
          {hasError && (
            <div style={{
              marginTop: '8px',
              padding: '8px',
              backgroundColor: 'rgba(248, 81, 73, 0.1)',
              borderRadius: '6px',
              fontSize: '12px',
              color: '#d73a49'
            }}>
              ⚠️ {(message.metadata as any)?.error}
            </div>
          )}
        </div>

        {/* Metadata */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          marginTop: '4px',
          fontSize: '11px',
          color: '#656d76'
        }}>
          {showTimestamps && (
            <span>{formatTimestamp(message.timestamp)}</span>
          )}
          
          {message.metadata?.model && (
            <span>via {message.metadata.model}</span>
          )}
          
          {message.metadata?.responseTime && (
            <span>{Math.round(message.metadata.responseTime)}ms</span>
          )}
          
          {message.metadata?.tokens && (
            <span>{message.metadata.tokens} tokens</span>
          )}
        </div>

        {/* Thinking Process */}
        {showThinking && reasoning && !isUser && (
          <details style={{ marginTop: '8px' }}>
            <summary style={{
              cursor: 'pointer',
              fontSize: '12px',
              color: '#656d76',
              padding: '4px 0'
            }}>
              🧠 Show thinking process
            </summary>
            <div style={{
              marginTop: '8px',
              padding: '12px',
              backgroundColor: '#f6f8fa',
              borderRadius: '6px',
              fontSize: '13px',
              color: '#24292f',
              border: '1px solid #d0d7de'
            }}>
              {reasoning}
            </div>
          </details>
        )}

        {/* Sources */}
        {showSources && sources.length > 0 && !isUser && (
          <details style={{ marginTop: '8px' }}>
            <summary style={{
              cursor: 'pointer',
              fontSize: '12px',
              color: '#656d76',
              padding: '4px 0'
            }}>
              📚 Sources ({sources.length})
            </summary>
            <div style={{
              marginTop: '8px',
              display: 'flex',
              flexDirection: 'column',
              gap: '4px'
            }}>
              {sources.map((source, index) => (
                <div
                  key={index}
                  style={{
                    padding: '8px',
                    backgroundColor: '#f6f8fa',
                    borderRadius: '6px',
                    fontSize: '12px',
                    border: '1px solid #d0d7de'
                  }}
                >
                  <div style={{ fontWeight: '600', marginBottom: '4px' }}>
                    {source.title || `Source ${index + 1}`}
                  </div>
                  {source.content && (
                    <div style={{ color: '#656d76' }}>
                      {source.content.slice(0, 200)}
                      {source.content.length > 200 && '...'}
                    </div>
                  )}
                  {(source as any).nodeId && (
                    <button
                      onClick={() => onNodeSelect((source as any).nodeId!)}
                      style={{
                        marginTop: '4px',
                        padding: '2px 6px',
                        backgroundColor: '#0969da',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        fontSize: '10px',
                        cursor: 'pointer'
                      }}
                    >
                      View Node
                    </button>
                  )}
                </div>
              ))}
            </div>
          </details>
        )}

        {/* Actions */}
        {showActions && !isEditing && (
          <div style={{
            display: 'flex',
            gap: '4px',
            marginTop: '8px',
            opacity: showActions ? 1 : 0,
            transition: 'opacity 0.2s'
          }}>
            {isUser && (
              <button
                onClick={() => setIsEditing(true)}
                style={{
                  padding: '4px 8px',
                  backgroundColor: 'transparent',
                  border: '1px solid #d0d7de',
                  borderRadius: '4px',
                  fontSize: '11px',
                  cursor: 'pointer',
                  color: '#656d76'
                }}
                title="Edit message"
              >
                ✏️
              </button>
            )}
            
            {hasError && (
              <button
                onClick={() => onRetry(message.id)}
                style={{
                  padding: '4px 8px',
                  backgroundColor: 'transparent',
                  border: '1px solid #d0d7de',
                  borderRadius: '4px',
                  fontSize: '11px',
                  cursor: 'pointer',
                  color: '#656d76'
                }}
                title="Retry message"
              >
                🔄
              </button>
            )}
            
            <button
              onClick={() => onDelete(message.id)}
              style={{
                padding: '4px 8px',
                backgroundColor: 'transparent',
                border: '1px solid #d0d7de',
                borderRadius: '4px',
                fontSize: '11px',
                cursor: 'pointer',
                color: '#656d76'
              }}
              title="Delete message"
            >
              🗑️
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
