/**
 * Message List Component
 * 
 * Displays chat messages with proper formatting, sources, and interactions.
 */

import React, { useEffect, useRef, useCallback } from 'react';
import { MessageListProps } from '../types';
import { MessageItem } from './MessageItem';

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  isStreaming = false,
  showSources = true,
  showThinking = true,
  showTimestamps = false,
  compactMode = false,
  onMessageEdit,
  onMessageDelete,
  onMessageRetry,
  onNodeSelect
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ 
        behavior: 'smooth',
        block: 'end'
      });
    }
  }, []);

  // Scroll to bottom when messages change or streaming
  useEffect(() => {
    scrollToBottom();
  }, [messages.length, isStreaming, scrollToBottom]);

  // Handle message actions
  const handleEdit = useCallback((messageId: string, content: string) => {
    onMessageEdit?.(messageId, content);
  }, [onMessageEdit]);

  const handleDelete = useCallback((messageId: string) => {
    onMessageDelete?.(messageId);
  }, [onMessageDelete]);

  const handleRetry = useCallback((messageId: string) => {
    onMessageRetry?.(messageId);
  }, [onMessageRetry]);

  const handleNodeSelect = useCallback((nodeId: string) => {
    onNodeSelect?.(nodeId);
  }, [onNodeSelect]);

  if (messages.length === 0) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        color: '#656d76',
        fontSize: '16px',
        textAlign: 'center',
        padding: '20px'
      }}>
        <div>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>
          <div style={{ fontWeight: '600', marginBottom: '8px' }}>
            Start a conversation
          </div>
          <div style={{ fontSize: '14px', opacity: 0.8 }}>
            Ask questions about your knowledge graph to get insights and explore connections.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className="message-list"
      style={{
        height: '100%',
        overflowY: 'auto',
        padding: compactMode ? '8px' : '16px',
        display: 'flex',
        flexDirection: 'column',
        gap: compactMode ? '8px' : '16px'
      }}
    >
      {messages.map((message, index) => (
        <MessageItem
          key={message.id}
          message={message}
          isLast={index === messages.length - 1}
          isStreaming={isStreaming && index === messages.length - 1}
          showSources={showSources}
          showThinking={showThinking}
          showTimestamps={showTimestamps}
          compactMode={compactMode}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onRetry={handleRetry}
          onNodeSelect={handleNodeSelect}
        />
      ))}
      
      {/* Scroll anchor */}
      <div ref={messagesEndRef} style={{ height: '1px' }} />
    </div>
  );
};
