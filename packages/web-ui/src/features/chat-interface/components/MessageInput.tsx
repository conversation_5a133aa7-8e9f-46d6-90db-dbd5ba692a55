/**
 * Message Input Component
 * 
 * Input component for sending chat messages with enhanced UX.
 */

import React, { useRef, useCallback, useEffect } from 'react';
import { MessageInputProps } from '../types';

export const MessageInput: React.FC<MessageInputProps> = ({
  value,
  placeholder = 'Type your message...',
  disabled = false,
  isLoading = false,
  maxLength = 4000,
  onSend,
  onChange,
  onKeyDown
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  const adjustHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const newHeight = Math.min(textarea.scrollHeight, 120); // Max 120px
      textarea.style.height = `${newHeight}px`;
    }
  }, []);

  // Adjust height when value changes
  useEffect(() => {
    adjustHeight();
  }, [value, adjustHeight]);

  // Handle input change
  const handleChange = useCallback((event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = event.target.value;
    if (newValue.length <= maxLength) {
      onChange(newValue);
    }
  }, [onChange, maxLength]);

  // Handle key press
  const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Call external handler first
    onKeyDown?.(event);
    
    // Handle send on Enter (without Shift)
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      if (value.trim() && !disabled && !isLoading) {
        onSend(value.trim());
      }
    }
  }, [value, disabled, isLoading, onSend, onKeyDown]);

  // Handle send button click
  const handleSend = useCallback(() => {
    if (value.trim() && !disabled && !isLoading) {
      onSend(value.trim());
    }
  }, [value, disabled, isLoading, onSend]);

  // Focus textarea when not disabled
  useEffect(() => {
    if (!disabled && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [disabled]);

  const canSend = value.trim().length > 0 && !disabled && !isLoading;
  const remainingChars = maxLength - value.length;
  const isNearLimit = remainingChars < 100;

  return (
    <div style={{
      padding: '16px',
      backgroundColor: '#ffffff',
      borderTop: '1px solid #e1e5e9'
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'flex-end',
        gap: '12px',
        position: 'relative'
      }}>
        {/* Textarea */}
        <div style={{ flex: 1, position: 'relative' }}>
          <textarea
            ref={textareaRef}
            value={value}
            placeholder={placeholder}
            disabled={disabled}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            style={{
              width: '100%',
              minHeight: '40px',
              maxHeight: '120px',
              padding: '10px 12px',
              border: '1px solid #d0d7de',
              borderRadius: '8px',
              fontSize: '14px',
              fontFamily: 'inherit',
              resize: 'none',
              outline: 'none',
              backgroundColor: disabled ? '#f6f8fa' : '#ffffff',
              color: disabled ? '#656d76' : '#24292f',
              transition: 'border-color 0.2s',
              lineHeight: '1.4'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = '#0969da';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#d0d7de';
            }}
          />
          
          {/* Character count */}
          {isNearLimit && (
            <div style={{
              position: 'absolute',
              bottom: '4px',
              right: '8px',
              fontSize: '11px',
              color: remainingChars < 20 ? '#d73a49' : '#656d76',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              padding: '2px 4px',
              borderRadius: '3px'
            }}>
              {remainingChars}
            </div>
          )}
        </div>

        {/* Send Button */}
        <button
          onClick={handleSend}
          disabled={!canSend}
          style={{
            padding: '10px 16px',
            backgroundColor: canSend ? '#238636' : '#f6f8fa',
            color: canSend ? '#ffffff' : '#656d76',
            border: '1px solid',
            borderColor: canSend ? '#238636' : '#d0d7de',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: canSend ? 'pointer' : 'not-allowed',
            transition: 'all 0.2s',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            minWidth: '80px',
            justifyContent: 'center'
          }}
          title={
            !value.trim() ? 'Enter a message' :
            disabled ? 'Input disabled' :
            isLoading ? 'Sending...' :
            'Send message (Enter)'
          }
        >
          {isLoading ? (
            <>
              <div style={{
                width: '12px',
                height: '12px',
                border: '2px solid currentColor',
                borderTop: '2px solid transparent',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
              <span>Sending</span>
            </>
          ) : (
            <>
              <span>Send</span>
              <span style={{ fontSize: '12px', opacity: 0.8 }}>↵</span>
            </>
          )}
        </button>
      </div>

      {/* Help text */}
      <div style={{
        marginTop: '8px',
        fontSize: '12px',
        color: '#656d76',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <span>
          Press <kbd style={{
            padding: '2px 4px',
            backgroundColor: '#f6f8fa',
            border: '1px solid #d0d7de',
            borderRadius: '3px',
            fontSize: '11px'
          }}>Enter</kbd> to send, <kbd style={{
            padding: '2px 4px',
            backgroundColor: '#f6f8fa',
            border: '1px solid #d0d7de',
            borderRadius: '3px',
            fontSize: '11px'
          }}>Shift+Enter</kbd> for new line
        </span>
        
        {value.length > 0 && (
          <span style={{ opacity: 0.7 }}>
            {value.length} / {maxLength}
          </span>
        )}
      </div>

      {/* Add CSS animation for spinner */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};
