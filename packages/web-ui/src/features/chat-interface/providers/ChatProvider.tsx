/**
 * Chat Provider
 * 
 * Context provider for chat interface state management.
 * Implements modern React patterns with proper error handling and performance optimization.
 */

import React, { createContext, useContext, useReducer, useCallback, useEffect, useMemo } from 'react';
import { ChatState, ChatActions, ChatSelectors, ChatSession, ChatMessage, ChatConfig, ChatUserPreferences } from '../types';
import { chatApiService } from '../services/ChatApiService';
import { chatStorageService } from '../services/ChatStorageService';
import { featureBus } from '../../../shared/infrastructure/FeatureBus';
import { FeatureComponentProps } from '../../../shared/types/feature';

// Initial state
const initialState: ChatState = {
  currentSession: null,
  sessions: [],
  isLoading: false,
  isStreaming: false,
  error: null,
  inputValue: '',
  config: {
    defaultModel: 'gpt-4',
    defaultProvider: 'openai',
    maxSessionLength: 100,
    maxMessageLength: 4000,
    contextWindow: 8000,
    enableStreaming: true,
    enableSources: true,
    enableReasoning: true,
    rateLimits: {
      messagesPerMinute: 20,
      tokensPerHour: 100000
    }
  },
  performance: {
    lastMessageTime: 0,
    averageResponseTime: 0,
    totalMessages: 0
  },
  uiState: {
    showSources: true,
    showThinking: true,
    showTimestamps: false,
    compactMode: false
  }
};

// Action types
type ChatAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_STREAMING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_INPUT_VALUE'; payload: string }
  | { type: 'SET_CURRENT_SESSION'; payload: ChatSession | null }
  | { type: 'SET_SESSIONS'; payload: ChatSession[] }
  | { type: 'ADD_SESSION'; payload: ChatSession }
  | { type: 'UPDATE_SESSION'; payload: { sessionId: string; updates: Partial<ChatSession> } }
  | { type: 'DELETE_SESSION'; payload: string }
  | { type: 'ADD_MESSAGE'; payload: { sessionId: string; message: ChatMessage } }
  | { type: 'UPDATE_MESSAGE'; payload: { sessionId: string; messageId: string; updates: Partial<ChatMessage> } }
  | { type: 'DELETE_MESSAGE'; payload: { sessionId: string; messageId: string } }
  | { type: 'UPDATE_CONFIG'; payload: Partial<ChatConfig> }
  | { type: 'UPDATE_UI_STATE'; payload: Partial<ChatState['uiState']> }
  | { type: 'UPDATE_PERFORMANCE'; payload: Partial<ChatState['performance']> };

// Reducer
function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
      
    case 'SET_STREAMING':
      return { ...state, isStreaming: action.payload };
      
    case 'SET_ERROR':
      return { ...state, error: action.payload };
      
    case 'SET_INPUT_VALUE':
      return { ...state, inputValue: action.payload };
      
    case 'SET_CURRENT_SESSION':
      return { ...state, currentSession: action.payload };
      
    case 'SET_SESSIONS':
      return { ...state, sessions: action.payload };
      
    case 'ADD_SESSION':
      return { 
        ...state, 
        sessions: [...state.sessions, action.payload],
        currentSession: action.payload
      };
      
    case 'UPDATE_SESSION': {
      const { sessionId, updates } = action.payload;
      return {
        ...state,
        sessions: state.sessions.map(session =>
          session.id === sessionId ? { ...session, ...updates } : session
        ),
        currentSession: state.currentSession?.id === sessionId
          ? { ...state.currentSession, ...updates }
          : state.currentSession
      };
    }
    
    case 'DELETE_SESSION':
      return {
        ...state,
        sessions: state.sessions.filter(session => session.id !== action.payload),
        currentSession: state.currentSession?.id === action.payload ? null : state.currentSession
      };
      
    case 'ADD_MESSAGE': {
      const { sessionId, message } = action.payload;
      return {
        ...state,
        sessions: state.sessions.map(session =>
          session.id === sessionId
            ? { ...session, messages: [...session.messages, message], updatedAt: new Date().toISOString() }
            : session
        ),
        currentSession: state.currentSession?.id === sessionId
          ? { ...state.currentSession, messages: [...state.currentSession.messages, message], updatedAt: new Date().toISOString() }
          : state.currentSession
      };
    }
    
    case 'UPDATE_MESSAGE': {
      const { sessionId, messageId, updates } = action.payload;
      return {
        ...state,
        sessions: state.sessions.map(session =>
          session.id === sessionId
            ? {
                ...session,
                messages: session.messages.map((msg: any) =>
                  msg.id === messageId ? { ...msg, ...updates } : msg
                ),
                updatedAt: new Date().toISOString()
              }
            : session
        ),
        currentSession: state.currentSession?.id === sessionId
          ? {
              ...state.currentSession,
              messages: state.currentSession.messages.map((msg: any) =>
                msg.id === messageId ? { ...msg, ...updates } : msg
              ),
              updatedAt: new Date().toISOString()
            }
          : state.currentSession
      };
    }
    
    case 'DELETE_MESSAGE': {
      const { sessionId, messageId } = action.payload;
      return {
        ...state,
        sessions: state.sessions.map(session =>
          session.id === sessionId
            ? {
                ...session,
                messages: session.messages.filter((msg: any) => msg.id !== messageId),
                updatedAt: new Date().toISOString()
              }
            : session
        ),
        currentSession: state.currentSession?.id === sessionId
          ? {
              ...state.currentSession,
              messages: state.currentSession.messages.filter((msg: any) => msg.id !== messageId),
              updatedAt: new Date().toISOString()
            }
          : state.currentSession
      };
    }
    
    case 'UPDATE_CONFIG':
      return { ...state, config: { ...state.config, ...action.payload } };
      
    case 'UPDATE_UI_STATE':
      return { ...state, uiState: { ...state.uiState, ...action.payload } };
      
    case 'UPDATE_PERFORMANCE':
      return { ...state, performance: { ...state.performance, ...action.payload } };
      
    default:
      return state;
  }
}

// Context
const ChatContext = createContext<{
  state: ChatState;
  actions: ChatActions;
  selectors: ChatSelectors;
} | null>(null);

// Provider component
export interface ChatProviderProps extends FeatureComponentProps {
  children: React.ReactNode;
  initialConfig?: Partial<ChatConfig>;
  featureId?: string;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({
  children,
  initialConfig,
  featureId = 'chat-interface',
  onError
}) => {
  const [state, dispatch] = useReducer(chatReducer, {
    ...initialState,
    config: { ...initialState.config, ...initialConfig }
  });

  // Generate unique session ID
  const generateSessionId = useCallback(() => {
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Generate unique message ID
  const generateMessageId = useCallback(() => {
    return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Actions
  const actions: ChatActions = useMemo(() => ({
    // Session management
    createSession: async (title?: string) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        
        const session: ChatSession = {
          id: generateSessionId(),
          title: title || `Chat ${new Date().toLocaleDateString()}`,
          messages: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: {
            totalTokens: 0
          }
        };

        // Save to storage
        await chatStorageService.saveSession(session);
        
        dispatch({ type: 'ADD_SESSION', payload: session });
        
        // Publish event
        featureBus.communicate.publish({
          type: 'chat:session:created',
          payload: { sessionId: session.id },
          timestamp: new Date(),
          source: featureId
        });
        
        return session;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to create session';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        onError?.(error instanceof Error ? error : new Error(errorMessage));
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    loadSession: async (sessionId: string) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        
        const session = await chatStorageService.loadSession(sessionId);
        if (!session) {
          throw new Error('Session not found');
        }
        
        dispatch({ type: 'SET_CURRENT_SESSION', payload: session });
        
        // Publish event
        featureBus.communicate.publish({
          type: 'chat:session:loaded',
          payload: { sessionId },
          timestamp: new Date(),
          source: featureId
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to load session';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        onError?.(error instanceof Error ? error : new Error(errorMessage));
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    deleteSession: async (sessionId: string) => {
      try {
        await chatStorageService.deleteSession(sessionId);
        dispatch({ type: 'DELETE_SESSION', payload: sessionId });
        
        // Publish event
        featureBus.communicate.publish({
          type: 'chat:session:deleted',
          payload: { sessionId },
          timestamp: new Date(),
          source: featureId
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to delete session';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        onError?.(error instanceof Error ? error : new Error(errorMessage));
        throw error;
      }
    },

    updateSessionTitle: async (sessionId: string, title: string) => {
      try {
        const updates = { title, updatedAt: new Date().toISOString() };
        dispatch({ type: 'UPDATE_SESSION', payload: { sessionId, updates } });
        
        // Update storage
        const session = state.sessions.find(s => s.id === sessionId);
        if (session) {
          await chatStorageService.saveSession({ ...session, ...updates });
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update session title';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        onError?.(error instanceof Error ? error : new Error(errorMessage));
        throw error;
      }
    },

    // Message handling
    sendMessage: async (content: string, options = {}) => {
      if (!state.currentSession) {
        throw new Error('No active session');
      }

      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        dispatch({ type: 'SET_ERROR', payload: null });

        const startTime = performance.now();
        
        // Create user message
        const userMessage: ChatMessage = {
          id: generateMessageId(),
          content,
          role: 'user',
          timestamp: new Date().toISOString()
        };

        // Add user message to session
        dispatch({
          type: 'ADD_MESSAGE',
          payload: { sessionId: state.currentSession.id, message: userMessage }
        });

        // Send to API
        const assistantMessage = await chatApiService.sendMessage(
          content,
          state.currentSession.id,
          options
        );

        // Add assistant message to session
        dispatch({
          type: 'ADD_MESSAGE',
          payload: { sessionId: state.currentSession.id, message: assistantMessage }
        });

        // Update performance metrics
        const responseTime = performance.now() - startTime;
        const newTotalMessages = state.performance.totalMessages + 1;
        const newAverageResponseTime = 
          (state.performance.averageResponseTime * state.performance.totalMessages + responseTime) / newTotalMessages;

        dispatch({
          type: 'UPDATE_PERFORMANCE',
          payload: {
            lastMessageTime: responseTime,
            averageResponseTime: newAverageResponseTime,
            totalMessages: newTotalMessages
          }
        });

        // Clear input
        dispatch({ type: 'SET_INPUT_VALUE', payload: '' });

        // Publish event
        featureBus.communicate.publish({
          type: 'chat:message:sent',
          payload: {
            sessionId: state.currentSession.id,
            messageId: assistantMessage.id,
            responseTime
          },
          timestamp: new Date(),
          source: featureId
        });

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        onError?.(error instanceof Error ? error : new Error(errorMessage));
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    retryMessage: async (messageId: string) => {
      // Implementation for retrying a message
      console.log('Retry message:', messageId);
    },

    editMessage: async (messageId: string, newContent: string) => {
      if (!state.currentSession) return;
      
      dispatch({
        type: 'UPDATE_MESSAGE',
        payload: {
          sessionId: state.currentSession.id,
          messageId,
          updates: { content: newContent }
        }
      });
    },

    deleteMessage: async (messageId: string) => {
      if (!state.currentSession) return;
      
      dispatch({
        type: 'DELETE_MESSAGE',
        payload: { sessionId: state.currentSession.id, messageId }
      });
    },

    // Streaming
    startStreaming: async (content: string, options = {}) => {
      // Implementation for streaming
      console.log('Start streaming:', content, options);
    },

    stopStreaming: () => {
      dispatch({ type: 'SET_STREAMING', payload: false });
    },

    // Input management
    setInputValue: (value: string) => {
      dispatch({ type: 'SET_INPUT_VALUE', payload: value });
    },

    clearInput: () => {
      dispatch({ type: 'SET_INPUT_VALUE', payload: '' });
    },

    // Configuration
    updateConfig: (config: Partial<ChatConfig>) => {
      dispatch({ type: 'UPDATE_CONFIG', payload: config });
      chatStorageService.saveConfig({ ...state.config, ...config });
    },

    updateUserPreferences: (preferences: Partial<ChatUserPreferences>) => {
      // Implementation for user preferences
      console.log('Update preferences:', preferences);
    },

    // UI state
    toggleSources: () => {
      dispatch({
        type: 'UPDATE_UI_STATE',
        payload: { showSources: !state.uiState.showSources }
      });
    },

    toggleThinking: () => {
      dispatch({
        type: 'UPDATE_UI_STATE',
        payload: { showThinking: !state.uiState.showThinking }
      });
    },

    toggleTimestamps: () => {
      dispatch({
        type: 'UPDATE_UI_STATE',
        payload: { showTimestamps: !state.uiState.showTimestamps }
      });
    },

    toggleCompactMode: () => {
      dispatch({
        type: 'UPDATE_UI_STATE',
        payload: { compactMode: !state.uiState.compactMode }
      });
    },

    // Error handling
    clearError: () => {
      dispatch({ type: 'SET_ERROR', payload: null });
    },

    setError: (error: string) => {
      dispatch({ type: 'SET_ERROR', payload: error });
    }
  }), [state, generateSessionId, generateMessageId, featureId, onError]);

  // Selectors
  const selectors: ChatSelectors = useMemo(() => ({
    // Session selectors
    getCurrentSession: () => state.currentSession,
    getSessions: () => state.sessions,
    getSessionById: (id: string) => state.sessions.find(s => s.id === id) || null,

    // Message selectors
    getMessages: () => state.currentSession?.messages || [],
    getLastMessage: () => {
      const messages = state.currentSession?.messages || [];
      return messages[messages.length - 1] || null;
    },
    getMessageById: (id: string) => {
      const messages = state.currentSession?.messages || [];
      return messages.find((m: any) => m.id === id) || null;
    },

    // State selectors
    getIsLoading: () => state.isLoading,
    getIsStreaming: () => state.isStreaming,
    getError: () => state.error,
    getInputValue: () => state.inputValue,

    // Configuration selectors
    getConfig: () => state.config,
    getUserPreferences: () => ({
      responseLength: 'medium' as const,
      includeReferences: true,
      includeReasoning: true
    }),

    // UI state selectors
    getShowSources: () => state.uiState.showSources,
    getShowThinking: () => state.uiState.showThinking,
    getShowTimestamps: () => state.uiState.showTimestamps,
    getCompactMode: () => state.uiState.compactMode,

    // Performance selectors
    getPerformanceMetrics: () => state.performance,

    // Computed selectors
    getCanSend: () => !state.isLoading && !state.isStreaming && state.inputValue.trim().length > 0,
    getHasMessages: () => (state.currentSession?.messages.length || 0) > 0,
    getSessionCount: () => state.sessions.length
  }), [state]);

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const [sessions, config] = await Promise.all([
          chatStorageService.loadSessions(),
          chatStorageService.loadConfig()
        ]);

        if (sessions.length > 0) {
          dispatch({ type: 'SET_SESSIONS', payload: sessions });
        }

        if (config) {
          dispatch({ type: 'UPDATE_CONFIG', payload: config });
        }
      } catch (error) {
        console.error('Failed to load initial chat data:', error);
        onError?.(error instanceof Error ? error : new Error('Failed to load chat data'));
      }
    };

    loadInitialData();
  }, [onError]);

  const contextValue = useMemo(() => ({
    state,
    actions,
    selectors
  }), [state, actions, selectors]);

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
};

// Hook to use chat context
export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};
