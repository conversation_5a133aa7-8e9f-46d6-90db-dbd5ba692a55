/**
 * Chat Storage Service
 * 
 * Local storage service for chat sessions, messages, and configuration.
 * Implements proper error handling and data validation.
 */

import { ChatSession, ChatConfig, ChatUserPreferences, ChatStorageService } from '../types';

class ChatStorageServiceImpl implements ChatStorageService {
  private readonly STORAGE_KEYS = {
    SESSIONS: 'chat_sessions',
    CONFIG: 'chat_config',
    USER_PREFERENCES: 'chat_user_preferences',
    CURRENT_SESSION: 'chat_current_session'
  };

  private readonly MAX_SESSIONS = 50; // Limit stored sessions
  private readonly MAX_MESSAGES_PER_SESSION = 200; // Limit messages per session

  // Session management
  async saveSession(session: ChatSession): Promise<void> {
    try {
      const sessions = await this.loadSessions();
      const existingIndex = sessions.findIndex(s => s.id === session.id);
      
      // Trim messages if too many
      const trimmedSession = {
        ...session,
        messages: session.messages.slice(-this.MAX_MESSAGES_PER_SESSION)
      };
      
      if (existingIndex >= 0) {
        sessions[existingIndex] = trimmedSession;
      } else {
        sessions.push(trimmedSession);
      }
      
      // Keep only the most recent sessions
      const sortedSessions = sessions
        .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        .slice(0, this.MAX_SESSIONS);
      
      localStorage.setItem(this.STORAGE_KEYS.SESSIONS, JSON.stringify(sortedSessions));
    } catch (error) {
      console.error('Failed to save session:', error);
      throw new Error('Failed to save chat session');
    }
  }

  async loadSession(sessionId: string): Promise<ChatSession | null> {
    try {
      const sessions = await this.loadSessions();
      return sessions.find(s => s.id === sessionId) || null;
    } catch (error) {
      console.error('Failed to load session:', error);
      return null;
    }
  }

  async loadSessions(): Promise<ChatSession[]> {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.SESSIONS);
      if (!stored) return [];
      
      const sessions = JSON.parse(stored);
      
      // Validate session structure
      return sessions.filter(this.isValidSession);
    } catch (error) {
      console.error('Failed to load sessions:', error);
      return [];
    }
  }

  async deleteSession(sessionId: string): Promise<void> {
    try {
      const sessions = await this.loadSessions();
      const filteredSessions = sessions.filter(s => s.id !== sessionId);
      localStorage.setItem(this.STORAGE_KEYS.SESSIONS, JSON.stringify(filteredSessions));
    } catch (error) {
      console.error('Failed to delete session:', error);
      throw new Error('Failed to delete chat session');
    }
  }

  // Configuration management
  async saveConfig(config: ChatConfig): Promise<void> {
    try {
      localStorage.setItem(this.STORAGE_KEYS.CONFIG, JSON.stringify(config));
    } catch (error) {
      console.error('Failed to save config:', error);
      throw new Error('Failed to save chat configuration');
    }
  }

  async loadConfig(): Promise<ChatConfig | null> {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.CONFIG);
      if (!stored) return null;
      
      const config = JSON.parse(stored);
      return this.isValidConfig(config) ? config : null;
    } catch (error) {
      console.error('Failed to load config:', error);
      return null;
    }
  }

  // User preferences management
  async saveUserPreferences(preferences: ChatUserPreferences): Promise<void> {
    try {
      localStorage.setItem(this.STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(preferences));
    } catch (error) {
      console.error('Failed to save user preferences:', error);
      throw new Error('Failed to save user preferences');
    }
  }

  async loadUserPreferences(): Promise<ChatUserPreferences | null> {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.USER_PREFERENCES);
      if (!stored) return null;
      
      const preferences = JSON.parse(stored);
      return this.isValidUserPreferences(preferences) ? preferences : null;
    } catch (error) {
      console.error('Failed to load user preferences:', error);
      return null;
    }
  }

  // Current session management
  async saveCurrentSessionId(sessionId: string): Promise<void> {
    try {
      localStorage.setItem(this.STORAGE_KEYS.CURRENT_SESSION, sessionId);
    } catch (error) {
      console.error('Failed to save current session ID:', error);
    }
  }

  async loadCurrentSessionId(): Promise<string | null> {
    try {
      return localStorage.getItem(this.STORAGE_KEYS.CURRENT_SESSION);
    } catch (error) {
      console.error('Failed to load current session ID:', error);
      return null;
    }
  }

  // Utility methods
  async clearAllData(): Promise<void> {
    try {
      Object.values(this.STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.error('Failed to clear chat data:', error);
      throw new Error('Failed to clear chat data');
    }
  }

  async getStorageUsage(): Promise<{
    sessions: number;
    config: number;
    preferences: number;
    total: number;
  }> {
    try {
      const getSize = (key: string) => {
        const item = localStorage.getItem(key);
        return item ? new Blob([item]).size : 0;
      };

      const sessions = getSize(this.STORAGE_KEYS.SESSIONS);
      const config = getSize(this.STORAGE_KEYS.CONFIG);
      const preferences = getSize(this.STORAGE_KEYS.USER_PREFERENCES);
      const total = sessions + config + preferences;

      return { sessions, config, preferences, total };
    } catch (error) {
      console.error('Failed to calculate storage usage:', error);
      return { sessions: 0, config: 0, preferences: 0, total: 0 };
    }
  }

  async exportData(): Promise<{
    sessions: ChatSession[];
    config: ChatConfig | null;
    preferences: ChatUserPreferences | null;
    exportedAt: string;
  }> {
    try {
      const [sessions, config, preferences] = await Promise.all([
        this.loadSessions(),
        this.loadConfig(),
        this.loadUserPreferences()
      ]);

      return {
        sessions,
        config,
        preferences,
        exportedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to export data:', error);
      throw new Error('Failed to export chat data');
    }
  }

  async importData(data: {
    sessions?: ChatSession[];
    config?: ChatConfig;
    preferences?: ChatUserPreferences;
  }): Promise<void> {
    try {
      if (data.sessions) {
        const validSessions = data.sessions.filter(this.isValidSession);
        localStorage.setItem(this.STORAGE_KEYS.SESSIONS, JSON.stringify(validSessions));
      }

      if (data.config && this.isValidConfig(data.config)) {
        await this.saveConfig(data.config);
      }

      if (data.preferences && this.isValidUserPreferences(data.preferences)) {
        await this.saveUserPreferences(data.preferences);
      }
    } catch (error) {
      console.error('Failed to import data:', error);
      throw new Error('Failed to import chat data');
    }
  }

  // Validation methods
  private isValidSession(session: any): session is ChatSession {
    return (
      session &&
      typeof session.id === 'string' &&
      typeof session.createdAt === 'string' &&
      typeof session.updatedAt === 'string' &&
      Array.isArray(session.messages) &&
      session.messages.every(this.isValidMessage)
    );
  }

  private isValidMessage(message: any): boolean {
    return (
      message &&
      typeof message.id === 'string' &&
      typeof message.content === 'string' &&
      typeof message.role === 'string' &&
      ['user', 'assistant', 'system'].includes(message.role) &&
      typeof message.timestamp === 'string'
    );
  }

  private isValidConfig(config: any): config is ChatConfig {
    return (
      config &&
      typeof config.defaultModel === 'string' &&
      typeof config.defaultProvider === 'string' &&
      typeof config.maxSessionLength === 'number' &&
      typeof config.maxMessageLength === 'number' &&
      typeof config.contextWindow === 'number' &&
      typeof config.enableStreaming === 'boolean' &&
      typeof config.enableSources === 'boolean' &&
      typeof config.enableReasoning === 'boolean' &&
      config.rateLimits &&
      typeof config.rateLimits.messagesPerMinute === 'number' &&
      typeof config.rateLimits.tokensPerHour === 'number'
    );
  }

  private isValidUserPreferences(preferences: any): preferences is ChatUserPreferences {
    return (
      preferences &&
      ['short', 'medium', 'long'].includes(preferences.responseLength) &&
      typeof preferences.includeReferences === 'boolean' &&
      typeof preferences.includeReasoning === 'boolean'
    );
  }

  // Cleanup old sessions periodically
  async cleanupOldSessions(maxAge: number = 30 * 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const sessions = await this.loadSessions();
      const cutoffDate = new Date(Date.now() - maxAge);
      
      const recentSessions = sessions.filter(session => {
        const updatedAt = new Date(session.updatedAt);
        return updatedAt > cutoffDate;
      });

      localStorage.setItem(this.STORAGE_KEYS.SESSIONS, JSON.stringify(recentSessions));
    } catch (error) {
      console.error('Failed to cleanup old sessions:', error);
    }
  }
}

// Export singleton instance
export const chatStorageService = new ChatStorageServiceImpl();
