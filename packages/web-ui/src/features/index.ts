/**
 * Features Index
 * 
 * Central export point for all feature-slice modules.
 * Provides a clean API for consuming features across the application.
 */

// Feature exports
export * from './graph-visualization';
export * from './chat-interface';
export * from './analysis-tools';
export * from './search-filter';

// Feature registry
import { FeatureRegistryEntry } from '../shared/types/feature';

// Graph Visualization Feature
const graphVisualizationFeature: FeatureRegistryEntry = {
  metadata: {
    name: 'graph-visualization',
    version: '1.0.0',
    description: 'Interactive graph visualization with layout algorithms and rendering',
    dependencies: [],
    isolationLevel: 'partial' as any,
    permissions: ['graph:read', 'graph:interact'],
    tags: ['visualization', 'graph', 'interactive']
  },
  provider: {} as any, // Will be populated by actual implementations
  hooks: {},
  services: {},
  components: {}
};

// Chat Interface Feature
const chatInterfaceFeature: FeatureRegistryEntry = {
  metadata: {
    name: 'chat-interface',
    version: '1.0.0',
    description: 'Chat interface with LLM integration and streaming support',
    dependencies: [],
    isolationLevel: 'partial' as any,
    permissions: ['chat:read', 'chat:write', 'llm:access'],
    tags: ['chat', 'llm', 'streaming', 'interface']
  },
  provider: {} as any,
  hooks: {},
  services: {},
  components: {}
};

// Analysis Tools Feature
const analysisToolsFeature: FeatureRegistryEntry = {
  metadata: {
    name: 'analysis-tools',
    version: '1.0.0',
    description: 'Graph analysis tools including centrality, pathfinding, and clustering',
    dependencies: [
      {
        name: 'graph-visualization',
        version: '1.0.0',
        required: true,
        isolationLevel: 'partial' as any
      }
    ],
    isolationLevel: 'partial' as any,
    permissions: ['analysis:read', 'analysis:compute'],
    tags: ['analysis', 'algorithms', 'metrics']
  },
  provider: {} as any,
  hooks: {},
  services: {},
  components: {}
};

// Search & Filter Feature
const searchFilterFeature: FeatureRegistryEntry = {
  metadata: {
    name: 'search-filter',
    version: '1.0.0',
    description: 'Advanced search and filtering capabilities for graph data',
    dependencies: [
      {
        name: 'graph-visualization',
        version: '1.0.0',
        required: true,
        isolationLevel: 'partial' as any
      }
    ],
    isolationLevel: 'partial' as any,
    permissions: ['search:read', 'filter:apply'],
    tags: ['search', 'filter', 'query']
  },
  provider: {} as any,
  hooks: {},
  services: {},
  components: {}
};

// Feature registry
export const FEATURE_REGISTRY = {
  'graph-visualization': graphVisualizationFeature,
  'chat-interface': chatInterfaceFeature,
  'analysis-tools': analysisToolsFeature,
  'search-filter': searchFilterFeature
} as const;

// Feature names
export const FEATURE_NAMES = Object.keys(FEATURE_REGISTRY) as Array<keyof typeof FEATURE_REGISTRY>;

// Feature utilities
export const getFeatureMetadata = (featureName: keyof typeof FEATURE_REGISTRY) => {
  return FEATURE_REGISTRY[featureName]?.metadata;
};

export const getFeatureDependencies = (featureName: keyof typeof FEATURE_REGISTRY) => {
  return FEATURE_REGISTRY[featureName]?.metadata.dependencies || [];
};

export const isFeatureAvailable = (featureName: string): featureName is keyof typeof FEATURE_REGISTRY => {
  return featureName in FEATURE_REGISTRY;
};

// Feature loading utilities
export const loadFeature = async (featureName: keyof typeof FEATURE_REGISTRY) => {
  const feature = FEATURE_REGISTRY[featureName];
  if (!feature) {
    throw new Error(`Feature ${featureName} not found in registry`);
  }
  
  // Dynamic import based on feature name
  switch (featureName) {
    case 'graph-visualization':
      return import('./graph-visualization');
    case 'chat-interface':
      return import('./chat-interface');
    case 'analysis-tools':
      return import('./analysis-tools');
    case 'search-filter':
      return import('./search-filter');
    default:
      throw new Error(`Unknown feature: ${featureName}`);
  }
};

// Feature initialization
export const initializeFeatures = async (featureNames: Array<keyof typeof FEATURE_REGISTRY> = FEATURE_NAMES) => {
  const loadedFeatures: Record<string, any> = {};
  
  for (const featureName of featureNames) {
    try {
      const feature = await loadFeature(featureName);
      loadedFeatures[featureName] = feature;
      console.log(`Feature ${featureName} loaded successfully`);
    } catch (error) {
      console.error(`Failed to load feature ${featureName}:`, error);
    }
  }
  
  return loadedFeatures;
};

// Feature health check
export const checkFeatureHealth = () => {
  const health: Record<string, any> = {};
  
  FEATURE_NAMES.forEach(featureName => {
    const metadata = getFeatureMetadata(featureName);
    const dependencies = getFeatureDependencies(featureName);
    
    health[featureName] = {
      available: true,
      version: metadata?.version,
      dependencies: dependencies.map(dep => ({
        name: dep.name,
        required: dep.required,
        available: isFeatureAvailable(dep.name)
      })),
      permissions: metadata?.permissions || [],
      tags: metadata?.tags || []
    };
  });
  
  return health;
};
