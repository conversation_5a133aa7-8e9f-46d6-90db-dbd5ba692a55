{"name": "@kg-visualizer/web-ui", "version": "1.0.0", "description": "Web UI for Knowledge Graph Visualizer", "type": "module", "scripts": {"build": "tsc && vite build", "build:watch": "vite build --watch", "dev": "vite", "start": "vite preview", "start:production": "vite preview --port 3000 --host", "clean": "rm -rf dist", "clean:build": "npm run clean && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "migration:legacy": "cd ../../360t-kg-ui && npm run dev"}, "dependencies": {"@kg-visualizer/shared": "^1.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.0", "react-query": "^3.39.0", "axios": "^1.4.0", "d3": "^7.8.0", "vis-network": "^9.1.0", "vis-data": "^7.1.0", "react-markdown": "^8.0.0", "prismjs": "^1.29.0", "react-syntax-highlighter": "^15.5.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/d3": "^7.4.0", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@playwright/test": "^1.36.0", "@vitejs/plugin-react": "^4.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "typescript": "^5.1.0", "vite": "^4.4.0"}, "files": ["dist", "src"]}