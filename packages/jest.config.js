module.exports = {
  projects: [
    '<rootDir>/shared',
    '<rootDir>/graph-api',
    '<rootDir>/web-ui',
    '<rootDir>/chat-service'
  ],
  collectCoverageFrom: [
    '**/src/**/*.{ts,tsx}',
    '!**/src/**/*.d.ts',
    '!**/src/**/*.test.{ts,tsx}',
    '!**/src/**/*.spec.{ts,tsx}',
    '!**/node_modules/**',
    '!**/dist/**',
    '!**/coverage/**'
  ],
  coverageDirectory: '<rootDir>/coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  testMatch: [
    '**/src/**/__tests__/**/*.{ts,tsx}',
    '**/src/**/*.{test,spec}.{ts,tsx}'
  ],
  testEnvironment: 'node',
  transform: {
    '^.+\\.tsx?$': 'ts-jest'
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testTimeout: 10000,
  verbose: true
};
