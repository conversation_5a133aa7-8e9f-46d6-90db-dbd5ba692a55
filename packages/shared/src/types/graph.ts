/**
 * Graph-specific types and interfaces
 */

// Core graph types
export interface GraphNode {
  id: string;
  label: string;
  properties: Record<string, any>;
  type: string;
  metadata?: Record<string, any>;
  x?: number;
  y?: number;
  size?: number;
  color?: string;
  group?: string;
}

export interface GraphRelationship {
  id: string;
  type: string;
  startNodeId?: string;
  endNodeId?: string;
  source?: string;
  target?: string;
  properties: Record<string, any>;
  weight?: number;
  color?: string;
  width?: number;
  metadata?: Record<string, any>;
}

export interface GraphData {
  nodes: GraphNode[];
  relationships: GraphRelationship[];
  metadata?: GraphMetadata;
}

export interface GraphMetadata {
  nodeCount: number;
  relationshipCount: number;
  nodeTypes: string[];
  relationshipTypes: string[];
  lastUpdated: string;
}

// Graph query types
export interface GraphQuery {
  cypher: string;
  parameters?: Record<string, any>;
  limit?: number;
  offset?: number;
}

export interface GraphQueryResult {
  data: GraphData;
  summary: {
    executionTime: number;
    nodesReturned: number;
    relationshipsReturned: number;
    query: string;
  };
}

// Graph analysis types
export interface GraphAnalysis {
  centrality: {
    betweenness: Record<string, number>;
    closeness: Record<string, number>;
    degree: Record<string, number>;
    pagerank: Record<string, number>;
  };
  clustering: {
    coefficient: number;
    communities: Array<{
      id: string;
      nodes: string[];
      size: number;
    }>;
  };
  statistics: {
    density: number;
    diameter: number;
    averagePathLength: number;
    connectedComponents: number;
  };
}

// Graph visualization types
export interface GraphLayout {
  type: 'force' | 'circular' | 'hierarchical' | 'grid';
  options: Record<string, any>;
}

export interface GraphVisualizationConfig {
  layout: GraphLayout;
  physics: {
    enabled: boolean;
    stabilization: boolean;
    iterations: number;
  };
  interaction: {
    dragNodes: boolean;
    dragView: boolean;
    zoomView: boolean;
    selectConnectedEdges: boolean;
  };
  styling: {
    nodes: {
      defaultSize: number;
      defaultColor: string;
      borderWidth: number;
    };
    edges: {
      defaultWidth: number;
      defaultColor: string;
      arrows: boolean;
    };
  };
}

// Graph filter types
export interface GraphFilter {
  nodeTypes?: string[];
  relationshipTypes?: string[];
  properties?: Array<{
    key: string;
    operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex';
    value: any;
  }>;
  dateRange?: {
    start: string;
    end: string;
    property: string;
  };
}

// Graph export types
export interface GraphExportOptions {
  format: 'json' | 'csv' | 'graphml' | 'gexf' | 'cypher';
  includeProperties: boolean;
  includeMetadata: boolean;
  compression?: 'gzip' | 'zip';
}

export interface GraphExportResult {
  data: string | Buffer;
  filename: string;
  mimeType: string;
  size: number;
}
