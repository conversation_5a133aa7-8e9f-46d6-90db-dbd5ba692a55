/**
 * Chat-specific types and interfaces
 */

// Core chat types
export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: string;
  metadata?: ChatMessageMetadata;
}

export interface ChatMessageMetadata {
  tokens?: number;
  model?: string;
  provider?: string;
  responseTime?: number;
  sources?: ChatSource[];
  reasoning?: string;
  confidence?: number;
}

export interface ChatSource {
  type: 'node' | 'relationship' | 'document' | 'external';
  id: string;
  title: string;
  content: string;
  relevance: number;
  metadata?: Record<string, any>;
}

export interface ChatSession {
  id: string;
  title?: string;
  messages: ChatMessage[];
  createdAt: string;
  updatedAt: string;
  metadata?: ChatSessionMetadata;
}

export interface ChatSessionMetadata {
  userId?: string;
  context?: string;
  totalTokens?: number;
  totalMessages?: number;
  averageResponseTime?: number;
  tags?: string[];
}

// Chat request/response types
export interface ChatRequest {
  message: string;
  sessionId?: string;
  context?: ChatContext;
  options?: ChatOptions;
}

export interface ChatResponse {
  message: ChatMessage;
  sessionId: string;
  sources?: ChatSource[];
  suggestions?: string[];
  metadata?: ChatResponseMetadata;
}

export interface ChatResponseMetadata {
  model: string;
  provider: string;
  tokens: {
    prompt: number;
    completion: number;
    total: number;
  };
  responseTime: number;
  reasoning?: string;
  confidence: number;
}

export interface ChatContext {
  graphQuery?: string;
  selectedNodes?: string[];
  selectedRelationships?: string[];
  filters?: Record<string, any>;
  userPreferences?: ChatUserPreferences;
}

export interface ChatUserPreferences {
  responseLength: 'short' | 'medium' | 'long';
  includeReferences: boolean;
  includeReasoning: boolean;
  preferredModel?: string;
  language?: string;
}

export interface ChatOptions {
  model?: string;
  provider?: string;
  temperature?: number;
  maxTokens?: number;
  includeContext?: boolean;
  includeSources?: boolean;
  stream?: boolean;
}

// LLM provider types
export interface LLMProvider {
  name: string;
  models: LLMModel[];
  capabilities: LLMCapabilities;
  config: LLMProviderConfig;
}

export interface LLMModel {
  id: string;
  name: string;
  description: string;
  maxTokens: number;
  costPer1kTokens: number;
  capabilities: string[];
}

export interface LLMCapabilities {
  streaming: boolean;
  functionCalling: boolean;
  imageInput: boolean;
  codeGeneration: boolean;
  reasoning: boolean;
}

export interface LLMProviderConfig {
  apiKey?: string;
  baseUrl?: string;
  timeout?: number;
  retries?: number;
  rateLimit?: {
    requests: number;
    window: number;
  };
}

// Chat analytics types
export interface ChatAnalytics {
  sessions: {
    total: number;
    active: number;
    averageDuration: number;
    averageMessages: number;
  };
  messages: {
    total: number;
    byRole: Record<string, number>;
    averageLength: number;
    averageResponseTime: number;
  };
  models: {
    usage: Record<string, number>;
    performance: Record<string, {
      averageResponseTime: number;
      successRate: number;
      averageTokens: number;
    }>;
  };
  topics: Array<{
    name: string;
    count: number;
    sentiment: number;
  }>;
}

// Chat streaming types
export interface ChatStreamChunk {
  id: string;
  type: 'start' | 'content' | 'sources' | 'end' | 'error';
  content?: string;
  sources?: ChatSource[];
  metadata?: Record<string, any>;
  error?: string;
}

export interface ChatStreamResponse {
  sessionId: string;
  messageId: string;
  stream: ReadableStream<ChatStreamChunk>;
}

// Chat configuration types
export interface ChatConfig {
  defaultModel: string;
  defaultProvider: string;
  maxSessionLength: number;
  maxMessageLength: number;
  contextWindow: number;
  enableStreaming: boolean;
  enableSources: boolean;
  enableReasoning: boolean;
  rateLimits: {
    messagesPerMinute: number;
    tokensPerHour: number;
  };
}
