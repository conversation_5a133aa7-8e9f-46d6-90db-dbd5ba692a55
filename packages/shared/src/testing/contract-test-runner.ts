#!/usr/bin/env ts-node
/**
 * Contract Test Runner
 * 
 * This script runs contract tests to ensure API backward compatibility
 * during migration from legacy to new system.
 */

import { config } from 'dotenv';
import { join } from 'path';
import { 
  ContractTestingFramework, 
  ContractTest, 
  knowledgeGraphContracts,
  createContractTestConfig 
} from './contract-testing-framework';

// Load environment variables
config({ path: join(__dirname, '../../../../.env') });

interface ContractTestOptions {
  baseUrl: string;
  contracts?: ContractTest[];
  outputDir?: string;
  validateCompatibility?: boolean;
  previousContractsPath?: string;
}

class ContractTestRunner {
  private framework: ContractTestingFramework;
  private contracts: ContractTest[];

  constructor(options: ContractTestOptions) {
    const { framework, contracts } = createContractTestConfig(options.baseUrl);
    
    this.framework = framework;
    this.contracts = options.contracts || contracts;
  }

  /**
   * Run all contract tests
   */
  async runAllContractTests(): Promise<void> {
    console.log('📋 Running Complete Contract Test Suite');
    console.log('='.repeat(60));
    
    const result = await this.framework.runContractTests(this.contracts);
    
    console.log('\n📊 Contract Test Results Summary:');
    console.log(`Total Tests: ${result.totalTests}`);
    console.log(`Passed: ${result.passedTests}/${result.totalTests}`);
    console.log(`Failed: ${result.failedTests}`);
    console.log(`Schema Violations: ${result.schemaViolations}`);
    console.log(`Business Rule Violations: ${result.businessRuleViolations}`);
    console.log(`Average Response Time: ${result.averageResponseTime}ms`);
    
    // Check for critical issues
    const criticalIssues = result.results.filter(r => !r.success);
    
    if (criticalIssues.length > 0) {
      console.log(`\n⚠️  Critical Contract Violations: ${criticalIssues.length}`);
      criticalIssues.forEach(issue => {
        console.log(`  - ${issue.testName}: ${issue.violations.join(', ')}`);
      });
      
      console.log('\n🚨 MIGRATION RISK: API contract violations detected');
      console.log('   These violations may break existing clients');
      console.log('   Review and fix before proceeding with migration');
    } else {
      console.log('\n✅ All contract tests passed - API is backward compatible');
    }
  }

  /**
   * Run contract tests by category
   */
  async runCategoryTests(category: 'health' | 'graph' | 'chat' | 'analysis' | 'search'): Promise<void> {
    console.log(`🔍 Running ${category.toUpperCase()} Contract Tests`);
    console.log('-'.repeat(40));
    
    const categoryContracts = this.getContractsByCategory(category);
    
    if (categoryContracts.length === 0) {
      console.log(`No contracts found for category: ${category}`);
      return;
    }
    
    const result = await this.framework.runContractTests(categoryContracts);
    
    console.log(`\n📊 ${category.toUpperCase()} Contract Results:`);
    console.log(`Tests: ${result.totalTests}`);
    console.log(`Passed: ${result.passedTests}/${result.totalTests}`);
    console.log(`Average Response Time: ${result.averageResponseTime}ms`);
    
    if (result.failedTests > 0) {
      console.log(`\n⚠️  Failed Tests:`);
      result.results.filter(r => !r.success).forEach(failure => {
        console.log(`  - ${failure.testName}: ${failure.violations.join(', ')}`);
      });
    }
  }

  /**
   * Get contracts by category
   */
  private getContractsByCategory(category: string): ContractTest[] {
    const categoryMap: Record<string, string[]> = {
      health: ['Health Check'],
      graph: ['Graph Data'],
      analysis: ['Graph Analysis'],
      search: ['Graph Search'],
      chat: ['Chat Message']
    };

    const contractNames = categoryMap[category] || [];
    return this.contracts.filter(contract => 
      contractNames.includes(contract.endpoint.name)
    );
  }

  /**
   * Run critical path contract tests
   */
  async runCriticalPathTests(): Promise<void> {
    console.log('🎯 Running Critical Path Contract Tests');
    console.log('-'.repeat(40));
    
    // Critical endpoints that must work for basic functionality
    const criticalContracts = this.contracts.filter(contract =>
      ['Health Check', 'Graph Data', 'Chat Message'].includes(contract.endpoint.name)
    );
    
    const result = await this.framework.runContractTests(criticalContracts);
    
    if (result.passedTests === result.totalTests) {
      console.log('✅ All critical path tests passed - Core functionality intact');
    } else {
      console.log('🚨 Critical path failures detected - Core functionality at risk');
      
      result.results.filter(r => !r.success).forEach(failure => {
        console.log(`  ❌ ${failure.testName}: ${failure.violations.join(', ')}`);
      });
    }
  }

  /**
   * Run performance contract tests
   */
  async runPerformanceContractTests(): Promise<void> {
    console.log('⚡ Running Performance Contract Tests');
    console.log('-'.repeat(40));
    
    const result = await this.framework.runContractTests(this.contracts);
    
    // Analyze performance
    const slowTests = result.results.filter(r => r.responseTime > 5000); // > 5 seconds
    const fastTests = result.results.filter(r => r.responseTime < 1000); // < 1 second
    
    console.log(`\n📊 Performance Analysis:`);
    console.log(`Average Response Time: ${result.averageResponseTime}ms`);
    console.log(`Fast Tests (< 1s): ${fastTests.length}/${result.totalTests}`);
    console.log(`Slow Tests (> 5s): ${slowTests.length}/${result.totalTests}`);
    
    if (slowTests.length > 0) {
      console.log(`\n⚠️  Slow Endpoints:`);
      slowTests.forEach(test => {
        console.log(`  - ${test.testName}: ${test.responseTime}ms`);
      });
    }
    
    // Performance contract validation
    const performanceViolations = result.results.filter(r => r.responseTime > 10000); // > 10 seconds
    
    if (performanceViolations.length > 0) {
      console.log(`\n🚨 Performance Contract Violations:`);
      performanceViolations.forEach(violation => {
        console.log(`  - ${violation.testName}: ${violation.responseTime}ms (exceeds 10s limit)`);
      });
    } else {
      console.log('\n✅ All endpoints meet performance contracts');
    }
  }

  /**
   * Validate backward compatibility
   */
  async validateBackwardCompatibility(previousContractsPath?: string): Promise<void> {
    console.log('🔄 Validating Backward Compatibility');
    console.log('-'.repeat(40));
    
    if (!previousContractsPath) {
      console.log('⚠️  No previous contracts provided - skipping compatibility check');
      return;
    }
    
    try {
      // In a real implementation, this would load previous contracts from file
      console.log(`Loading previous contracts from: ${previousContractsPath}`);
      
      // For now, simulate with current contracts as "old" version
      const oldContracts = this.contracts;
      const newContracts = this.contracts;
      
      const compatibility = await this.framework.validateBackwardCompatibility(
        oldContracts,
        newContracts
      );
      
      console.log(`\n📊 Compatibility Analysis:`);
      console.log(`Compatible: ${compatibility.compatible ? 'YES' : 'NO'}`);
      console.log(`Breaking Changes: ${compatibility.breakingChanges.length}`);
      console.log(`Warnings: ${compatibility.warnings.length}`);
      
      if (compatibility.breakingChanges.length > 0) {
        console.log(`\n🚨 Breaking Changes:`);
        compatibility.breakingChanges.forEach(change => {
          console.log(`  - ${change}`);
        });
      }
      
      if (compatibility.warnings.length > 0) {
        console.log(`\n⚠️  Warnings:`);
        compatibility.warnings.forEach(warning => {
          console.log(`  - ${warning}`);
        });
      }
      
      if (compatibility.compatible) {
        console.log('\n✅ API is backward compatible');
      } else {
        console.log('\n🚨 API has breaking changes - migration risk detected');
      }
      
    } catch (error) {
      console.error('❌ Failed to validate backward compatibility:', error);
    }
  }

  /**
   * Generate contract documentation
   */
  async generateContractDocumentation(): Promise<void> {
    console.log('📚 Generating Contract Documentation');
    console.log('-'.repeat(40));
    
    let documentation = `# API Contract Documentation\n\n`;
    documentation += `Generated: ${new Date().toISOString()}\n\n`;
    
    documentation += `## Overview\n\n`;
    documentation += `This document describes the API contracts for the Knowledge Graph Visualizer.\n`;
    documentation += `These contracts ensure backward compatibility during system migration.\n\n`;
    
    documentation += `## Endpoints\n\n`;
    
    for (const contract of this.contracts) {
      documentation += `### ${contract.endpoint.name}\n\n`;
      documentation += `- **Method**: ${contract.endpoint.method}\n`;
      documentation += `- **Path**: ${contract.endpoint.path}\n`;
      documentation += `- **Expected Status**: ${contract.endpoint.expectedStatus || 200}\n\n`;
      
      if (contract.description) {
        documentation += `**Description**: ${contract.description}\n\n`;
      }
      
      if (contract.endpoint.body) {
        documentation += `**Request Body**:\n`;
        documentation += `\`\`\`json\n${JSON.stringify(contract.endpoint.body, null, 2)}\n\`\`\`\n\n`;
      }
      
      documentation += `**Response Schema**:\n`;
      documentation += `\`\`\`json\n${JSON.stringify(contract.responseSchema, null, 2)}\n\`\`\`\n\n`;
      
      if (contract.businessRules && contract.businessRules.length > 0) {
        documentation += `**Business Rules**:\n`;
        contract.businessRules.forEach(rule => {
          documentation += `- ${rule.name}: ${rule.description}\n`;
        });
        documentation += `\n`;
      }
    }
    
    // Save documentation
    const fs = require('fs');
    const path = require('path');
    
    const outputPath = path.join('./contract-test-results', 'api-contracts.md');
    fs.writeFileSync(outputPath, documentation);
    
    console.log(`📁 Contract documentation saved: ${outputPath}`);
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'all';
  
  // Parse base URL
  const baseUrl = process.env.API_BASE_URL || 
                  process.env.NEW_BASE_URL || 
                  'http://localhost:3003';
  
  const testRunner = new ContractTestRunner({ baseUrl });

  try {
    switch (command) {
      case 'all':
        await testRunner.runAllContractTests();
        break;
      case 'critical':
        await testRunner.runCriticalPathTests();
        break;
      case 'performance':
        await testRunner.runPerformanceContractTests();
        break;
      case 'compatibility':
        await testRunner.validateBackwardCompatibility(args[1]);
        break;
      case 'docs':
        await testRunner.generateContractDocumentation();
        break;
      case 'health':
      case 'graph':
      case 'chat':
      case 'analysis':
      case 'search':
        await testRunner.runCategoryTests(command as any);
        break;
      default:
        console.log('Usage: npm run test:contracts [command] [options]');
        console.log('');
        console.log('Commands:');
        console.log('  all           Run all contract tests (default)');
        console.log('  critical      Run critical path tests only');
        console.log('  performance   Run performance contract tests');
        console.log('  compatibility Validate backward compatibility');
        console.log('  docs          Generate contract documentation');
        console.log('  health        Run health endpoint tests');
        console.log('  graph         Run graph endpoint tests');
        console.log('  chat          Run chat endpoint tests');
        console.log('  analysis      Run analysis endpoint tests');
        console.log('  search        Run search endpoint tests');
        console.log('');
        console.log('Environment Variables:');
        console.log('  API_BASE_URL  Base URL for API testing (default: http://localhost:3003)');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ Contract test execution failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { ContractTestRunner };
export type { ContractTestOptions };
