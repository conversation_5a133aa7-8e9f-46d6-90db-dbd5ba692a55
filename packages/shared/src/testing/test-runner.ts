#!/usr/bin/env ts-node
/**
 * Parallel Testing Framework Test Runner
 * 
 * This script runs comprehensive parallel tests between legacy and new systems
 * to validate migration accuracy and performance.
 */

import { ParallelTestingFramework, TestEndpoint, SystemConfig } from './parallel-testing-framework';
import { config } from 'dotenv';
import { join } from 'path';

// Load environment variables
config({ path: join(__dirname, '../../../../.env') });

// System configurations
const legacySystem: SystemConfig = {
  name: 'Legacy System',
  baseUrl: process.env.LEGACY_BASE_URL || 'http://localhost:3000',
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'Parallel-Test-Runner/1.0.0'
  },
  timeout: 30000
};

const newSystem: SystemConfig = {
  name: 'New System',
  baseUrl: process.env.NEW_BASE_URL || 'http://localhost:3003',
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'Parallel-Test-Runner/1.0.0'
  },
  timeout: 30000
};

// Comprehensive test endpoints
const allEndpoints: TestEndpoint[] = [
  // Health and Status Endpoints
  {
    name: 'System Health Check',
    path: '/health',
    method: 'GET'
  },
  {
    name: 'API Status',
    path: '/api/status',
    method: 'GET'
  },
  {
    name: 'Database Health',
    path: '/api/health/database',
    method: 'GET'
  },

  // Graph API Endpoints
  {
    name: 'Graph Data - Default Group',
    path: '/api/graph',
    method: 'GET',
    params: { group_id: 'user_guides' }
  },
  {
    name: 'Graph Data - With Filters',
    path: '/api/graph',
    method: 'GET',
    params: { 
      group_id: 'user_guides',
      limit: 50,
      include_relationships: true
    }
  },
  {
    name: 'Graph Analysis - Basic',
    path: '/api/analysis',
    method: 'POST',
    body: {
      group_id: 'user_guides',
      analysis_type: 'basic',
      include_metrics: true
    }
  },
  {
    name: 'Graph Analysis - Advanced',
    path: '/api/analysis',
    method: 'POST',
    body: {
      group_id: 'user_guides',
      analysis_type: 'advanced',
      algorithms: ['pagerank', 'centrality'],
      include_metrics: true
    }
  },
  {
    name: 'Graph Search - Simple Query',
    path: '/api/search',
    method: 'POST',
    body: {
      query: 'knowledge graph',
      group_id: 'user_guides',
      limit: 10
    }
  },
  {
    name: 'Graph Search - Complex Query',
    path: '/api/search',
    method: 'POST',
    body: {
      query: 'data visualization and analysis',
      group_id: 'user_guides',
      limit: 20,
      include_context: true,
      search_type: 'semantic'
    }
  },

  // Chat API Endpoints
  {
    name: 'Chat Message - Simple',
    path: '/api/chat/message',
    method: 'POST',
    headers: {
      'X-Session-ID': 'test-session-001'
    },
    body: {
      message: 'Hello, can you help me understand knowledge graphs?'
    }
  },
  {
    name: 'Chat Message - With Context',
    path: '/api/chat/message',
    method: 'POST',
    headers: {
      'X-Session-ID': 'test-session-002'
    },
    body: {
      message: 'What are the main components of a graph database?',
      context: {
        group_id: 'user_guides',
        include_graph_context: true
      }
    }
  },
  {
    name: 'Chat History',
    path: '/api/chat/history',
    method: 'GET',
    headers: {
      'X-Session-ID': 'test-session-001'
    }
  },
  {
    name: 'Chat Sessions List',
    path: '/api/chat/sessions',
    method: 'GET'
  },

  // Monitoring and Service Discovery
  {
    name: 'Service Discovery - All Services',
    path: '/api/monitoring/services',
    method: 'GET'
  },
  {
    name: 'Service Discovery - Healthy Services',
    path: '/api/monitoring/services',
    method: 'GET',
    params: { status: 'healthy' }
  },
  {
    name: 'Monitoring Health Status',
    path: '/api/monitoring/health',
    method: 'GET'
  },
  {
    name: 'System Statistics',
    path: '/api/monitoring/statistics',
    method: 'GET'
  },

  // Performance and Metrics
  {
    name: 'Performance Metrics',
    path: '/api/metrics',
    method: 'GET'
  },
  {
    name: 'System Info',
    path: '/api/info',
    method: 'GET'
  }
];

// Test categories for focused testing
const testCategories = {
  health: [
    'System Health Check',
    'API Status',
    'Database Health'
  ],
  graph: [
    'Graph Data - Default Group',
    'Graph Data - With Filters',
    'Graph Analysis - Basic',
    'Graph Analysis - Advanced',
    'Graph Search - Simple Query',
    'Graph Search - Complex Query'
  ],
  chat: [
    'Chat Message - Simple',
    'Chat Message - With Context',
    'Chat History',
    'Chat Sessions List'
  ],
  monitoring: [
    'Service Discovery - All Services',
    'Service Discovery - Healthy Services',
    'Monitoring Health Status',
    'System Statistics'
  ],
  performance: [
    'Performance Metrics',
    'System Info'
  ]
};

class TestRunner {
  private framework: ParallelTestingFramework;

  constructor() {
    this.framework = new ParallelTestingFramework(
      legacySystem,
      newSystem,
      './test-results'
    );
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<void> {
    console.log('🚀 Running Complete Parallel Test Suite');
    console.log('='.repeat(60));
    
    const result = await this.framework.runParallelTests(allEndpoints);
    
    console.log('\n📊 Test Results Summary:');
    console.log(`Total Tests: ${result.totalTests}`);
    console.log(`Identical Responses: ${result.identicalResponses}/${result.totalTests}`);
    console.log(`Performance Improvement: ${result.performanceImprovement.toFixed(1)}%`);
    
    // Check for critical issues
    const criticalIssues = result.comparisons.filter(c => 
      !c.statusCodeMatch || (!c.dataMatch && c.legacyResult.success && c.newResult.success)
    );
    
    if (criticalIssues.length > 0) {
      console.log(`\n⚠️  Critical Issues Found: ${criticalIssues.length}`);
      criticalIssues.forEach(issue => {
        console.log(`  - ${issue.endpoint}: ${issue.differences.join(', ')}`);
      });
    }
  }

  /**
   * Run tests by category
   */
  async runCategoryTests(category: keyof typeof testCategories): Promise<void> {
    console.log(`🔍 Running ${category.toUpperCase()} Tests`);
    console.log('-'.repeat(40));
    
    const categoryEndpoints = testCategories[category];
    const endpoints = allEndpoints.filter(e => categoryEndpoints.includes(e.name));
    
    const result = await this.framework.runParallelTests(endpoints);
    
    console.log(`\n📊 ${category.toUpperCase()} Test Results:`);
    console.log(`Tests: ${result.totalTests}`);
    console.log(`Identical: ${result.identicalResponses}/${result.totalTests}`);
    console.log(`Performance: ${result.performanceImprovement.toFixed(1)}%`);
  }

  /**
   * Run load testing comparison
   */
  async runLoadTests(): Promise<void> {
    console.log('🔥 Running Load Test Comparison');
    console.log('-'.repeat(40));
    
    // Test critical endpoints under load
    const loadTestEndpoints = [
      allEndpoints.find(e => e.name === 'Graph Data - Default Group')!,
      allEndpoints.find(e => e.name === 'Chat Message - Simple')!,
      allEndpoints.find(e => e.name === 'Graph Search - Simple Query')!
    ];

    for (const endpoint of loadTestEndpoints) {
      console.log(`\n🔍 Load testing: ${endpoint.name}`);
      
      const result = await this.framework.runLoadTestComparison(
        endpoint,
        10, // 10 concurrent users
        30000 // 30 seconds
      );
      
      console.log(`Legacy: ${result.legacy.requestsPerSecond.toFixed(1)} req/s, ${result.legacy.responseTime.average}ms avg`);
      console.log(`New: ${result.new.requestsPerSecond.toFixed(1)} req/s, ${result.new.responseTime.average}ms avg`);
      console.log(`Winner: ${result.comparison.winner}`);
      console.log(`Summary: ${result.comparison.summary}`);
    }
  }

  /**
   * Run smoke tests (quick validation)
   */
  async runSmokeTests(): Promise<void> {
    console.log('💨 Running Smoke Tests');
    console.log('-'.repeat(30));
    
    const smokeTestEndpoints = [
      allEndpoints.find(e => e.name === 'System Health Check')!,
      allEndpoints.find(e => e.name === 'Graph Data - Default Group')!,
      allEndpoints.find(e => e.name === 'Chat Message - Simple')!
    ];

    const result = await this.framework.runParallelTests(smokeTestEndpoints);
    
    if (result.identicalResponses === result.totalTests) {
      console.log('✅ All smoke tests passed - systems are equivalent');
    } else {
      console.log('⚠️  Smoke test differences detected');
      result.comparisons.forEach(c => {
        if (!c.identical) {
          console.log(`  - ${c.endpoint}: ${c.differences.join(', ')}`);
        }
      });
    }
  }
}

// CLI interface
async function main() {
  const testRunner = new TestRunner();
  const args = process.argv.slice(2);
  const command = args[0] || 'all';

  try {
    switch (command) {
      case 'all':
        await testRunner.runAllTests();
        break;
      case 'smoke':
        await testRunner.runSmokeTests();
        break;
      case 'load':
        await testRunner.runLoadTests();
        break;
      case 'health':
      case 'graph':
      case 'chat':
      case 'monitoring':
      case 'performance':
        await testRunner.runCategoryTests(command as keyof typeof testCategories);
        break;
      default:
        console.log('Usage: npm run test:parallel [all|smoke|load|health|graph|chat|monitoring|performance]');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { TestRunner, allEndpoints, testCategories };
