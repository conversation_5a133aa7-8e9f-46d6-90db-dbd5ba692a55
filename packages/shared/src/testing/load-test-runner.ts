#!/usr/bin/env ts-node
/**
 * Migration Load Test Runner
 * 
 * This script runs comprehensive load tests during migration scenarios
 * to validate system performance under various traffic conditions.
 */

import { config } from 'dotenv';
import { join } from 'path';
import { 
  MigrationLoadTester, 
  LoadTestConfig, 
  migrationLoadTestScenarios 
} from './migration-load-testing';

// Load environment variables
config({ path: join(__dirname, '../../../../.env') });

interface LoadTestRunnerOptions {
  scenario: keyof typeof migrationLoadTestScenarios;
  customConfig?: Partial<LoadTestConfig>;
  outputDir?: string;
  dryRun?: boolean;
}

class LoadTestRunner {
  private outputDir: string;

  constructor(outputDir: string = './load-test-results') {
    this.outputDir = outputDir;
  }

  /**
   * Run a specific load test scenario
   */
  async runScenario(options: LoadTestRunnerOptions): Promise<void> {
    const scenarioName = options.scenario;
    const baseConfig = migrationLoadTestScenarios[scenarioName];
    
    if (!baseConfig) {
      throw new Error(`Unknown scenario: ${scenarioName}`);
    }

    // Merge custom configuration
    const config: LoadTestConfig = {
      ...baseConfig,
      ...options.customConfig
    };

    console.log(`🚀 Starting Load Test Scenario: ${scenarioName}`);
    console.log('='.repeat(60));
    
    if (options.dryRun) {
      console.log('🔍 DRY RUN MODE - Configuration Preview:');
      console.log(JSON.stringify(config, null, 2));
      return;
    }

    // Validate endpoints are accessible
    await this.validateEndpoints(config);

    // Run load test
    const tester = new MigrationLoadTester(config, this.outputDir);
    
    try {
      const summary = await tester.runMigrationLoadTest();
      
      // Analyze results
      this.analyzeResults(summary);
      
    } catch (error) {
      console.error('❌ Load test failed:', error);
      throw error;
    }
  }

  /**
   * Run progressive load test (increasing traffic to new system)
   */
  async runProgressiveLoadTest(): Promise<void> {
    console.log('📈 Starting Progressive Load Test');
    console.log('='.repeat(60));
    console.log('This test gradually increases traffic to the new system');
    console.log('to simulate a real migration scenario.');
    console.log('-'.repeat(60));

    const trafficSplits = [10, 25, 50, 75, 90]; // Progressive traffic percentages
    const results: any[] = [];

    for (const trafficSplit of trafficSplits) {
      console.log(`\n🔄 Phase: ${trafficSplit}% traffic to new system`);
      console.log('-'.repeat(40));

      const config: LoadTestConfig = {
        ...migrationLoadTestScenarios.migrationSimulation,
        name: `Progressive Migration - ${trafficSplit}%`,
        trafficSplit,
        duration: 120, // 2 minutes per phase
        concurrentUsers: 8
      };

      const tester = new MigrationLoadTester(config, this.outputDir);
      
      try {
        const summary = await tester.runMigrationLoadTest();
        results.push(summary);
        
        // Check if we should continue
        if (summary.thresholdViolations.length > 0) {
          console.log(`⚠️  Threshold violations detected at ${trafficSplit}% traffic`);
          console.log('Stopping progressive test for safety');
          break;
        }
        
        // Brief pause between phases
        console.log('⏸️  Pausing 30 seconds before next phase...');
        await this.sleep(30000);
        
      } catch (error) {
        console.error(`❌ Progressive test failed at ${trafficSplit}%:`, error);
        break;
      }
    }

    // Generate progressive test summary
    this.generateProgressiveSummary(results);
  }

  /**
   * Run stress test to find breaking point
   */
  async runStressTest(): Promise<void> {
    console.log('💥 Starting Stress Test');
    console.log('='.repeat(60));
    console.log('This test increases load until system breaking point');
    console.log('-'.repeat(60));

    const userCounts = [5, 10, 20, 30, 50]; // Progressive user counts
    const results: any[] = [];

    for (const userCount of userCounts) {
      console.log(`\n🔥 Stress Phase: ${userCount} concurrent users`);
      console.log('-'.repeat(40));

      const config: LoadTestConfig = {
        ...migrationLoadTestScenarios.heavyLoad,
        name: `Stress Test - ${userCount} users`,
        concurrentUsers: userCount,
        duration: 180, // 3 minutes per phase
        trafficSplit: 50 // Even split for stress testing
      };

      const tester = new MigrationLoadTester(config, this.outputDir);
      
      try {
        const summary = await tester.runMigrationLoadTest();
        results.push(summary);
        
        // Check if system is breaking
        if (summary.combinedMetrics.errorRate > 10 || 
            summary.combinedMetrics.p95ResponseTime > 10000) {
          console.log(`🚨 System breaking point reached at ${userCount} users`);
          console.log(`Error rate: ${summary.combinedMetrics.errorRate}%`);
          console.log(`P95 response time: ${summary.combinedMetrics.p95ResponseTime}ms`);
          break;
        }
        
        // Brief pause between phases
        console.log('⏸️  Pausing 60 seconds before next phase...');
        await this.sleep(60000);
        
      } catch (error) {
        console.error(`❌ Stress test failed at ${userCount} users:`, error);
        break;
      }
    }

    // Generate stress test summary
    this.generateStressSummary(results);
  }

  /**
   * Validate that endpoints are accessible before testing
   */
  private async validateEndpoints(config: LoadTestConfig): Promise<void> {
    console.log('🔍 Validating endpoint accessibility...');
    
    const axios = require('axios');
    const endpoints = [config.legacyBaseUrl, config.newBaseUrl];
    
    for (const baseUrl of endpoints) {
      try {
        const response = await axios.get(`${baseUrl}/health`, { timeout: 5000 });
        console.log(`✅ ${baseUrl}: ${response.status}`);
      } catch (error) {
        console.log(`⚠️  ${baseUrl}: Not accessible - ${(error as any).message}`);
      }
    }
  }

  /**
   * Analyze load test results
   */
  private analyzeResults(summary: any): void {
    console.log('\n📊 Load Test Analysis');
    console.log('-'.repeat(30));
    
    // Performance comparison
    if (summary.newMetrics.totalRequests > 0 && summary.legacyMetrics.totalRequests > 0) {
      const responseTimeImprovement = 
        ((summary.legacyMetrics.averageResponseTime - summary.newMetrics.averageResponseTime) / 
         summary.legacyMetrics.averageResponseTime) * 100;
      
      const throughputImprovement = 
        ((summary.newMetrics.throughput - summary.legacyMetrics.throughput) / 
         summary.legacyMetrics.throughput) * 100;
      
      console.log(`🚀 Performance Comparison:`);
      console.log(`   Response Time: ${responseTimeImprovement > 0 ? '+' : ''}${responseTimeImprovement.toFixed(1)}%`);
      console.log(`   Throughput: ${throughputImprovement > 0 ? '+' : ''}${throughputImprovement.toFixed(1)}%`);
    }
    
    // Threshold analysis
    if (summary.thresholdViolations.length === 0) {
      console.log('✅ All performance thresholds met');
    } else {
      console.log(`⚠️  ${summary.thresholdViolations.length} threshold violations`);
    }
    
    // Migration readiness
    console.log(`📝 Migration Status: ${summary.migrationRecommendation}`);
  }

  /**
   * Generate progressive test summary
   */
  private generateProgressiveSummary(results: any[]): void {
    console.log('\n📈 Progressive Load Test Summary');
    console.log('='.repeat(50));
    
    results.forEach((result, index) => {
      console.log(`Phase ${index + 1} (${result.trafficSplit}% traffic):`);
      console.log(`  Combined Error Rate: ${result.combinedMetrics.errorRate}%`);
      console.log(`  Combined P95 Time: ${result.combinedMetrics.p95ResponseTime}ms`);
      console.log(`  Violations: ${result.thresholdViolations.length}`);
      console.log(`  Recommendation: ${result.migrationRecommendation}`);
      console.log('');
    });
    
    const maxSafeTraffic = results.length > 0 ? 
      results[results.length - 1].trafficSplit : 0;
    
    console.log(`🎯 Maximum Safe Traffic Split: ${maxSafeTraffic}%`);
  }

  /**
   * Generate stress test summary
   */
  private generateStressSummary(results: any[]): void {
    console.log('\n💥 Stress Test Summary');
    console.log('='.repeat(40));
    
    results.forEach((result) => {
      console.log(`${result.concurrentUsers} users:`);
      console.log(`  Error Rate: ${result.combinedMetrics.errorRate}%`);
      console.log(`  P95 Time: ${result.combinedMetrics.p95ResponseTime}ms`);
      console.log(`  Throughput: ${result.combinedMetrics.throughput} req/s`);
      console.log('');
    });
    
    const maxUsers = results.length > 0 ? 
      results[results.length - 1].concurrentUsers : 0;
    
    console.log(`🎯 Maximum Concurrent Users: ${maxUsers}`);
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'light';
  
  const runner = new LoadTestRunner('./load-test-results');

  try {
    switch (command) {
      case 'light':
        await runner.runScenario({ scenario: 'lightLoad' });
        break;
      case 'medium':
        await runner.runScenario({ scenario: 'mediumLoad' });
        break;
      case 'heavy':
        await runner.runScenario({ scenario: 'heavyLoad' });
        break;
      case 'migration':
        await runner.runScenario({ scenario: 'migrationSimulation' });
        break;
      case 'progressive':
        await runner.runProgressiveLoadTest();
        break;
      case 'stress':
        await runner.runStressTest();
        break;
      case 'dry-run':
        await runner.runScenario({ 
          scenario: args[1] as any || 'lightLoad', 
          dryRun: true 
        });
        break;
      default:
        console.log('Usage: npm run test:load [command]');
        console.log('');
        console.log('Commands:');
        console.log('  light       Run light load test (5 users, 1 min)');
        console.log('  medium      Run medium load test (15 users, 5 min)');
        console.log('  heavy       Run heavy load test (30 users, 10 min)');
        console.log('  migration   Run migration simulation (10 users, 3 min)');
        console.log('  progressive Run progressive traffic split test');
        console.log('  stress      Run stress test to find breaking point');
        console.log('  dry-run     Preview test configuration');
        console.log('');
        console.log('Environment Variables:');
        console.log('  LEGACY_BASE_URL  Legacy system URL (default: http://localhost:3000)');
        console.log('  NEW_BASE_URL     New system URL (default: http://localhost:3003)');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ Load test execution failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { LoadTestRunner };
export type { LoadTestRunnerOptions };
