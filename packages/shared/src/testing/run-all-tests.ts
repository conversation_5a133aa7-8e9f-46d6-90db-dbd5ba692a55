#!/usr/bin/env ts-node
/**
 * Comprehensive Test Suite Runner
 * 
 * This script runs all testing frameworks to validate the complete
 * migration testing infrastructure and demonstrate functionality.
 */

import { config } from 'dotenv';
import { join } from 'path';

// Load environment variables
config({ path: join(__dirname, '../../../../.env') });

interface TestSuiteResults {
  parallelFramework: boolean;
  contractFramework: boolean;
  loadTesting: boolean;
  integration: boolean;
  overallSuccess: boolean;
  errors: string[];
  warnings: string[];
}

class ComprehensiveTestRunner {
  private results: TestSuiteResults = {
    parallelFramework: false,
    contractFramework: false,
    loadTesting: false,
    integration: false,
    overallSuccess: false,
    errors: [],
    warnings: []
  };

  /**
   * Run all test frameworks
   */
  async runAllTests(): Promise<TestSuiteResults> {
    console.log('🚀 Starting Comprehensive Test Suite Validation');
    console.log('='.repeat(70));
    console.log('This will validate all migration testing frameworks');
    console.log('-'.repeat(70));

    try {
      // Phase 1: Test Parallel Testing Framework
      await this.testParallelFramework();
      
      // Phase 2: Test Contract Testing Framework
      await this.testContractFramework();
      
      // Phase 3: Test Load Testing Framework
      await this.testLoadTestingFramework();
      
      // Phase 4: Test Integration Suite
      await this.testIntegrationSuite();
      
      // Generate final assessment
      this.generateFinalAssessment();
      
      return this.results;

    } catch (error) {
      console.error('❌ Comprehensive test suite failed:', error);
      this.results.errors.push(`Test execution failed: ${error}`);
      return this.results;
    }
  }

  /**
   * Test the parallel testing framework
   */
  private async testParallelFramework(): Promise<void> {
    console.log('\n📊 Phase 1: Testing Parallel Testing Framework');
    console.log('-'.repeat(50));
    
    try {
      // Import and test the framework
      const { testParallelFramework } = await import('./test-parallel-framework');
      
      console.log('Running parallel framework validation...');
      await testParallelFramework();
      
      this.results.parallelFramework = true;
      console.log('✅ Parallel testing framework validation passed');
      
    } catch (error) {
      console.error('❌ Parallel testing framework validation failed:', error);
      this.results.errors.push(`Parallel framework: ${error}`);
      this.results.warnings.push('Parallel testing framework may not work correctly');
    }
  }

  /**
   * Test the contract testing framework
   */
  private async testContractFramework(): Promise<void> {
    console.log('\n📋 Phase 2: Testing Contract Testing Framework');
    console.log('-'.repeat(50));
    
    try {
      // Import and test the framework
      const { testContractFramework } = await import('./test-contract-framework');
      
      console.log('Running contract framework validation...');
      await testContractFramework();
      
      this.results.contractFramework = true;
      console.log('✅ Contract testing framework validation passed');
      
    } catch (error) {
      console.error('❌ Contract testing framework validation failed:', error);
      this.results.errors.push(`Contract framework: ${error}`);
      this.results.warnings.push('Contract testing framework may not work correctly');
    }
  }

  /**
   * Test the load testing framework
   */
  private async testLoadTestingFramework(): Promise<void> {
    console.log('\n🔥 Phase 3: Testing Load Testing Framework');
    console.log('-'.repeat(50));
    
    try {
      // Test load testing configuration
      const { migrationLoadTestScenarios } = await import('./migration-load-testing');
      
      console.log('Validating load test scenarios...');
      
      // Validate all scenarios have required properties
      const scenarios = Object.keys(migrationLoadTestScenarios);
      console.log(`Found ${scenarios.length} load test scenarios:`);
      
      for (const scenarioName of scenarios) {
        const scenario = migrationLoadTestScenarios[scenarioName as keyof typeof migrationLoadTestScenarios];
        
        // Validate scenario structure
        if (!scenario.name || !scenario.endpoints || !scenario.thresholds) {
          throw new Error(`Invalid scenario structure: ${scenarioName}`);
        }
        
        console.log(`  ✅ ${scenarioName}: ${scenario.endpoints.length} endpoints`);
      }
      
      // Test dry run of light load scenario
      console.log('\nTesting load test dry run...');
      const { LoadTestRunner } = await import('./load-test-runner');
      const runner = new LoadTestRunner('./test-results/load-test-validation');
      
      await runner.runScenario({ 
        scenario: 'lightLoad', 
        dryRun: true,
        customConfig: { duration: 5, concurrentUsers: 1 } // Very short test
      });
      
      this.results.loadTesting = true;
      console.log('✅ Load testing framework validation passed');
      
    } catch (error) {
      console.error('❌ Load testing framework validation failed:', error);
      this.results.errors.push(`Load testing framework: ${error}`);
      this.results.warnings.push('Load testing framework may not work correctly');
    }
  }

  /**
   * Test the integration suite
   */
  private async testIntegrationSuite(): Promise<void> {
    console.log('\n🔗 Phase 4: Testing Integration Suite');
    console.log('-'.repeat(50));
    
    try {
      // Import integration suite
      const { IntegrationTestSuite } = await import('./integration-test-suite');
      
      console.log('Validating integration test configuration...');
      
      // Test configuration validation
      const config = {
        legacyBaseUrl: 'http://localhost:3000',
        newBaseUrl: 'http://localhost:3003',
        runParallelTests: false, // Disable for validation
        runContractTests: false, // Disable for validation
        runDatabaseTests: false, // Disable for validation
        runLoadTests: false, // Disable for validation
        outputDir: './test-results/integration-validation'
      };
      
      new IntegrationTestSuite(config);
      console.log('✅ Integration suite configuration valid');
      
      // Test that all components are properly imported
      const { knowledgeGraphContracts } = await import('./contract-testing-framework');
      const { knowledgeGraphQueries } = await import('./database-comparison');
      
      console.log(`✅ Contract definitions: ${knowledgeGraphContracts.length} contracts`);
      console.log(`✅ Database queries: ${knowledgeGraphQueries.length} queries`);
      
      this.results.integration = true;
      console.log('✅ Integration suite validation passed');
      
    } catch (error) {
      console.error('❌ Integration suite validation failed:', error);
      this.results.errors.push(`Integration suite: ${error}`);
      this.results.warnings.push('Integration suite may not work correctly');
    }
  }

  /**
   * Generate final assessment
   */
  private generateFinalAssessment(): void {
    console.log('\n' + '='.repeat(70));
    console.log('📋 COMPREHENSIVE TEST FRAMEWORK VALIDATION SUMMARY');
    console.log('='.repeat(70));

    // Count successful frameworks
    const successCount = [
      this.results.parallelFramework,
      this.results.contractFramework,
      this.results.loadTesting,
      this.results.integration
    ].filter(Boolean).length;

    const totalFrameworks = 4;
    const successPercentage = (successCount / totalFrameworks) * 100;

    console.log(`📊 Framework Validation Results:`);
    console.log(`   Parallel Testing: ${this.results.parallelFramework ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Contract Testing: ${this.results.contractFramework ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Load Testing: ${this.results.loadTesting ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Integration Suite: ${this.results.integration ? '✅ PASS' : '❌ FAIL'}`);
    
    console.log(`\n📈 Overall Success Rate: ${successCount}/${totalFrameworks} (${successPercentage.toFixed(1)}%)`);

    // Determine overall status
    if (successCount === totalFrameworks) {
      this.results.overallSuccess = true;
      console.log(`\n🎉 ALL FRAMEWORKS VALIDATED SUCCESSFULLY!`);
      console.log(`✅ Migration testing infrastructure is ready for use`);
      console.log(`✅ All testing frameworks are functional and integrated`);
    } else if (successCount >= 3) {
      console.log(`\n⚠️  MOSTLY SUCCESSFUL - Minor Issues Detected`);
      console.log(`✅ Core testing infrastructure is functional`);
      console.log(`⚠️  Some frameworks may need attention`);
    } else {
      console.log(`\n🚨 CRITICAL ISSUES DETECTED`);
      console.log(`❌ Multiple testing frameworks failed validation`);
      console.log(`🔧 Significant fixes required before migration testing`);
    }

    // Print errors and warnings
    if (this.results.errors.length > 0) {
      console.log(`\n🚨 Errors (${this.results.errors.length}):`);
      this.results.errors.forEach(error => {
        console.log(`   - ${error}`);
      });
    }

    if (this.results.warnings.length > 0) {
      console.log(`\n⚠️  Warnings (${this.results.warnings.length}):`);
      this.results.warnings.forEach(warning => {
        console.log(`   - ${warning}`);
      });
    }

    // Next steps
    console.log(`\n📝 Next Steps:`);
    if (this.results.overallSuccess) {
      console.log(`   1. ✅ Begin migration testing with confidence`);
      console.log(`   2. ✅ Run parallel tests to validate system equivalence`);
      console.log(`   3. ✅ Execute contract tests to ensure API compatibility`);
      console.log(`   4. ✅ Perform load testing to validate performance`);
      console.log(`   5. ✅ Use integration suite for comprehensive validation`);
    } else {
      console.log(`   1. 🔧 Fix failing framework validations`);
      console.log(`   2. 🔧 Address errors and warnings`);
      console.log(`   3. 🔧 Re-run comprehensive validation`);
      console.log(`   4. ✅ Proceed with migration testing once all frameworks pass`);
    }

    // Available commands
    console.log(`\n🛠️  Available Test Commands:`);
    console.log(`   npm run test:parallel          - Run parallel system tests`);
    console.log(`   npm run test:contracts         - Run API contract tests`);
    console.log(`   npm run test:load:light        - Run light load test`);
    console.log(`   npm run test:integration       - Run complete integration suite`);
    console.log(`   npm run test:load:progressive  - Run progressive migration test`);

    console.log('='.repeat(70));
  }

  /**
   * Run quick validation tests
   */
  async runQuickValidation(): Promise<void> {
    console.log('⚡ Running Quick Framework Validation');
    console.log('-'.repeat(50));
    
    try {
      // Quick import tests
      console.log('Testing framework imports...');
      
      await import('./parallel-testing-framework');
      console.log('✅ Parallel testing framework imported');
      
      await import('./contract-testing-framework');
      console.log('✅ Contract testing framework imported');
      
      await import('./migration-load-testing');
      console.log('✅ Load testing framework imported');
      
      await import('./integration-test-suite');
      console.log('✅ Integration suite imported');
      
      console.log('\n✅ Quick validation passed - All frameworks can be imported');
      
    } catch (error) {
      console.error('❌ Quick validation failed:', error);
      throw error;
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'full';
  
  const runner = new ComprehensiveTestRunner();

  try {
    switch (command) {
      case 'full':
        await runner.runAllTests();
        break;
      case 'quick':
        await runner.runQuickValidation();
        break;
      default:
        console.log('Usage: npm run test:validate [command]');
        console.log('');
        console.log('Commands:');
        console.log('  full   Run complete framework validation (default)');
        console.log('  quick  Run quick import validation');
        console.log('');
        console.log('This validates all migration testing frameworks:');
        console.log('  - Parallel Testing Framework');
        console.log('  - Contract Testing Framework');
        console.log('  - Load Testing Framework');
        console.log('  - Integration Test Suite');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ Test validation failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { ComprehensiveTestRunner };
export type { TestSuiteResults };
