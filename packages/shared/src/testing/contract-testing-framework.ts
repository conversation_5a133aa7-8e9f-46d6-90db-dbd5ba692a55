/**
 * Contract Testing Framework
 * 
 * This framework validates API backward compatibility by ensuring that
 * all existing endpoints maintain exact same response formats and behavior
 * during migration from legacy to new system.
 */

import axios, { AxiosResponse, AxiosRequestConfig } from 'axios';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

export interface ContractEndpoint {
  name: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, any>;
  expectedStatus?: number;
  timeout?: number;
}

export interface ResponseSchema {
  type: 'object' | 'array' | 'string' | 'number' | 'boolean' | 'null';
  properties?: Record<string, ResponseSchema>;
  items?: ResponseSchema;
  required?: string[];
  format?: string;
  enum?: any[];
  minimum?: number;
  maximum?: number;
  minLength?: number;
  maxLength?: number;
}

export interface ContractTest {
  endpoint: ContractEndpoint;
  responseSchema: ResponseSchema;
  businessRules?: ContractRule[];
  description?: string;
}

export interface ContractRule {
  name: string;
  description: string;
  validator: (response: any) => boolean | string;
}

export interface ContractTestResult {
  testName: string;
  endpoint: string;
  success: boolean;
  statusCode?: number;
  responseTime: number;
  schemaValid: boolean;
  businessRulesValid: boolean;
  violations: string[];
  response?: any;
  timestamp: string;
}

export interface ContractTestSuite {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  schemaViolations: number;
  businessRuleViolations: number;
  averageResponseTime: number;
  results: ContractTestResult[];
  summary: string;
  timestamp: string;
}

export class ContractTestingFramework {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private outputDir: string;

  constructor(
    baseUrl: string,
    defaultHeaders: Record<string, string> = {},
    outputDir: string = './contract-test-results'
  ) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.defaultHeaders = defaultHeaders;
    this.outputDir = outputDir;

    // Ensure output directory exists
    if (!existsSync(this.outputDir)) {
      mkdirSync(this.outputDir, { recursive: true });
    }
  }

  /**
   * Run contract tests
   */
  async runContractTests(contracts: ContractTest[]): Promise<ContractTestSuite> {
    console.log('📋 Starting Contract Testing Suite');
    console.log(`🎯 Testing ${contracts.length} API contracts`);
    console.log(`🌐 Base URL: ${this.baseUrl}`);
    console.log('-'.repeat(60));

    const results: ContractTestResult[] = [];

    for (const contract of contracts) {
      console.log(`\n🔍 Testing: ${contract.endpoint.name}`);
      
      try {
        const result = await this.runSingleContractTest(contract);
        results.push(result);
        this.logContractResult(result);
      } catch (error) {
        console.error(`❌ Failed to test ${contract.endpoint.name}:`, error);
        
        // Create error result
        const errorResult: ContractTestResult = {
          testName: contract.endpoint.name,
          endpoint: contract.endpoint.path,
          success: false,
          responseTime: 0,
          schemaValid: false,
          businessRulesValid: false,
          violations: [`Test execution failed: ${error}`],
          timestamp: new Date().toISOString()
        };
        results.push(errorResult);
      }
    }

    // Generate test suite result
    const suiteResult = this.generateContractTestSuite(results);
    
    // Save results
    await this.saveResults(suiteResult);
    
    // Print summary
    this.printSummary(suiteResult);

    return suiteResult;
  }

  /**
   * Run a single contract test
   */
  private async runSingleContractTest(contract: ContractTest): Promise<ContractTestResult> {
    const startTime = performance.now();
    
    try {
      // Make API request
      const config: AxiosRequestConfig = {
        method: contract.endpoint.method,
        url: `${this.baseUrl}${contract.endpoint.path}`,
        headers: {
          ...this.defaultHeaders,
          ...contract.endpoint.headers
        },
        timeout: contract.endpoint.timeout || 30000,
        params: contract.endpoint.params,
        data: contract.endpoint.body,
        validateStatus: () => true // Accept all status codes
      };

      const response: AxiosResponse = await axios(config);
      const endTime = performance.now();

      // Validate response
      const violations: string[] = [];
      
      // Check expected status code
      const expectedStatus = contract.endpoint.expectedStatus || 200;
      if (response.status !== expectedStatus) {
        violations.push(`Expected status ${expectedStatus}, got ${response.status}`);
      }

      // Validate response schema
      const schemaValid = this.validateSchema(response.data, contract.responseSchema, violations);

      // Validate business rules
      const businessRulesValid = this.validateBusinessRules(
        response.data, 
        contract.businessRules || [], 
        violations
      );

      const success = violations.length === 0;

      return {
        testName: contract.endpoint.name,
        endpoint: contract.endpoint.path,
        success,
        statusCode: response.status,
        responseTime: Math.round(endTime - startTime),
        schemaValid,
        businessRulesValid,
        violations,
        response: response.data,
        timestamp: new Date().toISOString()
      };

    } catch (error: any) {
      const endTime = performance.now();
      
      return {
        testName: contract.endpoint.name,
        endpoint: contract.endpoint.path,
        success: false,
        responseTime: Math.round(endTime - startTime),
        schemaValid: false,
        businessRulesValid: false,
        violations: [`Request failed: ${error.message}`],
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Validate response against schema
   */
  private validateSchema(
    data: any, 
    schema: ResponseSchema, 
    violations: string[], 
    path: string = ''
  ): boolean {
    const currentPath = path || 'root';
    
    // Type validation
    const actualType = this.getDataType(data);
    if (actualType !== schema.type) {
      violations.push(`${currentPath}: Expected type '${schema.type}', got '${actualType}'`);
      return false;
    }

    let isValid = true;

    // Object validation
    if (schema.type === 'object' && schema.properties) {
      if (typeof data !== 'object' || data === null) {
        violations.push(`${currentPath}: Expected object, got ${actualType}`);
        return false;
      }

      // Check required properties
      if (schema.required) {
        for (const requiredProp of schema.required) {
          if (!(requiredProp in data)) {
            violations.push(`${currentPath}: Missing required property '${requiredProp}'`);
            isValid = false;
          }
        }
      }

      // Validate properties
      for (const [propName, propSchema] of Object.entries(schema.properties)) {
        if (propName in data) {
          const propPath = path ? `${path}.${propName}` : propName;
          if (!this.validateSchema(data[propName], propSchema, violations, propPath)) {
            isValid = false;
          }
        }
      }
    }

    // Array validation
    if (schema.type === 'array' && schema.items) {
      if (!Array.isArray(data)) {
        violations.push(`${currentPath}: Expected array, got ${actualType}`);
        return false;
      }

      for (let i = 0; i < data.length; i++) {
        const itemPath = `${currentPath}[${i}]`;
        if (!this.validateSchema(data[i], schema.items, violations, itemPath)) {
          isValid = false;
        }
      }
    }

    // String validation
    if (schema.type === 'string') {
      if (schema.minLength !== undefined && data.length < schema.minLength) {
        violations.push(`${currentPath}: String too short (${data.length} < ${schema.minLength})`);
        isValid = false;
      }
      if (schema.maxLength !== undefined && data.length > schema.maxLength) {
        violations.push(`${currentPath}: String too long (${data.length} > ${schema.maxLength})`);
        isValid = false;
      }
      if (schema.enum && !schema.enum.includes(data)) {
        violations.push(`${currentPath}: Value '${data}' not in allowed enum values`);
        isValid = false;
      }
    }

    // Number validation
    if (schema.type === 'number') {
      if (schema.minimum !== undefined && data < schema.minimum) {
        violations.push(`${currentPath}: Number too small (${data} < ${schema.minimum})`);
        isValid = false;
      }
      if (schema.maximum !== undefined && data > schema.maximum) {
        violations.push(`${currentPath}: Number too large (${data} > ${schema.maximum})`);
        isValid = false;
      }
    }

    return isValid;
  }

  /**
   * Validate business rules
   */
  private validateBusinessRules(
    data: any, 
    rules: ContractRule[], 
    violations: string[]
  ): boolean {
    let allValid = true;

    for (const rule of rules) {
      try {
        const result = rule.validator(data);
        
        if (result === false) {
          violations.push(`Business rule violation: ${rule.name}`);
          allValid = false;
        } else if (typeof result === 'string') {
          violations.push(`Business rule violation: ${rule.name} - ${result}`);
          allValid = false;
        }
      } catch (error) {
        violations.push(`Business rule error: ${rule.name} - ${error}`);
        allValid = false;
      }
    }

    return allValid;
  }

  /**
   * Get data type for validation
   */
  private getDataType(data: any): string {
    if (data === null) return 'null';
    if (Array.isArray(data)) return 'array';
    return typeof data;
  }

  /**
   * Generate contract test suite result
   */
  private generateContractTestSuite(results: ContractTestResult[]): ContractTestSuite {
    const totalTests = results.length;
    const passedTests = results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const schemaViolations = results.filter(r => !r.schemaValid).length;
    const businessRuleViolations = results.filter(r => !r.businessRulesValid).length;

    // Calculate average response time
    const responseTimes = results
      .filter(r => r.responseTime > 0)
      .map(r => r.responseTime);
    const averageResponseTime = responseTimes.length > 0 
      ? Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length)
      : 0;

    // Generate summary
    const passPercentage = (passedTests / totalTests) * 100;
    let summary = `Contract Testing: ${passedTests}/${totalTests} tests passed (${passPercentage.toFixed(1)}%)`;
    
    if (schemaViolations > 0) {
      summary += `, ${schemaViolations} schema violations`;
    }
    if (businessRuleViolations > 0) {
      summary += `, ${businessRuleViolations} business rule violations`;
    }

    return {
      totalTests,
      passedTests,
      failedTests,
      schemaViolations,
      businessRuleViolations,
      averageResponseTime,
      results,
      summary,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Log contract test result
   */
  private logContractResult(result: ContractTestResult): void {
    if (result.success) {
      console.log(`  ✅ ${result.testName}: PASSED (${result.responseTime}ms)`);
    } else {
      console.log(`  ❌ ${result.testName}: FAILED (${result.responseTime}ms)`);
      result.violations.forEach(violation => {
        console.log(`     - ${violation}`);
      });
    }
  }

  /**
   * Save test results to files
   */
  private async saveResults(suiteResult: ContractTestSuite): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Save detailed results as JSON
    const detailedResultsPath = join(this.outputDir, `contract-test-results-${timestamp}.json`);
    writeFileSync(detailedResultsPath, JSON.stringify(suiteResult, null, 2));
    
    // Save summary report as text
    const summaryPath = join(this.outputDir, `contract-test-summary-${timestamp}.txt`);
    const summaryReport = this.generateSummaryReport(suiteResult);
    writeFileSync(summaryPath, summaryReport);
    
    console.log(`\n📁 Contract test results saved:`);
    console.log(`   Detailed: ${detailedResultsPath}`);
    console.log(`   Summary: ${summaryPath}`);
  }

  /**
   * Generate human-readable summary report
   */
  private generateSummaryReport(suiteResult: ContractTestSuite): string {
    const { 
      totalTests, 
      passedTests, 
      failedTests,
      schemaViolations,
      businessRuleViolations,
      averageResponseTime,
      results 
    } = suiteResult;

    let report = `Contract Testing Report\n`;
    report += `Generated: ${suiteResult.timestamp}\n`;
    report += `${'='.repeat(50)}\n\n`;
    
    report += `SUMMARY\n`;
    report += `-------\n`;
    report += `Total Tests: ${totalTests}\n`;
    report += `Passed: ${passedTests}\n`;
    report += `Failed: ${failedTests}\n`;
    report += `Schema Violations: ${schemaViolations}\n`;
    report += `Business Rule Violations: ${businessRuleViolations}\n`;
    report += `Average Response Time: ${averageResponseTime}ms\n`;
    report += `Success Rate: ${(passedTests/totalTests*100).toFixed(1)}%\n\n`;
    
    report += `DETAILED RESULTS\n`;
    report += `----------------\n`;
    
    for (const result of results) {
      report += `\n${result.testName}:\n`;
      report += `  Endpoint: ${result.endpoint}\n`;
      report += `  Status: ${result.success ? 'PASSED' : 'FAILED'}\n`;
      report += `  Response Time: ${result.responseTime}ms\n`;
      report += `  Status Code: ${result.statusCode || 'N/A'}\n`;
      report += `  Schema Valid: ${result.schemaValid}\n`;
      report += `  Business Rules Valid: ${result.businessRulesValid}\n`;
      
      if (result.violations.length > 0) {
        report += `  Violations:\n`;
        result.violations.forEach(violation => {
          report += `    - ${violation}\n`;
        });
      }
    }
    
    return report;
  }

  /**
   * Print summary to console
   */
  private printSummary(suiteResult: ContractTestSuite): void {
    console.log('\n' + '='.repeat(60));
    console.log('📋 CONTRACT TESTING SUMMARY');
    console.log('='.repeat(60));
    console.log(`📊 Total Tests: ${suiteResult.totalTests}`);
    console.log(`✅ Passed: ${suiteResult.passedTests}`);
    console.log(`❌ Failed: ${suiteResult.failedTests}`);
    console.log(`🔍 Schema Violations: ${suiteResult.schemaViolations}`);
    console.log(`📋 Business Rule Violations: ${suiteResult.businessRuleViolations}`);
    console.log(`⏱️  Average Response Time: ${suiteResult.averageResponseTime}ms`);
    console.log(`📈 Success Rate: ${(suiteResult.passedTests/suiteResult.totalTests*100).toFixed(1)}%`);
    console.log(`\n${suiteResult.summary}`);
    console.log('='.repeat(60));
  }

  /**
   * Create contract test from OpenAPI specification
   */
  static createFromOpenAPI(spec: any, endpoint: string): ContractTest {
    // This would parse OpenAPI spec and create contract test
    // Implementation would depend on OpenAPI spec format
    throw new Error('OpenAPI contract generation not implemented yet');
  }

  /**
   * Validate contract compatibility between versions
   */
  async validateBackwardCompatibility(
    oldContracts: ContractTest[],
    newContracts: ContractTest[]
  ): Promise<{
    compatible: boolean;
    breakingChanges: string[];
    warnings: string[];
  }> {
    const breakingChanges: string[] = [];
    const warnings: string[] = [];

    // Check for removed endpoints
    const oldEndpoints = new Set(oldContracts.map(c => `${c.endpoint.method} ${c.endpoint.path}`));
    const newEndpoints = new Set(newContracts.map(c => `${c.endpoint.method} ${c.endpoint.path}`));

    for (const oldEndpoint of oldEndpoints) {
      if (!newEndpoints.has(oldEndpoint)) {
        breakingChanges.push(`Endpoint removed: ${oldEndpoint}`);
      }
    }

    // Check for schema changes
    for (const oldContract of oldContracts) {
      const newContract = newContracts.find(c =>
        c.endpoint.method === oldContract.endpoint.method &&
        c.endpoint.path === oldContract.endpoint.path
      );

      if (newContract) {
        const schemaChanges = this.compareSchemas(oldContract.responseSchema, newContract.responseSchema);
        breakingChanges.push(...schemaChanges.breaking);
        warnings.push(...schemaChanges.warnings);
      }
    }

    return {
      compatible: breakingChanges.length === 0,
      breakingChanges,
      warnings
    };
  }

  /**
   * Compare two schemas for compatibility
   */
  private compareSchemas(oldSchema: ResponseSchema, newSchema: ResponseSchema): {
    breaking: string[];
    warnings: string[];
  } {
    const breaking: string[] = [];
    const warnings: string[] = [];

    // Type changes are breaking
    if (oldSchema.type !== newSchema.type) {
      breaking.push(`Type changed from ${oldSchema.type} to ${newSchema.type}`);
    }

    // Required property changes
    if (oldSchema.required && newSchema.required) {
      for (const oldRequired of oldSchema.required) {
        if (!newSchema.required.includes(oldRequired)) {
          breaking.push(`Required property removed: ${oldRequired}`);
        }
      }

      for (const newRequired of newSchema.required) {
        if (!oldSchema.required.includes(newRequired)) {
          warnings.push(`New required property added: ${newRequired}`);
        }
      }
    }

    // Property changes for objects
    if (oldSchema.properties && newSchema.properties) {
      for (const [propName, oldPropSchema] of Object.entries(oldSchema.properties)) {
        if (!newSchema.properties[propName]) {
          breaking.push(`Property removed: ${propName}`);
        } else {
          const propChanges = this.compareSchemas(oldPropSchema, newSchema.properties[propName]);
          breaking.push(...propChanges.breaking.map(c => `${propName}.${c}`));
          warnings.push(...propChanges.warnings.map(c => `${propName}.${c}`));
        }
      }
    }

    return { breaking, warnings };
  }
}

// Knowledge Graph Visualizer API Contract Definitions
export const knowledgeGraphContracts: ContractTest[] = [
  // Health endpoint
  {
    endpoint: {
      name: 'Health Check',
      path: '/health',
      method: 'GET',
      expectedStatus: 200
    },
    responseSchema: {
      type: 'object',
      required: ['status', 'timestamp'],
      properties: {
        status: { type: 'string', enum: ['healthy', 'unhealthy'] },
        timestamp: { type: 'string' },
        uptime: { type: 'number' },
        version: { type: 'string' }
      }
    },
    businessRules: [
      {
        name: 'Status must be healthy',
        description: 'Health endpoint should return healthy status',
        validator: (response) => response.status === 'healthy'
      },
      {
        name: 'Timestamp format',
        description: 'Timestamp should be valid ISO string',
        validator: (response) => {
          try {
            new Date(response.timestamp);
            return true;
          } catch {
            return 'Invalid timestamp format';
          }
        }
      }
    ],
    description: 'Validates system health endpoint'
  },

  // Graph data endpoint
  {
    endpoint: {
      name: 'Graph Data',
      path: '/api/graph',
      method: 'GET',
      params: { group_id: 'user_guides' },
      expectedStatus: 200
    },
    responseSchema: {
      type: 'object',
      required: ['nodes', 'relationships'],
      properties: {
        nodes: {
          type: 'array',
          items: {
            type: 'object',
            required: ['id', 'labels', 'properties'],
            properties: {
              id: { type: 'number' },
              labels: { type: 'array', items: { type: 'string' } },
              properties: {
                type: 'object',
                required: ['group_id'],
                properties: {
                  group_id: { type: 'string' },
                  name: { type: 'string' },
                  summary: { type: 'string' },
                  entity_type: { type: 'string' },
                  created_at: { type: 'string' }
                }
              }
            }
          }
        },
        relationships: {
          type: 'array',
          items: {
            type: 'object',
            required: ['id', 'type', 'startNode', 'endNode', 'properties'],
            properties: {
              id: { type: 'number' },
              type: { type: 'string' },
              startNode: { type: 'number' },
              endNode: { type: 'number' },
              properties: { type: 'object' }
            }
          }
        }
      }
    },
    businessRules: [
      {
        name: 'All nodes have group_id',
        description: 'Every node must have the requested group_id',
        validator: (response) => {
          return response.nodes.every((node: any) =>
            node.properties.group_id === 'user_guides'
          );
        }
      },
      {
        name: 'Valid relationship references',
        description: 'All relationships must reference existing nodes',
        validator: (response) => {
          const nodeIds = new Set(response.nodes.map((n: any) => n.id));
          return response.relationships.every((rel: any) =>
            nodeIds.has(rel.startNode) && nodeIds.has(rel.endNode)
          );
        }
      }
    ],
    description: 'Validates graph data retrieval with nodes and relationships'
  },

  // Analysis endpoint
  {
    endpoint: {
      name: 'Graph Analysis',
      path: '/api/analysis',
      method: 'POST',
      body: {
        group_id: 'user_guides',
        analysis_type: 'basic',
        include_metrics: true
      },
      expectedStatus: 200
    },
    responseSchema: {
      type: 'object',
      required: ['analysis_type', 'metrics', 'timestamp'],
      properties: {
        analysis_type: { type: 'string' },
        metrics: {
          type: 'object',
          required: ['node_count', 'relationship_count'],
          properties: {
            node_count: { type: 'number', minimum: 0 },
            relationship_count: { type: 'number', minimum: 0 },
            density: { type: 'number', minimum: 0, maximum: 1 },
            avg_degree: { type: 'number', minimum: 0 }
          }
        },
        timestamp: { type: 'string' },
        execution_time_ms: { type: 'number', minimum: 0 }
      }
    },
    businessRules: [
      {
        name: 'Analysis type matches request',
        description: 'Response analysis_type should match request',
        validator: (response) => response.analysis_type === 'basic'
      },
      {
        name: 'Reasonable execution time',
        description: 'Analysis should complete within reasonable time',
        validator: (response) => response.execution_time_ms < 30000 // 30 seconds
      }
    ],
    description: 'Validates graph analysis functionality'
  },

  // Search endpoint
  {
    endpoint: {
      name: 'Graph Search',
      path: '/api/search',
      method: 'POST',
      body: {
        query: 'knowledge graph',
        group_id: 'user_guides',
        limit: 10
      },
      expectedStatus: 200
    },
    responseSchema: {
      type: 'object',
      required: ['results', 'total_count', 'query'],
      properties: {
        results: {
          type: 'array',
          items: {
            type: 'object',
            required: ['node', 'score'],
            properties: {
              node: {
                type: 'object',
                required: ['id', 'labels', 'properties'],
                properties: {
                  id: { type: 'number' },
                  labels: { type: 'array', items: { type: 'string' } },
                  properties: { type: 'object' }
                }
              },
              score: { type: 'number', minimum: 0, maximum: 1 },
              relevance: { type: 'string' }
            }
          }
        },
        total_count: { type: 'number', minimum: 0 },
        query: { type: 'string' },
        execution_time_ms: { type: 'number', minimum: 0 }
      }
    },
    businessRules: [
      {
        name: 'Results within limit',
        description: 'Number of results should not exceed requested limit',
        validator: (response) => response.results.length <= 10
      },
      {
        name: 'Query matches request',
        description: 'Response query should match request query',
        validator: (response) => response.query === 'knowledge graph'
      },
      {
        name: 'Valid scores',
        description: 'All search scores should be between 0 and 1',
        validator: (response) => {
          return response.results.every((result: any) =>
            result.score >= 0 && result.score <= 1
          );
        }
      }
    ],
    description: 'Validates graph search functionality'
  },

  // Chat endpoint
  {
    endpoint: {
      name: 'Chat Message',
      path: '/api/chat/message',
      method: 'POST',
      headers: { 'X-Session-ID': 'test-session' },
      body: {
        message: 'What is a knowledge graph?'
      },
      expectedStatus: 200
    },
    responseSchema: {
      type: 'object',
      required: ['message', 'conversation_id', 'timestamp'],
      properties: {
        message: {
          type: 'object',
          required: ['role', 'content'],
          properties: {
            role: { type: 'string', enum: ['assistant', 'user'] },
            content: { type: 'string', minLength: 1 },
            timestamp: { type: 'string' }
          }
        },
        conversation_id: { type: 'string' },
        timestamp: { type: 'string' },
        metadata: {
          type: 'object',
          properties: {
            model: { type: 'string' },
            tokens_used: { type: 'number' },
            response_time_ms: { type: 'number' }
          }
        }
      }
    },
    businessRules: [
      {
        name: 'Assistant response',
        description: 'Chat should return assistant role message',
        validator: (response) => response.message.role === 'assistant'
      },
      {
        name: 'Non-empty content',
        description: 'Response content should not be empty',
        validator: (response) => response.message.content.trim().length > 0
      },
      {
        name: 'Valid conversation ID',
        description: 'Conversation ID should be present and valid',
        validator: (response) => typeof response.conversation_id === 'string' && response.conversation_id.length > 0
      }
    ],
    description: 'Validates chat message functionality'
  }
];

// Contract test configuration factory
export function createContractTestConfig(baseUrl: string): {
  framework: ContractTestingFramework;
  contracts: ContractTest[];
} {
  const framework = new ContractTestingFramework(
    baseUrl,
    {
      'Content-Type': 'application/json',
      'User-Agent': 'Contract-Test-Suite/1.0.0'
    },
    './contract-test-results'
  );

  return {
    framework,
    contracts: knowledgeGraphContracts
  };
}
