/**
 * Migration Load Testing Framework
 * 
 * This framework provides comprehensive load testing specifically designed
 * for migration scenarios, including traffic splitting, gradual rollout
 * simulation, and performance validation under migration conditions.
 */

import axios, { AxiosResponse, AxiosRequestConfig } from 'axios';
import { performance } from 'perf_hooks';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

export interface LoadTestEndpoint {
  name: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, any>;
  weight: number; // Relative frequency of this endpoint
}

export interface LoadTestConfig {
  name: string;
  legacyBaseUrl: string;
  newBaseUrl: string;
  endpoints: LoadTestEndpoint[];
  duration: number; // Test duration in seconds
  concurrentUsers: number;
  rampUpTime: number; // Time to reach full load in seconds
  trafficSplit: number; // Percentage of traffic to new system (0-100)
  thresholds: {
    maxResponseTime: number; // Maximum acceptable response time in ms
    maxErrorRate: number; // Maximum acceptable error rate (0-1)
    minThroughput: number; // Minimum requests per second
  };
}

export interface LoadTestResult {
  endpoint: string;
  system: 'legacy' | 'new';
  timestamp: number;
  responseTime: number;
  statusCode: number;
  success: boolean;
  error?: string;
  userId: number;
}

export interface LoadTestMetrics {
  system: 'legacy' | 'new';
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  p50ResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  requestsPerSecond: number;
  errorRate: number;
  throughput: number;
}

export interface LoadTestSummary {
  testName: string;
  duration: number;
  concurrentUsers: number;
  trafficSplit: number;
  legacyMetrics: LoadTestMetrics;
  newMetrics: LoadTestMetrics;
  combinedMetrics: LoadTestMetrics;
  thresholdViolations: string[];
  migrationRecommendation: string;
  timestamp: string;
}

export class MigrationLoadTester {
  private config: LoadTestConfig;
  private results: LoadTestResult[] = [];
  private outputDir: string;
  private isRunning = false;

  constructor(config: LoadTestConfig, outputDir: string = './load-test-results') {
    this.config = config;
    this.outputDir = outputDir;

    // Ensure output directory exists
    if (!existsSync(this.outputDir)) {
      mkdirSync(this.outputDir, { recursive: true });
    }
  }

  /**
   * Run migration load test
   */
  async runMigrationLoadTest(): Promise<LoadTestSummary> {
    console.log('🔥 Starting Migration Load Test');
    console.log(`📊 Test: ${this.config.name}`);
    console.log(`👥 Concurrent Users: ${this.config.concurrentUsers}`);
    console.log(`⏱️  Duration: ${this.config.duration}s`);
    console.log(`🔀 Traffic Split: ${this.config.trafficSplit}% to new system`);
    console.log(`🏛️  Legacy: ${this.config.legacyBaseUrl}`);
    console.log(`🆕 New: ${this.config.newBaseUrl}`);
    console.log('-'.repeat(60));

    this.results = [];
    this.isRunning = true;

    const startTime = Date.now();
    const endTime = startTime + (this.config.duration * 1000);

    // Start concurrent users
    const userPromises: Promise<void>[] = [];
    
    for (let userId = 0; userId < this.config.concurrentUsers; userId++) {
      const userPromise = this.simulateUser(userId, startTime, endTime);
      userPromises.push(userPromise);
      
      // Ramp up gradually
      if (this.config.rampUpTime > 0) {
        const rampDelay = (this.config.rampUpTime * 1000) / this.config.concurrentUsers;
        await this.sleep(rampDelay);
      }
    }

    // Wait for all users to complete
    await Promise.all(userPromises);

    this.isRunning = false;

    // Generate summary
    const summary = this.generateLoadTestSummary();
    
    // Save results
    await this.saveResults(summary);
    
    // Print summary
    this.printSummary(summary);

    return summary;
  }

  /**
   * Simulate a single user's load testing behavior
   */
  private async simulateUser(userId: number, startTime: number, endTime: number): Promise<void> {
    while (Date.now() < endTime && this.isRunning) {
      try {
        // Select random endpoint based on weights
        const endpoint = this.selectRandomEndpoint();
        
        // Determine which system to use based on traffic split
        const useNewSystem = Math.random() * 100 < this.config.trafficSplit;
        const baseUrl = useNewSystem ? this.config.newBaseUrl : this.config.legacyBaseUrl;
        const system = useNewSystem ? 'new' : 'legacy';

        // Make request
        const result = await this.makeRequest(endpoint, baseUrl, system, userId);
        this.results.push(result);

        // Random delay between requests (simulate user think time)
        await this.sleep(Math.random() * 2000 + 500); // 0.5-2.5 seconds

      } catch (error) {
        // Continue on error
        console.error(`User ${userId} error:`, error);
      }
    }
  }

  /**
   * Select random endpoint based on weights
   */
  private selectRandomEndpoint(): LoadTestEndpoint {
    const totalWeight = this.config.endpoints.reduce((sum, ep) => sum + ep.weight, 0);
    let random = Math.random() * totalWeight;
    
    for (const endpoint of this.config.endpoints) {
      random -= endpoint.weight;
      if (random <= 0) {
        return endpoint;
      }
    }
    
    return this.config.endpoints[0]; // Fallback
  }

  /**
   * Make HTTP request to endpoint
   */
  private async makeRequest(
    endpoint: LoadTestEndpoint,
    baseUrl: string,
    system: 'legacy' | 'new',
    userId: number
  ): Promise<LoadTestResult> {
    const startTime = performance.now();
    
    try {
      const config: AxiosRequestConfig = {
        method: endpoint.method,
        url: `${baseUrl}${endpoint.path}`,
        headers: {
          'User-Agent': `LoadTest-User-${userId}`,
          'X-Load-Test': 'true',
          ...endpoint.headers
        },
        timeout: 30000,
        params: endpoint.params,
        data: endpoint.body,
        validateStatus: () => true // Accept all status codes
      };

      const response: AxiosResponse = await axios(config);
      const endTime = performance.now();

      return {
        endpoint: endpoint.name,
        system,
        timestamp: Date.now(),
        responseTime: Math.round(endTime - startTime),
        statusCode: response.status,
        success: response.status >= 200 && response.status < 400,
        userId
      };

    } catch (error: any) {
      const endTime = performance.now();
      
      return {
        endpoint: endpoint.name,
        system,
        timestamp: Date.now(),
        responseTime: Math.round(endTime - startTime),
        statusCode: 0,
        success: false,
        error: error.message,
        userId
      };
    }
  }

  /**
   * Generate load test summary
   */
  private generateLoadTestSummary(): LoadTestSummary {
    const legacyResults = this.results.filter(r => r.system === 'legacy');
    const newResults = this.results.filter(r => r.system === 'new');
    
    const legacyMetrics = this.calculateMetrics(legacyResults, 'legacy');
    const newMetrics = this.calculateMetrics(newResults, 'new');
    const combinedMetrics = this.calculateMetrics(this.results, 'combined' as any);

    // Check threshold violations
    const thresholdViolations = this.checkThresholds(combinedMetrics);
    
    // Generate migration recommendation
    const migrationRecommendation = this.generateMigrationRecommendation(
      legacyMetrics,
      newMetrics,
      thresholdViolations
    );

    return {
      testName: this.config.name,
      duration: this.config.duration,
      concurrentUsers: this.config.concurrentUsers,
      trafficSplit: this.config.trafficSplit,
      legacyMetrics,
      newMetrics,
      combinedMetrics,
      thresholdViolations,
      migrationRecommendation,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Calculate metrics for a set of results
   */
  private calculateMetrics(results: LoadTestResult[], system: string): LoadTestMetrics {
    if (results.length === 0) {
      return {
        system: system as any,
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        minResponseTime: 0,
        maxResponseTime: 0,
        p50ResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        requestsPerSecond: 0,
        errorRate: 0,
        throughput: 0
      };
    }

    const totalRequests = results.length;
    const successfulRequests = results.filter(r => r.success).length;
    const failedRequests = totalRequests - successfulRequests;
    
    const responseTimes = results.map(r => r.responseTime).sort((a, b) => a - b);
    const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const minResponseTime = responseTimes[0];
    const maxResponseTime = responseTimes[responseTimes.length - 1];
    
    // Calculate percentiles
    const p50ResponseTime = responseTimes[Math.floor(responseTimes.length * 0.5)];
    const p95ResponseTime = responseTimes[Math.floor(responseTimes.length * 0.95)];
    const p99ResponseTime = responseTimes[Math.floor(responseTimes.length * 0.99)];
    
    // Calculate throughput
    const timeSpan = (Math.max(...results.map(r => r.timestamp)) - Math.min(...results.map(r => r.timestamp))) / 1000;
    const requestsPerSecond = totalRequests / Math.max(timeSpan, 1);
    const errorRate = failedRequests / totalRequests;
    const throughput = successfulRequests / Math.max(timeSpan, 1);

    return {
      system: system as any,
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime: Math.round(averageResponseTime),
      minResponseTime,
      maxResponseTime,
      p50ResponseTime,
      p95ResponseTime,
      p99ResponseTime,
      requestsPerSecond: Math.round(requestsPerSecond * 100) / 100,
      errorRate: Math.round(errorRate * 10000) / 100, // Percentage
      throughput: Math.round(throughput * 100) / 100
    };
  }

  /**
   * Check performance thresholds
   */
  private checkThresholds(metrics: LoadTestMetrics): string[] {
    const violations: string[] = [];
    
    if (metrics.p95ResponseTime > this.config.thresholds.maxResponseTime) {
      violations.push(
        `P95 response time (${metrics.p95ResponseTime}ms) exceeds threshold (${this.config.thresholds.maxResponseTime}ms)`
      );
    }
    
    if (metrics.errorRate > this.config.thresholds.maxErrorRate * 100) {
      violations.push(
        `Error rate (${metrics.errorRate}%) exceeds threshold (${this.config.thresholds.maxErrorRate * 100}%)`
      );
    }
    
    if (metrics.throughput < this.config.thresholds.minThroughput) {
      violations.push(
        `Throughput (${metrics.throughput} req/s) below threshold (${this.config.thresholds.minThroughput} req/s)`
      );
    }

    return violations;
  }

  /**
   * Generate migration recommendation
   */
  private generateMigrationRecommendation(
    legacyMetrics: LoadTestMetrics,
    newMetrics: LoadTestMetrics,
    violations: string[]
  ): string {
    if (violations.length > 0) {
      return 'STOP MIGRATION: Performance thresholds violated. Address issues before proceeding.';
    }
    
    if (newMetrics.totalRequests === 0) {
      return 'INCREASE TRAFFIC: No traffic to new system. Gradually increase traffic split.';
    }
    
    // Compare performance
    const responseTimeImprovement = legacyMetrics.averageResponseTime > 0 
      ? ((legacyMetrics.averageResponseTime - newMetrics.averageResponseTime) / legacyMetrics.averageResponseTime) * 100
      : 0;
    
    const errorRateComparison = newMetrics.errorRate - legacyMetrics.errorRate;
    
    if (responseTimeImprovement > 10 && errorRateComparison <= 0) {
      return 'ACCELERATE MIGRATION: New system shows significant performance improvement.';
    } else if (responseTimeImprovement < -20 || errorRateComparison > 5) {
      return 'SLOW MIGRATION: New system shows performance degradation. Monitor closely.';
    } else {
      return 'CONTINUE MIGRATION: Performance is acceptable. Proceed with gradual rollout.';
    }
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Save test results
   */
  private async saveResults(summary: LoadTestSummary): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Save detailed results
    const detailedPath = join(this.outputDir, `load-test-results-${timestamp}.json`);
    const detailedData = {
      summary,
      rawResults: this.results
    };
    writeFileSync(detailedPath, JSON.stringify(detailedData, null, 2));
    
    // Save summary report
    const summaryPath = join(this.outputDir, `load-test-summary-${timestamp}.txt`);
    const summaryReport = this.generateSummaryReport(summary);
    writeFileSync(summaryPath, summaryReport);
    
    console.log(`\n📁 Load test results saved:`);
    console.log(`   Detailed: ${detailedPath}`);
    console.log(`   Summary: ${summaryPath}`);
  }

  /**
   * Generate summary report
   */
  private generateSummaryReport(summary: LoadTestSummary): string {
    let report = `Migration Load Test Report\n`;
    report += `Test: ${summary.testName}\n`;
    report += `Generated: ${summary.timestamp}\n`;
    report += `${'='.repeat(60)}\n\n`;
    
    report += `TEST CONFIGURATION\n`;
    report += `------------------\n`;
    report += `Duration: ${summary.duration}s\n`;
    report += `Concurrent Users: ${summary.concurrentUsers}\n`;
    report += `Traffic Split: ${summary.trafficSplit}% to new system\n\n`;
    
    report += `PERFORMANCE METRICS\n`;
    report += `-------------------\n`;
    report += `Legacy System:\n`;
    report += `  Total Requests: ${summary.legacyMetrics.totalRequests}\n`;
    report += `  Success Rate: ${((summary.legacyMetrics.successfulRequests / summary.legacyMetrics.totalRequests) * 100).toFixed(1)}%\n`;
    report += `  Avg Response Time: ${summary.legacyMetrics.averageResponseTime}ms\n`;
    report += `  P95 Response Time: ${summary.legacyMetrics.p95ResponseTime}ms\n`;
    report += `  Throughput: ${summary.legacyMetrics.throughput} req/s\n\n`;
    
    report += `New System:\n`;
    report += `  Total Requests: ${summary.newMetrics.totalRequests}\n`;
    report += `  Success Rate: ${summary.newMetrics.totalRequests > 0 ? ((summary.newMetrics.successfulRequests / summary.newMetrics.totalRequests) * 100).toFixed(1) : 'N/A'}%\n`;
    report += `  Avg Response Time: ${summary.newMetrics.averageResponseTime}ms\n`;
    report += `  P95 Response Time: ${summary.newMetrics.p95ResponseTime}ms\n`;
    report += `  Throughput: ${summary.newMetrics.throughput} req/s\n\n`;
    
    if (summary.thresholdViolations.length > 0) {
      report += `THRESHOLD VIOLATIONS\n`;
      report += `--------------------\n`;
      summary.thresholdViolations.forEach(violation => {
        report += `- ${violation}\n`;
      });
      report += `\n`;
    }
    
    report += `MIGRATION RECOMMENDATION\n`;
    report += `------------------------\n`;
    report += `${summary.migrationRecommendation}\n`;
    
    return report;
  }

  /**
   * Print summary to console
   */
  private printSummary(summary: LoadTestSummary): void {
    console.log('\n' + '='.repeat(60));
    console.log('📋 MIGRATION LOAD TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`🎯 Test: ${summary.testName}`);
    console.log(`👥 Users: ${summary.concurrentUsers} | ⏱️  Duration: ${summary.duration}s`);
    console.log(`🔀 Traffic Split: ${summary.trafficSplit}% to new system`);
    
    console.log(`\n🏛️  Legacy System:`);
    console.log(`   Requests: ${summary.legacyMetrics.totalRequests}`);
    console.log(`   Success: ${((summary.legacyMetrics.successfulRequests / summary.legacyMetrics.totalRequests) * 100).toFixed(1)}%`);
    console.log(`   Avg Time: ${summary.legacyMetrics.averageResponseTime}ms`);
    console.log(`   P95 Time: ${summary.legacyMetrics.p95ResponseTime}ms`);
    console.log(`   Throughput: ${summary.legacyMetrics.throughput} req/s`);
    
    console.log(`\n🆕 New System:`);
    console.log(`   Requests: ${summary.newMetrics.totalRequests}`);
    if (summary.newMetrics.totalRequests > 0) {
      console.log(`   Success: ${((summary.newMetrics.successfulRequests / summary.newMetrics.totalRequests) * 100).toFixed(1)}%`);
      console.log(`   Avg Time: ${summary.newMetrics.averageResponseTime}ms`);
      console.log(`   P95 Time: ${summary.newMetrics.p95ResponseTime}ms`);
      console.log(`   Throughput: ${summary.newMetrics.throughput} req/s`);
    } else {
      console.log(`   No traffic routed to new system`);
    }
    
    if (summary.thresholdViolations.length > 0) {
      console.log(`\n⚠️  Threshold Violations:`);
      summary.thresholdViolations.forEach(violation => {
        console.log(`   - ${violation}`);
      });
    }
    
    console.log(`\n📝 Recommendation: ${summary.migrationRecommendation}`);
    console.log('='.repeat(60));
  }

  /**
   * Stop running load test
   */
  stopTest(): void {
    this.isRunning = false;
  }
}

// Pre-defined load test scenarios for Knowledge Graph Visualizer
export const migrationLoadTestScenarios = {
  // Light load scenario for initial testing
  lightLoad: {
    name: 'Light Load Test',
    legacyBaseUrl: process.env.LEGACY_BASE_URL || 'http://localhost:3000',
    newBaseUrl: process.env.NEW_BASE_URL || 'http://localhost:3003',
    endpoints: [
      {
        name: 'Health Check',
        path: '/health',
        method: 'GET' as const,
        weight: 10
      },
      {
        name: 'Graph Data',
        path: '/api/graph',
        method: 'GET' as const,
        params: { group_id: 'user_guides' },
        weight: 30
      },
      {
        name: 'Chat Message',
        path: '/api/chat/message',
        method: 'POST' as const,
        headers: { 'X-Session-ID': 'load-test-session' },
        body: { message: 'What is a knowledge graph?' },
        weight: 20
      },
      {
        name: 'Graph Search',
        path: '/api/search',
        method: 'POST' as const,
        body: { query: 'visualization', group_id: 'user_guides', limit: 10 },
        weight: 15
      },
      {
        name: 'Graph Analysis',
        path: '/api/analysis',
        method: 'POST' as const,
        body: { group_id: 'user_guides', analysis_type: 'basic' },
        weight: 10
      }
    ],
    duration: 60, // 1 minute
    concurrentUsers: 5,
    rampUpTime: 10,
    trafficSplit: 20, // 20% to new system
    thresholds: {
      maxResponseTime: 5000, // 5 seconds
      maxErrorRate: 0.05, // 5%
      minThroughput: 1 // 1 req/s minimum
    }
  } as LoadTestConfig,

  // Medium load scenario for migration validation
  mediumLoad: {
    name: 'Medium Load Test',
    legacyBaseUrl: process.env.LEGACY_BASE_URL || 'http://localhost:3000',
    newBaseUrl: process.env.NEW_BASE_URL || 'http://localhost:3003',
    endpoints: [
      {
        name: 'Health Check',
        path: '/health',
        method: 'GET' as const,
        weight: 5
      },
      {
        name: 'Graph Data',
        path: '/api/graph',
        method: 'GET' as const,
        params: { group_id: 'user_guides' },
        weight: 25
      },
      {
        name: 'Graph Data with Filters',
        path: '/api/graph',
        method: 'GET' as const,
        params: { group_id: 'user_guides', limit: 100, include_relationships: true },
        weight: 20
      },
      {
        name: 'Chat Message',
        path: '/api/chat/message',
        method: 'POST' as const,
        headers: { 'X-Session-ID': 'load-test-session' },
        body: { message: 'Explain graph databases and their benefits' },
        weight: 20
      },
      {
        name: 'Chat History',
        path: '/api/chat/history',
        method: 'GET' as const,
        headers: { 'X-Session-ID': 'load-test-session' },
        weight: 10
      },
      {
        name: 'Graph Search',
        path: '/api/search',
        method: 'POST' as const,
        body: { query: 'data visualization analysis', group_id: 'user_guides', limit: 20 },
        weight: 15
      },
      {
        name: 'Graph Analysis',
        path: '/api/analysis',
        method: 'POST' as const,
        body: { group_id: 'user_guides', analysis_type: 'advanced', include_metrics: true },
        weight: 5
      }
    ],
    duration: 300, // 5 minutes
    concurrentUsers: 15,
    rampUpTime: 30,
    trafficSplit: 50, // 50% to new system
    thresholds: {
      maxResponseTime: 3000, // 3 seconds
      maxErrorRate: 0.03, // 3%
      minThroughput: 5 // 5 req/s minimum
    }
  } as LoadTestConfig,

  // Heavy load scenario for stress testing
  heavyLoad: {
    name: 'Heavy Load Test',
    legacyBaseUrl: process.env.LEGACY_BASE_URL || 'http://localhost:3000',
    newBaseUrl: process.env.NEW_BASE_URL || 'http://localhost:3003',
    endpoints: [
      {
        name: 'Health Check',
        path: '/health',
        method: 'GET' as const,
        weight: 3
      },
      {
        name: 'Graph Data',
        path: '/api/graph',
        method: 'GET' as const,
        params: { group_id: 'user_guides' },
        weight: 30
      },
      {
        name: 'Graph Data Large',
        path: '/api/graph',
        method: 'GET' as const,
        params: { group_id: 'user_guides', limit: 500 },
        weight: 15
      },
      {
        name: 'Chat Message Simple',
        path: '/api/chat/message',
        method: 'POST' as const,
        headers: { 'X-Session-ID': 'load-test-session-1' },
        body: { message: 'What is Neo4j?' },
        weight: 20
      },
      {
        name: 'Chat Message Complex',
        path: '/api/chat/message',
        method: 'POST' as const,
        headers: { 'X-Session-ID': 'load-test-session-2' },
        body: {
          message: 'Provide a detailed analysis of graph database performance characteristics and optimization strategies',
          context: { group_id: 'user_guides', include_graph_context: true }
        },
        weight: 10
      },
      {
        name: 'Graph Search Simple',
        path: '/api/search',
        method: 'POST' as const,
        body: { query: 'graph', group_id: 'user_guides', limit: 10 },
        weight: 12
      },
      {
        name: 'Graph Search Complex',
        path: '/api/search',
        method: 'POST' as const,
        body: {
          query: 'knowledge graph visualization analysis performance optimization',
          group_id: 'user_guides',
          limit: 50,
          include_context: true,
          search_type: 'semantic'
        },
        weight: 8
      },
      {
        name: 'Graph Analysis',
        path: '/api/analysis',
        method: 'POST' as const,
        body: {
          group_id: 'user_guides',
          analysis_type: 'advanced',
          algorithms: ['pagerank', 'centrality'],
          include_metrics: true
        },
        weight: 2
      }
    ],
    duration: 600, // 10 minutes
    concurrentUsers: 30,
    rampUpTime: 60,
    trafficSplit: 80, // 80% to new system
    thresholds: {
      maxResponseTime: 2000, // 2 seconds
      maxErrorRate: 0.02, // 2%
      minThroughput: 10 // 10 req/s minimum
    }
  } as LoadTestConfig,

  // Migration simulation scenario
  migrationSimulation: {
    name: 'Migration Simulation Test',
    legacyBaseUrl: process.env.LEGACY_BASE_URL || 'http://localhost:3000',
    newBaseUrl: process.env.NEW_BASE_URL || 'http://localhost:3003',
    endpoints: [
      {
        name: 'Health Check',
        path: '/health',
        method: 'GET' as const,
        weight: 5
      },
      {
        name: 'Graph Data',
        path: '/api/graph',
        method: 'GET' as const,
        params: { group_id: 'user_guides' },
        weight: 35
      },
      {
        name: 'Chat Message',
        path: '/api/chat/message',
        method: 'POST' as const,
        headers: { 'X-Session-ID': 'migration-test-session' },
        body: { message: 'How does graph migration work?' },
        weight: 30
      },
      {
        name: 'Graph Search',
        path: '/api/search',
        method: 'POST' as const,
        body: { query: 'migration testing', group_id: 'user_guides', limit: 15 },
        weight: 20
      },
      {
        name: 'Graph Analysis',
        path: '/api/analysis',
        method: 'POST' as const,
        body: { group_id: 'user_guides', analysis_type: 'basic', include_metrics: true },
        weight: 10
      }
    ],
    duration: 180, // 3 minutes
    concurrentUsers: 10,
    rampUpTime: 20,
    trafficSplit: 30, // 30% to new system (gradual migration)
    thresholds: {
      maxResponseTime: 4000, // 4 seconds
      maxErrorRate: 0.05, // 5%
      minThroughput: 3 // 3 req/s minimum
    }
  } as LoadTestConfig
};
