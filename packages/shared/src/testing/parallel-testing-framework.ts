/**
 * Parallel System Testing Framework
 * 
 * This framework runs identical tests on both legacy and new systems
 * to validate migration accuracy by comparing API responses, database
 * results, and performance metrics.
 */

import axios, { AxiosResponse, AxiosRequestConfig } from 'axios';
import { performance } from 'perf_hooks';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

export interface TestEndpoint {
  name: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, any>;
  timeout?: number;
}

export interface SystemConfig {
  name: string;
  baseUrl: string;
  headers?: Record<string, string>;
  timeout?: number;
}

export interface TestResult {
  endpoint: string;
  system: string;
  success: boolean;
  statusCode?: number;
  responseTime: number;
  responseSize: number;
  data?: any;
  error?: string;
  timestamp: string;
}

export interface ComparisonResult {
  endpoint: string;
  identical: boolean;
  statusCodeMatch: boolean;
  dataMatch: boolean;
  performanceDelta: number;
  sizeDelta: number;
  differences: string[];
  legacyResult: TestResult;
  newResult: TestResult;
}

export interface TestSuiteResult {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  identicalResponses: number;
  performanceImprovement: number;
  averageResponseTime: {
    legacy: number;
    new: number;
  };
  comparisons: ComparisonResult[];
  summary: string;
  timestamp: string;
}

export class ParallelTestingFramework {
  private legacySystem: SystemConfig;
  private newSystem: SystemConfig;
  private testResults: TestResult[] = [];
  private outputDir: string;

  constructor(
    legacySystem: SystemConfig,
    newSystem: SystemConfig,
    outputDir: string = './test-results'
  ) {
    this.legacySystem = legacySystem;
    this.newSystem = newSystem;
    this.outputDir = outputDir;

    // Ensure output directory exists
    if (!existsSync(this.outputDir)) {
      mkdirSync(this.outputDir, { recursive: true });
    }
  }

  /**
   * Run parallel tests on both systems
   */
  async runParallelTests(endpoints: TestEndpoint[]): Promise<TestSuiteResult> {
    console.log('🚀 Starting Parallel System Testing');
    console.log(`📊 Testing ${endpoints.length} endpoints on both systems`);
    console.log(`🏛️  Legacy System: ${this.legacySystem.baseUrl}`);
    console.log(`🆕 New System: ${this.newSystem.baseUrl}`);
    console.log('-'.repeat(60));

    this.testResults = [];
    const comparisons: ComparisonResult[] = [];

    for (const endpoint of endpoints) {
      console.log(`\n🔍 Testing: ${endpoint.name}`);
      
      try {
        // Run tests on both systems in parallel
        const [legacyResult, newResult] = await Promise.allSettled([
          this.runSingleTest(endpoint, this.legacySystem),
          this.runSingleTest(endpoint, this.newSystem)
        ]);

        // Process results
        const legacyTestResult = legacyResult.status === 'fulfilled' 
          ? legacyResult.value 
          : this.createErrorResult(endpoint, this.legacySystem, legacyResult.reason);

        const newTestResult = newResult.status === 'fulfilled' 
          ? newResult.value 
          : this.createErrorResult(endpoint, this.newSystem, newResult.reason);

        this.testResults.push(legacyTestResult, newTestResult);

        // Compare results
        const comparison = this.compareResults(endpoint, legacyTestResult, newTestResult);
        comparisons.push(comparison);

        // Log comparison result
        this.logComparisonResult(comparison);

      } catch (error) {
        console.error(`❌ Failed to test ${endpoint.name}:`, error);
      }
    }

    // Generate test suite result
    const suiteResult = this.generateTestSuiteResult(comparisons);
    
    // Save results
    await this.saveResults(suiteResult);
    
    // Print summary
    this.printSummary(suiteResult);

    return suiteResult;
  }

  /**
   * Run a single test on a specific system
   */
  private async runSingleTest(
    endpoint: TestEndpoint, 
    system: SystemConfig
  ): Promise<TestResult> {
    const startTime = performance.now();
    
    try {
      const config: AxiosRequestConfig = {
        method: endpoint.method,
        url: `${system.baseUrl}${endpoint.path}`,
        headers: {
          ...system.headers,
          ...endpoint.headers
        },
        timeout: endpoint.timeout || system.timeout || 30000,
        params: endpoint.params,
        data: endpoint.body,
        validateStatus: () => true // Accept all status codes
      };

      const response: AxiosResponse = await axios(config);
      const endTime = performance.now();

      return {
        endpoint: endpoint.name,
        system: system.name,
        success: true,
        statusCode: response.status,
        responseTime: Math.round(endTime - startTime),
        responseSize: JSON.stringify(response.data).length,
        data: response.data,
        timestamp: new Date().toISOString()
      };

    } catch (error: any) {
      const endTime = performance.now();
      
      return {
        endpoint: endpoint.name,
        system: system.name,
        success: false,
        responseTime: Math.round(endTime - startTime),
        responseSize: 0,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Create error result for failed tests
   */
  private createErrorResult(
    endpoint: TestEndpoint, 
    system: SystemConfig, 
    error: any
  ): TestResult {
    return {
      endpoint: endpoint.name,
      system: system.name,
      success: false,
      responseTime: 0,
      responseSize: 0,
      error: error?.message || 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Compare results from both systems
   */
  private compareResults(
    endpoint: TestEndpoint,
    legacyResult: TestResult,
    newResult: TestResult
  ): ComparisonResult {
    const differences: string[] = [];
    
    // Compare status codes
    const statusCodeMatch = legacyResult.statusCode === newResult.statusCode;
    if (!statusCodeMatch) {
      differences.push(
        `Status code: Legacy=${legacyResult.statusCode}, New=${newResult.statusCode}`
      );
    }

    // Compare response data
    const dataMatch = this.deepEqual(legacyResult.data, newResult.data);
    if (!dataMatch) {
      differences.push('Response data differs');
      
      // Add specific data differences
      const dataDiffs = this.findDataDifferences(legacyResult.data, newResult.data);
      differences.push(...dataDiffs);
    }

    // Calculate performance delta
    const performanceDelta = newResult.responseTime - legacyResult.responseTime;
    const sizeDelta = newResult.responseSize - legacyResult.responseSize;

    // Check for errors
    if (legacyResult.error && !newResult.error) {
      differences.push(`Legacy system error: ${legacyResult.error}`);
    } else if (!legacyResult.error && newResult.error) {
      differences.push(`New system error: ${newResult.error}`);
    } else if (legacyResult.error && newResult.error && legacyResult.error !== newResult.error) {
      differences.push(`Different errors: Legacy="${legacyResult.error}", New="${newResult.error}"`);
    }

    return {
      endpoint: endpoint.name,
      identical: statusCodeMatch && dataMatch && !legacyResult.error && !newResult.error,
      statusCodeMatch,
      dataMatch,
      performanceDelta,
      sizeDelta,
      differences,
      legacyResult,
      newResult
    };
  }

  /**
   * Deep equality check for objects
   */
  private deepEqual(obj1: any, obj2: any): boolean {
    if (obj1 === obj2) return true;
    
    if (obj1 == null || obj2 == null) return obj1 === obj2;
    
    if (typeof obj1 !== typeof obj2) return false;
    
    if (typeof obj1 !== 'object') return obj1 === obj2;
    
    if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;
    
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    for (const key of keys1) {
      if (!keys2.includes(key)) return false;
      if (!this.deepEqual(obj1[key], obj2[key])) return false;
    }
    
    return true;
  }

  /**
   * Find specific differences in data objects
   */
  private findDataDifferences(obj1: any, obj2: any, path: string = ''): string[] {
    const differences: string[] = [];
    
    if (typeof obj1 !== typeof obj2) {
      differences.push(`${path}: Type mismatch (${typeof obj1} vs ${typeof obj2})`);
      return differences;
    }
    
    if (typeof obj1 !== 'object' || obj1 === null || obj2 === null) {
      if (obj1 !== obj2) {
        differences.push(`${path}: Value mismatch (${obj1} vs ${obj2})`);
      }
      return differences;
    }
    
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    // Check for missing keys
    for (const key of keys1) {
      if (!keys2.includes(key)) {
        differences.push(`${path}.${key}: Missing in new system`);
      }
    }
    
    for (const key of keys2) {
      if (!keys1.includes(key)) {
        differences.push(`${path}.${key}: Extra in new system`);
      }
    }
    
    // Check common keys
    for (const key of keys1) {
      if (keys2.includes(key)) {
        const subPath = path ? `${path}.${key}` : key;
        differences.push(...this.findDataDifferences(obj1[key], obj2[key], subPath));
      }
    }
    
    return differences;
  }

  /**
   * Generate test suite result summary
   */
  private generateTestSuiteResult(comparisons: ComparisonResult[]): TestSuiteResult {
    const totalTests = comparisons.length;
    const identicalResponses = comparisons.filter(c => c.identical).length;
    const passedTests = comparisons.filter(c => 
      c.legacyResult.success && c.newResult.success
    ).length;
    const failedTests = totalTests - passedTests;

    // Calculate average response times
    const legacyTimes = comparisons
      .filter(c => c.legacyResult.success)
      .map(c => c.legacyResult.responseTime);
    const newTimes = comparisons
      .filter(c => c.newResult.success)
      .map(c => c.newResult.responseTime);

    const avgLegacyTime = legacyTimes.length > 0 
      ? legacyTimes.reduce((a, b) => a + b, 0) / legacyTimes.length 
      : 0;
    const avgNewTime = newTimes.length > 0 
      ? newTimes.reduce((a, b) => a + b, 0) / newTimes.length 
      : 0;

    const performanceImprovement = avgLegacyTime > 0 
      ? ((avgLegacyTime - avgNewTime) / avgLegacyTime) * 100 
      : 0;

    // Generate summary
    const identicalPercentage = (identicalResponses / totalTests) * 100;
    let summary = `Migration Validation: ${identicalPercentage.toFixed(1)}% identical responses`;
    
    if (performanceImprovement > 0) {
      summary += `, ${performanceImprovement.toFixed(1)}% performance improvement`;
    } else if (performanceImprovement < 0) {
      summary += `, ${Math.abs(performanceImprovement).toFixed(1)}% performance regression`;
    }

    return {
      totalTests,
      passedTests,
      failedTests,
      identicalResponses,
      performanceImprovement,
      averageResponseTime: {
        legacy: Math.round(avgLegacyTime),
        new: Math.round(avgNewTime)
      },
      comparisons,
      summary,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Log comparison result to console
   */
  private logComparisonResult(comparison: ComparisonResult): void {
    const { endpoint, identical, performanceDelta, legacyResult, newResult } = comparison;
    
    if (identical) {
      console.log(`  ✅ ${endpoint}: Identical responses`);
    } else {
      console.log(`  ⚠️  ${endpoint}: Differences found`);
      comparison.differences.forEach(diff => {
        console.log(`     - ${diff}`);
      });
    }
    
    // Performance comparison
    if (performanceDelta < 0) {
      console.log(`  🚀 Performance: ${Math.abs(performanceDelta)}ms faster`);
    } else if (performanceDelta > 0) {
      console.log(`  🐌 Performance: ${performanceDelta}ms slower`);
    }
    
    console.log(`  📊 Times: Legacy=${legacyResult.responseTime}ms, New=${newResult.responseTime}ms`);
  }

  /**
   * Save test results to files
   */
  private async saveResults(suiteResult: TestSuiteResult): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Save detailed results as JSON
    const detailedResultsPath = join(this.outputDir, `parallel-test-results-${timestamp}.json`);
    writeFileSync(detailedResultsPath, JSON.stringify(suiteResult, null, 2));
    
    // Save summary report as text
    const summaryPath = join(this.outputDir, `parallel-test-summary-${timestamp}.txt`);
    const summaryReport = this.generateSummaryReport(suiteResult);
    writeFileSync(summaryPath, summaryReport);
    
    console.log(`\n📁 Results saved:`);
    console.log(`   Detailed: ${detailedResultsPath}`);
    console.log(`   Summary: ${summaryPath}`);
  }

  /**
   * Generate human-readable summary report
   */
  private generateSummaryReport(suiteResult: TestSuiteResult): string {
    const { 
      totalTests, 
      identicalResponses, 
      performanceImprovement, 
      averageResponseTime,
      comparisons 
    } = suiteResult;

    let report = `Parallel System Testing Report\n`;
    report += `Generated: ${suiteResult.timestamp}\n`;
    report += `${'='.repeat(50)}\n\n`;
    
    report += `SUMMARY\n`;
    report += `-------\n`;
    report += `Total Tests: ${totalTests}\n`;
    report += `Identical Responses: ${identicalResponses}/${totalTests} (${(identicalResponses/totalTests*100).toFixed(1)}%)\n`;
    report += `Performance Change: ${performanceImprovement > 0 ? '+' : ''}${performanceImprovement.toFixed(1)}%\n`;
    report += `Average Response Time:\n`;
    report += `  Legacy System: ${averageResponseTime.legacy}ms\n`;
    report += `  New System: ${averageResponseTime.new}ms\n\n`;
    
    report += `DETAILED RESULTS\n`;
    report += `----------------\n`;
    
    for (const comparison of comparisons) {
      report += `\n${comparison.endpoint}:\n`;
      report += `  Status: ${comparison.identical ? 'IDENTICAL' : 'DIFFERENT'}\n`;
      report += `  Legacy: ${comparison.legacyResult.statusCode || 'ERROR'} (${comparison.legacyResult.responseTime}ms)\n`;
      report += `  New: ${comparison.newResult.statusCode || 'ERROR'} (${comparison.newResult.responseTime}ms)\n`;
      
      if (comparison.differences.length > 0) {
        report += `  Differences:\n`;
        comparison.differences.forEach(diff => {
          report += `    - ${diff}\n`;
        });
      }
    }
    
    return report;
  }

  /**
   * Print summary to console
   */
  private printSummary(suiteResult: TestSuiteResult): void {
    console.log('\n' + '='.repeat(60));
    console.log('📋 PARALLEL TESTING SUMMARY');
    console.log('='.repeat(60));
    console.log(`📊 Total Tests: ${suiteResult.totalTests}`);
    console.log(`✅ Identical Responses: ${suiteResult.identicalResponses}/${suiteResult.totalTests} (${(suiteResult.identicalResponses/suiteResult.totalTests*100).toFixed(1)}%)`);
    console.log(`🚀 Performance: ${suiteResult.performanceImprovement > 0 ? '+' : ''}${suiteResult.performanceImprovement.toFixed(1)}%`);
    console.log(`⏱️  Average Response Time:`);
    console.log(`   Legacy: ${suiteResult.averageResponseTime.legacy}ms`);
    console.log(`   New: ${suiteResult.averageResponseTime.new}ms`);
    console.log(`\n${suiteResult.summary}`);
    console.log('='.repeat(60));
  }

  /**
   * Run specific test categories
   */
  async runCategoryTests(category: 'api' | 'graph' | 'chat' | 'health'): Promise<TestSuiteResult> {
    const endpoints = this.getEndpointsByCategory(category);
    return this.runParallelTests(endpoints);
  }

  /**
   * Get endpoints by category
   */
  private getEndpointsByCategory(category: string): TestEndpoint[] {
    const endpointCategories = {
      api: [
        {
          name: 'Health Check',
          path: '/health',
          method: 'GET' as const
        },
        {
          name: 'API Status',
          path: '/api/status',
          method: 'GET' as const
        }
      ],
      graph: [
        {
          name: 'Graph Data',
          path: '/api/graph',
          method: 'GET' as const,
          params: { group_id: 'test' }
        },
        {
          name: 'Graph Analysis',
          path: '/api/analysis',
          method: 'POST' as const,
          body: { group_id: 'test', analysis_type: 'basic' }
        },
        {
          name: 'Graph Search',
          path: '/api/search',
          method: 'POST' as const,
          body: { query: 'test query', group_id: 'test' }
        }
      ],
      chat: [
        {
          name: 'Chat Message',
          path: '/api/chat/message',
          method: 'POST' as const,
          body: { message: 'Hello, this is a test message' },
          headers: { 'X-Session-ID': 'test-session' }
        },
        {
          name: 'Chat History',
          path: '/api/chat/history',
          method: 'GET' as const,
          headers: { 'X-Session-ID': 'test-session' }
        }
      ],
      health: [
        {
          name: 'System Health',
          path: '/health',
          method: 'GET' as const
        },
        {
          name: 'Database Health',
          path: '/api/health/database',
          method: 'GET' as const
        },
        {
          name: 'Service Health',
          path: '/api/health/services',
          method: 'GET' as const
        }
      ]
    };

    return (endpointCategories as any)[category] || [];
  }

  /**
   * Run load testing comparison
   */
  async runLoadTestComparison(
    endpoint: TestEndpoint,
    concurrentUsers: number = 10,
    duration: number = 30000
  ): Promise<{
    legacy: LoadTestResult;
    new: LoadTestResult;
    comparison: LoadTestComparison;
  }> {
    console.log(`🔥 Running load test comparison for ${endpoint.name}`);
    console.log(`👥 Concurrent users: ${concurrentUsers}`);
    console.log(`⏱️  Duration: ${duration}ms`);

    const [legacyResult, newResult] = await Promise.all([
      this.runLoadTest(endpoint, this.legacySystem, concurrentUsers, duration),
      this.runLoadTest(endpoint, this.newSystem, concurrentUsers, duration)
    ]);

    const comparison = this.compareLoadTestResults(legacyResult, newResult);

    return { legacy: legacyResult, new: newResult, comparison };
  }

  /**
   * Run load test on a single system
   */
  private async runLoadTest(
    endpoint: TestEndpoint,
    system: SystemConfig,
    concurrentUsers: number,
    duration: number
  ): Promise<LoadTestResult> {
    const startTime = Date.now();
    const endTime = startTime + duration;
    const results: TestResult[] = [];
    const promises: Promise<TestResult[]>[] = [];

    // Start concurrent users
    for (let i = 0; i < concurrentUsers; i++) {
      const userPromise = this.runUserLoadTest(endpoint, system, endTime);
      promises.push(userPromise);
    }

    // Wait for all users to complete
    const userResults = await Promise.all(promises);

    // Flatten results
    userResults.forEach(userResult => {
      if (Array.isArray(userResult)) {
        results.push(...userResult);
      } else {
        results.push(userResult);
      }
    });

    // Calculate statistics
    const successfulRequests = results.filter(r => r.success);
    const failedRequests = results.filter(r => !r.success);
    const responseTimes = successfulRequests.map(r => r.responseTime);

    const avgResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
      : 0;

    const minResponseTime = responseTimes.length > 0 ? Math.min(...responseTimes) : 0;
    const maxResponseTime = responseTimes.length > 0 ? Math.max(...responseTimes) : 0;

    // Calculate percentiles
    const sortedTimes = responseTimes.sort((a, b) => a - b);
    const p95 = sortedTimes[Math.floor(sortedTimes.length * 0.95)] || 0;
    const p99 = sortedTimes[Math.floor(sortedTimes.length * 0.99)] || 0;

    const requestsPerSecond = results.length / (duration / 1000);
    const errorRate = results.length > 0 ? (failedRequests.length / results.length) * 100 : 0;

    return {
      system: system.name,
      endpoint: endpoint.name,
      duration,
      concurrentUsers,
      totalRequests: results.length,
      successfulRequests: successfulRequests.length,
      failedRequests: failedRequests.length,
      requestsPerSecond,
      errorRate,
      responseTime: {
        average: Math.round(avgResponseTime),
        min: minResponseTime,
        max: maxResponseTime,
        p95,
        p99
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Run load test for a single user
   */
  private async runUserLoadTest(
    endpoint: TestEndpoint,
    system: SystemConfig,
    endTime: number
  ): Promise<TestResult[]> {
    const results: TestResult[] = [];

    while (Date.now() < endTime) {
      try {
        const result = await this.runSingleTest(endpoint, system);
        results.push(result);

        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        // Continue on error
      }
    }

    return results;
  }

  /**
   * Compare load test results
   */
  private compareLoadTestResults(
    legacy: LoadTestResult,
    newSystem: LoadTestResult
  ): LoadTestComparison {
    const throughputImprovement = ((newSystem.requestsPerSecond - legacy.requestsPerSecond) / legacy.requestsPerSecond) * 100;
    const responseTimeImprovement = ((legacy.responseTime.average - newSystem.responseTime.average) / legacy.responseTime.average) * 100;
    const errorRateChange = newSystem.errorRate - legacy.errorRate;

    return {
      throughputImprovement,
      responseTimeImprovement,
      errorRateChange,
      winner: this.determineLoadTestWinner(legacy, newSystem),
      summary: this.generateLoadTestSummary(legacy, newSystem, throughputImprovement, responseTimeImprovement, errorRateChange)
    };
  }

  /**
   * Determine load test winner
   */
  private determineLoadTestWinner(legacy: LoadTestResult, newSystem: LoadTestResult): 'legacy' | 'new' | 'tie' {
    let legacyScore = 0;
    let newScore = 0;

    // Throughput comparison
    if (newSystem.requestsPerSecond > legacy.requestsPerSecond) newScore++;
    else if (legacy.requestsPerSecond > newSystem.requestsPerSecond) legacyScore++;

    // Response time comparison
    if (newSystem.responseTime.average < legacy.responseTime.average) newScore++;
    else if (legacy.responseTime.average < newSystem.responseTime.average) legacyScore++;

    // Error rate comparison
    if (newSystem.errorRate < legacy.errorRate) newScore++;
    else if (legacy.errorRate < newSystem.errorRate) legacyScore++;

    if (newScore > legacyScore) return 'new';
    if (legacyScore > newScore) return 'legacy';
    return 'tie';
  }

  /**
   * Generate load test summary
   */
  private generateLoadTestSummary(
    legacy: LoadTestResult,
    newSystem: LoadTestResult,
    throughputImprovement: number,
    responseTimeImprovement: number,
    errorRateChange: number
  ): string {
    let summary = `Load Test Comparison: `;

    if (throughputImprovement > 0) {
      summary += `${throughputImprovement.toFixed(1)}% throughput improvement, `;
    } else {
      summary += `${Math.abs(throughputImprovement).toFixed(1)}% throughput regression, `;
    }

    if (responseTimeImprovement > 0) {
      summary += `${responseTimeImprovement.toFixed(1)}% faster response time`;
    } else {
      summary += `${Math.abs(responseTimeImprovement).toFixed(1)}% slower response time`;
    }

    if (errorRateChange !== 0) {
      summary += `, ${errorRateChange > 0 ? '+' : ''}${errorRateChange.toFixed(1)}% error rate change`;
    }

    return summary;
  }
}

// Additional interfaces for load testing
interface LoadTestResult {
  system: string;
  endpoint: string;
  duration: number;
  concurrentUsers: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  requestsPerSecond: number;
  errorRate: number;
  responseTime: {
    average: number;
    min: number;
    max: number;
    p95: number;
    p99: number;
  };
  timestamp: string;
}

interface LoadTestComparison {
  throughputImprovement: number;
  responseTimeImprovement: number;
  errorRateChange: number;
  winner: 'legacy' | 'new' | 'tie';
  summary: string;
}
