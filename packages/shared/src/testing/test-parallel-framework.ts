#!/usr/bin/env ts-node
/**
 * Test the Parallel Testing Framework
 * 
 * This script tests the parallel testing framework itself to ensure
 * it works correctly before using it for migration validation.
 */

import { ParallelTestingFramework, TestEndpoint, SystemConfig } from './parallel-testing-framework';

// Mock system configurations for testing
const mockLegacySystem: SystemConfig = {
  name: 'Mock Legacy System',
  baseUrl: 'https://httpbin.org',
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'Parallel-Test-Framework-Test/1.0.0'
  },
  timeout: 10000
};

const mockNewSystem: SystemConfig = {
  name: 'Mock New System',
  baseUrl: 'https://httpbin.org',
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'Parallel-Test-Framework-Test/1.0.0'
  },
  timeout: 10000
};

// Test endpoints using httpbin.org (public testing service)
const testEndpoints: TestEndpoint[] = [
  {
    name: 'Simple GET Request',
    path: '/get',
    method: 'GET'
  },
  {
    name: 'GET with Query Parameters',
    path: '/get',
    method: 'GET',
    params: {
      test: 'value',
      number: 42
    }
  },
  {
    name: 'POST Request with JSON',
    path: '/post',
    method: 'POST',
    body: {
      message: 'Hello, World!',
      timestamp: new Date().toISOString(),
      test: true
    }
  },
  {
    name: 'GET with Custom Headers',
    path: '/headers',
    method: 'GET',
    headers: {
      'X-Test-Header': 'test-value',
      'X-Custom-ID': '12345'
    }
  },
  {
    name: 'Status Code Test',
    path: '/status/200',
    method: 'GET'
  }
];

async function testParallelFramework(): Promise<void> {
  console.log('🧪 Testing Parallel Testing Framework');
  console.log('='.repeat(50));
  console.log('Using httpbin.org as mock systems for testing');
  console.log('-'.repeat(50));

  try {
    // Initialize framework
    const framework = new ParallelTestingFramework(
      mockLegacySystem,
      mockNewSystem,
      './test-results/framework-test'
    );

    // Run parallel tests
    const result = await framework.runParallelTests(testEndpoints);

    // Validate results
    console.log('\n📊 Framework Test Results:');
    console.log(`Total Tests: ${result.totalTests}`);
    console.log(`Identical Responses: ${result.identicalResponses}/${result.totalTests}`);
    console.log(`Performance: ${result.performanceImprovement.toFixed(1)}%`);

    // Since we're testing against the same service, all responses should be identical
    if (result.identicalResponses === result.totalTests) {
      console.log('✅ Framework test PASSED - All responses identical as expected');
    } else {
      console.log('⚠️  Framework test WARNING - Some responses differ (unexpected)');
      
      // Show differences
      result.comparisons.forEach(comparison => {
        if (!comparison.identical) {
          console.log(`  - ${comparison.endpoint}: ${comparison.differences.join(', ')}`);
        }
      });
    }

    // Test individual components
    await testComparisonLogic(framework);
    await testErrorHandling(framework);

    console.log('\n✅ Parallel Testing Framework validation complete');

  } catch (error) {
    console.error('❌ Framework test failed:', error);
    throw error;
  }
}

async function testComparisonLogic(framework: ParallelTestingFramework): Promise<void> {
  console.log('\n🔍 Testing Comparison Logic');
  
  // Test deep equality
  const testCases = [
    {
      name: 'Identical objects',
      obj1: { a: 1, b: 'test', c: [1, 2, 3] },
      obj2: { a: 1, b: 'test', c: [1, 2, 3] },
      expected: true
    },
    {
      name: 'Different values',
      obj1: { a: 1, b: 'test' },
      obj2: { a: 2, b: 'test' },
      expected: false
    },
    {
      name: 'Different array order',
      obj1: { arr: [1, 2, 3] },
      obj2: { arr: [3, 2, 1] },
      expected: false // Arrays should maintain order
    },
    {
      name: 'Missing property',
      obj1: { a: 1, b: 2 },
      obj2: { a: 1 },
      expected: false
    }
  ];

  for (const testCase of testCases) {
    const result = (framework as any).deepEqual(testCase.obj1, testCase.obj2);
    if (result === testCase.expected) {
      console.log(`  ✅ ${testCase.name}: ${result}`);
    } else {
      console.log(`  ❌ ${testCase.name}: Expected ${testCase.expected}, got ${result}`);
    }
  }
}

async function testErrorHandling(framework: ParallelTestingFramework): Promise<void> {
  console.log('\n🚨 Testing Error Handling');

  // Test with non-existent endpoint
  const errorEndpoint: TestEndpoint = {
    name: 'Non-existent Endpoint',
    path: '/nonexistent',
    method: 'GET'
  };

  try {
    const result = await framework.runParallelTests([errorEndpoint]);
    
    // Both systems should return 404, so they should be identical
    if (result.identicalResponses === 1) {
      console.log('  ✅ Error handling: Both systems returned identical 404 responses');
    } else {
      console.log('  ⚠️  Error handling: Different error responses');
    }
  } catch (error) {
    console.log('  ❌ Error handling test failed:', error);
  }
}

async function testLoadTestingCapability(): Promise<void> {
  console.log('\n🔥 Testing Load Testing Capability');

  try {
    const framework = new ParallelTestingFramework(
      mockLegacySystem,
      mockNewSystem,
      './test-results/load-test'
    );

    const loadTestEndpoint: TestEndpoint = {
      name: 'Load Test Endpoint',
      path: '/get',
      method: 'GET'
    };

    console.log('Running mini load test (5 users, 10 seconds)...');
    const result = await framework.runLoadTestComparison(
      loadTestEndpoint,
      5, // 5 concurrent users
      10000 // 10 seconds
    );

    console.log(`Legacy: ${result.legacy.requestsPerSecond.toFixed(1)} req/s`);
    console.log(`New: ${result.new.requestsPerSecond.toFixed(1)} req/s`);
    console.log(`Winner: ${result.comparison.winner}`);
    console.log(`Summary: ${result.comparison.summary}`);

    console.log('✅ Load testing capability validated');

  } catch (error) {
    console.error('❌ Load testing test failed:', error);
  }
}

async function main(): Promise<void> {
  try {
    await testParallelFramework();
    await testLoadTestingCapability();
    
    console.log('\n🎉 All framework tests completed successfully!');
    console.log('📁 Test results saved in ./test-results/framework-test/');
    console.log('🚀 Framework is ready for migration validation');
    
  } catch (error) {
    console.error('❌ Framework validation failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { testParallelFramework, testComparisonLogic, testErrorHandling };
