#!/usr/bin/env ts-node
/**
 * Test the Contract Testing Framework
 * 
 * This script tests the contract testing framework itself to ensure
 * it correctly validates API contracts and detects violations.
 */

import { 
  ContractTestingFramework, 
  ContractTest, 
  ResponseSchema,
  ContractRule 
} from './contract-testing-framework';

// Mock API server responses for testing
const mockResponses = {
  '/test/valid': {
    status: 200,
    data: {
      id: 1,
      name: 'Test Item',
      active: true,
      tags: ['test', 'mock'],
      metadata: {
        created: '2024-01-01T00:00:00Z',
        version: '1.0.0'
      }
    }
  },
  '/test/invalid-schema': {
    status: 200,
    data: {
      id: 'not-a-number', // Schema violation
      name: 'Test Item',
      // missing required 'active' field
      tags: 'not-an-array' // Schema violation
    }
  },
  '/test/business-rule-violation': {
    status: 200,
    data: {
      id: 1,
      name: '', // Business rule violation - empty name
      active: true,
      tags: ['test'],
      score: 1.5 // Business rule violation - score > 1
    }
  },
  '/test/status-error': {
    status: 500,
    data: { error: 'Internal server error' }
  }
};

// Mock axios for testing
const originalAxios = require('axios');
const mockAxios = {
  ...originalAxios,
  default: async (config: any) => {
    const url = config.url.replace('http://mock-api', '');
    const mockResponse = mockResponses[url as keyof typeof mockResponses];
    
    if (!mockResponse) {
      throw new Error(`Mock response not found for ${url}`);
    }
    
    return {
      status: mockResponse.status,
      data: mockResponse.data,
      headers: {},
      config
    };
  }
};

// Replace axios with mock
require.cache[require.resolve('axios')] = {
  exports: mockAxios
} as any;

async function testContractFramework(): Promise<void> {
  console.log('🧪 Testing Contract Testing Framework');
  console.log('='.repeat(50));
  console.log('Using mock API responses for testing');
  console.log('-'.repeat(50));

  try {
    // Initialize framework with mock API
    const framework = new ContractTestingFramework(
      'http://mock-api',
      { 'Content-Type': 'application/json' },
      './test-results/contract-framework-test'
    );

    // Test schema validation
    await testSchemaValidation(framework);
    
    // Test business rule validation
    await testBusinessRuleValidation(framework);
    
    // Test error handling
    await testErrorHandling(framework);
    
    // Test backward compatibility
    await testBackwardCompatibility(framework);

    console.log('\n✅ Contract Testing Framework validation complete');

  } catch (error) {
    console.error('❌ Framework test failed:', error);
    throw error;
  }
}

async function testSchemaValidation(framework: ContractTestingFramework): Promise<void> {
  console.log('\n🔍 Testing Schema Validation');
  
  const validSchema: ResponseSchema = {
    type: 'object',
    required: ['id', 'name', 'active'],
    properties: {
      id: { type: 'number' },
      name: { type: 'string', minLength: 1 },
      active: { type: 'boolean' },
      tags: { 
        type: 'array', 
        items: { type: 'string' } 
      },
      metadata: {
        type: 'object',
        properties: {
          created: { type: 'string' },
          version: { type: 'string' }
        }
      }
    }
  };

  // Test valid response
  const validContract: ContractTest = {
    endpoint: {
      name: 'Valid Response Test',
      path: '/test/valid',
      method: 'GET',
      expectedStatus: 200
    },
    responseSchema: validSchema,
    description: 'Test valid response schema'
  };

  const validResult = await framework.runContractTests([validContract]);
  
  if (validResult.passedTests === 1 && validResult.schemaViolations === 0) {
    console.log('  ✅ Valid schema test passed');
  } else {
    console.log('  ❌ Valid schema test failed');
  }

  // Test invalid response
  const invalidContract: ContractTest = {
    endpoint: {
      name: 'Invalid Schema Test',
      path: '/test/invalid-schema',
      method: 'GET',
      expectedStatus: 200
    },
    responseSchema: validSchema,
    description: 'Test invalid response schema'
  };

  const invalidResult = await framework.runContractTests([invalidContract]);
  
  if (invalidResult.failedTests === 1 && invalidResult.schemaViolations === 1) {
    console.log('  ✅ Invalid schema test correctly detected violations');
    console.log(`     Violations: ${invalidResult.results[0].violations.length}`);
  } else {
    console.log('  ❌ Invalid schema test failed to detect violations');
  }
}

async function testBusinessRuleValidation(framework: ContractTestingFramework): Promise<void> {
  console.log('\n📋 Testing Business Rule Validation');
  
  const businessRules: ContractRule[] = [
    {
      name: 'Non-empty name',
      description: 'Name field must not be empty',
      validator: (response) => response.name && response.name.trim().length > 0
    },
    {
      name: 'Valid score range',
      description: 'Score must be between 0 and 1',
      validator: (response) => {
        if (response.score === undefined) return true; // Optional field
        return response.score >= 0 && response.score <= 1;
      }
    },
    {
      name: 'Active status check',
      description: 'Active field must be boolean',
      validator: (response) => typeof response.active === 'boolean'
    }
  ];

  const businessRuleContract: ContractTest = {
    endpoint: {
      name: 'Business Rule Violation Test',
      path: '/test/business-rule-violation',
      method: 'GET',
      expectedStatus: 200
    },
    responseSchema: {
      type: 'object',
      required: ['id', 'name', 'active'],
      properties: {
        id: { type: 'number' },
        name: { type: 'string' },
        active: { type: 'boolean' },
        tags: { type: 'array', items: { type: 'string' } },
        score: { type: 'number' }
      }
    },
    businessRules,
    description: 'Test business rule violations'
  };

  const result = await framework.runContractTests([businessRuleContract]);
  
  if (result.failedTests === 1 && result.businessRuleViolations === 1) {
    console.log('  ✅ Business rule violations correctly detected');
    console.log(`     Violations: ${result.results[0].violations.length}`);
    
    // Check specific violations
    const violations = result.results[0].violations;
    const hasNameViolation = violations.some(v => v.includes('Non-empty name'));
    const hasScoreViolation = violations.some(v => v.includes('Valid score range'));
    
    if (hasNameViolation && hasScoreViolation) {
      console.log('  ✅ Specific business rule violations detected correctly');
    } else {
      console.log('  ⚠️  Some business rule violations not detected');
    }
  } else {
    console.log('  ❌ Business rule validation failed');
  }
}

async function testErrorHandling(framework: ContractTestingFramework): Promise<void> {
  console.log('\n🚨 Testing Error Handling');
  
  // Test status code mismatch
  const statusErrorContract: ContractTest = {
    endpoint: {
      name: 'Status Error Test',
      path: '/test/status-error',
      method: 'GET',
      expectedStatus: 200 // Expecting 200 but will get 500
    },
    responseSchema: {
      type: 'object',
      properties: {
        error: { type: 'string' }
      }
    },
    description: 'Test status code error handling'
  };

  const result = await framework.runContractTests([statusErrorContract]);
  
  if (result.failedTests === 1) {
    console.log('  ✅ Status code error correctly detected');
    
    const violations = result.results[0].violations;
    const hasStatusViolation = violations.some(v => v.includes('Expected status 200, got 500'));
    
    if (hasStatusViolation) {
      console.log('  ✅ Status code violation message correct');
    } else {
      console.log('  ⚠️  Status code violation message incorrect');
    }
  } else {
    console.log('  ❌ Status code error not detected');
  }

  // Test non-existent endpoint
  const nonExistentContract: ContractTest = {
    endpoint: {
      name: 'Non-existent Endpoint Test',
      path: '/test/nonexistent',
      method: 'GET',
      expectedStatus: 200
    },
    responseSchema: { type: 'object' },
    description: 'Test non-existent endpoint handling'
  };

  const nonExistentResult = await framework.runContractTests([nonExistentContract]);
  
  if (nonExistentResult.failedTests === 1) {
    console.log('  ✅ Non-existent endpoint error correctly handled');
  } else {
    console.log('  ❌ Non-existent endpoint error not handled');
  }
}

async function testBackwardCompatibility(framework: ContractTestingFramework): Promise<void> {
  console.log('\n🔄 Testing Backward Compatibility Validation');
  
  // Old contract (v1)
  const oldContract: ContractTest = {
    endpoint: {
      name: 'API v1',
      path: '/api/data',
      method: 'GET'
    },
    responseSchema: {
      type: 'object',
      required: ['id', 'name'],
      properties: {
        id: { type: 'number' },
        name: { type: 'string' },
        description: { type: 'string' }
      }
    }
  };

  // New contract (v2) - backward compatible
  const compatibleContract: ContractTest = {
    endpoint: {
      name: 'API v2 Compatible',
      path: '/api/data',
      method: 'GET'
    },
    responseSchema: {
      type: 'object',
      required: ['id', 'name'], // Same required fields
      properties: {
        id: { type: 'number' },
        name: { type: 'string' },
        description: { type: 'string' },
        newField: { type: 'string' } // Added optional field
      }
    }
  };

  // New contract (v2) - breaking change
  const breakingContract: ContractTest = {
    endpoint: {
      name: 'API v2 Breaking',
      path: '/api/data',
      method: 'GET'
    },
    responseSchema: {
      type: 'object',
      required: ['id', 'email'], // Changed required field
      properties: {
        id: { type: 'number' },
        email: { type: 'string' }, // Renamed field
        description: { type: 'string' }
      }
    }
  };

  // Test compatible change
  const compatibleResult = await framework.validateBackwardCompatibility(
    [oldContract],
    [compatibleContract]
  );

  if (compatibleResult.compatible && compatibleResult.breakingChanges.length === 0) {
    console.log('  ✅ Compatible change correctly identified');
  } else {
    console.log('  ❌ Compatible change incorrectly flagged as breaking');
  }

  // Test breaking change
  const breakingResult = await framework.validateBackwardCompatibility(
    [oldContract],
    [breakingContract]
  );

  if (!breakingResult.compatible && breakingResult.breakingChanges.length > 0) {
    console.log('  ✅ Breaking change correctly detected');
    console.log(`     Breaking changes: ${breakingResult.breakingChanges.length}`);
  } else {
    console.log('  ❌ Breaking change not detected');
  }
}

async function main(): Promise<void> {
  try {
    await testContractFramework();
    
    console.log('\n🎉 All contract framework tests completed successfully!');
    console.log('📁 Test results saved in ./test-results/contract-framework-test/');
    console.log('🚀 Framework is ready for API contract validation');
    
  } catch (error) {
    console.error('❌ Contract framework validation failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { testContractFramework };
