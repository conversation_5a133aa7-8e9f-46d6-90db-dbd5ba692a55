/**
 * Tests for validation utilities
 */

import {
  validateEmail,
  validateUrl,
  validatePassword,
  validateNodeId,
  validateCypherQuery,
  validateJSON,
  validateFeatureFlagName,
  validatePaginationParams,
  validateEnvironment,
  validatePort,
  validateDatabaseUri,
  validateApiKey,
  isString,
  isNumber,
  isBoolean,
  isObject,
  isArray,
  isEmpty
} from '../validation';

describe('Basic type validation', () => {
  test('isString', () => {
    expect(isString('hello')).toBe(true);
    expect(isString('')).toBe(true);
    expect(isString(123)).toBe(false);
    expect(isString(null)).toBe(false);
    expect(isString(undefined)).toBe(false);
  });

  test('isNumber', () => {
    expect(isNumber(123)).toBe(true);
    expect(isNumber(0)).toBe(true);
    expect(isNumber(-123)).toBe(true);
    expect(isNumber(123.45)).toBe(true);
    expect(isNumber(NaN)).toBe(false);
    expect(isNumber('123')).toBe(false);
    expect(isNumber(null)).toBe(false);
  });

  test('isBoolean', () => {
    expect(isBoolean(true)).toBe(true);
    expect(isBoolean(false)).toBe(true);
    expect(isBoolean(0)).toBe(false);
    expect(isBoolean(1)).toBe(false);
    expect(isBoolean('true')).toBe(false);
  });

  test('isObject', () => {
    expect(isObject({})).toBe(true);
    expect(isObject({ key: 'value' })).toBe(true);
    expect(isObject([])).toBe(false);
    expect(isObject(null)).toBe(false);
    expect(isObject('object')).toBe(false);
  });

  test('isArray', () => {
    expect(isArray([])).toBe(true);
    expect(isArray([1, 2, 3])).toBe(true);
    expect(isArray({})).toBe(false);
    expect(isArray('array')).toBe(false);
  });

  test('isEmpty', () => {
    expect(isEmpty(null)).toBe(true);
    expect(isEmpty(undefined)).toBe(true);
    expect(isEmpty('')).toBe(true);
    expect(isEmpty('   ')).toBe(true);
    expect(isEmpty([])).toBe(true);
    expect(isEmpty({})).toBe(true);
    expect(isEmpty('hello')).toBe(false);
    expect(isEmpty([1])).toBe(false);
    expect(isEmpty({ key: 'value' })).toBe(false);
  });
});

describe('Email validation', () => {
  test('valid emails', () => {
    expect(validateEmail('<EMAIL>')).toBe(true);
    expect(validateEmail('<EMAIL>')).toBe(true);
    expect(validateEmail('<EMAIL>')).toBe(true);
  });

  test('invalid emails', () => {
    expect(validateEmail('invalid')).toBe(false);
    expect(validateEmail('invalid@')).toBe(false);
    expect(validateEmail('@invalid.com')).toBe(false);
    expect(validateEmail('invalid@.com')).toBe(false);
  });
});

describe('URL validation', () => {
  test('valid URLs', () => {
    expect(validateUrl('https://example.com')).toBe(true);
    expect(validateUrl('http://localhost:3000')).toBe(true);
    expect(validateUrl('ftp://files.example.com')).toBe(true);
  });

  test('invalid URLs', () => {
    expect(validateUrl('invalid')).toBe(false);
    expect(validateUrl('not-a-url')).toBe(false);
    expect(validateUrl('')).toBe(false);
  });
});

describe('Password validation', () => {
  test('valid passwords', () => {
    const result = validatePassword('StrongP@ss123');
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  test('invalid passwords', () => {
    const result = validatePassword('weak');
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(0);
  });
});

describe('Node ID validation', () => {
  test('valid node IDs', () => {
    expect(validateNodeId('node123')).toBe(true);
    expect(validateNodeId('user-profile')).toBe(true);
    expect(validateNodeId('entity_001')).toBe(true);
  });

  test('invalid node IDs', () => {
    expect(validateNodeId('')).toBe(false);
    expect(validateNodeId('   ')).toBe(false);
    expect(validateNodeId('node<123>')).toBe(false);
    expect(validateNodeId('node/path')).toBe(false);
  });
});

describe('Cypher query validation', () => {
  test('valid queries', () => {
    const result = validateCypherQuery('MATCH (n) RETURN n LIMIT 10');
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  test('invalid queries', () => {
    const result = validateCypherQuery('');
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(0);
  });

  test('mismatched parentheses', () => {
    const result = validateCypherQuery('MATCH (n RETURN n');
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('Mismatched parentheses in query');
  });
});

describe('JSON validation', () => {
  test('valid JSON', () => {
    const result = validateJSON('{"key": "value"}');
    expect(result.isValid).toBe(true);
    expect(result.parsed).toEqual({ key: 'value' });
  });

  test('invalid JSON', () => {
    const result = validateJSON('invalid json');
    expect(result.isValid).toBe(false);
    expect(result.error).toBeDefined();
  });
});

describe('Feature flag name validation', () => {
  test('valid feature flag names', () => {
    expect(validateFeatureFlagName('NEW_FEATURE')).toBe(true);
    expect(validateFeatureFlagName('ENABLE_CHAT_SERVICE')).toBe(true);
    expect(validateFeatureFlagName('DEBUG_MODE')).toBe(true);
  });

  test('invalid feature flag names', () => {
    expect(validateFeatureFlagName('new_feature')).toBe(false);
    expect(validateFeatureFlagName('123_FEATURE')).toBe(false);
    expect(validateFeatureFlagName('feature-name')).toBe(false);
  });
});

describe('Pagination validation', () => {
  test('valid pagination params', () => {
    const result = validatePaginationParams({ page: 1, limit: 20 });
    expect(result.isValid).toBe(true);
    expect(result.validated).toEqual({ page: 1, limit: 20 });
  });

  test('invalid pagination params', () => {
    const result = validatePaginationParams({ page: -1, limit: 200 });
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(0);
  });
});

describe('Environment validation', () => {
  test('valid environments', () => {
    expect(validateEnvironment('development')).toBe(true);
    expect(validateEnvironment('staging')).toBe(true);
    expect(validateEnvironment('production')).toBe(true);
    expect(validateEnvironment('test')).toBe(true);
  });

  test('invalid environments', () => {
    expect(validateEnvironment('invalid')).toBe(false);
    expect(validateEnvironment('')).toBe(false);
  });
});

describe('Port validation', () => {
  test('valid ports', () => {
    expect(validatePort(3000)).toBe(true);
    expect(validatePort(80)).toBe(true);
    expect(validatePort(65535)).toBe(true);
  });

  test('invalid ports', () => {
    expect(validatePort(0)).toBe(false);
    expect(validatePort(65536)).toBe(false);
    expect(validatePort(3000.5)).toBe(false);
    expect(validatePort('3000')).toBe(false);
  });
});

describe('Database URI validation', () => {
  test('valid database URIs', () => {
    expect(validateDatabaseUri('neo4j://localhost:7687')).toBe(true);
    expect(validateDatabaseUri('bolt://localhost:7687')).toBe(true);
    expect(validateDatabaseUri('neo4j+s://example.com:7687')).toBe(true);
  });

  test('invalid database URIs', () => {
    expect(validateDatabaseUri('invalid')).toBe(false);
    expect(validateDatabaseUri('http://localhost:7687')).toBe(false);
    expect(validateDatabaseUri('')).toBe(false);
  });
});

describe('API key validation', () => {
  test('valid API keys', () => {
    expect(validateApiKey('abcdefghijklmnopqrstuvwxyz123456')).toBe(true);
    expect(validateApiKey('sk-1234567890abcdefghijklmnopqrstuvwxyz')).toBe(true);
  });

  test('invalid API keys', () => {
    expect(validateApiKey('short')).toBe(false);
    expect(validateApiKey('invalid@key!')).toBe(false);
    expect(validateApiKey('')).toBe(false);
  });
});
