/**
 * Logging utilities
 */

export type LogLevel = 'error' | 'warn' | 'info' | 'debug' | 'verbose';

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: string;
  metadata?: Record<string, any>;
  error?: Error;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  filename?: string;
  maxFileSize?: number;
  maxFiles?: number;
  format?: 'json' | 'text';
}

class Logger {
  private config: LoggerConfig;
  private logLevels: Record<LogLevel, number> = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3,
    verbose: 4
  };

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: 'info',
      enableConsole: true,
      enableFile: false,
      format: 'text',
      ...config
    };
  }

  private shouldLog(level: LogLevel): boolean {
    return this.logLevels[level] <= this.logLevels[this.config.level];
  }

  private formatMessage(entry: LogEntry): string {
    if (this.config.format === 'json') {
      return JSON.stringify(entry);
    }

    let message = `[${entry.timestamp}] ${entry.level.toUpperCase()}`;
    
    if (entry.context) {
      message += ` [${entry.context}]`;
    }
    
    message += `: ${entry.message}`;
    
    if (entry.metadata && Object.keys(entry.metadata).length > 0) {
      message += ` ${JSON.stringify(entry.metadata)}`;
    }
    
    if (entry.error) {
      message += `\n${entry.error.stack || entry.error.message}`;
    }
    
    return message;
  }

  private log(level: LogLevel, message: string, context?: string, metadata?: Record<string, any>, error?: Error): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context,
      metadata,
      error
    };

    const formattedMessage = this.formatMessage(entry);

    if (this.config.enableConsole) {
      switch (level) {
        case 'error':
          console.error(formattedMessage);
          break;
        case 'warn':
          console.warn(formattedMessage);
          break;
        case 'info':
          console.info(formattedMessage);
          break;
        case 'debug':
        case 'verbose':
          console.log(formattedMessage);
          break;
      }
    }

    // File logging would be implemented here
    // For now, we'll skip it to keep the shared package simple
  }

  error(message: string, context?: string, metadata?: Record<string, any>, error?: Error): void {
    this.log('error', message, context, metadata, error);
  }

  warn(message: string, context?: string, metadata?: Record<string, any>): void {
    this.log('warn', message, context, metadata);
  }

  info(message: string, context?: string, metadata?: Record<string, any>): void {
    this.log('info', message, context, metadata);
  }

  debug(message: string, context?: string, metadata?: Record<string, any>): void {
    this.log('debug', message, context, metadata);
  }

  verbose(message: string, context?: string, metadata?: Record<string, any>): void {
    this.log('verbose', message, context, metadata);
  }

  setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  getLevel(): LogLevel {
    return this.config.level;
  }

  child(context: string): Logger {
    const childLogger = new Logger(this.config);
    const originalLog = childLogger.log.bind(childLogger);
    
    childLogger.log = (level: LogLevel, message: string, childContext?: string, metadata?: Record<string, any>, error?: Error) => {
      const fullContext = childContext ? `${context}:${childContext}` : context;
      originalLog(level, message, fullContext, metadata, error);
    };
    
    return childLogger;
  }
}

// Create default logger instance
export const logger = new Logger({
  level: (process.env.LOG_LEVEL as LogLevel) || 'info',
  enableConsole: true,
  format: process.env.LOG_FORMAT === 'json' ? 'json' : 'text'
});

// Export Logger class for custom instances
export { Logger };

// Convenience functions
export const createLogger = (config: Partial<LoggerConfig> = {}): Logger => {
  return new Logger(config);
};

export const getLogger = (context: string): Logger => {
  return logger.child(context);
};
