{"name": "@kg-visualizer/shared", "version": "1.0.0", "description": "Shared utilities and types for Knowledge Graph Visualizer", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "tsc --watch", "clean": "rm -rf dist", "clean:build": "npm run clean && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"zod": "^3.22.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.1.0"}, "files": ["dist", "src"], "publishConfig": {"access": "restricted"}}