# Build outputs
dist/
build/
coverage/
.next/
.nuxt/
.vuepress/dist/

# Dependencies
node_modules/

# Generated files
*.d.ts
*.tsbuildinfo

# Config files
*.config.js
*.config.ts
jest.setup.js

# Test files
**/*.test.js
**/*.test.ts
**/*.spec.js
**/*.spec.ts

# Legacy files
../server.js
../public/
../static/

# Temporary files
tmp/
temp/
.cache/

# Environment files
.env*

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Documentation
*.md

# Docker files
Dockerfile*
docker-compose*.yml
