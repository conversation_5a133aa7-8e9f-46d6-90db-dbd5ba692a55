version: '3.8'

services:
  # Neo4j Database for Staging
  neo4j:
    image: neo4j:5.13-community
    container_name: kg-neo4j-staging
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      - NEO4J_AUTH=neo4j/${NEO4J_PASSWORD:-staging_password}
      - NEO4J_PLUGINS=["graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=gds.*
      - NEO4J_dbms_memory_heap_initial__size=1G
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
      - NEO4J_dbms_logs_debug_level=INFO
    volumes:
      - neo4j_staging_data:/data
      - neo4j_staging_logs:/logs
      - neo4j_staging_import:/var/lib/neo4j/import
      - neo4j_staging_plugins:/plugins
    networks:
      - kg-staging-network
    healthcheck:
      test: ["CMD-SHELL", "cypher-shell -u neo4j -p ${NEO4J_PASSWORD:-staging_password} 'RETURN 1'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Graph API Service for Staging
  graph-api:
    build:
      context: .
      target: graph-api-production
    container_name: kg-graph-api-staging
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=staging
      - DATABASE_URI=neo4j://neo4j:7687
      - DATABASE_USERNAME=neo4j
      - DATABASE_PASSWORD=${NEO4J_PASSWORD:-staging_password}
      - PORT=3002
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:3000}
      - LOG_LEVEL=info
      - JWT_SECRET=${JWT_SECRET}
      - SESSION_SECRET=${SESSION_SECRET}
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - kg-staging-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3002/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Chat Service for Staging
  chat-service:
    build:
      context: .
      target: chat-service-production
    container_name: kg-chat-service-staging
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=staging
      - DATABASE_URI=neo4j://neo4j:7687
      - DATABASE_USERNAME=neo4j
      - DATABASE_PASSWORD=${NEO4J_PASSWORD:-staging_password}
      - PORT=8000
      - API_BASE_URL=http://graph-api:3002
      - LOG_LEVEL=info
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    depends_on:
      neo4j:
        condition: service_healthy
      graph-api:
        condition: service_healthy
    networks:
      - kg-staging-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Web UI for Staging
  web-ui:
    build:
      context: .
      target: web-ui-production
    container_name: kg-web-ui-staging
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=${API_URL:-http://localhost:3002}
      - VITE_CHAT_URL=${CHAT_URL:-http://localhost:8000}
      - VITE_ENVIRONMENT=staging
    depends_on:
      graph-api:
        condition: service_healthy
      chat-service:
        condition: service_healthy
    networks:
      - kg-staging-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis for Staging
  redis:
    image: redis:7-alpine
    container_name: kg-redis-staging
    ports:
      - "6379:6379"
    volumes:
      - redis_staging_data:/data
    networks:
      - kg-staging-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: kg-nginx-staging
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/staging.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - web-ui
      - graph-api
      - chat-service
    networks:
      - kg-staging-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: kg-prometheus-staging
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus-staging.yml:/etc/prometheus/prometheus.yml
      - prometheus_staging_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - kg-staging-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: kg-grafana-staging
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_staging_data:/var/lib/grafana
      - ./monitoring/grafana/staging:/etc/grafana/provisioning
    networks:
      - kg-staging-network
    restart: unless-stopped

networks:
  kg-staging-network:
    driver: bridge
    name: kg-staging-network

volumes:
  neo4j_staging_data:
    name: kg-neo4j-staging-data
  neo4j_staging_logs:
    name: kg-neo4j-staging-logs
  neo4j_staging_import:
    name: kg-neo4j-staging-import
  neo4j_staging_plugins:
    name: kg-neo4j-staging-plugins
  redis_staging_data:
    name: kg-redis-staging-data
  prometheus_staging_data:
    name: kg-prometheus-staging-data
  grafana_staging_data:
    name: kg-grafana-staging-data
