# Development Guide

This guide covers development workflows, tools, and best practices for the Knowledge Graph Visualizer monorepo.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm 9+
- VS Code (recommended)
- Neo4j Desktop

### Setup
```bash
# Clone and navigate to packages
cd packages

# Install dependencies
npm install

# Build shared package first
npm run build:shared

# Start development
npm run dev
```

## 🛠️ Development Tools

### VS Code Setup
1. Open the workspace file: `kg-visualizer.code-workspace`
2. Install recommended extensions when prompted
3. VS Code will automatically configure TypeScript, ESLint, and Prettier

### Available Scripts

#### Build Commands
```bash
npm run build              # Build all packages
npm run build:shared       # Build shared package only
npm run build:api          # Build graph-api package
npm run build:ui           # Build web-ui package
npm run build:chat         # Build chat-service package
```

#### Development Commands
```bash
npm run dev                # Start all services in development
npm run dev:api            # Start API server only
npm run dev:ui             # Start frontend only
npm run dev:chat           # Start chat service only
```

#### Testing Commands
```bash
npm test                   # Run all tests
npm run test:watch         # Run tests in watch mode
npm run test --workspace=shared  # Test specific package
```

#### Code Quality Commands
```bash
npm run lint               # Lint all packages
npm run lint:fix           # Fix linting issues
npm run format             # Format code with Prettier
npm run format:check       # Check code formatting
npm run type-check         # TypeScript type checking
```

#### Utility Commands
```bash
npm run clean              # Clean all build artifacts
npm run deps:check         # Check for outdated dependencies
npm run deps:update        # Update dependencies
npm run security:audit     # Security audit
npm run security:fix       # Fix security issues
```

#### Migration Commands
```bash
npm run migration:health   # Check system health
npm run migration:monitor  # Start monitoring
npm run migration:rollback # Emergency rollback
```

## 🏗️ Architecture

### Package Structure
```
packages/
├── shared/          # Common utilities and types
├── graph-api/       # Backend API service
├── web-ui/          # React frontend
└── chat-service/    # Chat microservice
```

### Dependency Flow
```
graph-api  ──┐
web-ui     ──┼──► shared
chat-service ─┘
```

All packages depend on the shared package for common utilities.

## 🧪 Testing Strategy

### Unit Tests
- Located in `__tests__` directories or `.test.ts` files
- Use Jest testing framework
- Aim for 80%+ code coverage

### Integration Tests
- Test API endpoints and service interactions
- Use supertest for HTTP testing
- Mock external dependencies

### End-to-End Tests
- Use Playwright for browser testing
- Test complete user workflows
- Run against staging environment

## 🔧 Debugging

### VS Code Debugging
1. Set breakpoints in TypeScript files
2. Use "Debug Full Stack" configuration
3. Debug individual packages with specific configurations

### Available Debug Configurations
- **Debug Graph API**: Debug backend API server
- **Debug Chat Service**: Debug chat microservice
- **Debug Jest Tests**: Debug unit tests
- **Debug Full Stack**: Debug multiple services

### Logging
```typescript
import { getLogger } from '@kg-visualizer/shared';

const logger = getLogger('MyComponent');
logger.info('Something happened', { data: value });
logger.error('Error occurred', 'context', { error });
```

## 📝 Code Style

### TypeScript Guidelines
- Use strict TypeScript configuration
- Prefer interfaces over types for object shapes
- Use proper type annotations for function parameters
- Avoid `any` type - use `unknown` instead

### Naming Conventions
- **Files**: kebab-case (`user-service.ts`)
- **Directories**: kebab-case (`user-management/`)
- **Variables/Functions**: camelCase (`getUserData`)
- **Classes**: PascalCase (`UserService`)
- **Constants**: UPPER_SNAKE_CASE (`API_BASE_URL`)
- **Interfaces**: PascalCase (`UserData`)

### Import Organization
```typescript
// 1. Node modules
import express from 'express';
import { Request, Response } from 'express';

// 2. Internal packages
import { logger, validateEmail } from '@kg-visualizer/shared';

// 3. Relative imports
import { UserService } from './user-service';
import { config } from '../config';
```

## 🔄 Git Workflow

### Branch Naming
- `feature/description` - New features
- `fix/description` - Bug fixes
- `refactor/description` - Code refactoring
- `docs/description` - Documentation updates

### Commit Messages
```
type(scope): description

feat(api): add user authentication endpoint
fix(ui): resolve graph rendering issue
docs(readme): update installation instructions
refactor(shared): extract validation utilities
```

### Pull Request Process
1. Create feature branch from `main`
2. Make changes with proper tests
3. Run `npm run lint` and `npm test`
4. Create pull request with description
5. Request code review
6. Merge after approval

## 🚨 Troubleshooting

### Common Issues

#### TypeScript Errors
```bash
# Clear TypeScript cache
npm run clean
npm run build:shared
npm run type-check
```

#### Dependency Issues
```bash
# Clear node_modules and reinstall
npm run clean
rm -rf node_modules package-lock.json
npm install
```

#### Port Conflicts
```bash
# Check running processes
lsof -i :3000  # Frontend
lsof -i :3002  # API
lsof -i :8000  # Chat service
```

#### Neo4j Connection Issues
1. Ensure Neo4j Desktop is running
2. Check connection URI in environment variables
3. Verify credentials are correct

### Getting Help
1. Check this documentation
2. Look at existing code examples
3. Check VS Code problems panel
4. Review test files for usage examples
5. Ask team members for assistance

## 📚 Additional Resources

- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)
- [ESLint Rules](https://eslint.org/docs/rules/)
- [Prettier Configuration](https://prettier.io/docs/en/configuration.html)
- [VS Code Debugging](https://code.visualstudio.com/docs/editor/debugging)
