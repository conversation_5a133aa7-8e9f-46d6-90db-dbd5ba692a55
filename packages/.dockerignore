# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
build/
coverage/
.next/
.nuxt/
.vuepress/dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache/
.parcel-cache/

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test/

# Temporary folders
tmp/
temp/

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
README.md
CHANGELOG.md
LICENSE
*.md

# Test files
**/*.test.js
**/*.test.ts
**/*.spec.js
**/*.spec.ts
test/
tests/
__tests__/

# Migration and monitoring files
migration-*.log
rollback-*.log
baseline-metrics-*.json
alerts.json
metrics.json

# Backup files
backups/
*.backup
*.bak

# Legacy files
../server.js
../public/
../static/

# Monitoring configuration
monitoring/
nginx/

# Development tools
.prettierrc
.prettierignore
.eslintrc.js
.eslintignore
jest.config.js
jest.setup.js

# TypeScript
*.tsbuildinfo
tsconfig*.json

# Package manager lock files (keep package-lock.json for production)
yarn.lock
pnpm-lock.yaml
