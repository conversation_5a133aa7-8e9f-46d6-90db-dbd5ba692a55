# Build outputs
dist/
build/
coverage/
.next/
.nuxt/
.vuepress/dist/

# Dependencies
node_modules/

# Generated files
*.d.ts
*.tsbuildinfo

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Environment files
.env*

# Logs
*.log

# Legacy files
../server.js
../public/
../static/

# Temporary files
tmp/
temp/
.cache/

# Documentation that should maintain formatting
CHANGELOG.md
LICENSE

# Docker files
Dockerfile*

# Configuration files that have specific formatting
.vscode/
.github/
