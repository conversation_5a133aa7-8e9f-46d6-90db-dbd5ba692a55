# Repository Pattern Documentation

This document describes the Repository pattern implementation for Neo4j database access with dual-execution validation during migration.

## 🎯 Overview

The Repository pattern provides:

- **Data Access Abstraction**: Clean separation between business logic and database operations
- **Dual Execution Validation**: Compare new and legacy query implementations for consistency
- **Connection Management**: Automatic session handling and connection pooling
- **Error Handling & Retry**: Robust error handling with configurable retry logic
- **Performance Monitoring**: Query timing and execution metrics

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Service       │───▶│  Repository      │───▶│  Neo4j Driver   │
│   Layer         │    │  Layer           │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  Dual Execution │
                       │  Validation     │
                       └─────────────────┘
```

## 📦 Components

### 1. BaseRepository

Abstract base class providing common functionality:

```typescript
export abstract class BaseRepository {
  // Core execution methods
  protected async executeRead<T>(
    query: string,
    parameters: Record<string, any>,
    transformer: (result: QueryResult) => T,
    legacyQuery?: string
  ): Promise<T | DualExecutionResult<T>>

  protected async executeWrite<T>(
    query: string,
    parameters: Record<string, any>,
    transformer: (result: QueryResult) => T,
    legacyQuery?: string
  ): Promise<T | DualExecutionResult<T>>

  // Transaction support
  protected async executeInTransaction<T>(
    operation: (tx: any) => Promise<T>,
    mode: 'read' | 'write'
  ): Promise<T>

  // Health monitoring
  async healthCheck(): Promise<HealthStatus>
}
```

### 2. GraphRepository

Handles graph data operations:

```typescript
export class GraphRepository extends BaseRepository {
  // Get initial graph data for visualization
  async getInitialGraph(limit: number): Promise<GraphData | DualExecutionResult<GraphData>>

  // Search for nodes by term
  async searchNodes(term: string, limit: number): Promise<GraphNode[] | DualExecutionResult<GraphNode[]>>

  // Expand a node to show connected nodes
  async expandNode(nodeId: string, limit: number): Promise<GraphData | DualExecutionResult<GraphData>>

  // Filter graph data based on criteria
  async filterGraph(filters: GraphFilters): Promise<GraphData | DualExecutionResult<GraphData>>

  // Execute custom Cypher query
  async executeCustomQuery(query: string, parameters: Record<string, any>): Promise<any[]>

  // Get database metadata and statistics
  async getMetadata(): Promise<DatabaseMetadata | DualExecutionResult<DatabaseMetadata>>
}
```

### 3. AnalysisRepository

Handles graph analysis operations:

```typescript
export class AnalysisRepository extends BaseRepository {
  // Calculate node centrality metrics
  async getCentrality(type: CentralityType, limit: number): Promise<CentralityResult[]>

  // Perform community detection
  async getClusters(algorithm: ClusteringAlgorithm, limit: number): Promise<ClusterResult[]>

  // Predict potential links
  async predictLinks(topN: number, threshold: number): Promise<LinkPrediction[]>

  // Find paths between two nodes
  async findPaths(startNodeId: string, endNodeId: string, maxDepth: number): Promise<PathResult[]>

  // Get comprehensive graph statistics
  async getStatistics(): Promise<GraphStatistics>

  // Find similar nodes to a given node
  async getSimilarity(nodeId: string, limit: number): Promise<SimilarityResult[]>
}
```

### 4. RepositoryFactory

Factory for creating and managing repository instances:

```typescript
export class RepositoryFactory {
  constructor(dependencies: RepositoryDependencies)

  // Get repository instances
  getGraphRepository(): GraphRepository
  getAnalysisRepository(): AnalysisRepository
  getAllRepositories(): RepositoryInstances

  // Health monitoring
  async healthCheck(): Promise<OverallHealthStatus>

  // Migration management
  getMigrationStatus(): MigrationStatus
  updateConfiguration(newFeatureFlags: FeatureFlags): void
}
```

## 🚀 Usage

### Basic Setup

```typescript
import { RepositoryFactory } from './repositories/RepositoryFactory';
import neo4j from 'neo4j-driver';

// Create Neo4j driver
const driver = neo4j.driver(
  'bolt://localhost:7687',
  neo4j.auth.basic('neo4j', 'password')
);

// Initialize repository factory
const repositoryFactory = new RepositoryFactory({
  driver,
  database: 'neo4j',
  featureFlags: {
    NEW_REPOSITORY_PATTERN: true,
    DUAL_EXECUTION_MODE: true,
    // ... other flags
  }
});

// Get repositories
const graphRepo = repositoryFactory.getGraphRepository();
const analysisRepo = repositoryFactory.getAnalysisRepository();
```

### Dual Execution Example

```typescript
// With dual execution enabled, both new and legacy queries are executed
const result = await graphRepo.getInitialGraph(100);

if ('primary' in result) {
  // Dual execution result
  console.log('Primary result:', result.primary);
  console.log('Secondary result:', result.secondary);
  console.log('Validation passed:', result.isValid);
  console.log('Differences:', result.differences);
  console.log('Execution times:', result.executionTime);
} else {
  // Single execution result
  console.log('Result:', result);
}
```

### Transaction Usage

```typescript
const result = await graphRepo.executeInTransaction(async (tx) => {
  // Multiple operations in a single transaction
  const nodes = await tx.run('CREATE (n:Product {name: $name}) RETURN n', { name: 'New Product' });
  const relationships = await tx.run('MATCH (a:Product), (b:Category) CREATE (a)-[:BELONGS_TO]->(b)');
  
  return {
    nodesCreated: nodes.summary.counters.nodesCreated,
    relationshipsCreated: relationships.summary.counters.relationshipsCreated
  };
}, 'write');
```

## 🔧 Configuration

### Feature Flags

```bash
# Enable repository pattern
NEW_REPOSITORY_PATTERN=true

# Enable dual execution for validation
DUAL_EXECUTION_MODE=true

# Enable validation of dual execution results
ENABLE_VALIDATION=true
```

### Repository Configuration

```typescript
const config: RepositoryConfig = {
  enableDualExecution: true,
  enableValidation: true,
  timeout: 30000,        // 30 seconds
  retryAttempts: 3,      // Retry failed queries 3 times
  retryDelay: 1000       // 1 second delay between retries
};
```

## 📊 Dual Execution Validation

### How It Works

1. **Primary Execution**: Execute the new query implementation
2. **Secondary Execution**: Execute the legacy query for comparison
3. **Result Validation**: Compare results for consistency
4. **Difference Detection**: Identify and log any discrepancies

### Validation Results

```typescript
interface DualExecutionResult<T> {
  primary: T;                    // Result from new implementation
  secondary?: T;                 // Result from legacy implementation
  isValid: boolean;              // Whether results match
  differences: string[];         // List of differences found
  executionTime: {
    primary: number;             // Primary execution time (ms)
    secondary?: number;          // Secondary execution time (ms)
  };
}
```

### Example Validation Output

```json
{
  "primary": {
    "nodes": [{"id": "1", "name": "Product A"}],
    "relationships": []
  },
  "secondary": {
    "nodes": [{"id": "1", "name": "Product A"}],
    "relationships": []
  },
  "isValid": true,
  "differences": [],
  "executionTime": {
    "primary": 150,
    "secondary": 200
  }
}
```

## 🧪 Testing

### Unit Tests

```typescript
describe('GraphRepository', () => {
  let repository: GraphRepository;
  let mockDriver: jest.Mocked<Driver>;

  beforeEach(() => {
    mockDriver = createMockDriver();
    repository = new GraphRepository(mockDriver, 'neo4j', {
      enableDualExecution: true,
      enableValidation: true
    });
  });

  it('should execute dual queries and validate results', async () => {
    // Setup mock responses
    mockDriver.session().executeRead
      .mockResolvedValueOnce(mockPrimaryResult)
      .mockResolvedValueOnce(mockSecondaryResult);

    const result = await repository.getInitialGraph(10);

    expect(result).toHaveProperty('primary');
    expect(result).toHaveProperty('secondary');
    expect(result).toHaveProperty('isValid');
  });
});
```

### Integration Tests

```typescript
describe('Repository Integration', () => {
  it('should handle real Neo4j operations', async () => {
    const driver = neo4j.driver(process.env.NEO4J_URI);
    const repository = new GraphRepository(driver);

    const result = await repository.getInitialGraph(5);
    
    expect(result.nodes).toBeDefined();
    expect(result.relationships).toBeDefined();
    
    await driver.close();
  });
});
```

## 🔍 Monitoring

### Health Checks

```typescript
// Check individual repository health
const graphHealth = await graphRepo.healthCheck();
console.log('Graph repository status:', graphHealth.status);

// Check all repositories
const overallHealth = await repositoryFactory.healthCheck();
console.log('Overall status:', overallHealth.overall);
```

### Performance Metrics

```typescript
// Dual execution provides automatic performance comparison
const result = await graphRepo.searchNodes('product');

if ('executionTime' in result) {
  console.log('Primary query time:', result.executionTime.primary);
  console.log('Secondary query time:', result.executionTime.secondary);
  console.log('Performance difference:', 
    result.executionTime.secondary - result.executionTime.primary);
}
```

## 🚨 Error Handling

### Automatic Retry

```typescript
// Repositories automatically retry failed operations
const config = {
  retryAttempts: 3,
  retryDelay: 1000
};

// Will retry up to 3 times with 1 second delay
const result = await repository.getInitialGraph(100);
```

### Graceful Degradation

```typescript
// If secondary query fails during dual execution, primary result is still returned
const result = await repository.searchNodes('term');

if ('differences' in result && result.differences.includes('Secondary execution failed')) {
  console.log('Legacy query failed, but new implementation succeeded');
  console.log('Primary result:', result.primary);
}
```

## 📈 Migration Phases

### Phase 1: Repository Pattern Only
```typescript
const featureFlags = {
  NEW_REPOSITORY_PATTERN: true,
  DUAL_EXECUTION_MODE: false
};
// → Use new repository pattern without validation
```

### Phase 2: Dual Execution Validation
```typescript
const featureFlags = {
  NEW_REPOSITORY_PATTERN: true,
  DUAL_EXECUTION_MODE: true
};
// → Execute both new and legacy queries for validation
```

### Phase 3: New Implementation Only
```typescript
const featureFlags = {
  NEW_REPOSITORY_PATTERN: true,
  DUAL_EXECUTION_MODE: false
};
// → Use only new implementation (legacy queries removed)
```

## 🔗 Related Documentation

- [Service Layer](./SERVICES.md)
- [Controller Layer](./CONTROLLERS.md)
- [Compatibility Layer](./COMPATIBILITY.md)
- [Migration Guide](../MIGRATION.md)
