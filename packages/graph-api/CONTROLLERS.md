# Controller Layer Documentation

This document describes the controller layer implementation that gradually replaces the monolithic server.js using the Strangler Fig pattern.

## 🎯 Overview

The controller layer provides:

- **Structured Request Handling**: Clean separation of concerns with dedicated controllers
- **Strangler Fig Pattern**: Gradual replacement of legacy routes with new implementations
- **Feature Flag Control**: Dynamic routing based on feature flags
- **Comprehensive Error Handling**: Consistent error responses and logging
- **Request/Response Transformation**: Automatic handling of different API formats

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   HTTP Request  │───▶│  Controller      │───▶│  Service Layer  │
│                 │    │  Factory         │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  Feature Flags  │
                       │  (Route Control)│
                       └─────────────────┘
```

## 📦 Components

### 1. BaseController

Abstract base class providing common functionality:

```typescript
export abstract class BaseController {
  // Error handling wrapper
  protected asyncHandler(fn: Function): Function

  // Response formatting
  protected sendSuccess<T>(res: Response, data: T, statusCode?: number): void
  protected sendError(res: Response, error: string | Error, statusCode?: number): void

  // Request validation
  protected validateRequired(params: Record<string, any>, required: string[]): ValidationResult
  protected getPaginationParams(req: Request): PaginationParams
  protected getSortParams(req: Request): SortParams

  // Utility methods
  protected withTiming<T>(operation: () => Promise<T>, name: string): Promise<TimedResult<T>>
  protected getClientInfo(req: Request): ClientInfo
  protected isLegacyRequest(req: Request): boolean
}
```

### 2. GraphController

Handles graph-related operations:

```typescript
export class GraphController extends BaseController {
  // GET /api/graph/initial - Get initial graph data
  getInitialGraph = this.asyncHandler(async (req, res) => { ... })

  // GET /api/graph/search - Search nodes
  searchNodes = this.asyncHandler(async (req, res) => { ... })

  // GET /api/graph/expand - Expand node connections
  expandNode = this.asyncHandler(async (req, res) => { ... })

  // POST /api/graph/filter - Filter graph data
  filterGraph = this.asyncHandler(async (req, res) => { ... })

  // POST /api/graph/query - Execute custom Cypher query
  executeQuery = this.asyncHandler(async (req, res) => { ... })

  // GET /api/graph/metadata - Get database metadata
  getMetadata = this.asyncHandler(async (req, res) => { ... })
}
```

### 3. AnalysisController

Handles graph analysis operations:

```typescript
export class AnalysisController extends BaseController {
  // GET /api/analysis/centrality - Calculate centrality metrics
  getCentrality = this.asyncHandler(async (req, res) => { ... })

  // GET /api/analysis/clusters - Community detection
  getClusters = this.asyncHandler(async (req, res) => { ... })

  // GET /api/analysis/hidden-links - Link prediction
  predictLinks = this.asyncHandler(async (req, res) => { ... })

  // GET /api/analysis/paths - Find paths between nodes
  findPaths = this.asyncHandler(async (req, res) => { ... })

  // GET /api/analysis/statistics - Graph statistics
  getStatistics = this.asyncHandler(async (req, res) => { ... })

  // GET /api/analysis/similarity - Node similarity
  getSimilarity = this.asyncHandler(async (req, res) => { ... })

  // POST /api/analysis/batch - Batch analysis operations
  runBatchAnalysis = this.asyncHandler(async (req, res) => { ... })
}
```

### 4. HealthController

Handles health checks and monitoring:

```typescript
export class HealthController extends BaseController {
  // GET /api/health - Basic health check
  getHealth = this.asyncHandler(async (req, res) => { ... })

  // GET /api/health/detailed - Comprehensive health check
  getDetailedHealth = this.asyncHandler(async (req, res) => { ... })

  // GET /api/health/ready - Kubernetes readiness probe
  getReadiness = this.asyncHandler(async (req, res) => { ... })

  // GET /api/health/live - Kubernetes liveness probe
  getLiveness = this.asyncHandler(async (req, res) => { ... })

  // GET /api/health/metrics - Prometheus metrics
  getMetrics = this.asyncHandler(async (req, res) => { ... })
}
```

### 5. FeatureFlagsController

Handles feature flag management:

```typescript
export class FeatureFlagsController extends BaseController {
  // GET /api/feature-flags - Get current flags
  getFlags = this.asyncHandler(async (req, res) => { ... })

  // POST /api/feature-flags/update - Update single flag (admin)
  updateFlag = this.asyncHandler(async (req, res) => { ... })

  // POST /api/feature-flags/batch-update - Update multiple flags (admin)
  batchUpdateFlags = this.asyncHandler(async (req, res) => { ... })

  // POST /api/feature-flags/reset - Reset all flags (admin)
  resetFlags = this.asyncHandler(async (req, res) => { ... })

  // GET /api/feature-flags/history - Flag change history (admin)
  getFlagHistory = this.asyncHandler(async (req, res) => { ... })

  // GET /api/feature-flags/migration-status - Migration status
  getMigrationStatus = this.asyncHandler(async (req, res) => { ... })
}
```

### 6. ControllerFactory

Implements the Strangler Fig pattern:

```typescript
export class ControllerFactory {
  constructor(dependencies: ControllerDependencies)

  // Create router with feature flag-controlled routing
  createRouter(): Router

  // Register routes based on feature flags
  private registerGraphRoutes(router: Router): void
  private registerAnalysisRoutes(router: Router): void
  private registerHealthRoutes(router: Router): void
  private registerFeatureFlagRoutes(router: Router): void

  // Get migration status
  getMigrationStatus(): MigrationStatus
}
```

## 🚀 Usage

### Basic Setup

```typescript
import { ControllerFactory } from './controllers/ControllerFactory';

// Initialize dependencies
const dependencies: ControllerDependencies = {
  graphService: new GraphService(driver),
  analysisService: new AnalysisService(driver),
  healthService: new HealthService(),
  featureFlagsService: new FeatureFlagsService(),
  compatibilityService: new CompatibilityService(),
  featureFlags: loadFeatureFlags(),
  legacyApiUrl: process.env.LEGACY_API_URL
};

// Create controller factory
const controllerFactory = new ControllerFactory(dependencies);

// Create router with Strangler Fig pattern
const router = controllerFactory.createRouter();

// Mount router
app.use('/api', router);
```

### Strangler Fig Migration

The controller factory automatically routes requests based on feature flags:

```typescript
// Phase 1: Legacy routes only
const featureFlags = {
  NEW_CONTROLLER_LAYER: false,
  NEW_SERVICE_LAYER: false,
  DUAL_EXECUTION_MODE: false
};
// → All requests go to legacy server.js

// Phase 2: New controllers with dual execution
const featureFlags = {
  NEW_CONTROLLER_LAYER: true,
  NEW_SERVICE_LAYER: false,
  DUAL_EXECUTION_MODE: true
};
// → New controllers execute alongside legacy for validation

// Phase 3: New controllers only
const featureFlags = {
  NEW_CONTROLLER_LAYER: true,
  NEW_SERVICE_LAYER: true,
  DUAL_EXECUTION_MODE: false
};
// → Full migration to new controllers
```

## 🔧 Configuration

### Feature Flags

```bash
# Enable new controller layer
NEW_CONTROLLER_LAYER=true

# Enable new service layer
NEW_SERVICE_LAYER=true

# Enable dual execution for validation
DUAL_EXECUTION_MODE=true

# Traffic percentage for new API
TRAFFIC_PERCENTAGE_NEW_API=25

# Admin API key for feature flag management
ADMIN_API_KEY=your-secure-admin-key
```

### Environment Variables

```bash
# Legacy API URL for fallback
LEGACY_API_URL=http://localhost:3002

# Log level
LOG_LEVEL=info

# Request timeout
REQUEST_TIMEOUT=30000
```

## 📊 Response Formats

### Success Response

```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req-123",
  "metadata": {
    "executionTime": 150,
    "resultCount": 25
  }
}
```

### Error Response

```json
{
  "code": "VALIDATION_ERROR",
  "message": "Invalid parameters",
  "statusCode": 400,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req-123",
  "details": {
    "errors": ["Field 'name' is required"]
  }
}
```

## 🧪 Testing

### Unit Tests

```typescript
describe('GraphController', () => {
  let controller: GraphController;
  let mockService: jest.Mocked<GraphService>;

  beforeEach(() => {
    mockService = createMockGraphService();
    controller = new GraphController(mockService);
  });

  it('should get initial graph data', async () => {
    const mockData = { nodes: [], relationships: [] };
    mockService.getInitialGraph.mockResolvedValue(mockData);

    await controller.getInitialGraph(mockReq, mockRes);

    expect(mockRes.json).toHaveBeenCalledWith(
      expect.objectContaining({
        success: true,
        data: mockData
      })
    );
  });
});
```

### Integration Tests

```typescript
describe('Controller Integration', () => {
  it('should handle complete request flow', async () => {
    const response = await request(app)
      .get('/api/graph/initial')
      .expect(200);

    expect(response.body).toMatchObject({
      success: true,
      data: expect.objectContaining({
        nodes: expect.any(Array),
        relationships: expect.any(Array)
      })
    });
  });
});
```

## 🔍 Monitoring

### Request Logging

All controllers automatically log:
- Request details (path, method, user)
- Execution timing
- Error information
- Performance metrics

### Health Checks

```bash
# Basic health check
curl /api/health

# Detailed health check
curl /api/health/detailed

# Kubernetes probes
curl /api/health/ready
curl /api/health/live

# Prometheus metrics
curl /api/health/metrics
```

### Migration Status

```bash
# Get current migration status
curl /api/feature-flags/migration-status
```

## 🚨 Error Handling

### Automatic Error Handling

All controller methods are wrapped with `asyncHandler` for automatic error catching:

```typescript
someMethod = this.asyncHandler(async (req, res) => {
  // Any thrown error is automatically caught and handled
  throw new Error('Something went wrong');
});
```

### Custom Error Responses

```typescript
// Validation error
this.sendError(res, 'Invalid input', 400, { field: 'name' });

// Service error
this.sendError(res, error, 500);
```

## 📈 Performance

### Caching

Controllers automatically set appropriate cache headers:

```typescript
// Cache for 5 minutes
this.setCacheHeaders(res, 300);

// No cache for dynamic data
res.setHeader('Cache-Control', 'no-cache');
```

### Timing

All operations are automatically timed:

```typescript
const { result, duration } = await this.withTiming(
  () => this.service.getData(),
  'getData',
  req
);
```

## 🔗 Related Documentation

- [Compatibility Layer](./COMPATIBILITY.md)
- [Service Layer](./SERVICES.md)
- [Migration Guide](../MIGRATION.md)
- [API Documentation](./API.md)
