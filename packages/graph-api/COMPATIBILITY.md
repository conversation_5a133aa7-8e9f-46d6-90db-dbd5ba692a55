# Backward Compatibility Layer

This document describes the backward compatibility layer that ensures seamless migration from the legacy API to the new microservices architecture.

## 🎯 Overview

The backward compatibility layer provides:

- **API Contract Preservation**: Maintains exact same request/response formats
- **Dual Execution**: Runs both old and new implementations for validation
- **Gradual Migration**: Feature flag-controlled rollout of new features
- **Automatic Fallback**: Falls back to legacy implementation on errors
- **Request/Response Transformation**: Converts between legacy and new formats

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client        │    │  Compatibility   │    │  New API        │
│   Request       │───▶│  Layer           │───▶│  Implementation │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  Legacy API     │
                       │  (Fallback)     │
                       └─────────────────┘
```

## 📦 Components

### 1. Compatibility Middleware

#### `legacyResponseTransformer`
Transforms new API responses to legacy format:

```typescript
// New format
{
  success: true,
  data: { nodes: [...], edges: [...] },
  timestamp: "2024-01-01T00:00:00.000Z"
}

// Legacy format
{
  nodes: [...],
  edges: [...]
}
```

#### `legacyRequestTransformer`
Transforms legacy requests to new format:
- Adds request IDs for tracking
- Validates and limits pagination parameters
- Restructures filter parameters

#### `compatibilityHeaders`
Adds compatibility headers:
- `X-API-Version: 2.0`
- `X-Legacy-Compatible: true`
- `X-Migration-Phase: phase-1`

### 2. Compatibility Service

Handles dual execution pattern:

```typescript
const result = await compatibilityService.dualExecute(
  newImplementation,
  '/api/graph/initial',
  requestPayload,
  {
    enableDualExecution: true,
    enableValidation: true,
    timeout: 30000
  }
);
```

Features:
- Executes both implementations in parallel
- Compares results for validation
- Logs performance metrics
- Handles fallback scenarios

### 3. Compatibility Router

Routes requests between implementations:

```typescript
compatibilityRouter.registerRoute({
  path: '/initial',
  method: 'GET',
  legacyEndpoint: '/api/graph/initial',
  newHandler: myNewHandler,
  enableDualExecution: true,
  enableFallback: true
});
```

## 🚀 Usage

### Basic Setup

```typescript
import express from 'express';
import {
  legacyResponseTransformer,
  legacyRequestTransformer,
  compatibilityHeaders,
  compatibilityErrorHandler
} from './middleware/compatibility';

const app = express();

// Apply compatibility middleware
app.use(compatibilityHeaders);
app.use(legacyRequestTransformer);
app.use(legacyResponseTransformer);

// Your routes here...

// Error handling (must be last)
app.use(compatibilityErrorHandler);
```

### Gradual Migration

```typescript
// Phase 1: Dual execution with validation
compatibilityRouter.registerRoute({
  path: '/search',
  method: 'GET',
  legacyEndpoint: '/api/graph/search',
  newHandler: newSearchHandler,
  enableDualExecution: true,  // Run both implementations
  enableFallback: true        // Fallback on errors
});

// Phase 2: New implementation with fallback
compatibilityRouter.registerRoute({
  path: '/search',
  method: 'GET',
  legacyEndpoint: '/api/graph/search',
  newHandler: newSearchHandler,
  enableDualExecution: false, // Only new implementation
  enableFallback: true        // Fallback on errors
});

// Phase 3: New implementation only
compatibilityRouter.registerRoute({
  path: '/search',
  method: 'GET',
  legacyEndpoint: '/api/graph/search',
  newHandler: newSearchHandler,
  enableDualExecution: false,
  enableFallback: false      // No fallback
});
```

### Feature Flag Control

```typescript
const featureFlags = {
  NEW_CONTROLLER_LAYER: true,     // Use new controllers
  DUAL_EXECUTION_MODE: true,      // Run both implementations
  TRAFFIC_PERCENTAGE_NEW_API: 25  // 25% traffic to new API
};

// Router automatically respects feature flags
const shouldUseNew = featureFlags.NEW_CONTROLLER_LAYER;
```

## 🔧 Configuration

### Environment Variables

```bash
# Legacy API URL for fallback
LEGACY_API_URL=http://localhost:3002

# Migration phase
MIGRATION_PHASE=phase-1

# Feature flags
NEW_CONTROLLER_LAYER=true
DUAL_EXECUTION_MODE=true
TRAFFIC_PERCENTAGE_NEW_API=25
```

### Request Headers

Clients can control behavior with headers:

```bash
# Force legacy implementation
curl -H "X-Use-Legacy: true" /api/graph/initial

# Force new implementation
curl -H "X-Use-New: true" /api/graph/initial

# Request legacy response format
curl -H "X-Legacy-API: true" /api/graph/initial
```

## 📊 Monitoring

### Compatibility Metrics

The system logs detailed metrics:

```json
{
  "endpoint": "/api/graph/initial",
  "success": true,
  "isValid": true,
  "primaryTime": 150,
  "secondaryTime": 200,
  "hasDifferences": false,
  "differenceCount": 0
}
```

### Validation Results

When dual execution is enabled:

```json
{
  "primary": { "nodes": [...] },
  "secondary": { "nodes": [...] },
  "isValid": true,
  "differences": [],
  "executionTime": {
    "primary": 150,
    "secondary": 200
  }
}
```

## 🧪 Testing

### Unit Tests

```typescript
describe('Compatibility Middleware', () => {
  it('should transform new API response to legacy format', () => {
    // Test response transformation
  });

  it('should add request ID to legacy requests', () => {
    // Test request transformation
  });
});
```

### Integration Tests

```typescript
describe('Dual Execution', () => {
  it('should execute both implementations and compare results', async () => {
    const result = await compatibilityService.dualExecute(
      newImplementation,
      '/api/graph/initial',
      {}
    );
    
    expect(result.isValid).toBe(true);
    expect(result.primary).toBeDefined();
    expect(result.secondary).toBeDefined();
  });
});
```

## 🚨 Error Handling

### Fallback Strategy

1. **Primary Failure**: Falls back to legacy implementation
2. **Secondary Failure**: Continues with primary result
3. **Both Fail**: Returns error to client

### Error Transformation

Errors are transformed to legacy format:

```typescript
// New format error
{
  success: false,
  error: "Validation failed",
  timestamp: "2024-01-01T00:00:00.000Z"
}

// Legacy format error
{
  error: "Validation failed"
}
```

## 📈 Migration Phases

### Phase 1: Dual Execution
- Run both implementations
- Compare results
- Log differences
- Fallback on errors

### Phase 2: Primary with Fallback
- Use new implementation as primary
- Fallback to legacy on errors
- Gradual traffic increase

### Phase 3: New Implementation Only
- Disable dual execution
- Remove fallback mechanisms
- Full migration complete

## 🔍 Troubleshooting

### Common Issues

1. **Response Format Mismatch**
   - Check transformation logic
   - Verify endpoint mapping

2. **Performance Degradation**
   - Monitor dual execution overhead
   - Adjust timeout settings

3. **Validation Failures**
   - Review result comparison logic
   - Check for acceptable differences

### Debug Headers

Add debug information to responses:

```json
{
  "X-Implementation": "new",
  "X-Dual-Execution": "true",
  "X-Validation-Passed": "true",
  "X-Execution-Time": "150ms"
}
```

## 📚 Best Practices

1. **Start Small**: Begin with low-risk endpoints
2. **Monitor Closely**: Watch metrics and logs
3. **Gradual Rollout**: Increase traffic percentage slowly
4. **Test Thoroughly**: Validate all scenarios
5. **Plan Rollback**: Have rollback procedures ready

## 🔗 Related Documentation

- [Feature Flags](../shared/src/config/feature-flags.md)
- [Migration Guide](../MIGRATION.md)
- [API Documentation](./API.md)
- [Testing Guide](./TESTING.md)
