/**
 * Example integration of the compatibility layer
 * 
 * This file demonstrates how to integrate the backward compatibility layer
 * into the new graph API server.
 */

import express from 'express';
import { FeatureFlags } from '@kg-visualizer/shared';
import { CompatibilityService } from '../services/CompatibilityService';
import { CompatibilityRouter } from '../routes/CompatibilityRouter';
import {
  legacyResponseTransformer,
  legacyRequestTransformer,
  compatibilityHeaders,
  compatibilityErrorHandler
} from '../middleware/compatibility';

// Example feature flags configuration
const featureFlags: FeatureFlags = {
  NEW_CONTROLLER_LAYER: true,
  NEW_SERVICE_LAYER: false,
  NEW_REPOSITORY_PATTERN: true,
  NEW_MIDDLEWARE_STACK: true,
  NEW_CHAT_SERVICE: false,
  CIRCUIT_BREAKER_ENABLED: true,
  DUAL_EXECUTION_MODE: true,
  PERFORMANCE_MONITORING: true,
  AUTOMATIC_ROLLBACK: false,
  TRAFFIC_PERCENTAGE_NEW_API: 25,
  DEBUG_MODE: true,
  VERBOSE_LOGGING: true,
  MIGRATION_METRICS: true
};

/**
 * Example of setting up a new API server with backward compatibility
 */
export function createCompatibleApiServer(): express.Application {
  const app = express();
  
  // Basic middleware
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // Compatibility middleware (order is important)
  app.use(compatibilityHeaders);
  app.use(legacyRequestTransformer);
  app.use(legacyResponseTransformer);

  // Initialize compatibility service
  const compatibilityService = new CompatibilityService(
    process.env.LEGACY_API_URL || 'http://localhost:3002',
    featureFlags
  );

  // Create compatibility router
  const compatibilityRouter = new CompatibilityRouter(
    compatibilityService,
    featureFlags,
    process.env.LEGACY_API_URL || 'http://localhost:3002'
  );

  // Example: Register a new implementation for the graph initial endpoint
  compatibilityRouter.registerRoute({
    path: '/initial',
    method: 'GET',
    legacyEndpoint: '/api/graph/initial',
    newHandler: async (req, res, next) => {
      try {
        // New implementation logic here
        const result = await getInitialGraphData(req.query);
        
        res.json({
          success: true,
          data: result,
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      } catch (error) {
        next(error);
      }
    },
    enableDualExecution: true,
    enableFallback: true
  });

  // Register all legacy routes for compatibility
  compatibilityRouter.registerLegacyRoutes();

  // Mount the compatibility router
  app.use('/api/graph', compatibilityRouter.getRouter());

  // Health endpoint with compatibility
  app.get('/api/health', async (_req, res) => {
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      migrationPhase: {
        backend: {
          enabled: featureFlags.NEW_CONTROLLER_LAYER ? 1 : 0,
          total: 1,
          percentage: featureFlags.NEW_CONTROLLER_LAYER ? 100 : 0
        },
        services: {
          chatService: featureFlags.NEW_CHAT_SERVICE,
          circuitBreaker: featureFlags.CIRCUIT_BREAKER_ENABLED
        },
        traffic: {
          api: featureFlags.TRAFFIC_PERCENTAGE_NEW_API
        }
      }
    };

    res.json({
      success: true,
      data: health,
      timestamp: new Date().toISOString()
    });
  });

  // Feature flags endpoint with compatibility
  app.get('/api/feature-flags', (req, res) => {
    const userId = req.headers['x-user-id'] as string || req.ip;
    
    res.json({
      success: true,
      data: {
        flags: featureFlags,
        migrationPhase: {
          backend: {
            enabled: featureFlags.NEW_CONTROLLER_LAYER ? 1 : 0,
            total: 1,
            percentage: featureFlags.NEW_CONTROLLER_LAYER ? 100 : 0
          }
        },
        userId: userId ? userId.substring(0, 8) + '...' : 'anonymous',
      },
      timestamp: new Date().toISOString()
    });
  });

  // Error handling middleware (must be last)
  app.use(compatibilityErrorHandler);

  return app;
}

/**
 * Example new implementation of graph initial data
 */
async function getInitialGraphData(_query: any): Promise<any> {
  // This would be replaced with actual new implementation
  // For now, return mock data in the expected format
  return {
    nodes: [
      {
        id: '1',
        label: 'Product A',
        properties: { name: 'Product A', type: 'product' }
      },
      {
        id: '2',
        label: 'Feature B',
        properties: { name: 'Feature B', type: 'feature' }
      }
    ],
    edges: [
      {
        id: 'e1',
        from: '1',
        to: '2',
        type: 'CONTAINS',
        properties: {}
      }
    ]
  };
}

/**
 * Example of gradual migration strategy
 */
export class GradualMigrationExample {
  private compatibilityService: CompatibilityService;
  private featureFlags: FeatureFlags;

  constructor(compatibilityService: CompatibilityService, featureFlags: FeatureFlags) {
    this.compatibilityService = compatibilityService;
    this.featureFlags = featureFlags;
  }

  /**
   * Example of migrating a single endpoint with validation
   */
  async migrateGraphSearchEndpoint(req: express.Request, res: express.Response): Promise<void> {
    const newImplementation = async () => {
      // New search implementation
      const term = req.query.term as string;
      const limit = parseInt(req.query.limit as string) || 20;
      
      // Mock new search logic
      return {
        results: [
          { id: '1', name: `Search result for: ${term}`, score: 0.95 }
        ],
        total: 1,
        limit,
        term
      };
    };

    try {
      const result = await this.compatibilityService.dualExecute(
        newImplementation,
        '/api/graph/search',
        { term: req.query.term, limit: req.query.limit },
        {
          enableDualExecution: this.featureFlags.DUAL_EXECUTION_MODE,
          enableValidation: true,
          timeout: 10000
        }
      );

      // Log metrics for monitoring
      this.compatibilityService.logCompatibilityMetrics(
        req.path,
        result,
        true
      );

      // Return the new implementation result
      res.json({
        success: true,
        data: result.primary,
        timestamp: new Date().toISOString(),
        metadata: {
          executionTime: result.executionTime.primary,
          validationPassed: result.isValid,
          dualExecutionEnabled: !!result.secondary
        }
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Example of A/B testing with feature flags
   */
  async handleWithABTesting(
    req: express.Request,
    res: express.Response,
    newHandler: () => Promise<any>,
    legacyEndpoint: string
  ): Promise<void> {
    const userId = req.headers['x-user-id'] as string || req.ip;
    const userHash = this.hashUserId(userId || 'anonymous');
    
    // Use traffic percentage to determine which implementation to use
    const useNewImplementation = userHash < this.featureFlags.TRAFFIC_PERCENTAGE_NEW_API;

    if (useNewImplementation) {
      try {
        const result = await newHandler();
        res.json({
          success: true,
          data: result,
          timestamp: new Date().toISOString(),
          metadata: { implementation: 'new', userId: userId ? userId.substring(0, 8) + '...' : 'anonymous' }
        });
      } catch (error) {
        // Fallback to legacy on error
        await this.fallbackToLegacy(req, res, legacyEndpoint);
      }
    } else {
      await this.fallbackToLegacy(req, res, legacyEndpoint);
    }
  }

  private hashUserId(userId: string): number {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash) % 100;
  }

  private async fallbackToLegacy(
    _req: express.Request,
    res: express.Response,
    _legacyEndpoint: string
  ): Promise<void> {
    // This would proxy to the legacy API
    // Implementation would be similar to CompatibilityRouter.proxyToLegacy
    res.json({
      success: true,
      data: { message: 'Served by legacy implementation' },
      timestamp: new Date().toISOString(),
      metadata: { implementation: 'legacy' }
    });
  }
}

/**
 * Example usage
 */
export function startCompatibleServer(): void {
  const app = createCompatibleApiServer();
  const port = process.env.PORT || 3003;

  app.listen(port, () => {
    console.log(`Compatible API server running on port ${port}`);
    console.log('Backward compatibility layer active');
    console.log('Feature flags:', {
      newController: featureFlags.NEW_CONTROLLER_LAYER,
      dualExecution: featureFlags.DUAL_EXECUTION_MODE,
      trafficPercentage: featureFlags.TRAFFIC_PERCENTAGE_NEW_API
    });
  });
}

// Export for testing
export { featureFlags };
