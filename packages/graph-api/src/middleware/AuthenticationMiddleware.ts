/**
 * Authentication Middleware
 * 
 * Handles authentication and authorization for API endpoints.
 * Supports multiple authentication methods and role-based access control.
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { logger } from '@kg-visualizer/shared';

export interface AuthenticatedUser {
  id: string;
  email?: string;
  roles: string[];
  permissions: string[];
  sessionId?: string;
  isAdmin?: boolean;
}

export interface AuthenticationConfig {
  jwtSecret?: string;
  apiKeyHeader?: string;
  sessionCookieName?: string;
  adminApiKey?: string;
  enableGuestAccess?: boolean;
  requiredPermissions?: string[];
}

declare global {
  namespace Express {
    interface Request {
      user?: AuthenticatedUser;
      isAuthenticated?: boolean;
      authMethod?: 'jwt' | 'apikey' | 'session' | 'admin' | 'guest';
    }
  }
}

export class AuthenticationMiddleware {
  private readonly config: AuthenticationConfig;
  private readonly logger = logger.child('auth-middleware');

  constructor(config: AuthenticationConfig = {}) {
    this.config = {
      jwtSecret: process.env.JWT_SECRET || 'default-secret',
      apiKeyHeader: 'x-api-key',
      sessionCookieName: 'session-id',
      adminApiKey: process.env.ADMIN_API_KEY,
      enableGuestAccess: true,
      ...config
    };
  }

  /**
   * Main authentication middleware
   */
  authenticate = (options: { 
    required?: boolean; 
    roles?: string[]; 
    permissions?: string[];
    allowGuest?: boolean;
  } = {}) => {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      const { required = false, roles = [], permissions = [], allowGuest = true } = options;

      try {
        // Try different authentication methods
        const authResult = await this.tryAuthentication(req);

        if (authResult.success && authResult.user) {
          req.user = authResult.user;
          req.isAuthenticated = true;
          req.authMethod = authResult.method;

          // Check role requirements
          if (roles.length > 0 && !this.hasRequiredRoles(authResult.user, roles)) {
            this.logger.warn('Access denied - insufficient roles', 'auth-middleware', {
              userId: authResult.user.id,
              requiredRoles: roles,
              userRoles: authResult.user.roles,
              path: req.path
            });
            return this.sendUnauthorized(res, 'Insufficient roles');
          }

          // Check permission requirements
          if (permissions.length > 0 && !this.hasRequiredPermissions(authResult.user, permissions)) {
            this.logger.warn('Access denied - insufficient permissions', 'auth-middleware', {
              userId: authResult.user.id,
              requiredPermissions: permissions,
              userPermissions: authResult.user.permissions,
              path: req.path
            });
            return this.sendUnauthorized(res, 'Insufficient permissions');
          }

          this.logger.debug('Authentication successful', 'auth-middleware', {
            userId: authResult.user.id,
            method: authResult.method,
            roles: authResult.user.roles,
            path: req.path
          });

        } else if (required && !allowGuest) {
          this.logger.warn('Authentication required but not provided', 'auth-middleware', {
            path: req.path,
            method: req.method,
            ip: req.ip
          });
          return this.sendUnauthenticated(res, 'Authentication required');

        } else if (allowGuest && this.config.enableGuestAccess) {
          // Set guest user
          req.user = this.createGuestUser(req);
          req.isAuthenticated = false;
          req.authMethod = 'guest';

          this.logger.debug('Guest access granted', 'auth-middleware', {
            guestId: req.user.id,
            path: req.path
          });
        }

        next();

      } catch (error) {
        this.logger.error('Authentication error', 'auth-middleware', {
          error: (error as Error).message,
          path: req.path,
          method: req.method
        });

        if (required) {
          return this.sendUnauthenticated(res, 'Authentication failed');
        }

        // Continue as guest if authentication is not required
        if (allowGuest && this.config.enableGuestAccess) {
          req.user = this.createGuestUser(req);
          req.isAuthenticated = false;
          req.authMethod = 'guest';
        }

        next();
      }
    };
  };

  /**
   * Admin-only authentication middleware
   */
  requireAdmin = () => {
    return this.authenticate({ 
      required: true, 
      roles: ['admin'], 
      allowGuest: false 
    });
  };

  /**
   * API key authentication middleware
   */
  requireApiKey = () => {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      const apiKey = req.headers[this.config.apiKeyHeader!] as string;

      if (!apiKey) {
        return this.sendUnauthenticated(res, 'API key required');
      }

      // Validate API key (implement your validation logic)
      const isValid = await this.validateApiKey(apiKey);
      if (!isValid) {
        return this.sendUnauthorized(res, 'Invalid API key');
      }

      req.user = {
        id: `api-key-${apiKey.substring(0, 8)}`,
        roles: ['api-user'],
        permissions: ['read', 'write']
      };
      req.isAuthenticated = true;
      req.authMethod = 'apikey';

      next();
    };
  };

  /**
   * Try different authentication methods
   */
  private async tryAuthentication(req: Request): Promise<{
    success: boolean;
    user?: AuthenticatedUser;
    method?: 'jwt' | 'apikey' | 'session' | 'admin';
  }> {
    // Try admin API key first
    const adminResult = await this.tryAdminAuthentication(req);
    if (adminResult.success) {
      return adminResult;
    }

    // Try JWT authentication
    const jwtResult = await this.tryJwtAuthentication(req);
    if (jwtResult.success) {
      return jwtResult;
    }

    // Try API key authentication
    const apiKeyResult = await this.tryApiKeyAuthentication(req);
    if (apiKeyResult.success) {
      return apiKeyResult;
    }

    // Try session authentication
    const sessionResult = await this.trySessionAuthentication(req);
    if (sessionResult.success) {
      return sessionResult;
    }

    return { success: false };
  }

  /**
   * Try admin API key authentication
   */
  private async tryAdminAuthentication(req: Request): Promise<{
    success: boolean;
    user?: AuthenticatedUser;
    method?: 'admin';
  }> {
    const adminKey = req.headers['x-admin-key'] as string;
    
    if (adminKey && this.config.adminApiKey && adminKey === this.config.adminApiKey) {
      return {
        success: true,
        user: {
          id: 'admin',
          roles: ['admin'],
          permissions: ['*'],
          isAdmin: true
        },
        method: 'admin'
      };
    }

    return { success: false };
  }

  /**
   * Try JWT authentication
   */
  private async tryJwtAuthentication(req: Request): Promise<{
    success: boolean;
    user?: AuthenticatedUser;
    method?: 'jwt';
  }> {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { success: false };
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, this.config.jwtSecret!) as any;
      
      return {
        success: true,
        user: {
          id: decoded.sub || decoded.userId,
          email: decoded.email,
          roles: decoded.roles || ['user'],
          permissions: decoded.permissions || ['read'],
          sessionId: decoded.sessionId
        },
        method: 'jwt'
      };
    } catch (error) {
      return { success: false };
    }
  }

  /**
   * Try API key authentication
   */
  private async tryApiKeyAuthentication(req: Request): Promise<{
    success: boolean;
    user?: AuthenticatedUser;
    method?: 'apikey';
  }> {
    const apiKey = req.headers[this.config.apiKeyHeader!] as string;
    
    if (apiKey && await this.validateApiKey(apiKey)) {
      return {
        success: true,
        user: {
          id: `api-${apiKey.substring(0, 8)}`,
          roles: ['api-user'],
          permissions: ['read', 'write']
        },
        method: 'apikey'
      };
    }

    return { success: false };
  }

  /**
   * Try session authentication
   */
  private async trySessionAuthentication(req: Request): Promise<{
    success: boolean;
    user?: AuthenticatedUser;
    method?: 'session';
  }> {
    const sessionId = req.cookies?.[this.config.sessionCookieName!] || 
                     req.headers['x-session-id'] as string;

    if (sessionId && await this.validateSession(sessionId)) {
      // In a real implementation, you would look up the session in a store
      return {
        success: true,
        user: {
          id: `session-${sessionId.substring(0, 8)}`,
          roles: ['user'],
          permissions: ['read'],
          sessionId
        },
        method: 'session'
      };
    }

    return { success: false };
  }

  /**
   * Create guest user
   */
  private createGuestUser(req: Request): AuthenticatedUser {
    const guestId = `guest-${req.ip}-${Date.now()}`;
    
    return {
      id: guestId,
      roles: ['guest'],
      permissions: ['read']
    };
  }

  /**
   * Check if user has required roles
   */
  private hasRequiredRoles(user: AuthenticatedUser, requiredRoles: string[]): boolean {
    if (user.isAdmin || user.permissions.includes('*')) {
      return true;
    }

    return requiredRoles.some(role => user.roles.includes(role));
  }

  /**
   * Check if user has required permissions
   */
  private hasRequiredPermissions(user: AuthenticatedUser, requiredPermissions: string[]): boolean {
    if (user.isAdmin || user.permissions.includes('*')) {
      return true;
    }

    return requiredPermissions.every(permission => user.permissions.includes(permission));
  }

  /**
   * Validate API key (implement your validation logic)
   */
  private async validateApiKey(apiKey: string): Promise<boolean> {
    // Implement your API key validation logic here
    // This could involve checking against a database, cache, or external service
    return apiKey.length >= 32; // Simple validation for demo
  }

  /**
   * Validate session (implement your validation logic)
   */
  private async validateSession(sessionId: string): Promise<boolean> {
    // Implement your session validation logic here
    // This could involve checking against a session store
    return sessionId.length >= 16; // Simple validation for demo
  }

  /**
   * Send unauthenticated response
   */
  private sendUnauthenticated(res: Response, message: string): void {
    res.status(401).json({
      code: 'AUTHENTICATION_REQUIRED',
      message,
      statusCode: 401,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Send unauthorized response
   */
  private sendUnauthorized(res: Response, message: string): void {
    res.status(403).json({
      code: 'AUTHORIZATION_FAILED',
      message,
      statusCode: 403,
      timestamp: new Date().toISOString()
    });
  }
}
