/**
 * Tests for AuthenticationMiddleware
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AuthenticationMiddleware } from '../AuthenticationMiddleware';

// Mock logger
jest.mock('@kg-visualizer/shared', () => ({
  logger: {
    child: jest.fn(() => ({
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }))
  }
}));

describe('AuthenticationMiddleware', () => {
  let middleware: AuthenticationMiddleware;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    middleware = new AuthenticationMiddleware({
      jwtSecret: 'test-secret',
      adminApiKey: 'admin-key-123',
      enableGuestAccess: true
    });

    mockRequest = {
      headers: {},
      ip: '127.0.0.1',
      path: '/api/test',
      method: 'GET'
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();
  });

  describe('authenticate', () => {
    it('should allow guest access when authentication not required', async () => {
      const authMiddleware = middleware.authenticate({ required: false, allowGuest: true });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toBeDefined();
      expect(mockRequest.user?.roles).toContain('guest');
      expect(mockRequest.isAuthenticated).toBe(false);
      expect(mockRequest.authMethod).toBe('guest');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should reject when authentication required but not provided', async () => {
      const authMiddleware = middleware.authenticate({ required: true, allowGuest: false });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          code: 'AUTHENTICATION_REQUIRED',
          statusCode: 401
        })
      );
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should authenticate with valid JWT token', async () => {
      const token = jwt.sign(
        { 
          sub: 'user123', 
          email: '<EMAIL>',
          roles: ['user'],
          permissions: ['read', 'write']
        }, 
        'test-secret'
      );

      mockRequest.headers = {
        authorization: `Bearer ${token}`
      };

      const authMiddleware = middleware.authenticate({ required: true });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toBeDefined();
      expect(mockRequest.user?.id).toBe('user123');
      expect(mockRequest.user?.email).toBe('<EMAIL>');
      expect(mockRequest.user?.roles).toContain('user');
      expect(mockRequest.isAuthenticated).toBe(true);
      expect(mockRequest.authMethod).toBe('jwt');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should authenticate with admin API key', async () => {
      mockRequest.headers = {
        'x-admin-key': 'admin-key-123'
      };

      const authMiddleware = middleware.authenticate({ required: true });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toBeDefined();
      expect(mockRequest.user?.id).toBe('admin');
      expect(mockRequest.user?.roles).toContain('admin');
      expect(mockRequest.user?.isAdmin).toBe(true);
      expect(mockRequest.isAuthenticated).toBe(true);
      expect(mockRequest.authMethod).toBe('admin');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should authenticate with valid API key', async () => {
      mockRequest.headers = {
        'x-api-key': 'valid-api-key-with-32-characters'
      };

      const authMiddleware = middleware.authenticate({ required: true });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toBeDefined();
      expect(mockRequest.user?.roles).toContain('api-user');
      expect(mockRequest.user?.permissions).toContain('read');
      expect(mockRequest.user?.permissions).toContain('write');
      expect(mockRequest.isAuthenticated).toBe(true);
      expect(mockRequest.authMethod).toBe('apikey');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should reject invalid JWT token', async () => {
      mockRequest.headers = {
        authorization: 'Bearer invalid-token'
      };

      const authMiddleware = middleware.authenticate({ required: true, allowGuest: false });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject insufficient roles', async () => {
      const token = jwt.sign(
        { 
          sub: 'user123', 
          roles: ['user'],
          permissions: ['read']
        }, 
        'test-secret'
      );

      mockRequest.headers = {
        authorization: `Bearer ${token}`
      };

      const authMiddleware = middleware.authenticate({ 
        required: true, 
        roles: ['admin'] 
      });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          code: 'AUTHORIZATION_FAILED',
          statusCode: 403
        })
      );
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject insufficient permissions', async () => {
      const token = jwt.sign(
        { 
          sub: 'user123', 
          roles: ['user'],
          permissions: ['read']
        }, 
        'test-secret'
      );

      mockRequest.headers = {
        authorization: `Bearer ${token}`
      };

      const authMiddleware = middleware.authenticate({ 
        required: true, 
        permissions: ['write', 'admin'] 
      });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should allow admin users to bypass role/permission checks', async () => {
      mockRequest.headers = {
        'x-admin-key': 'admin-key-123'
      };

      const authMiddleware = middleware.authenticate({ 
        required: true, 
        roles: ['super-admin'],
        permissions: ['nuclear-launch']
      });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user?.isAdmin).toBe(true);
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('requireAdmin', () => {
    it('should allow admin users', async () => {
      mockRequest.headers = {
        'x-admin-key': 'admin-key-123'
      };

      const adminMiddleware = middleware.requireAdmin();
      
      await adminMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user?.isAdmin).toBe(true);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should reject non-admin users', async () => {
      const token = jwt.sign(
        { 
          sub: 'user123', 
          roles: ['user'],
          permissions: ['read']
        }, 
        'test-secret'
      );

      mockRequest.headers = {
        authorization: `Bearer ${token}`
      };

      const adminMiddleware = middleware.requireAdmin();
      
      await adminMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('requireApiKey', () => {
    it('should allow valid API key', async () => {
      mockRequest.headers = {
        'x-api-key': 'valid-api-key-with-32-characters'
      };

      const apiKeyMiddleware = middleware.requireApiKey();
      
      await apiKeyMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toBeDefined();
      expect(mockRequest.authMethod).toBe('apikey');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should reject missing API key', async () => {
      const apiKeyMiddleware = middleware.requireApiKey();
      
      await apiKeyMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          code: 'AUTHENTICATION_REQUIRED',
          message: 'API key required'
        })
      );
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject invalid API key', async () => {
      mockRequest.headers = {
        'x-api-key': 'short'
      };

      const apiKeyMiddleware = middleware.requireApiKey();
      
      await apiKeyMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          code: 'AUTHORIZATION_FAILED',
          message: 'Invalid API key'
        })
      );
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('session authentication', () => {
    it('should authenticate with valid session', async () => {
      mockRequest.headers = {
        'x-session-id': 'valid-session-id-123'
      };

      const authMiddleware = middleware.authenticate({ required: true });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toBeDefined();
      expect(mockRequest.user?.sessionId).toBe('valid-session-id-123');
      expect(mockRequest.authMethod).toBe('session');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should reject invalid session', async () => {
      mockRequest.headers = {
        'x-session-id': 'short'
      };

      const authMiddleware = middleware.authenticate({ required: true, allowGuest: false });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('should handle authentication errors gracefully', async () => {
      // Mock JWT verify to throw an error
      jest.spyOn(jwt, 'verify').mockImplementation(() => {
        throw new Error('Token verification failed');
      });

      mockRequest.headers = {
        authorization: 'Bearer some-token'
      };

      const authMiddleware = middleware.authenticate({ required: false, allowGuest: true });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user?.roles).toContain('guest');
      expect(mockNext).toHaveBeenCalled();

      // Restore original implementation
      (jwt.verify as jest.Mock).mockRestore();
    });

    it('should fail when required authentication throws error', async () => {
      // Mock JWT verify to throw an error
      jest.spyOn(jwt, 'verify').mockImplementation(() => {
        throw new Error('Token verification failed');
      });

      mockRequest.headers = {
        authorization: 'Bearer some-token'
      };

      const authMiddleware = middleware.authenticate({ required: true, allowGuest: false });
      
      await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockNext).not.toHaveBeenCalled();

      // Restore original implementation
      (jwt.verify as jest.Mock).mockRestore();
    });
  });
});
