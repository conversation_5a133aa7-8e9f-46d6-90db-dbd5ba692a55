/**
 * Security Middleware
 * 
 * Comprehensive security middleware including CORS, rate limiting,
 * security headers, and request sanitization.
 */

import { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { logger } from '@kg-visualizer/shared';

export interface SecurityConfig {
  cors?: {
    origins?: string[] | string;
    credentials?: boolean;
    methods?: string[];
    allowedHeaders?: string[];
    exposedHeaders?: string[];
    maxAge?: number;
  };
  rateLimit?: {
    windowMs?: number;
    max?: number;
    message?: string;
    standardHeaders?: boolean;
    legacyHeaders?: boolean;
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
  };
  helmet?: {
    contentSecurityPolicy?: any;
    crossOriginEmbedderPolicy?: boolean;
    crossOriginOpenerPolicy?: boolean;
    crossOriginResourcePolicy?: any;
    dnsPrefetchControl?: boolean;
    frameguard?: any;
    hidePoweredBy?: boolean;
    hsts?: any;
    ieNoOpen?: boolean;
    noSniff?: boolean;
    originAgentCluster?: boolean;
    permittedCrossDomainPolicies?: boolean;
    referrerPolicy?: any;
    xssFilter?: boolean;
  };
  trustedProxies?: string[];
  enableSanitization?: boolean;
  enableRequestSizeLimit?: boolean;
  maxRequestSize?: string;
}

export interface RateLimitInfo {
  limit: number;
  current: number;
  remaining: number;
  resetTime: Date;
}

declare global {
  namespace Express {
    interface Request {
      rateLimit?: RateLimitInfo;
      isSecure?: boolean;
      trustedProxy?: boolean;
    }
  }
}

export class SecurityMiddleware {
  private readonly config: SecurityConfig;
  private readonly logger = logger.child('security-middleware');

  constructor(config: SecurityConfig = {}) {
    this.config = {
      cors: {
        origins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Correlation-ID', 'X-API-Key'],
        exposedHeaders: ['X-Total-Count', 'X-Rate-Limit-Remaining', 'X-Rate-Limit-Reset'],
        maxAge: 86400 // 24 hours
      },
      rateLimit: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100, // Limit each IP to 100 requests per windowMs
        message: 'Too many requests from this IP, please try again later.',
        standardHeaders: true,
        legacyHeaders: false,
        skipSuccessfulRequests: false,
        skipFailedRequests: false
      },
      helmet: {
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", 'data:', 'blob:', 'https:'],
            connectSrc: ["'self'", 'ws:', 'wss:'],
            fontSrc: ["'self'", 'https:', 'data:'],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"]
          }
        },
        crossOriginEmbedderPolicy: false,
        crossOriginOpenerPolicy: false,
        crossOriginResourcePolicy: { policy: 'cross-origin' },
        hsts: {
          maxAge: 31536000,
          includeSubDomains: true,
          preload: true
        }
      },
      trustedProxies: ['127.0.0.1', '::1'],
      enableSanitization: true,
      enableRequestSizeLimit: true,
      maxRequestSize: '10mb',
      ...config
    };
  }

  /**
   * Configure CORS middleware
   */
  cors = () => {
    const corsOptions: cors.CorsOptions = {
      origin: (origin, callback) => {
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) return callback(null, true);

        const allowedOrigins = Array.isArray(this.config.cors!.origins) 
          ? this.config.cors!.origins 
          : [this.config.cors!.origins as string];

        // Check if origin is allowed
        if (allowedOrigins.includes('*') || allowedOrigins.includes(origin)) {
          return callback(null, true);
        }

        // Check for wildcard patterns
        const isAllowed = allowedOrigins.some(allowedOrigin => {
          if (allowedOrigin.includes('*')) {
            const pattern = allowedOrigin.replace(/\*/g, '.*');
            return new RegExp(`^${pattern}$`).test(origin);
          }
          return false;
        });

        if (isAllowed) {
          return callback(null, true);
        }

        this.logger.warn('CORS origin blocked', 'security-middleware', {
          origin,
          allowedOrigins,
          userAgent: 'unknown' // Will be filled by actual request
        });

        callback(new Error('Not allowed by CORS'));
      },
      credentials: this.config.cors!.credentials,
      methods: this.config.cors!.methods,
      allowedHeaders: this.config.cors!.allowedHeaders,
      exposedHeaders: this.config.cors!.exposedHeaders,
      maxAge: this.config.cors!.maxAge,
      optionsSuccessStatus: 200
    };

    return cors(corsOptions);
  };

  /**
   * Configure rate limiting middleware
   */
  rateLimit = (customConfig?: Partial<SecurityConfig['rateLimit']>) => {
    const config = { ...this.config.rateLimit, ...customConfig };

    return rateLimit({
      windowMs: config.windowMs,
      max: config.max,
      message: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: config.message,
        statusCode: 429,
        timestamp: new Date().toISOString()
      },
      standardHeaders: config.standardHeaders,
      legacyHeaders: config.legacyHeaders,
      skipSuccessfulRequests: config.skipSuccessfulRequests,
      skipFailedRequests: config.skipFailedRequests,
      handler: (req: Request, res: Response) => {
        this.logger.warn('Rate limit exceeded', 'security-middleware', {
          ip: req.ip,
          path: req.path,
          method: req.method,
          userAgent: req.get('User-Agent'),
          userId: (req as any).user?.id
        });

        res.status(429).json({
          code: 'RATE_LIMIT_EXCEEDED',
          message: config.message,
          statusCode: 429,
          timestamp: new Date().toISOString(),
          retryAfter: Math.round(config.windowMs! / 1000)
        });
      },
      onLimitReached: (req: Request) => {
        this.logger.error('Rate limit threshold reached', 'security-middleware', {
          ip: req.ip,
          path: req.path,
          method: req.method,
          limit: config.max
        });
      }
    });
  };

  /**
   * Configure security headers middleware
   */
  securityHeaders = () => {
    return helmet(this.config.helmet);
  };

  /**
   * Request sanitization middleware
   */
  sanitization = () => {
    return (req: Request, res: Response, next: NextFunction): void => {
      if (!this.config.enableSanitization) {
        return next();
      }

      try {
        // Sanitize query parameters
        if (req.query) {
          req.query = this.sanitizeObject(req.query);
        }

        // Sanitize request body
        if (req.body) {
          req.body = this.sanitizeObject(req.body);
        }

        // Sanitize path parameters
        if (req.params) {
          req.params = this.sanitizeObject(req.params);
        }

        next();

      } catch (error) {
        this.logger.error('Request sanitization failed', 'security-middleware', {
          error: (error as Error).message,
          path: req.path,
          method: req.method
        });

        res.status(400).json({
          code: 'INVALID_REQUEST',
          message: 'Request contains invalid characters',
          statusCode: 400,
          timestamp: new Date().toISOString()
        });
      }
    };
  };

  /**
   * Trusted proxy configuration
   */
  trustedProxy = () => {
    return (req: Request, _res: Response, next: NextFunction): void => {
      const forwardedFor = req.get('X-Forwarded-For');
      const realIp = req.get('X-Real-IP');
      
      if (forwardedFor || realIp) {
        const proxyIp = req.connection.remoteAddress;
        req.trustedProxy = this.config.trustedProxies!.includes(proxyIp || '');
        
        if (!req.trustedProxy) {
          this.logger.warn('Untrusted proxy detected', 'security-middleware', {
            proxyIp,
            forwardedFor,
            realIp,
            path: req.path
          });
        }
      }

      next();
    };
  };

  /**
   * Security audit middleware
   */
  securityAudit = () => {
    return (req: Request, _res: Response, next: NextFunction): void => {
      const securityFlags = {
        hasAuth: !!req.headers.authorization,
        hasApiKey: !!req.headers['x-api-key'],
        isHttps: req.secure || req.get('X-Forwarded-Proto') === 'https',
        hasUserAgent: !!req.get('User-Agent'),
        hasReferer: !!req.get('Referer'),
        suspiciousHeaders: this.detectSuspiciousHeaders(req.headers)
      };

      // Log security audit
      this.logger.debug('Security audit', 'security-middleware', {
        path: req.path,
        method: req.method,
        ip: req.ip,
        ...securityFlags
      });

      // Add security info to request
      req.isSecure = securityFlags.isHttps;

      next();
    };
  };

  /**
   * Content Security Policy violation reporting
   */
  cspReporting = () => {
    return (req: Request, res: Response, next: NextFunction): void => {
      if (req.path === '/csp-violation-report' && req.method === 'POST') {
        this.logger.warn('CSP violation reported', 'security-middleware', {
          violation: req.body,
          userAgent: req.get('User-Agent'),
          ip: req.ip
        });

        res.status(204).send();
        return;
      }

      next();
    };
  };

  /**
   * Sanitize object recursively
   */
  private sanitizeObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return this.sanitizeValue(obj);
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item));
    }

    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const sanitizedKey = this.sanitizeValue(key);
      sanitized[sanitizedKey] = this.sanitizeObject(value);
    }

    return sanitized;
  }

  /**
   * Sanitize individual value
   */
  private sanitizeValue(value: any): any {
    if (typeof value !== 'string') {
      return value;
    }

    // Remove potentially dangerous characters
    return value
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .replace(/[<>'"]/g, '') // Remove HTML characters
      .trim();
  }

  /**
   * Detect suspicious headers
   */
  private detectSuspiciousHeaders(headers: any): string[] {
    const suspicious: string[] = [];
    const suspiciousPatterns = [
      'x-forwarded-host',
      'x-original-url',
      'x-rewrite-url',
      'x-cluster-client-ip'
    ];

    for (const [key, value] of Object.entries(headers)) {
      if (suspiciousPatterns.includes(key.toLowerCase())) {
        suspicious.push(key);
      }

      // Check for suspicious values
      if (typeof value === 'string') {
        if (value.includes('<script>') || value.includes('javascript:')) {
          suspicious.push(`${key}:suspicious-content`);
        }
      }
    }

    return suspicious;
  }

  /**
   * Get security statistics
   */
  getStatistics(): {
    corsViolations: number;
    rateLimitViolations: number;
    suspiciousRequests: number;
    cspViolations: number;
  } {
    // In a real implementation, you would track these metrics
    return {
      corsViolations: 0,
      rateLimitViolations: 0,
      suspiciousRequests: 0,
      cspViolations: 0
    };
  }
}

// Pre-configured security middleware
export const securityMiddleware = new SecurityMiddleware();

// Export common middleware functions
export const configureCors = securityMiddleware.cors();
export const configureRateLimit = securityMiddleware.rateLimit();
export const configureSecurityHeaders = securityMiddleware.securityHeaders();
export const configureSanitization = securityMiddleware.sanitization();
export const configureTrustedProxy = securityMiddleware.trustedProxy();
export const configureSecurityAudit = securityMiddleware.securityAudit();
export const configureCspReporting = securityMiddleware.cspReporting();

// Environment-specific configurations
export const createProductionSecurity = () => new SecurityMiddleware({
  cors: {
    origins: process.env.CORS_ORIGINS?.split(',') || [],
    credentials: true
  },
  rateLimit: {
    windowMs: 15 * 60 * 1000,
    max: 50 // Stricter in production
  },
  helmet: {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'"],
        styleSrc: ["'self'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"]
      }
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  }
});

export const createDevelopmentSecurity = () => new SecurityMiddleware({
  cors: {
    origins: ['*'], // Allow all origins in development
    credentials: true
  },
  rateLimit: {
    windowMs: 15 * 60 * 1000,
    max: 1000 // More lenient in development
  },
  helmet: {
    contentSecurityPolicy: false // Disable CSP in development
  }
});
