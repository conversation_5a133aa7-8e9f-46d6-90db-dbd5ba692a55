/**
 * Validation Middleware
 * 
 * Comprehensive request validation middleware using Joi schemas.
 * Provides validation for query parameters, body, headers, and custom rules.
 */

import { Request, Response, NextFunction } from 'express';
import Joi from 'joi';
import { logger } from '@kg-visualizer/shared';

export interface ValidationConfig {
  abortEarly?: boolean;
  allowUnknown?: boolean;
  stripUnknown?: boolean;
  skipFunctions?: boolean;
}

export interface ValidationSchemas {
  query?: Joi.ObjectSchema;
  body?: Joi.ObjectSchema;
  params?: Joi.ObjectSchema;
  headers?: Joi.ObjectSchema;
}

export interface ValidationError {
  field: string;
  message: string;
  value: any;
  type: string;
}

export class ValidationMiddleware {
  private readonly config: ValidationConfig;
  private readonly logger = logger.child('validation-middleware');

  constructor(config: ValidationConfig = {}) {
    this.config = {
      abortEarly: false,
      allowUnknown: true,
      stripUnknown: true,
      skipFunctions: true,
      ...config
    };
  }

  /**
   * Create validation middleware for specific schemas
   */
  validate = (schemas: ValidationSchemas) => {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const errors: ValidationError[] = [];

        // Validate query parameters
        if (schemas.query) {
          const queryResult = this.validateSchema(schemas.query, req.query, 'query');
          if (queryResult.errors.length > 0) {
            errors.push(...queryResult.errors);
          } else {
            req.query = queryResult.value;
          }
        }

        // Validate request body
        if (schemas.body) {
          const bodyResult = this.validateSchema(schemas.body, req.body, 'body');
          if (bodyResult.errors.length > 0) {
            errors.push(...bodyResult.errors);
          } else {
            req.body = bodyResult.value;
          }
        }

        // Validate path parameters
        if (schemas.params) {
          const paramsResult = this.validateSchema(schemas.params, req.params, 'params');
          if (paramsResult.errors.length > 0) {
            errors.push(...paramsResult.errors);
          } else {
            req.params = paramsResult.value;
          }
        }

        // Validate headers
        if (schemas.headers) {
          const headersResult = this.validateSchema(schemas.headers, req.headers, 'headers');
          if (headersResult.errors.length > 0) {
            errors.push(...headersResult.errors);
          }
        }

        if (errors.length > 0) {
          this.logger.warn('Validation failed', 'validation-middleware', {
            path: req.path,
            method: req.method,
            errors: errors.slice(0, 5), // Log first 5 errors
            errorCount: errors.length
          });

          return this.sendValidationError(res, errors);
        }

        this.logger.debug('Validation passed', 'validation-middleware', {
          path: req.path,
          method: req.method,
          schemasValidated: Object.keys(schemas)
        });

        next();

      } catch (error) {
        this.logger.error('Validation middleware error', 'validation-middleware', {
          error: (error as Error).message,
          path: req.path,
          method: req.method
        });

        res.status(500).json({
          code: 'VALIDATION_ERROR',
          message: 'Internal validation error',
          statusCode: 500,
          timestamp: new Date().toISOString()
        });
      }
    };
  };

  /**
   * Validate a single schema
   */
  private validateSchema(schema: Joi.ObjectSchema, data: any, source: string): {
    value: any;
    errors: ValidationError[];
  } {
    const { error, value } = schema.validate(data, this.config);

    if (error) {
      const errors: ValidationError[] = error.details.map(detail => ({
        field: `${source}.${detail.path.join('.')}`,
        message: detail.message,
        value: detail.context?.value,
        type: detail.type
      }));

      return { value: data, errors };
    }

    return { value, errors: [] };
  }

  /**
   * Send validation error response
   */
  private sendValidationError(res: Response, errors: ValidationError[]): void {
    res.status(400).json({
      code: 'VALIDATION_FAILED',
      message: 'Request validation failed',
      statusCode: 400,
      timestamp: new Date().toISOString(),
      errors: errors.map(error => ({
        field: error.field,
        message: error.message,
        value: error.value
      }))
    });
  }
}

// Common validation schemas
export const CommonSchemas = {
  // Pagination
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(1000).default(20),
    offset: Joi.number().integer().min(0)
  }),

  // Graph query parameters
  graphQuery: Joi.object({
    limit: Joi.number().integer().min(1).max(1000).default(100),
    nodeTypes: Joi.array().items(
      Joi.string().valid('Product', 'Feature', 'Workflow', 'Module', 'TestCase', 'UI_Area')
    ),
    relationshipTypes: Joi.array().items(
      Joi.string().valid('IMPLEMENTS', 'DEPENDS_ON', 'INTEGRATES_WITH', 'USES', 'CONFIGURES', 'VALIDATES')
    ),
    includeProperties: Joi.boolean().default(true),
    format: Joi.string().valid('graph', 'table', 'json').default('graph')
  }),

  // Search parameters
  search: Joi.object({
    term: Joi.string().min(1).max(100).required(),
    limit: Joi.number().integer().min(1).max(100).default(20),
    fuzzy: Joi.boolean().default(false),
    caseSensitive: Joi.boolean().default(false)
  }),

  // Node creation/update
  node: Joi.object({
    name: Joi.string().min(1).max(255).required(),
    type: Joi.string().valid('Product', 'Feature', 'Workflow', 'Module', 'TestCase', 'UI_Area').required(),
    properties: Joi.object().default({}),
    description: Joi.string().max(1000),
    tags: Joi.array().items(Joi.string().max(50))
  }),

  // Relationship creation/update
  relationship: Joi.object({
    fromNode: Joi.string().required(),
    toNode: Joi.string().required(),
    type: Joi.string().valid('IMPLEMENTS', 'DEPENDS_ON', 'INTEGRATES_WITH', 'USES', 'CONFIGURES', 'VALIDATES').required(),
    properties: Joi.object().default({}),
    weight: Joi.number().min(0).max(1).default(1)
  }),

  // Analysis parameters
  analysis: Joi.object({
    algorithm: Joi.string().valid('pagerank', 'betweenness', 'closeness', 'degree', 'eigenvector').required(),
    limit: Joi.number().integer().min(1).max(100).default(20),
    threshold: Joi.number().min(0).max(1).default(0.1),
    includeMetadata: Joi.boolean().default(true)
  }),

  // Clustering parameters
  clustering: Joi.object({
    algorithm: Joi.string().valid('louvain', 'leiden', 'label_propagation', 'weakly_connected').required(),
    resolution: Joi.number().min(0.1).max(2.0).default(1.0),
    iterations: Joi.number().integer().min(1).max(100).default(10),
    randomSeed: Joi.number().integer()
  }),

  // Path finding parameters
  pathFinding: Joi.object({
    startNode: Joi.string().required(),
    endNode: Joi.string().required(),
    maxDepth: Joi.number().integer().min(1).max(10).default(5),
    algorithm: Joi.string().valid('shortest', 'all', 'dijkstra').default('shortest'),
    relationshipTypes: Joi.array().items(Joi.string())
  }),

  // Filter parameters
  filters: Joi.object({
    nodeLabels: Joi.array().items(Joi.string()),
    relationshipTypes: Joi.array().items(Joi.string()),
    properties: Joi.array().items(
      Joi.object({
        key: Joi.string().required(),
        operator: Joi.string().valid('equals', 'contains', 'startsWith', 'endsWith', 'gt', 'lt', 'gte', 'lte').required(),
        value: Joi.alternatives().try(Joi.string(), Joi.number(), Joi.boolean()).required()
      })
    ),
    dateRange: Joi.object({
      start: Joi.date().iso().required(),
      end: Joi.date().iso().min(Joi.ref('start')).required(),
      property: Joi.string().required()
    })
  }),

  // Chat parameters
  chat: Joi.object({
    message: Joi.string().min(1).max(2000).required(),
    sessionId: Joi.string().uuid(),
    context: Joi.object(),
    model: Joi.string().valid('gpt-4', 'gpt-3.5-turbo', 'claude-3', 'gemini-pro').default('gpt-4'),
    temperature: Joi.number().min(0).max(2).default(0.7),
    maxTokens: Joi.number().integer().min(1).max(4000).default(1000)
  }),

  // Feature flags
  featureFlags: Joi.object({
    flags: Joi.object().pattern(
      Joi.string(),
      Joi.alternatives().try(Joi.boolean(), Joi.number().min(0).max(100))
    ).required()
  }),

  // Health check parameters
  healthCheck: Joi.object({
    includeDetails: Joi.boolean().default(false),
    timeout: Joi.number().integer().min(1000).max(30000).default(5000)
  }),

  // Common headers
  headers: Joi.object({
    'content-type': Joi.string().valid('application/json').when('$method', {
      is: Joi.string().valid('POST', 'PUT', 'PATCH'),
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    'x-correlation-id': Joi.string().uuid(),
    'x-user-id': Joi.string(),
    'x-session-id': Joi.string(),
    'authorization': Joi.string().pattern(/^Bearer .+$/),
    'x-api-key': Joi.string().min(16),
    'user-agent': Joi.string()
  }).unknown(true),

  // ID parameter
  id: Joi.object({
    id: Joi.alternatives().try(
      Joi.string().min(1),
      Joi.number().integer().positive()
    ).required()
  }),

  // UUID parameter
  uuid: Joi.object({
    id: Joi.string().uuid().required()
  })
};

// Pre-configured validation middleware instances
export const validationMiddleware = new ValidationMiddleware();

// Common validation middleware functions
export const validatePagination = validationMiddleware.validate({
  query: CommonSchemas.pagination
});

export const validateGraphQuery = validationMiddleware.validate({
  query: CommonSchemas.graphQuery
});

export const validateSearch = validationMiddleware.validate({
  query: CommonSchemas.search
});

export const validateNode = validationMiddleware.validate({
  body: CommonSchemas.node
});

export const validateRelationship = validationMiddleware.validate({
  body: CommonSchemas.relationship
});

export const validateAnalysis = validationMiddleware.validate({
  query: CommonSchemas.analysis
});

export const validateClustering = validationMiddleware.validate({
  body: CommonSchemas.clustering
});

export const validatePathFinding = validationMiddleware.validate({
  query: CommonSchemas.pathFinding
});

export const validateFilters = validationMiddleware.validate({
  body: CommonSchemas.filters
});

export const validateChat = validationMiddleware.validate({
  body: CommonSchemas.chat
});

export const validateId = validationMiddleware.validate({
  params: CommonSchemas.id
});

export const validateUuid = validationMiddleware.validate({
  params: CommonSchemas.uuid
});

export const validateHeaders = validationMiddleware.validate({
  headers: CommonSchemas.headers
});

// Custom validation helpers
export const createCustomValidation = (schemas: ValidationSchemas, config?: ValidationConfig) => {
  const middleware = new ValidationMiddleware(config);
  return middleware.validate(schemas);
};
