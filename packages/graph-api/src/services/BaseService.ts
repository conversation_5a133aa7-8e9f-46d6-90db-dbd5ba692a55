/**
 * Base Service
 * 
 * Abstract base class for all services providing common functionality
 * like error handling, logging, caching, and performance monitoring.
 */

import { logger } from '@kg-visualizer/shared';

class PerformanceTimer {
  private startTime: number;

  constructor() {
    this.startTime = Date.now();
  }

  elapsed(): number {
    return Date.now() - this.startTime;
  }
}

export interface ServiceConfig {
  enableCaching?: boolean;
  cacheTimeout?: number;
  enableMetrics?: boolean;
  timeout?: number;
}

export interface ServiceMetrics {
  operationName: string;
  duration: number;
  success: boolean;
  error?: string;
  cacheHit?: boolean;
  timestamp: string;
}

export abstract class BaseService {
  protected readonly logger = logger.child('service');
  protected readonly config: ServiceConfig;
  private readonly metrics: ServiceMetrics[] = [];
  private readonly cache: Map<string, { data: any; expires: number }> = new Map();

  constructor(config: ServiceConfig = {}) {
    this.config = {
      enableCaching: false,
      cacheTimeout: 300000, // 5 minutes
      enableMetrics: true,
      timeout: 30000, // 30 seconds
      ...config
    };
  }

  /**
   * Execute operation with error handling, timing, and caching
   */
  protected async executeOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    cacheKey?: string
  ): Promise<T> {
    const timer = new PerformanceTimer();
    let cacheHit = false;

    try {
      // Check cache first
      if (cacheKey && this.config.enableCaching) {
        const cached = this.getFromCache<T>(cacheKey);
        if (cached) {
          cacheHit = true;
          this.recordMetrics(operationName, timer.elapsed(), true, undefined, cacheHit);
          return cached;
        }
      }

      // Execute operation with timeout
      const result = await this.withTimeout(operation(), this.config.timeout!);

      // Cache result if caching is enabled
      if (cacheKey && this.config.enableCaching) {
        this.setCache(cacheKey, result);
      }

      this.recordMetrics(operationName, timer.elapsed(), true, undefined, cacheHit);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.recordMetrics(operationName, timer.elapsed(), false, errorMessage, cacheHit);
      
      this.logger.error(`Service operation failed: ${operationName}`, 'service', {
        operationName,
        error: errorMessage,
        duration: timer.elapsed()
      });

      throw error;
    }
  }

  /**
   * Execute operation with retry logic
   */
  protected async executeWithRetry<T>(
    operationName: string,
    operation: () => Promise<T>,
    maxRetries: number = 3,
    retryDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.executeOperation(operationName, operation);
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          break;
        }

        this.logger.warn(`Operation failed, retrying (${attempt}/${maxRetries})`, 'service', {
          operationName,
          attempt,
          error: lastError.message
        });

        await this.delay(retryDelay * attempt);
      }
    }

    throw lastError!;
  }

  /**
   * Validate input parameters
   */
  protected validateInput(
    input: any,
    validator: (input: any) => { isValid: boolean; errors: string[] }
  ): void {
    const validation = validator(input);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }
  }

  /**
   * Transform data using provided transformer
   */
  protected transformData<TInput, TOutput>(
    data: TInput,
    transformer: (input: TInput) => TOutput
  ): TOutput {
    try {
      return transformer(data);
    } catch (error) {
      this.logger.error('Data transformation failed', 'service', {
        error: (error as Error).message
      });
      throw new Error(`Data transformation failed: ${(error as Error).message}`);
    }
  }

  /**
   * Get cached data
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) {
      return null;
    }

    if (Date.now() > cached.expires) {
      this.cache.delete(key);
      return null;
    }

    return cached.data as T;
  }

  /**
   * Set cache data
   */
  private setCache<T>(key: string, data: T): void {
    const expires = Date.now() + this.config.cacheTimeout!;
    this.cache.set(key, { data, expires });
  }

  /**
   * Clear cache
   */
  protected clearCache(pattern?: string): void {
    if (pattern) {
      const regex = new RegExp(pattern);
      for (const key of this.cache.keys()) {
        if (regex.test(key)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  /**
   * Add timeout to promise
   */
  private async withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  /**
   * Delay execution
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Record operation metrics
   */
  private recordMetrics(
    operationName: string,
    duration: number,
    success: boolean,
    error?: string,
    cacheHit?: boolean
  ): void {
    if (!this.config.enableMetrics) {
      return;
    }

    const metric: ServiceMetrics = {
      operationName,
      duration,
      success,
      error,
      cacheHit,
      timestamp: new Date().toISOString()
    };

    this.metrics.push(metric);

    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics.splice(0, this.metrics.length - 1000);
    }

    this.logger.debug('Service operation completed', 'service', metric);
  }

  /**
   * Get service metrics
   */
  getMetrics(): ServiceMetrics[] {
    return [...this.metrics];
  }

  /**
   * Get service statistics
   */
  getStatistics(): {
    totalOperations: number;
    successRate: number;
    averageDuration: number;
    cacheHitRate: number;
    recentErrors: string[];
  } {
    const total = this.metrics.length;
    const successful = this.metrics.filter(m => m.success).length;
    const cacheHits = this.metrics.filter(m => m.cacheHit).length;
    const totalDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0);
    const recentErrors = this.metrics
      .filter(m => !m.success && m.error)
      .slice(-10)
      .map(m => m.error!);

    return {
      totalOperations: total,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      averageDuration: total > 0 ? totalDuration / total : 0,
      cacheHitRate: total > 0 ? (cacheHits / total) * 100 : 0,
      recentErrors
    };
  }

  /**
   * Health check for the service
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: Record<string, any>;
  }> {
    const stats = this.getStatistics();
    
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    // Check success rate
    if (stats.successRate < 50) {
      status = 'unhealthy';
    } else if (stats.successRate < 90) {
      status = 'degraded';
    }

    // Check average response time
    if (stats.averageDuration > 10000) { // 10 seconds
      status = status === 'healthy' ? 'degraded' : 'unhealthy';
    }

    return {
      status,
      details: {
        ...stats,
        cacheSize: this.cache.size,
        configuredTimeout: this.config.timeout,
        cachingEnabled: this.config.enableCaching
      }
    };
  }

  /**
   * Reset service state
   */
  reset(): void {
    this.metrics.length = 0;
    this.cache.clear();
    this.logger.info('Service state reset', 'service');
  }
}
