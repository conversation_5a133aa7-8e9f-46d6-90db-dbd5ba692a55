/**
 * Analysis Service
 * 
 * Handles graph analysis operations including centrality measures,
 * community detection, path finding, and link prediction using Neo4j GDS.
 */

import { Driver, Session } from 'neo4j-driver';
import { BaseService, ServiceConfig } from './BaseService';

export type CentralityType = 'degree' | 'betweenness' | 'closeness' | 'pagerank' | 'eigenvector';
export type ClusteringAlgorithm = 'louvain' | 'leiden' | 'label_propagation' | 'weakly_connected';

export interface CentralityResult {
  nodeId: string;
  nodeName: string;
  score: number;
  rank: number;
  properties?: Record<string, any>;
}

export interface ClusterResult {
  clusterId: string;
  nodes: Array<{
    nodeId: string;
    nodeName: string;
    properties?: Record<string, any>;
  }>;
  size: number;
  density: number;
  modularity?: number;
}

export interface LinkPrediction {
  sourceNodeId: string;
  targetNodeId: string;
  sourceName: string;
  targetName: string;
  score: number;
  confidence: number;
  reasoning?: string;
}

export interface PathResult {
  path: Array<{
    nodeId: string;
    nodeName: string;
    relationshipType?: string;
  }>;
  length: number;
  weight?: number;
  cost?: number;
}

export interface GraphStatistics {
  nodeCount: number;
  relationshipCount: number;
  density: number;
  diameter: number;
  averagePathLength: number;
  clusteringCoefficient: number;
  connectedComponents: number;
  stronglyConnectedComponents: number;
  nodeTypeDistribution: Record<string, number>;
  relationshipTypeDistribution: Record<string, number>;
  degreeDistribution: {
    min: number;
    max: number;
    mean: number;
    median: number;
    standardDeviation: number;
  };
}

export interface SimilarityResult {
  nodeId: string;
  nodeName: string;
  similarity: number;
  commonNeighbors: number;
  sharedProperties: string[];
}

export class AnalysisService extends BaseService {
  private readonly driver: Driver;
  private readonly database: string;

  constructor(driver: Driver, database: string = 'neo4j', config?: ServiceConfig) {
    super({
      enableCaching: true,
      cacheTimeout: 900000, // 15 minutes for analysis results
      ...config
    });
    
    this.driver = driver;
    this.database = database;
  }

  /**
   * Calculate node centrality metrics
   */
  async getCentrality(type: CentralityType, limit: number = 20): Promise<CentralityResult[]> {
    this.validateInput({ type, limit }, this.validateCentralityInput);

    return this.executeOperation(
      'getCentrality',
      async () => {
        const session = this.getSession();
        try {
          const graphName = `centrality_${Date.now()}`;
          
          // Create graph projection
          await this.createGraphProjection(session, graphName);

          // Run centrality algorithm
          const query = this.getCentralityQuery(type, graphName, limit);
          const result = await session.run(query.cypher, query.parameters);

          // Clean up graph projection
          await this.dropGraphProjection(session, graphName);

          return result.records.map((record, index) => ({
            nodeId: record.get('nodeId').toString(),
            nodeName: record.get('nodeName') || 'Unknown',
            score: record.get('score'),
            rank: index + 1,
            properties: record.get('properties') || {}
          }));

        } finally {
          await session.close();
        }
      },
      `centrality-${type}-${limit}`
    );
  }

  /**
   * Perform community detection
   */
  async getClusters(algorithm: ClusteringAlgorithm = 'louvain', limit: number = 20): Promise<ClusterResult[]> {
    this.validateInput({ algorithm, limit }, this.validateClusterInput);

    return this.executeOperation(
      'getClusters',
      async () => {
        const session = this.getSession();
        try {
          const graphName = `clustering_${Date.now()}`;
          
          // Create graph projection
          await this.createGraphProjection(session, graphName);

          // Run clustering algorithm
          const query = this.getClusteringQuery(algorithm, graphName, limit);
          const result = await session.run(query.cypher, query.parameters);

          // Clean up graph projection
          await this.dropGraphProjection(session, graphName);

          // Group results by cluster
          const clusterMap = new Map<string, ClusterResult>();
          
          result.records.forEach(record => {
            const clusterId = record.get('clusterId').toString();
            const nodeId = record.get('nodeId').toString();
            const nodeName = record.get('nodeName') || 'Unknown';
            
            if (!clusterMap.has(clusterId)) {
              clusterMap.set(clusterId, {
                clusterId,
                nodes: [],
                size: 0,
                density: 0,
                modularity: record.get('modularity') || 0
              });
            }

            const cluster = clusterMap.get(clusterId)!;
            cluster.nodes.push({
              nodeId,
              nodeName,
              properties: record.get('properties') || {}
            });
            cluster.size = cluster.nodes.length;
          });

          return Array.from(clusterMap.values()).slice(0, limit);

        } finally {
          await session.close();
        }
      },
      `clusters-${algorithm}-${limit}`
    );
  }

  /**
   * Predict potential links using machine learning
   */
  async predictLinks(topN: number = 20, threshold: number = 0.4): Promise<LinkPrediction[]> {
    this.validateInput({ topN, threshold }, this.validateLinkPredictionInput);

    return this.executeOperation(
      'predictLinks',
      async () => {
        const session = this.getSession();
        try {
          const graphName = `linkpred_${Date.now()}`;
          
          // Create graph projection
          await this.createGraphProjection(session, graphName);

          // Use Adamic Adar for link prediction (simpler than Node2Vec)
          const result = await session.run(`
            CALL gds.alpha.linkprediction.adamicAdar.stream($graphName)
            YIELD node1, node2, score
            WHERE score >= $threshold
            MATCH (n1) WHERE id(n1) = node1
            MATCH (n2) WHERE id(n2) = node2
            RETURN 
              id(n1) as sourceId,
              id(n2) as targetId,
              coalesce(n1.name, n1.title, 'Unknown') as sourceName,
              coalesce(n2.name, n2.title, 'Unknown') as targetName,
              score
            ORDER BY score DESC
            LIMIT $topN
          `, { 
            graphName, 
            threshold, 
            topN 
          });

          // Clean up graph projection
          await this.dropGraphProjection(session, graphName);

          return result.records.map(record => ({
            sourceNodeId: record.get('sourceId').toString(),
            targetNodeId: record.get('targetId').toString(),
            sourceName: record.get('sourceName'),
            targetName: record.get('targetName'),
            score: record.get('score'),
            confidence: Math.min(record.get('score') * 100, 100),
            reasoning: 'Adamic Adar similarity'
          }));

        } catch (error) {
          // Fallback to simple similarity if GDS not available
          return this.fallbackLinkPrediction(session, topN, threshold);
        } finally {
          await session.close();
        }
      },
      `links-${topN}-${threshold}`
    );
  }

  /**
   * Find paths between two nodes
   */
  async findPaths(startNodeId: string, endNodeId: string, maxDepth: number = 5): Promise<PathResult[]> {
    this.validateInput({ startNodeId, endNodeId, maxDepth }, this.validatePathInput);

    return this.executeOperation(
      'findPaths',
      async () => {
        const session = this.getSession();
        try {
          const result = await session.run(`
            MATCH path = shortestPath((start)-[*1..$maxDepth]-(end))
            WHERE id(start) = toInteger($startNodeId) AND id(end) = toInteger($endNodeId)
            RETURN path
            LIMIT 10
          `, { startNodeId, endNodeId, maxDepth });

          return result.records.map(record => {
            const path = record.get('path');
            const nodes = path.segments.map((segment: any) => ({
              nodeId: segment.start.identity.toString(),
              nodeName: segment.start.properties.name || 'Unknown',
              relationshipType: segment.relationship.type
            }));

            // Add the end node
            if (path.segments.length > 0) {
              const lastSegment = path.segments[path.segments.length - 1];
              nodes.push({
                nodeId: lastSegment.end.identity.toString(),
                nodeName: lastSegment.end.properties.name || 'Unknown'
              });
            }

            return {
              path: nodes,
              length: path.length,
              weight: path.segments.reduce((sum: number, seg: any) => 
                sum + (seg.relationship.properties.weight || 1), 0)
            };
          });

        } finally {
          await session.close();
        }
      }
    );
  }

  /**
   * Get comprehensive graph statistics
   */
  async getGraphStatistics(): Promise<GraphStatistics> {
    return this.executeOperation(
      'getStatistics',
      async () => {
        const session = this.getSession();
        try {
          const [
            nodeCount,
            relCount,
            nodeTypes,
            relTypes,
            degreeStats
          ] = await Promise.all([
            this.getNodeCount(session),
            this.getRelationshipCount(session),
            this.getNodeTypeDistribution(session),
            this.getRelationshipTypeDistribution(session),
            this.getDegreeDistribution(session)
          ]);

          const density = relCount > 0 ? (2 * relCount) / (nodeCount * (nodeCount - 1)) : 0;

          return {
            nodeCount,
            relationshipCount: relCount,
            density,
            diameter: 0, // Would need complex calculation
            averagePathLength: 0, // Would need complex calculation
            clusteringCoefficient: 0, // Would need complex calculation
            connectedComponents: 0, // Would need GDS
            stronglyConnectedComponents: 0, // Would need GDS
            nodeTypeDistribution: nodeTypes,
            relationshipTypeDistribution: relTypes,
            degreeDistribution: degreeStats
          };

        } finally {
          await session.close();
        }
      },
      'statistics'
    );
  }

  /**
   * Find similar nodes to a given node
   */
  async getSimilarity(nodeId: string, limit: number = 20): Promise<SimilarityResult[]> {
    this.validateInput({ nodeId, limit }, this.validateSimilarityInput);

    return this.executeOperation(
      'getSimilarity',
      async () => {
        const session = this.getSession();
        try {
          // Find nodes with similar connections
          const result = await session.run(`
            MATCH (target) WHERE id(target) = toInteger($nodeId)
            MATCH (target)-[:*1..2]-(similar)
            WHERE id(similar) <> id(target)
            WITH similar, count(*) as commonConnections
            MATCH (similar)-[r]-()
            WITH similar, commonConnections, count(r) as totalConnections
            RETURN 
              id(similar) as nodeId,
              coalesce(similar.name, similar.title, 'Unknown') as nodeName,
              toFloat(commonConnections) / toFloat(totalConnections) as similarity,
              commonConnections,
              labels(similar) as labels
            ORDER BY similarity DESC
            LIMIT $limit
          `, { nodeId, limit });

          return result.records.map(record => ({
            nodeId: record.get('nodeId').toString(),
            nodeName: record.get('nodeName'),
            similarity: record.get('similarity'),
            commonNeighbors: record.get('commonConnections'),
            sharedProperties: record.get('labels') || []
          }));

        } finally {
          await session.close();
        }
      },
      `similarity-${nodeId}-${limit}`
    );
  }

  /**
   * Get a Neo4j session
   */
  private getSession(): Session {
    return this.driver.session({ database: this.database });
  }

  /**
   * Create graph projection for GDS algorithms
   */
  private async createGraphProjection(session: Session, graphName: string): Promise<void> {
    try {
      await session.run(`
        CALL gds.graph.project($graphName, '*', '*')
      `, { graphName });
    } catch (error) {
      // Graph might already exist, try to drop and recreate
      try {
        await session.run(`CALL gds.graph.drop($graphName, false)`, { graphName });
        await session.run(`CALL gds.graph.project($graphName, '*', '*')`, { graphName });
      } catch (retryError) {
        this.logger.warn('Failed to create graph projection', 'analysis-service', {
          graphName,
          error: (retryError as Error).message
        });
      }
    }
  }

  /**
   * Drop graph projection
   */
  private async dropGraphProjection(session: Session, graphName: string): Promise<void> {
    try {
      await session.run(`CALL gds.graph.drop($graphName, false)`, { graphName });
    } catch (error) {
      // Ignore errors when dropping
    }
  }

  /**
   * Get centrality query based on type
   */
  private getCentralityQuery(type: CentralityType, graphName: string, limit: number) {
    const baseQuery = {
      degree: `CALL gds.degree.stream($graphName) YIELD nodeId, score`,
      pagerank: `CALL gds.pageRank.stream($graphName) YIELD nodeId, score`,
      betweenness: `CALL gds.betweenness.stream($graphName) YIELD nodeId, score`,
      closeness: `CALL gds.closeness.stream($graphName) YIELD nodeId, score`,
      eigenvector: `CALL gds.eigenvector.stream($graphName) YIELD nodeId, score`
    };

    const cypher = `
      ${baseQuery[type]}
      MATCH (n) WHERE id(n) = nodeId
      RETURN 
        nodeId,
        coalesce(n.name, n.title, 'Unknown') as nodeName,
        score,
        properties(n) as properties
      ORDER BY score DESC
      LIMIT $limit
    `;

    return {
      cypher,
      parameters: { graphName, limit }
    };
  }

  /**
   * Get clustering query based on algorithm
   */
  private getClusteringQuery(algorithm: ClusteringAlgorithm, graphName: string, limit: number) {
    const baseQuery = {
      louvain: `CALL gds.louvain.stream($graphName) YIELD nodeId, communityId`,
      leiden: `CALL gds.leiden.stream($graphName) YIELD nodeId, communityId`,
      label_propagation: `CALL gds.labelPropagation.stream($graphName) YIELD nodeId, communityId`,
      weakly_connected: `CALL gds.wcc.stream($graphName) YIELD nodeId, componentId as communityId`
    };

    const cypher = `
      ${baseQuery[algorithm]}
      MATCH (n) WHERE id(n) = nodeId
      RETURN 
        nodeId,
        communityId as clusterId,
        coalesce(n.name, n.title, 'Unknown') as nodeName,
        properties(n) as properties,
        0.0 as modularity
      ORDER BY clusterId, nodeName
      LIMIT $limit
    `;

    return {
      cypher,
      parameters: { graphName, limit }
    };
  }

  /**
   * Fallback link prediction without GDS
   */
  private async fallbackLinkPrediction(session: Session, topN: number, threshold: number): Promise<LinkPrediction[]> {
    const result = await session.run(`
      MATCH (n1)-[:*2]-(n2)
      WHERE id(n1) < id(n2)
      AND NOT (n1)-[]-(n2)
      WITH n1, n2, count(*) as commonNeighbors
      WHERE commonNeighbors >= $threshold
      RETURN 
        id(n1) as sourceId,
        id(n2) as targetId,
        coalesce(n1.name, n1.title, 'Unknown') as sourceName,
        coalesce(n2.name, n2.title, 'Unknown') as targetName,
        toFloat(commonNeighbors) / 10.0 as score
      ORDER BY score DESC
      LIMIT $topN
    `, { topN, threshold });

    return result.records.map(record => ({
      sourceNodeId: record.get('sourceId').toString(),
      targetNodeId: record.get('targetId').toString(),
      sourceName: record.get('sourceName'),
      targetName: record.get('targetName'),
      score: record.get('score'),
      confidence: Math.min(record.get('score') * 100, 100),
      reasoning: 'Common neighbors'
    }));
  }

  // Helper methods for statistics
  private async getNodeCount(session: Session): Promise<number> {
    const result = await session.run('MATCH (n) RETURN count(n) as count');
    return result.records[0]?.get('count')?.toNumber() || 0;
  }

  private async getRelationshipCount(session: Session): Promise<number> {
    const result = await session.run('MATCH ()-[r]->() RETURN count(r) as count');
    return result.records[0]?.get('count')?.toNumber() || 0;
  }

  private async getNodeTypeDistribution(session: Session): Promise<Record<string, number>> {
    const result = await session.run(`
      MATCH (n)
      RETURN labels(n)[0] as label, count(*) as count
      ORDER BY count DESC
    `);

    const distribution: Record<string, number> = {};
    result.records.forEach(record => {
      const label = record.get('label') || 'Unknown';
      const count = record.get('count').toNumber();
      distribution[label] = count;
    });

    return distribution;
  }

  private async getRelationshipTypeDistribution(session: Session): Promise<Record<string, number>> {
    const result = await session.run(`
      MATCH ()-[r]->()
      RETURN type(r) as type, count(*) as count
      ORDER BY count DESC
    `);

    const distribution: Record<string, number> = {};
    result.records.forEach(record => {
      const type = record.get('type');
      const count = record.get('count').toNumber();
      distribution[type] = count;
    });

    return distribution;
  }

  private async getDegreeDistribution(session: Session) {
    const result = await session.run(`
      MATCH (n)-[r]-()
      WITH n, count(r) as degree
      RETURN 
        min(degree) as min,
        max(degree) as max,
        avg(degree) as mean,
        percentileCont(degree, 0.5) as median,
        stDev(degree) as standardDeviation
    `);

    const record = result.records[0];
    return {
      min: record?.get('min')?.toNumber() || 0,
      max: record?.get('max')?.toNumber() || 0,
      mean: record?.get('mean') || 0,
      median: record?.get('median') || 0,
      standardDeviation: record?.get('standardDeviation') || 0
    };
  }

  // Validation methods
  private validateCentralityInput = (input: any) => {
    const errors: string[] = [];
    const validTypes: CentralityType[] = ['degree', 'betweenness', 'closeness', 'pagerank', 'eigenvector'];
    
    if (!validTypes.includes(input.type)) {
      errors.push(`Invalid centrality type. Must be one of: ${validTypes.join(', ')}`);
    }
    if (input.limit && (typeof input.limit !== 'number' || input.limit < 1 || input.limit > 1000)) {
      errors.push('Limit must be a number between 1 and 1000');
    }
    return { isValid: errors.length === 0, errors };
  };

  private validateClusterInput = (input: any) => {
    const errors: string[] = [];
    const validAlgorithms: ClusteringAlgorithm[] = ['louvain', 'leiden', 'label_propagation', 'weakly_connected'];
    
    if (!validAlgorithms.includes(input.algorithm)) {
      errors.push(`Invalid clustering algorithm. Must be one of: ${validAlgorithms.join(', ')}`);
    }
    if (input.limit && (typeof input.limit !== 'number' || input.limit < 1 || input.limit > 1000)) {
      errors.push('Limit must be a number between 1 and 1000');
    }
    return { isValid: errors.length === 0, errors };
  };

  private validateLinkPredictionInput = (input: any) => {
    const errors: string[] = [];
    if (input.topN && (typeof input.topN !== 'number' || input.topN < 1 || input.topN > 100)) {
      errors.push('TopN must be a number between 1 and 100');
    }
    if (input.threshold && (typeof input.threshold !== 'number' || input.threshold < 0 || input.threshold > 1)) {
      errors.push('Threshold must be a number between 0 and 1');
    }
    return { isValid: errors.length === 0, errors };
  };

  private validatePathInput = (input: any) => {
    const errors: string[] = [];
    if (!input.startNodeId || typeof input.startNodeId !== 'string') {
      errors.push('Start node ID is required and must be a string');
    }
    if (!input.endNodeId || typeof input.endNodeId !== 'string') {
      errors.push('End node ID is required and must be a string');
    }
    if (input.maxDepth && (typeof input.maxDepth !== 'number' || input.maxDepth < 1 || input.maxDepth > 10)) {
      errors.push('Max depth must be a number between 1 and 10');
    }
    return { isValid: errors.length === 0, errors };
  };

  private validateSimilarityInput = (input: any) => {
    const errors: string[] = [];
    if (!input.nodeId || typeof input.nodeId !== 'string') {
      errors.push('Node ID is required and must be a string');
    }
    if (input.limit && (typeof input.limit !== 'number' || input.limit < 1 || input.limit > 1000)) {
      errors.push('Limit must be a number between 1 and 1000');
    }
    return { isValid: errors.length === 0, errors };
  };
}
