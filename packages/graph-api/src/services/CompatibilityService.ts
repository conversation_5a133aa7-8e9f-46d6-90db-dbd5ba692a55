/**
 * Compatibility Service
 * 
 * Handles dual execution pattern for validating new implementations
 * against legacy behavior during migration.
 */

import { logger, FeatureFlags } from '@kg-visualizer/shared';
import axios from 'axios';

interface DualExecutionResult<T = any> {
  primary: T;
  secondary?: T;
  isValid: boolean;
  differences?: string[];
  executionTime: {
    primary: number;
    secondary?: number;
  };
}

interface ExecutionOptions {
  enableDualExecution?: boolean;
  enableValidation?: boolean;
  timeout?: number;
  skipSecondaryOnError?: boolean;
}

export class CompatibilityService {
  private readonly logger = logger.child('compatibility-service');
  private readonly legacyApiUrl: string;
  private readonly featureFlags: FeatureFlags;

  constructor(legacyApiUrl: string = 'http://localhost:3002', featureFlags: FeatureFlags) {
    this.legacyApiUrl = legacyApiUrl;
    this.featureFlags = featureFlags;
  }

  /**
   * Execute both new and legacy implementations and compare results
   */
  async dualExecute<T>(
    newImplementation: () => Promise<T>,
    legacyEndpoint: string,
    legacyPayload?: any,
    options: ExecutionOptions = {}
  ): Promise<DualExecutionResult<T>> {
    const {
      enableDualExecution = this.featureFlags.DUAL_EXECUTION_MODE,
      enableValidation = this.featureFlags.PERFORMANCE_MONITORING,
      timeout = 30000,
      skipSecondaryOnError = true
    } = options;

    const startTime = Date.now();
    let primaryResult: T;
    let secondaryResult: T | undefined;
    let primaryTime: number;
    let secondaryTime: number | undefined;

    try {
      // Execute new implementation (primary)
      const primaryStart = Date.now();
      primaryResult = await newImplementation();
      primaryTime = Date.now() - primaryStart;

      this.logger.debug('Primary execution completed', 'compatibility-service', {
        endpoint: legacyEndpoint,
        executionTime: primaryTime
      });

      // Execute legacy implementation (secondary) if dual execution is enabled
      if (enableDualExecution) {
        try {
          const secondaryStart = Date.now();
          secondaryResult = await this.executeLegacyEndpoint<T>(
            legacyEndpoint,
            legacyPayload,
            timeout
          );
          secondaryTime = Date.now() - secondaryStart;

          this.logger.debug('Secondary execution completed', 'compatibility-service', {
            endpoint: legacyEndpoint,
            executionTime: secondaryTime
          });
        } catch (secondaryError) {
          this.logger.warn('Secondary execution failed', 'compatibility-service', {
            endpoint: legacyEndpoint,
            error: (secondaryError as Error).message,
            skipSecondaryOnError
          });

          if (!skipSecondaryOnError) {
            throw secondaryError;
          }
        }
      }

      // Validate results if both executions completed
      let isValid = true;
      let differences: string[] = [];

      if (enableValidation && secondaryResult !== undefined) {
        const validation = this.validateResults(primaryResult, secondaryResult, legacyEndpoint);
        isValid = validation.isValid;
        differences = validation.differences;

        if (!isValid) {
          this.logger.warn('Result validation failed', 'compatibility-service', {
            endpoint: legacyEndpoint,
            differences,
            primaryTime,
            secondaryTime
          });
        }
      }

      return {
        primary: primaryResult,
        secondary: secondaryResult,
        isValid,
        differences,
        executionTime: {
          primary: primaryTime,
          secondary: secondaryTime
        }
      };

    } catch (error) {
      this.logger.error('Dual execution failed', 'compatibility-service', {
        endpoint: legacyEndpoint,
        error: (error as Error).message,
        executionTime: Date.now() - startTime
      });
      throw error;
    }
  }

  /**
   * Execute legacy endpoint via HTTP call
   */
  private async executeLegacyEndpoint<T>(
    endpoint: string,
    payload?: any,
    timeout: number = 30000
  ): Promise<T> {
    const url = `${this.legacyApiUrl}${endpoint}`;
    const config = {
      timeout,
      headers: {
        'Content-Type': 'application/json',
        'X-Legacy-Execution': 'true'
      }
    };

    try {
      let response;
      
      if (payload && (endpoint.includes('POST') || payload.method === 'POST')) {
        response = await axios.post(url, payload, config);
      } else if (payload && Object.keys(payload).length > 0) {
        response = await axios.get(url, { ...config, params: payload });
      } else {
        response = await axios.get(url, config);
      }

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(`Legacy API call failed: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Validate that new and legacy results are equivalent
   */
  private validateResults<T>(
    primaryResult: T,
    secondaryResult: T,
    endpoint: string
  ): { isValid: boolean; differences: string[] } {
    const differences: string[] = [];

    try {
      // Handle different endpoint types
      if (endpoint.includes('/api/graph/')) {
        return this.validateGraphResults(primaryResult, secondaryResult);
      } else if (endpoint.includes('/api/analysis/')) {
        return this.validateAnalysisResults(primaryResult, secondaryResult);
      } else if (endpoint.includes('/api/chat/')) {
        return this.validateChatResults(primaryResult, secondaryResult);
      } else {
        return this.validateGenericResults(primaryResult, secondaryResult);
      }
    } catch (error) {
      differences.push(`Validation error: ${(error as Error).message}`);
      return { isValid: false, differences };
    }
  }

  /**
   * Validate graph endpoint results
   */
  private validateGraphResults(primary: any, secondary: any): { isValid: boolean; differences: string[] } {
    const differences: string[] = [];

    // Check nodes
    if (primary.nodes && secondary.nodes) {
      if (primary.nodes.length !== secondary.nodes.length) {
        differences.push(`Node count mismatch: ${primary.nodes.length} vs ${secondary.nodes.length}`);
      }
    }

    // Check edges/relationships
    const primaryEdges = primary.edges || primary.relationships || [];
    const secondaryEdges = secondary.edges || secondary.relationships || [];
    
    if (primaryEdges.length !== secondaryEdges.length) {
      differences.push(`Edge count mismatch: ${primaryEdges.length} vs ${secondaryEdges.length}`);
    }

    return { isValid: differences.length === 0, differences };
  }

  /**
   * Validate analysis endpoint results
   */
  private validateAnalysisResults(primary: any, secondary: any): { isValid: boolean; differences: string[] } {
    const differences: string[] = [];

    // Check array lengths
    if (Array.isArray(primary) && Array.isArray(secondary)) {
      if (primary.length !== secondary.length) {
        differences.push(`Result count mismatch: ${primary.length} vs ${secondary.length}`);
      }
    }

    // Check centrality results
    if (primary.centrality && secondary.centrality) {
      const primaryKeys = Object.keys(primary.centrality);
      const secondaryKeys = Object.keys(secondary.centrality);
      
      if (primaryKeys.length !== secondaryKeys.length) {
        differences.push(`Centrality key count mismatch: ${primaryKeys.length} vs ${secondaryKeys.length}`);
      }
    }

    return { isValid: differences.length === 0, differences };
  }

  /**
   * Validate chat endpoint results
   */
  private validateChatResults(primary: any, secondary: any): { isValid: boolean; differences: string[] } {
    const differences: string[] = [];

    // For chat responses, we mainly check structure rather than exact content
    // since LLM responses may vary
    if (primary.response && secondary.response) {
      if (typeof primary.response !== typeof secondary.response) {
        differences.push('Response type mismatch');
      }
    }

    if (primary.conversations && secondary.conversations) {
      if (primary.conversations.length !== secondary.conversations.length) {
        differences.push(`Conversation count mismatch: ${primary.conversations.length} vs ${secondary.conversations.length}`);
      }
    }

    return { isValid: differences.length === 0, differences };
  }

  /**
   * Generic result validation
   */
  private validateGenericResults(primary: any, secondary: any): { isValid: boolean; differences: string[] } {
    const differences: string[] = [];

    try {
      const primaryStr = JSON.stringify(primary, null, 2);
      const secondaryStr = JSON.stringify(secondary, null, 2);

      if (primaryStr !== secondaryStr) {
        differences.push('JSON structure mismatch');
        
        // Try to identify specific differences
        if (typeof primary === 'object' && typeof secondary === 'object') {
          const primaryKeys = Object.keys(primary || {});
          const secondaryKeys = Object.keys(secondary || {});
          
          const missingInPrimary = secondaryKeys.filter(key => !primaryKeys.includes(key));
          const missingInSecondary = primaryKeys.filter(key => !secondaryKeys.includes(key));
          
          if (missingInPrimary.length > 0) {
            differences.push(`Keys missing in primary: ${missingInPrimary.join(', ')}`);
          }
          
          if (missingInSecondary.length > 0) {
            differences.push(`Keys missing in secondary: ${missingInSecondary.join(', ')}`);
          }
        }
      }
    } catch (error) {
      differences.push(`Serialization error: ${(error as Error).message}`);
    }

    return { isValid: differences.length === 0, differences };
  }

  /**
   * Log compatibility metrics for monitoring
   */
  logCompatibilityMetrics(
    endpoint: string,
    result: DualExecutionResult,
    success: boolean
  ): void {
    this.logger.info('Compatibility metrics', 'compatibility-service', {
      endpoint,
      success,
      isValid: result.isValid,
      primaryTime: result.executionTime.primary,
      secondaryTime: result.executionTime.secondary,
      hasDifferences: result.differences && result.differences.length > 0,
      differenceCount: result.differences?.length || 0
    });
  }
}
