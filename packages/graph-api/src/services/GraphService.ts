/**
 * Graph Service
 * 
 * Handles graph data operations including retrieval, search, filtering,
 * and metadata operations. Provides abstraction over Neo4j database.
 */

import neo4j, { Driver, Session } from 'neo4j-driver';
import { BaseService, ServiceConfig } from './BaseService';
import { GraphData, GraphNode, GraphRelationship } from '@kg-visualizer/shared';

export interface GraphFilters {
  nodeLabels?: string[];
  relationshipTypes?: string[];
  properties?: Array<{
    key: string;
    operator: 'equals' | 'contains' | 'startsWith' | 'endsWith';
    value: any;
  }>;
  dateRange?: {
    start: string;
    end: string;
    property: string;
  };
}

export interface DatabaseMetadata {
  nodeLabels: Array<{
    label: string;
    count: number;
    properties: string[];
  }>;
  relationshipTypes: Array<{
    type: string;
    count: number;
    properties: string[];
  }>;
  indexes: Array<{
    name: string;
    type: string;
    labels: string[];
    properties: string[];
  }>;
  constraints: Array<{
    name: string;
    type: string;
    label: string;
    properties: string[];
  }>;
  statistics: {
    totalNodes: number;
    totalRelationships: number;
    databaseSize: string;
    lastUpdated: string;
  };
}

export class GraphService extends BaseService {
  private readonly driver: Driver;
  private readonly database: string;

  constructor(driver: Driver, database: string = 'neo4j', config?: ServiceConfig) {
    super({
      enableCaching: true,
      cacheTimeout: 300000, // 5 minutes
      ...config
    });
    
    this.driver = driver;
    this.database = database;
  }

  /**
   * Get initial graph data for visualization
   */
  async getInitialGraph(limit: number = 1000): Promise<GraphData> {
    return this.executeOperation(
      'getInitialGraph',
      async () => {
        const session = this.getSession();
        try {
          const result = await session.run(`
            MATCH (n)-[r]->(m)
            RETURN n, r, m
            LIMIT $limit
          `, { limit: neo4j.int(limit) });

          return this.transformToGraphData(result.records);
        } finally {
          await session.close();
        }
      },
      `initial-graph-${limit}`
    );
  }

  /**
   * Search for nodes by term
   */
  async searchNodes(term: string, limit: number = 20): Promise<GraphNode[]> {
    this.validateInput({ term, limit }, this.validateSearchInput);

    return this.executeOperation(
      'searchNodes',
      async () => {
        const session = this.getSession();
        try {
          const result = await session.run(`
            MATCH (n)
            WHERE toLower(n.name) CONTAINS toLower($term)
               OR toLower(n.title) CONTAINS toLower($term)
               OR toLower(n.description) CONTAINS toLower($term)
               OR any(label IN labels(n) WHERE toLower(label) CONTAINS toLower($term))
            RETURN n
            ORDER BY 
              CASE 
                WHEN toLower(n.name) = toLower($term) THEN 1
                WHEN toLower(n.name) STARTS WITH toLower($term) THEN 2
                ELSE 3
              END,
              n.name
            LIMIT $limit
          `, { term, limit });

          return result.records.map(record => this.transformNode(record.get('n')));
        } finally {
          await session.close();
        }
      },
      `search-${term}-${limit}`
    );
  }

  /**
   * Expand a node to show connected nodes
   */
  async expandNode(nodeId: string, limit: number = 50): Promise<GraphData> {
    this.validateInput({ nodeId, limit }, this.validateExpandInput);

    return this.executeOperation(
      'expandNode',
      async () => {
        const session = this.getSession();
        try {
          const result = await session.run(`
            MATCH (n)-[r]-(m)
            WHERE id(n) = toInteger($nodeId) 
               OR n.id = $nodeId 
               OR n.name = $nodeId
            RETURN n, r, m
            LIMIT $limit
          `, { nodeId, limit });

          return this.transformToGraphData(result.records);
        } finally {
          await session.close();
        }
      },
      `expand-${nodeId}-${limit}`
    );
  }

  /**
   * Filter graph data based on criteria
   */
  async filterGraph(filters: GraphFilters): Promise<GraphData> {
    this.validateInput(filters, this.validateFilters);

    return this.executeOperation(
      'filterGraph',
      async () => {
        const session = this.getSession();
        try {
          const { query, parameters } = this.buildFilterQuery(filters);
          const result = await session.run(query, parameters);

          return this.transformToGraphData(result.records);
        } finally {
          await session.close();
        }
      }
    );
  }

  /**
   * Execute custom Cypher query
   */
  async executeQuery(query: string, parameters: Record<string, any> = {}): Promise<any[]> {
    this.validateInput({ query, parameters }, this.validateQueryInput);

    return this.executeOperation(
      'executeQuery',
      async () => {
        const session = this.getSession();
        try {
          const result = await session.run(query, parameters);
          return result.records.map(record => {
            const obj: Record<string, any> = {};
            record.keys.forEach((key) => {
              obj[String(key)] = this.transformValue(record.get(key));
            });
            return obj;
          });
        } finally {
          await session.close();
        }
      }
    );
  }

  /**
   * Get database metadata and statistics
   */
  async getMetadata(): Promise<DatabaseMetadata> {
    return this.executeOperation(
      'getMetadata',
      async () => {
        const session = this.getSession();
        try {
          const [nodeLabels, relationshipTypes, indexes, constraints, statistics] = await Promise.all([
            this.getNodeLabels(session),
            this.getRelationshipTypes(session),
            this.getIndexes(session),
            this.getConstraints(session),
            this.getDatabaseStatistics(session)
          ]);

          return {
            nodeLabels,
            relationshipTypes,
            indexes,
            constraints,
            statistics
          };
        } finally {
          await session.close();
        }
      },
      'metadata'
    );
  }

  /**
   * Get a Neo4j session
   */
  private getSession(): Session {
    return this.driver.session({ database: this.database });
  }

  /**
   * Transform Neo4j records to GraphData format
   */
  private transformToGraphData(records: any[]): GraphData {
    const nodesMap = new Map<string, GraphNode>();
    const relationships: GraphRelationship[] = [];

    for (const record of records) {
      // Process nodes
      ['n', 'm'].forEach(key => {
        if (record.has(key)) {
          const node = this.transformNode(record.get(key));
          nodesMap.set(node.id, node);
        }
      });

      // Process relationships
      if (record.has('r')) {
        const relationship = this.transformRelationship(record.get('r'));
        relationships.push(relationship);
      }
    }

    return {
      nodes: Array.from(nodesMap.values()),
      relationships
    };
  }

  /**
   * Transform Neo4j node to GraphNode format
   */
  private transformNode(node: any): GraphNode {
    const properties = node.properties || {};
    const labels = node.labels || [];
    
    return {
      id: node.identity?.toString() || node.id?.toString() || properties.id?.toString(),
      label: properties.name || properties.title || properties.id || 'Unnamed',
      type: labels[0] || 'Unknown',
      properties: this.transformProperties(properties),
      metadata: {
        labels,
        identity: node.identity?.toString()
      }
    };
  }

  /**
   * Transform Neo4j relationship to GraphRelationship format
   */
  private transformRelationship(relationship: any): GraphRelationship {
    const properties = relationship.properties || {};
    
    return {
      id: relationship.identity?.toString() || `${relationship.start}-${relationship.end}`,
      source: relationship.start?.toString(),
      target: relationship.end?.toString(),
      type: relationship.type || 'RELATED',
      properties: this.transformProperties(properties),
      metadata: {
        identity: relationship.identity?.toString()
      }
    };
  }

  /**
   * Transform Neo4j properties
   */
  private transformProperties(properties: any): Record<string, any> {
    const transformed: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(properties)) {
      transformed[key] = this.transformValue(value);
    }
    
    return transformed;
  }

  /**
   * Transform Neo4j values to JavaScript values
   */
  private transformValue(value: any): any {
    if (value === null || value === undefined) {
      return value;
    }

    // Handle Neo4j integers
    if (value.toNumber && typeof value.toNumber === 'function') {
      return value.toNumber();
    }

    // Handle Neo4j dates
    if (value.toString && value.toString().includes('T')) {
      try {
        return new Date(value.toString()).toISOString();
      } catch {
        return value.toString();
      }
    }

    // Handle arrays
    if (Array.isArray(value)) {
      return value.map(item => this.transformValue(item));
    }

    // Handle objects
    if (typeof value === 'object') {
      const transformed: Record<string, any> = {};
      for (const [key, val] of Object.entries(value)) {
        transformed[key] = this.transformValue(val);
      }
      return transformed;
    }

    return value;
  }

  /**
   * Build filter query from filters
   */
  private buildFilterQuery(filters: GraphFilters): { query: string; parameters: Record<string, any> } {
    let query = 'MATCH (n)-[r]->(m) WHERE 1=1';
    const parameters: Record<string, any> = {};

    if (filters.nodeLabels && filters.nodeLabels.length > 0) {
      query += ' AND (labels(n)[0] IN $nodeLabels OR labels(m)[0] IN $nodeLabels)';
      parameters.nodeLabels = filters.nodeLabels;
    }

    if (filters.relationshipTypes && filters.relationshipTypes.length > 0) {
      query += ' AND type(r) IN $relationshipTypes';
      parameters.relationshipTypes = filters.relationshipTypes;
    }

    if (filters.properties && filters.properties.length > 0) {
      filters.properties.forEach((prop, index) => {
        const paramKey = `propValue${index}`;
        switch (prop.operator) {
          case 'equals':
            query += ` AND (n.${prop.key} = $${paramKey} OR m.${prop.key} = $${paramKey})`;
            break;
          case 'contains':
            query += ` AND (toLower(toString(n.${prop.key})) CONTAINS toLower($${paramKey}) OR toLower(toString(m.${prop.key})) CONTAINS toLower($${paramKey}))`;
            break;
          case 'startsWith':
            query += ` AND (toLower(toString(n.${prop.key})) STARTS WITH toLower($${paramKey}) OR toLower(toString(m.${prop.key})) STARTS WITH toLower($${paramKey}))`;
            break;
          case 'endsWith':
            query += ` AND (toLower(toString(n.${prop.key})) ENDS WITH toLower($${paramKey}) OR toLower(toString(m.${prop.key})) ENDS WITH toLower($${paramKey}))`;
            break;
        }
        parameters[paramKey] = prop.value;
      });
    }

    query += ' RETURN n, r, m LIMIT 100';
    return { query, parameters };
  }

  // Validation methods
  private validateSearchInput = (input: any) => {
    const errors: string[] = [];
    if (!input.term || typeof input.term !== 'string') {
      errors.push('Search term is required and must be a string');
    }
    if (input.limit && (typeof input.limit !== 'number' || input.limit < 1 || input.limit > 1000)) {
      errors.push('Limit must be a number between 1 and 1000');
    }
    return { isValid: errors.length === 0, errors };
  };

  private validateExpandInput = (input: any) => {
    const errors: string[] = [];
    if (!input.nodeId || typeof input.nodeId !== 'string') {
      errors.push('Node ID is required and must be a string');
    }
    if (input.limit && (typeof input.limit !== 'number' || input.limit < 1 || input.limit > 1000)) {
      errors.push('Limit must be a number between 1 and 1000');
    }
    return { isValid: errors.length === 0, errors };
  };

  private validateFilters = (_filters: any) => {
    const errors: string[] = [];
    // Add filter validation logic here
    return { isValid: errors.length === 0, errors };
  };

  private validateQueryInput = (input: any) => {
    const errors: string[] = [];
    if (!input.query || typeof input.query !== 'string') {
      errors.push('Query is required and must be a string');
    }
    if (input.query && input.query.length > 10000) {
      errors.push('Query is too long (maximum 10,000 characters)');
    }
    return { isValid: errors.length === 0, errors };
  };

  // Metadata helper methods
  private async getNodeLabels(session: Session) {
    const result = await session.run('CALL db.labels()');
    return result.records.map(record => ({
      label: record.get('label'),
      count: 0, // Would need additional query to get counts
      properties: []
    }));
  }

  private async getRelationshipTypes(session: Session) {
    const result = await session.run('CALL db.relationshipTypes()');
    return result.records.map(record => ({
      type: record.get('relationshipType'),
      count: 0,
      properties: []
    }));
  }

  private async getIndexes(session: Session) {
    try {
      const result = await session.run('SHOW INDEXES');
      return result.records.map(record => ({
        name: record.get('name'),
        type: record.get('type'),
        labels: record.get('labelsOrTypes') || [],
        properties: record.get('properties') || []
      }));
    } catch {
      return [];
    }
  }

  private async getConstraints(session: Session) {
    try {
      const result = await session.run('SHOW CONSTRAINTS');
      return result.records.map(record => ({
        name: record.get('name'),
        type: record.get('type'),
        label: record.get('labelsOrTypes')?.[0] || '',
        properties: record.get('properties') || []
      }));
    } catch {
      return [];
    }
  }

  private async getDatabaseStatistics(session: Session) {
    const nodeCountResult = await session.run('MATCH (n) RETURN count(n) as count');
    const relCountResult = await session.run('MATCH ()-[r]->() RETURN count(r) as count');
    
    return {
      totalNodes: nodeCountResult.records[0]?.get('count')?.toNumber() || 0,
      totalRelationships: relCountResult.records[0]?.get('count')?.toNumber() || 0,
      databaseSize: 'Unknown',
      lastUpdated: new Date().toISOString()
    };
  }
}
