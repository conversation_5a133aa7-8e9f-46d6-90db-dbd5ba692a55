/**
 * Tests for GraphService
 */

import { GraphService } from '../GraphService';

// Mock Neo4j driver
const mockSession = {
  run: jest.fn(),
  close: jest.fn()
};

const mockDriver = {
  session: jest.fn(() => mockSession)
};

// Mock logger
jest.mock('@kg-visualizer/shared', () => ({
  logger: {
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }))
  },
  PerformanceTimer: jest.fn().mockImplementation(() => ({
    elapsed: jest.fn().mockReturnValue(100)
  }))
}));

describe('GraphService', () => {
  let service: GraphService;

  beforeEach(() => {
    jest.clearAllMocks();
    mockDriver.session.mockReturnValue(mockSession);
    service = new GraphService(mockDriver as any, 'neo4j');
  });

  describe('getInitialGraph', () => {
    it('should return initial graph data', async () => {
      const mockRecords = [
        {
          has: jest.fn((key) => ['n', 'm', 'r'].includes(key)),
          get: jest.fn((key) => {
            if (key === 'n') return {
              identity: { toString: () => '1' },
              labels: ['Product'],
              properties: { name: 'Product A', id: '1' }
            };
            if (key === 'm') return {
              identity: { toString: () => '2' },
              labels: ['Feature'],
              properties: { name: 'Feature B', id: '2' }
            };
            if (key === 'r') return {
              identity: { toString: () => 'r1' },
              start: { toString: () => '1' },
              end: { toString: () => '2' },
              type: 'CONTAINS',
              properties: {}
            };
          })
        }
      ];

      mockSession.run.mockResolvedValue({ records: mockRecords });

      const result = await service.getInitialGraph(10);

      expect(result).toEqual({
        nodes: [
          {
            id: '1',
            label: 'Product A',
            type: 'Product',
            properties: { name: 'Product A', id: '1' },
            metadata: { labels: ['Product'], identity: '1' }
          },
          {
            id: '2',
            label: 'Feature B',
            type: 'Feature',
            properties: { name: 'Feature B', id: '2' },
            metadata: { labels: ['Feature'], identity: '2' }
          }
        ],
        relationships: [
          {
            id: 'r1',
            source: '1',
            target: '2',
            type: 'CONTAINS',
            properties: {},
            metadata: { identity: 'r1' }
          }
        ]
      });

      expect(mockSession.run).toHaveBeenCalledWith(
        expect.stringContaining('MATCH (n)-[r]->(m)'),
        { limit: 10 }
      );
      expect(mockSession.close).toHaveBeenCalled();
    });

    it('should handle empty results', async () => {
      mockSession.run.mockResolvedValue({ records: [] });

      const result = await service.getInitialGraph();

      expect(result).toEqual({
        nodes: [],
        relationships: []
      });
    });
  });

  describe('searchNodes', () => {
    it('should search nodes by term', async () => {
      const mockRecords = [
        {
          get: jest.fn(() => ({
            identity: { toString: () => '1' },
            labels: ['Product'],
            properties: { name: 'Test Product', id: '1' }
          }))
        }
      ];

      mockSession.run.mockResolvedValue({ records: mockRecords });

      const result = await service.searchNodes('test', 5);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: '1',
        label: 'Test Product',
        type: 'Product',
        properties: { name: 'Test Product', id: '1' },
        metadata: { labels: ['Product'], identity: '1' }
      });

      expect(mockSession.run).toHaveBeenCalledWith(
        expect.stringContaining('WHERE toLower(n.name) CONTAINS toLower($term)'),
        { term: 'test', limit: 5 }
      );
    });

    it('should validate search input', async () => {
      await expect(service.searchNodes('', 5)).rejects.toThrow('Search term is required');
      await expect(service.searchNodes('test', 2000)).rejects.toThrow('Limit must be a number between 1 and 1000');
    });
  });

  describe('expandNode', () => {
    it('should expand node connections', async () => {
      const mockRecords = [
        {
          has: jest.fn((key) => ['n', 'm', 'r'].includes(key)),
          get: jest.fn((key) => {
            if (key === 'n') return {
              identity: { toString: () => '1' },
              labels: ['Product'],
              properties: { name: 'Product A' }
            };
            if (key === 'm') return {
              identity: { toString: () => '2' },
              labels: ['Feature'],
              properties: { name: 'Feature B' }
            };
            if (key === 'r') return {
              identity: { toString: () => 'r1' },
              start: { toString: () => '1' },
              end: { toString: () => '2' },
              type: 'CONTAINS',
              properties: {}
            };
          })
        }
      ];

      mockSession.run.mockResolvedValue({ records: mockRecords });

      const result = await service.expandNode('1', 10);

      expect(result.nodes).toHaveLength(2);
      expect(result.relationships).toHaveLength(1);

      expect(mockSession.run).toHaveBeenCalledWith(
        expect.stringContaining('WHERE id(n) = toInteger($nodeId)'),
        { nodeId: '1', limit: 10 }
      );
    });

    it('should validate expand input', async () => {
      await expect(service.expandNode('', 5)).rejects.toThrow('Node ID is required');
    });
  });

  describe('filterGraph', () => {
    it('should filter graph by node labels', async () => {
      const mockRecords = [];
      mockSession.run.mockResolvedValue({ records: mockRecords });

      const filters = {
        nodeLabels: ['Product', 'Feature']
      };

      const result = await service.filterGraph(filters);

      expect(result).toEqual({
        nodes: [],
        relationships: []
      });

      expect(mockSession.run).toHaveBeenCalledWith(
        expect.stringContaining('AND (labels(n)[0] IN $nodeLabels OR labels(m)[0] IN $nodeLabels)'),
        expect.objectContaining({
          nodeLabels: ['Product', 'Feature']
        })
      );
    });

    it('should filter graph by relationship types', async () => {
      const mockRecords = [];
      mockSession.run.mockResolvedValue({ records: mockRecords });

      const filters = {
        relationshipTypes: ['CONTAINS', 'RELATES_TO']
      };

      await service.filterGraph(filters);

      expect(mockSession.run).toHaveBeenCalledWith(
        expect.stringContaining('AND type(r) IN $relationshipTypes'),
        expect.objectContaining({
          relationshipTypes: ['CONTAINS', 'RELATES_TO']
        })
      );
    });

    it('should filter graph by properties', async () => {
      const mockRecords = [];
      mockSession.run.mockResolvedValue({ records: mockRecords });

      const filters = {
        properties: [
          { key: 'name', operator: 'contains' as const, value: 'test' },
          { key: 'status', operator: 'equals' as const, value: 'active' }
        ]
      };

      await service.filterGraph(filters);

      expect(mockSession.run).toHaveBeenCalledWith(
        expect.stringContaining('toLower(toString(n.name)) CONTAINS toLower($propValue0)'),
        expect.objectContaining({
          propValue0: 'test',
          propValue1: 'active'
        })
      );
    });
  });

  describe('executeQuery', () => {
    it('should execute custom Cypher query', async () => {
      const mockRecords = [
        {
          keys: ['name', 'count'],
          get: jest.fn((key) => {
            if (key === 'name') return 'Product A';
            if (key === 'count') return { toNumber: () => 5 };
          })
        }
      ];

      mockSession.run.mockResolvedValue({ records: mockRecords });

      const result = await service.executeQuery(
        'MATCH (n:Product) RETURN n.name as name, count(*) as count',
        {}
      );

      expect(result).toEqual([
        { name: 'Product A', count: 5 }
      ]);

      expect(mockSession.run).toHaveBeenCalledWith(
        'MATCH (n:Product) RETURN n.name as name, count(*) as count',
        {}
      );
    });

    it('should validate query input', async () => {
      await expect(service.executeQuery('', {})).rejects.toThrow('Query is required');
      
      const longQuery = 'A'.repeat(10001);
      await expect(service.executeQuery(longQuery, {})).rejects.toThrow('Query is too long');
    });
  });

  describe('getMetadata', () => {
    it('should return database metadata', async () => {
      // Mock different calls for metadata
      mockSession.run
        .mockResolvedValueOnce({ records: [{ get: () => 'Product' }] }) // labels
        .mockResolvedValueOnce({ records: [{ get: () => 'CONTAINS' }] }) // relationship types
        .mockResolvedValueOnce({ records: [] }) // indexes
        .mockResolvedValueOnce({ records: [] }) // constraints
        .mockResolvedValueOnce({ records: [{ get: () => ({ toNumber: () => 100 }) }] }) // node count
        .mockResolvedValueOnce({ records: [{ get: () => ({ toNumber: () => 50 }) }] }); // rel count

      const result = await service.getMetadata();

      expect(result).toEqual({
        nodeLabels: [{ label: 'Product', count: 0, properties: [] }],
        relationshipTypes: [{ type: 'CONTAINS', count: 0, properties: [] }],
        indexes: [],
        constraints: [],
        statistics: {
          totalNodes: 100,
          totalRelationships: 50,
          databaseSize: 'Unknown',
          lastUpdated: expect.any(String)
        }
      });
    });
  });

  describe('error handling', () => {
    it('should handle database connection errors', async () => {
      mockSession.run.mockRejectedValue(new Error('Connection failed'));

      await expect(service.getInitialGraph()).rejects.toThrow('Connection failed');
      expect(mockSession.close).toHaveBeenCalled();
    });

    it('should handle session creation errors', async () => {
      mockDriver.session.mockImplementation(() => {
        throw new Error('Session creation failed');
      });

      await expect(service.getInitialGraph()).rejects.toThrow('Session creation failed');
    });
  });

  describe('data transformation', () => {
    it('should transform Neo4j integers', async () => {
      const mockRecords = [
        {
          get: jest.fn(() => ({
            identity: { toString: () => '1' },
            labels: ['Product'],
            properties: { 
              name: 'Product A',
              count: { toNumber: () => 42 }
            }
          }))
        }
      ];

      mockSession.run.mockResolvedValue({ records: mockRecords });

      const result = await service.searchNodes('test');

      expect(result[0].properties.count).toBe(42);
    });

    it('should transform Neo4j dates', async () => {
      const mockRecords = [
        {
          get: jest.fn(() => ({
            identity: { toString: () => '1' },
            labels: ['Product'],
            properties: { 
              name: 'Product A',
              createdAt: { toString: () => '2024-01-01T00:00:00.000Z' }
            }
          }))
        }
      ];

      mockSession.run.mockResolvedValue({ records: mockRecords });

      const result = await service.searchNodes('test');

      expect(result[0].properties.createdAt).toBe('2024-01-01T00:00:00.000Z');
    });

    it('should handle null and undefined values', async () => {
      const mockRecords = [
        {
          get: jest.fn(() => ({
            identity: { toString: () => '1' },
            labels: ['Product'],
            properties: { 
              name: 'Product A',
              nullValue: null,
              undefinedValue: undefined
            }
          }))
        }
      ];

      mockSession.run.mockResolvedValue({ records: mockRecords });

      const result = await service.searchNodes('test');

      expect(result[0].properties.nullValue).toBeNull();
      expect(result[0].properties.undefinedValue).toBeUndefined();
    });
  });
});
