/**
 * Feature Flags Service
 * 
 * Handles feature flag management, persistence, and user-specific flag resolution.
 * Supports A/B testing and gradual rollout scenarios.
 */

import { BaseService, ServiceConfig } from './BaseService';
import { FeatureFlags } from '@kg-visualizer/shared';

export interface FlagHistoryEntry {
  flagName: keyof FeatureFlags;
  oldValue: boolean | number;
  newValue: boolean | number;
  changedBy: string;
  changedAt: string;
  reason?: string;
}

export interface UserFlagOverride {
  userId: string;
  flagName: keyof FeatureFlags;
  value: boolean | number;
  expiresAt?: string;
  reason?: string;
}

export class FeatureFlagsService extends BaseService {
  private flags: FeatureFlags;
  private readonly history: FlagHistoryEntry[] = [];
  private readonly userOverrides: Map<string, UserFlagOverride[]> = new Map();
  private readonly defaultFlags: FeatureFlags;

  constructor(initialFlags: FeatureFlags, config?: ServiceConfig) {
    super({
      enableCaching: true,
      cacheTimeout: 60000, // 1 minute cache for flags
      enableMetrics: true,
      ...config
    });
    
    this.flags = { ...initialFlags };
    this.defaultFlags = { ...initialFlags };
  }

  /**
   * Get feature flags for a specific user
   */
  async getFlags(userId?: string): Promise<FeatureFlags> {
    return this.executeOperation(
      'getFlags',
      async () => {
        let userFlags = { ...this.flags };

        // Apply user-specific overrides if userId is provided
        if (userId) {
          const overrides = this.getUserOverrides(userId);
          for (const override of overrides) {
            if (!this.isOverrideExpired(override)) {
              (userFlags as any)[override.flagName] = override.value;
            }
          }

          // Apply A/B testing logic for percentage-based flags
          userFlags = this.applyABTesting(userFlags, userId);
        }

        return userFlags;
      },
      userId ? `flags-${userId}` : 'flags-default'
    );
  }

  /**
   * Update a specific feature flag
   */
  async updateFlag(
    flagName: keyof FeatureFlags, 
    value: boolean | number, 
    changedBy: string = 'system',
    reason?: string
  ): Promise<void> {
    return this.executeOperation(
      'updateFlag',
      async () => {
        const oldValue = this.flags[flagName];
        
        // Validate the new value
        this.validateFlagValue(flagName, value);
        
        // Update the flag
        (this.flags as any)[flagName] = value;
        
        // Record in history
        this.addToHistory({
          flagName,
          oldValue,
          newValue: value,
          changedBy,
          changedAt: new Date().toISOString(),
          reason
        });

        // Clear cache for all users since flags changed
        this.clearCache('flags-');

        this.logger.info('Feature flag updated', 'feature-flags-service', {
          flagName,
          oldValue,
          newValue: value,
          changedBy,
          reason
        });
      }
    );
  }

  /**
   * Reset all flags to default values
   */
  async resetFlags(changedBy: string = 'system'): Promise<void> {
    return this.executeOperation(
      'resetFlags',
      async () => {
        const oldFlags = { ...this.flags };
        this.flags = { ...this.defaultFlags };

        // Record reset in history
        for (const [flagName, newValue] of Object.entries(this.defaultFlags)) {
          const oldValue = oldFlags[flagName as keyof FeatureFlags];
          if (oldValue !== newValue) {
            this.addToHistory({
              flagName: flagName as keyof FeatureFlags,
              oldValue,
              newValue,
              changedBy,
              changedAt: new Date().toISOString(),
              reason: 'Reset to defaults'
            });
          }
        }

        // Clear all caches
        this.clearCache();

        this.logger.warn('All feature flags reset to defaults', 'feature-flags-service', {
          changedBy
        });
      }
    );
  }

  /**
   * Get flag change history
   */
  async getFlagHistory(flagName?: keyof FeatureFlags): Promise<FlagHistoryEntry[]> {
    return this.executeOperation(
      'getFlagHistory',
      async () => {
        if (flagName) {
          return this.history.filter(entry => entry.flagName === flagName);
        }
        return [...this.history];
      },
      flagName ? `history-${flagName}` : 'history-all'
    );
  }

  /**
   * Set user-specific flag override
   */
  async setUserOverride(
    userId: string,
    flagName: keyof FeatureFlags,
    value: boolean | number,
    expiresAt?: string,
    reason?: string
  ): Promise<void> {
    return this.executeOperation(
      'setUserOverride',
      async () => {
        this.validateFlagValue(flagName, value);

        const override: UserFlagOverride = {
          userId,
          flagName,
          value,
          expiresAt,
          reason
        };

        const userOverrides = this.userOverrides.get(userId) || [];
        
        // Remove existing override for this flag
        const filteredOverrides = userOverrides.filter(o => o.flagName !== flagName);
        filteredOverrides.push(override);
        
        this.userOverrides.set(userId, filteredOverrides);

        // Clear cache for this user
        this.clearCache(`flags-${userId}`);

        this.logger.info('User flag override set', 'feature-flags-service', {
          userId: userId.substring(0, 8) + '...',
          flagName,
          value,
          expiresAt,
          reason
        });
      }
    );
  }

  /**
   * Remove user-specific flag override
   */
  async removeUserOverride(userId: string, flagName: keyof FeatureFlags): Promise<void> {
    return this.executeOperation(
      'removeUserOverride',
      async () => {
        const userOverrides = this.userOverrides.get(userId) || [];
        const filteredOverrides = userOverrides.filter(o => o.flagName !== flagName);
        
        if (filteredOverrides.length === 0) {
          this.userOverrides.delete(userId);
        } else {
          this.userOverrides.set(userId, filteredOverrides);
        }

        // Clear cache for this user
        this.clearCache(`flags-${userId}`);

        this.logger.info('User flag override removed', 'feature-flags-service', {
          userId: userId.substring(0, 8) + '...',
          flagName
        });
      }
    );
  }

  /**
   * Validate flag values
   */
  validateFlags(flags: Partial<FeatureFlags>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    for (const [flagName, value] of Object.entries(flags)) {
      try {
        this.validateFlagValue(flagName as keyof FeatureFlags, value);
      } catch (error) {
        errors.push(`${flagName}: ${(error as Error).message}`);
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Get current flag statistics
   */
  getFlagStatistics(): {
    totalFlags: number;
    enabledFlags: number;
    userOverrides: number;
    historyEntries: number;
    lastChanged: string | null;
  } {
    const enabledFlags = Object.values(this.flags).filter(value => 
      typeof value === 'boolean' ? value : value > 0
    ).length;

    const totalUserOverrides = Array.from(this.userOverrides.values())
      .reduce((sum, overrides) => sum + overrides.length, 0);

    const lastChanged = this.history.length > 0 
      ? this.history[this.history.length - 1].changedAt 
      : null;

    return {
      totalFlags: Object.keys(this.flags).length,
      enabledFlags,
      userOverrides: totalUserOverrides,
      historyEntries: this.history.length,
      lastChanged
    };
  }

  /**
   * Export flags configuration
   */
  exportFlags(): {
    flags: FeatureFlags;
    history: FlagHistoryEntry[];
    userOverrides: Record<string, UserFlagOverride[]>;
    exportedAt: string;
  } {
    return {
      flags: { ...this.flags },
      history: [...this.history],
      userOverrides: Object.fromEntries(this.userOverrides),
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * Import flags configuration
   */
  async importFlags(
    config: {
      flags?: FeatureFlags;
      history?: FlagHistoryEntry[];
      userOverrides?: Record<string, UserFlagOverride[]>;
    },
    changedBy: string = 'system'
  ): Promise<void> {
    return this.executeOperation(
      'importFlags',
      async () => {
        if (config.flags) {
          const validation = this.validateFlags(config.flags);
          if (!validation.isValid) {
            throw new Error(`Invalid flags configuration: ${validation.errors.join(', ')}`);
          }

          this.flags = { ...config.flags };
        }

        if (config.history) {
          this.history.push(...config.history);
        }

        if (config.userOverrides) {
          for (const [userId, overrides] of Object.entries(config.userOverrides)) {
            this.userOverrides.set(userId, overrides);
          }
        }

        // Clear all caches
        this.clearCache();

        this.logger.info('Flags configuration imported', 'feature-flags-service', {
          changedBy,
          flagsCount: config.flags ? Object.keys(config.flags).length : 0,
          historyCount: config.history?.length || 0,
          userOverridesCount: config.userOverrides ? Object.keys(config.userOverrides).length : 0
        });
      }
    );
  }

  /**
   * Get user overrides for a specific user
   */
  private getUserOverrides(userId: string): UserFlagOverride[] {
    return this.userOverrides.get(userId) || [];
  }

  /**
   * Check if override is expired
   */
  private isOverrideExpired(override: UserFlagOverride): boolean {
    if (!override.expiresAt) {
      return false;
    }
    return new Date(override.expiresAt) < new Date();
  }

  /**
   * Apply A/B testing logic for percentage-based flags
   */
  private applyABTesting(flags: FeatureFlags, _userId: string): FeatureFlags {
    // const userHash = this.hashUserId(userId);
    
    // Apply traffic percentage for API
    if (typeof flags.TRAFFIC_PERCENTAGE_NEW_API === 'number') {
      // const shouldUseNewApi = userHash < flags.TRAFFIC_PERCENTAGE_NEW_API;
      // This doesn't override the flag, just provides context for routing decisions
    }

    return flags;
  }

  /**
   * Hash user ID for consistent A/B testing
   */
  // private hashUserId(userId: string): number {
  //   let hash = 0;
  //   for (let i = 0; i < userId.length; i++) {
  //     const char = userId.charCodeAt(i);
  //     hash = ((hash << 5) - hash) + char;
  //     hash = hash & hash; // Convert to 32-bit integer
  //   }
  //   return Math.abs(hash) % 100;
  // }

  /**
   * Validate individual flag value
   */
  private validateFlagValue(flagName: keyof FeatureFlags, value: any): void {
    if (value === null || value === undefined) {
      throw new Error('Flag value cannot be null or undefined');
    }

    // Check if it's a boolean flag
    const booleanFlags = [
      'NEW_CONTROLLER_LAYER',
      'NEW_SERVICE_LAYER',
      'NEW_REPOSITORY_PATTERN',
      'NEW_MIDDLEWARE_STACK',
      'NEW_CHAT_SERVICE',
      'CIRCUIT_BREAKER_ENABLED',
      'DUAL_EXECUTION_MODE',
      'PERFORMANCE_MONITORING',
      'AUTOMATIC_ROLLBACK',
      'DEBUG_MODE',
      'VERBOSE_LOGGING',
      'MIGRATION_METRICS'
    ];

    if (booleanFlags.includes(flagName as string)) {
      if (typeof value !== 'boolean') {
        throw new Error('Boolean flag must have a boolean value');
      }
    }

    // Check if it's a numeric flag
    const numericFlags = ['TRAFFIC_PERCENTAGE_NEW_API'];
    if (numericFlags.includes(flagName as string)) {
      if (typeof value !== 'number' || value < 0 || value > 100) {
        throw new Error('Percentage flag must be a number between 0 and 100');
      }
    }
  }

  /**
   * Add entry to history
   */
  private addToHistory(entry: FlagHistoryEntry): void {
    this.history.push(entry);
    
    // Keep only last 1000 entries
    if (this.history.length > 1000) {
      this.history.splice(0, this.history.length - 1000);
    }
  }
}
