/**
 * Configuration Module Index
 * 
 * Central export point for all configuration-related functionality.
 */

// Core configuration management
export {
  ConfigManager,
  configManager
} from './ConfigManager';

export type {
  AppConfig,
  DatabaseConfig,
  ServerConfig,
  CorsConfig,
  SecurityConfig,
  LoggingConfig,
  MonitoringConfig,
  FeatureFlagsConfig,
  LLMConfig
} from './ConfigManager';

// Configuration validation
export {
  ConfigValidator,
  configValidator
} from './ConfigValidator';

export type {
  ValidationResult,
  ValidationError,
  ValidationWarning,
  ValidationSummary
} from './ConfigValidator';

// Configuration service
export {
  ConfigService,
  configService
} from './ConfigService';

export type {
  ConfigChangeEvent,
  ConfigChange,
  ConfigHealth
} from './ConfigService';

// Environment-specific configurations
export { default as developmentConfig } from './environments/development';
export { default as productionConfig } from './environments/production';
export { default as testConfig } from './environments/test';

// Utility functions
import { configManager, type FeatureFlagsConfig } from './ConfigManager';
import { configValidator } from './ConfigValidator';
import { configService, type ConfigChangeEvent } from './ConfigService';

export const getConfig = () => configManager.getConfig();
export const getDatabaseConfig = () => configManager.getDatabaseConfig();
export const getServerConfig = () => configManager.getServerConfig();
export const getCorsConfig = () => configManager.getCorsConfig();
export const getSecurityConfig = () => configManager.getSecurityConfig();
export const getLoggingConfig = () => configManager.getLoggingConfig();
export const getMonitoringConfig = () => configManager.getMonitoringConfig();
export const getFeatureFlagsConfig = () => configManager.getFeatureFlagsConfig();
export const getLLMConfig = () => configManager.getLLMConfig();

// Configuration validation utilities
export const validateConfig = () => configValidator.validateConfiguration();
export const getConfigHealth = () => configService.getHealthStatus();

// Environment detection utilities
export const isProduction = () => configManager.getConfig().environment === 'production';
export const isDevelopment = () => configManager.getConfig().environment === 'development';
export const isTest = () => configManager.getConfig().environment === 'test';
export const isStaging = () => configManager.getConfig().environment === 'staging';

// Feature flag utilities
export const isFeatureEnabled = (feature: keyof FeatureFlagsConfig): boolean => {
  const flags = configManager.getFeatureFlagsConfig();
  return (flags as any)[feature] as boolean;
};

export const getTrafficPercentage = (): number => {
  return configManager.getFeatureFlagsConfig().trafficPercentageNewApi;
};

// Configuration change monitoring
export const onConfigChange = (callback: (event: ConfigChangeEvent) => void) => {
  configService.on('configChanged', callback);
};

export const offConfigChange = (callback: (event: ConfigChangeEvent) => void) => {
  configService.off('configChanged', callback);
};

// Configuration documentation
export const generateConfigDocs = () => configService.generateDocumentation();

// Configuration reload
export const reloadConfig = () => configService.reloadConfiguration();

// Environment variable helpers
export const getEnvVar = (name: string, defaultValue?: string): string | undefined => {
  return process.env[name] || defaultValue;
};

export const getEnvVarAsNumber = (name: string, defaultValue?: number): number => {
  const value = process.env[name];
  if (value === undefined) return defaultValue || 0;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? (defaultValue || 0) : parsed;
};

export const getEnvVarAsBoolean = (name: string, defaultValue?: boolean): boolean => {
  const value = process.env[name];
  if (value === undefined) return defaultValue || false;
  return value.toLowerCase() === 'true';
};

export const getEnvVarAsArray = (name: string, separator: string = ',', defaultValue?: string[]): string[] => {
  const value = process.env[name];
  if (value === undefined) return defaultValue || [];
  return value.split(separator).map(item => item.trim()).filter(item => item.length > 0);
};

// Configuration constants
export const CONFIG_CONSTANTS = {
  DEFAULT_PORT: 3002,
  DEFAULT_HOST: '0.0.0.0',
  DEFAULT_DATABASE: 'neo4j',
  DEFAULT_LOG_LEVEL: 'info',
  DEFAULT_BCRYPT_ROUNDS: 12,
  DEFAULT_JWT_EXPIRY: '24h',
  DEFAULT_RATE_LIMIT: 100,
  DEFAULT_RATE_LIMIT_WINDOW: 15 * 60 * 1000, // 15 minutes
  MIN_JWT_SECRET_LENGTH: 32,
  MIN_SESSION_SECRET_LENGTH: 32,
  MAX_CONNECTION_POOL_SIZE: 1000,
  MIN_CONNECTION_POOL_SIZE: 1,
  MAX_REQUEST_SIZE: '100mb',
  DEFAULT_REQUEST_SIZE: '10mb'
} as const;

// Configuration schema validation
export const CONFIG_SCHEMA_VERSION = '1.0.0';

// Export configuration health check for monitoring
export const configHealthCheck = async () => {
  try {
    const health = await configService.getHealthStatus();
    return {
      status: health.status,
      score: health.score,
      timestamp: new Date().toISOString(),
      details: {
        validationErrors: health.validationResult.summary.criticalErrors,
        warnings: health.validationResult.summary.warnings,
        environmentOverrides: health.environmentOverrides.length,
        watchedFiles: health.watchedFiles.length
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      score: 0,
      timestamp: new Date().toISOString(),
      error: (error as Error).message
    };
  }
};

// Configuration initialization
export const initializeConfig = async () => {
  try {
    // Validate configuration on startup
    const validation = await configValidator.validateConfiguration();
    
    if (validation.summary.criticalErrors > 0) {
      throw new Error(`Configuration has ${validation.summary.criticalErrors} critical errors`);
    }
    
    // Log configuration status
    const config = configManager.getConfig();
    console.log(`Configuration initialized for ${config.environment} environment`);
    console.log(`Configuration score: ${validation.summary.configurationScore}/100`);
    
    if (validation.summary.warnings > 0) {
      console.warn(`Configuration has ${validation.summary.warnings} warnings`);
    }
    
    return {
      success: true,
      environment: config.environment,
      score: validation.summary.configurationScore,
      warnings: validation.summary.warnings
    };
    
  } catch (error) {
    console.error('Configuration initialization failed:', (error as Error).message);
    throw error;
  }
};

// Configuration cleanup
export const cleanupConfig = () => {
  configService.destroy();
};
