/**
 * Test Environment Configuration
 * 
 * Configuration overrides for test environment.
 * Optimized for fast, isolated testing.
 */

import { type AppConfig } from '../ConfigManager';

export const testConfig: Partial<AppConfig> = {
  environment: 'test',
  
  database: {
    uri: process.env.TEST_NEO4J_URI || 'neo4j://localhost:7687',
    username: process.env.TEST_NEO4J_USERNAME || 'neo4j',
    password: process.env.TEST_NEO4J_PASSWORD || 'test_password',
    database: process.env.TEST_NEO4J_DATABASE || 'test',
    maxConnectionPoolSize: 5, // Minimal for testing
    connectionTimeout: 10000, // Shorter for faster test failures
    maxTransactionRetryTime: 10000,
    encrypted: false,
    trust: 'TRUST_ALL_CERTIFICATES'
  },

  server: {
    port: 0, // Random port for testing
    host: '127.0.0.1',
    timeout: 5000, // Short timeout for tests
    keepAliveTimeout: 1000,
    headersTimeout: 5000,
    maxRequestSize: '1mb', // Small for testing
    compression: false,
    trustProxy: false
  },

  cors: {
    origins: ['*'], // Allow all for testing
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['*'],
    exposedHeaders: ['*'],
    maxAge: 0 // No caching in tests
  },

  security: {
    jwtSecret: 'test-jwt-secret-32-characters-minimum-for-testing',
    sessionSecret: 'test-session-secret-32-characters-minimum-for-testing',
    adminApiKey: 'test-admin-key',
    bcryptRounds: 4, // Minimal for speed
    rateLimitWindowMs: 60000,
    rateLimitMax: 10000, // High limit for testing
    enableHttps: false,
    httpsOptions: undefined
  },

  logging: {
    level: 'error', // Minimal logging in tests
    format: 'text',
    enableConsole: false, // No console output in tests
    enableFile: false,
    filePath: './logs/test.log',
    maxFileSize: '1MB',
    maxFiles: 1,
    enableColors: false,
    enableTimestamp: false
  },

  monitoring: {
    enableHealthChecks: false, // Disabled for testing
    healthCheckInterval: 60000,
    enableMetrics: false,
    metricsPort: 9092,
    enablePrometheus: false,
    enableTracing: false,
    tracingEndpoint: undefined
  },

  featureFlags: {
    enableNewControllerLayer: true,
    enableNewServiceLayer: true,
    enableNewRepositoryPattern: true,
    enableNewMiddlewareStack: true,
    enableDualExecution: true, // Test both paths
    enablePerformanceMonitoring: false, // Disabled for speed
    enableAdvancedSecurity: false, // Simplified for testing
    trafficPercentageNewApi: 100
  },

  llm: {
    primaryProvider: 'ollama', // Use mock/local for testing
    fallbackProviders: [],
    retryAttempts: 1, // No retries in tests
    retryDelay: 100,
    timeout: 5000, // Short timeout
    providers: {
      ollama: {
        baseUrl: 'http://localhost:11434',
        model: 'test-model',
        timeout: 5000
      },
      openai: {
        apiKey: 'test-api-key',
        model: 'gpt-3.5-turbo',
        maxTokens: 100, // Small for testing
        temperature: 0.0 // Deterministic
      },
      anthropic: {
        apiKey: 'test-api-key',
        model: 'claude-3-haiku-20240307',
        maxTokens: 100,
        temperature: 0.0
      },
      google: {
        apiKey: 'test-api-key',
        model: 'gemini-pro',
        maxTokens: 100,
        temperature: 0.0
      },
      groq: {
        apiKey: 'test-api-key',
        model: 'mixtral-8x7b-32768',
        maxTokens: 100,
        temperature: 0.0
      }
    }
  }
};

export default testConfig;
