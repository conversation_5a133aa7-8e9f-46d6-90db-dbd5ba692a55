/**
 * Production Environment Configuration
 * 
 * Configuration overrides for production environment.
 * Optimized for security, performance, and reliability.
 */

import { type AppConfig } from '../ConfigManager';

export const productionConfig: Partial<AppConfig> = {
  environment: 'production',
  
  database: {
    // Production values should come from environment variables
    uri: process.env.NEO4J_URI || 'neo4j://production-neo4j:7687',
    username: process.env.NEO4J_USERNAME || 'neo4j',
    password: process.env.NEO4J_PASSWORD!, // Required in production
    database: process.env.NEO4J_DATABASE || 'neo4j',
    maxConnectionPoolSize: 100, // Higher for production load
    connectionTimeout: 30000,
    maxTransactionRetryTime: 30000,
    encrypted: true, // Always encrypted in production
    trust: 'TRUST_SYSTEM_CA_SIGNED_CERTIFICATES'
  },

  server: {
    port: parseInt(process.env.PORT || '3002', 10),
    host: process.env.HOST || '0.0.0.0',
    timeout: 30000, // Standard timeout
    keepAliveTimeout: 5000,
    headersTimeout: 60000,
    maxRequestSize: '10mb', // Reasonable limit
    compression: true, // Enable for bandwidth savings
    trustProxy: true // Behind load balancer
  },

  cors: {
    origins: process.env.CORS_ORIGINS?.split(',') || [], // Strict origins
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'X-Correlation-ID',
      'X-API-Key'
    ],
    exposedHeaders: [
      'X-Total-Count',
      'X-Rate-Limit-Remaining',
      'X-Rate-Limit-Reset'
    ],
    maxAge: 86400
  },

  security: {
    jwtSecret: process.env.JWT_SECRET!, // Required in production
    sessionSecret: process.env.SESSION_SECRET!, // Required in production
    adminApiKey: process.env.ADMIN_API_KEY,
    bcryptRounds: 12, // Strong hashing
    rateLimitWindowMs: 15 * 60 * 1000,
    rateLimitMax: 100, // Strict rate limiting
    enableHttps: process.env.ENABLE_HTTPS === 'true',
    httpsOptions: {
      keyPath: process.env.HTTPS_KEY_PATH,
      certPath: process.env.HTTPS_CERT_PATH
    }
  },

  logging: {
    level: 'info', // Less verbose in production
    format: 'json', // Structured logging
    enableConsole: true,
    enableFile: true, // File logging for persistence
    filePath: process.env.LOG_FILE_PATH || '/var/log/kg-visualizer/app.log',
    maxFileSize: '100MB',
    maxFiles: 10,
    enableColors: false, // No colors in production logs
    enableTimestamp: true
  },

  monitoring: {
    enableHealthChecks: true,
    healthCheckInterval: 30000,
    enableMetrics: true, // Essential for production
    metricsPort: parseInt(process.env.METRICS_PORT || '9090', 10),
    enablePrometheus: true,
    enableTracing: process.env.ENABLE_TRACING === 'true',
    tracingEndpoint: process.env.TRACING_ENDPOINT
  },

  featureFlags: {
    enableNewControllerLayer: process.env.FEATURE_FLAG_NEW_CONTROLLER_LAYER !== 'false',
    enableNewServiceLayer: process.env.FEATURE_FLAG_NEW_SERVICE_LAYER !== 'false',
    enableNewRepositoryPattern: process.env.FEATURE_FLAG_NEW_REPOSITORY_PATTERN !== 'false',
    enableNewMiddlewareStack: process.env.FEATURE_FLAG_NEW_MIDDLEWARE_STACK !== 'false',
    enableDualExecution: process.env.FEATURE_FLAG_DUAL_EXECUTION === 'true',
    enablePerformanceMonitoring: true, // Always enabled in production
    enableAdvancedSecurity: true, // Always enabled in production
    trafficPercentageNewApi: parseInt(process.env.FEATURE_FLAG_TRAFFIC_PERCENTAGE || '100', 10)
  },

  llm: {
    primaryProvider: (process.env.LLM_PRIMARY_PROVIDER as any) || 'openai',
    fallbackProviders: process.env.LLM_FALLBACK_PROVIDERS?.split(',') || ['anthropic', 'google'],
    retryAttempts: 3,
    retryDelay: 1000,
    timeout: 30000,
    providers: {
      openai: {
        apiKey: process.env.OPENAI_API_KEY,
        model: process.env.OPENAI_MODEL || 'gpt-4',
        maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '4000', 10),
        temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7')
      },
      anthropic: {
        apiKey: process.env.ANTHROPIC_API_KEY,
        model: process.env.ANTHROPIC_MODEL || 'claude-3-sonnet-20240229',
        maxTokens: parseInt(process.env.ANTHROPIC_MAX_TOKENS || '4000', 10),
        temperature: parseFloat(process.env.ANTHROPIC_TEMPERATURE || '0.7')
      },
      google: {
        apiKey: process.env.GOOGLE_API_KEY,
        model: process.env.GOOGLE_MODEL || 'gemini-pro',
        maxTokens: parseInt(process.env.GOOGLE_MAX_TOKENS || '4000', 10),
        temperature: parseFloat(process.env.GOOGLE_TEMPERATURE || '0.7')
      },
      groq: {
        apiKey: process.env.GROQ_API_KEY,
        model: process.env.GROQ_MODEL || 'mixtral-8x7b-32768',
        maxTokens: parseInt(process.env.GROQ_MAX_TOKENS || '4000', 10),
        temperature: parseFloat(process.env.GROQ_TEMPERATURE || '0.7')
      },
      ollama: {
        baseUrl: process.env.OLLAMA_BASE_URL || 'http://ollama:11434',
        model: process.env.OLLAMA_MODEL || 'llama2',
        timeout: parseInt(process.env.OLLAMA_TIMEOUT || '30000', 10)
      }
    }
  }
};

export default productionConfig;
