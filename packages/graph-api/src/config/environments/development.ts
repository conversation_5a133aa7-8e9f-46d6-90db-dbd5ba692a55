/**
 * Development Environment Configuration
 * 
 * Configuration overrides for development environment.
 * Optimized for developer experience and debugging.
 */

import { type AppConfig } from '../ConfigManager';

export const developmentConfig: Partial<AppConfig> = {
  environment: 'development',
  
  database: {
    uri: 'neo4j://localhost:7687',
    username: 'neo4j',
    password: 'development_password',
    database: 'neo4j',
    maxConnectionPoolSize: 20, // Lower for development
    connectionTimeout: 30000,
    maxTransactionRetryTime: 30000,
    encrypted: false,
    trust: 'TRUST_ALL_CERTIFICATES'
  },

  server: {
    port: 3002,
    host: '0.0.0.0',
    timeout: 60000, // Longer timeout for debugging
    keepAliveTimeout: 5000,
    headersTimeout: 60000,
    maxRequestSize: '50mb', // Larger for development testing
    compression: false, // Disabled for easier debugging
    trustProxy: false
  },

  cors: {
    origins: ['*'], // Allow all origins in development
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'],
    allowedHeaders: ['*'],
    exposedHeaders: ['X-Total-Count', 'X-Rate-Limit-Remaining', 'X-Debug-Info'],
    maxAge: 86400
  },

  security: {
    jwtSecret: 'development-jwt-secret-change-in-production-32-chars-minimum',
    sessionSecret: 'development-session-secret-change-in-production-32-chars-minimum',
    adminApiKey: 'dev-admin-key-123',
    bcryptRounds: 8, // Lower for faster development
    rateLimitWindowMs: 15 * 60 * 1000,
    rateLimitMax: 10000, // Very high limit for development
    enableHttps: false,
    httpsOptions: undefined
  },

  logging: {
    level: 'debug',
    format: 'text',
    enableConsole: true,
    enableFile: false, // No file logging in development
    filePath: './logs/development.log',
    maxFileSize: '10MB',
    maxFiles: 3,
    enableColors: true,
    enableTimestamp: true
  },

  monitoring: {
    enableHealthChecks: true,
    healthCheckInterval: 60000, // Less frequent in development
    enableMetrics: false, // Disabled for cleaner development
    metricsPort: 9091,
    enablePrometheus: false,
    enableTracing: false,
    tracingEndpoint: undefined
  },

  featureFlags: {
    enableNewControllerLayer: true,
    enableNewServiceLayer: true,
    enableNewRepositoryPattern: true,
    enableNewMiddlewareStack: true,
    enableDualExecution: true, // Enable for testing
    enablePerformanceMonitoring: true,
    enableAdvancedSecurity: false, // Relaxed for development
    trafficPercentageNewApi: 100
  },

  llm: {
    primaryProvider: 'ollama', // Use local Ollama for development
    fallbackProviders: ['openai'],
    retryAttempts: 2, // Fewer retries for faster feedback
    retryDelay: 500,
    timeout: 60000, // Longer timeout for local models
    providers: {
      ollama: {
        baseUrl: 'http://localhost:11434',
        model: 'llama2',
        timeout: 60000
      },
      openai: {
        apiKey: undefined, // Optional in development
        model: 'gpt-3.5-turbo',
        maxTokens: 2000, // Lower for cost savings
        temperature: 0.7
      },
      anthropic: {
        apiKey: undefined,
        model: 'claude-3-haiku-20240307', // Cheaper model for development
        maxTokens: 2000,
        temperature: 0.7
      },
      google: {
        apiKey: undefined,
        model: 'gemini-pro',
        maxTokens: 2000,
        temperature: 0.7
      },
      groq: {
        apiKey: undefined,
        model: 'mixtral-8x7b-32768',
        maxTokens: 2000,
        temperature: 0.7
      }
    }
  }
};

export default developmentConfig;
