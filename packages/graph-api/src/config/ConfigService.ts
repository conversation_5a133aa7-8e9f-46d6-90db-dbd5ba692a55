/**
 * Configuration Service
 * 
 * Service layer for configuration management with health checks,
 * hot reloading, and integration with the application architecture.
 */

import { EventEmitter } from 'events';
import { watch } from 'fs';
import { resolve } from 'path';
import { logger } from '@kg-visualizer/shared';
import { configManager, type AppConfig } from './ConfigManager';
import { configValidator, type ValidationResult } from './ConfigValidator';
import developmentConfig from './environments/development';
import productionConfig from './environments/production';
import testConfig from './environments/test';

export interface ConfigChangeEvent {
  timestamp: Date;
  changes: ConfigChange[];
  previousConfig: AppConfig;
  newConfig: AppConfig;
  validationResult: ValidationResult;
}

export interface ConfigChange {
  path: string;
  oldValue: any;
  newValue: any;
  type: 'added' | 'modified' | 'removed';
}

export interface ConfigHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  score: number;
  lastValidation: Date;
  validationResult: ValidationResult;
  environmentOverrides: string[];
  watchedFiles: string[];
}

export class ConfigService extends EventEmitter {
  private static instance: ConfigService;
  private lastValidation: ValidationResult | null = null;
  private lastValidationTime: Date | null = null;
  private watchedFiles: string[] = [];
  private fileWatchers: any[] = [];
  private readonly logger = logger.child('config-service');

  private constructor() {
    super();
    this.initializeWatchers();
  }

  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  /**
   * Get current configuration
   */
  getConfig(): AppConfig {
    return configManager.getConfig();
  }

  /**
   * Get configuration for specific section
   */
  getDatabaseConfig() {
    return configManager.getDatabaseConfig();
  }

  getServerConfig() {
    return configManager.getServerConfig();
  }

  getCorsConfig() {
    return configManager.getCorsConfig();
  }

  getSecurityConfig() {
    return configManager.getSecurityConfig();
  }

  getLoggingConfig() {
    return configManager.getLoggingConfig();
  }

  getMonitoringConfig() {
    return configManager.getMonitoringConfig();
  }

  getFeatureFlagsConfig() {
    return configManager.getFeatureFlagsConfig();
  }

  getLLMConfig() {
    return configManager.getLLMConfig();
  }

  /**
   * Validate current configuration
   */
  async validateConfiguration(): Promise<ValidationResult> {
    try {
      const result = await configValidator.validateConfiguration();
      this.lastValidation = result;
      this.lastValidationTime = new Date();

      this.logger.info('Configuration validation completed', 'config-service', {
        isValid: result.isValid,
        score: result.summary.configurationScore,
        criticalErrors: result.summary.criticalErrors,
        warnings: result.summary.warnings
      });

      return result;
    } catch (error) {
      this.logger.error('Configuration validation failed', 'config-service', {
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Reload configuration from environment
   */
  async reloadConfiguration(): Promise<ConfigChangeEvent> {
    const previousConfig = { ...this.getConfig() };
    
    try {
      configManager.reloadConfiguration();
      const newConfig = this.getConfig();
      const validationResult = await this.validateConfiguration();
      
      const changes = this.detectChanges(previousConfig, newConfig);
      
      const changeEvent: ConfigChangeEvent = {
        timestamp: new Date(),
        changes,
        previousConfig,
        newConfig,
        validationResult
      };

      this.emit('configChanged', changeEvent);
      
      this.logger.info('Configuration reloaded', 'config-service', {
        changesCount: changes.length,
        isValid: validationResult.isValid,
        score: validationResult.summary.configurationScore
      });

      return changeEvent;
    } catch (error) {
      this.logger.error('Configuration reload failed', 'config-service', {
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Get configuration health status
   */
  async getHealthStatus(): Promise<ConfigHealth> {
    let validationResult = this.lastValidation;
    
    if (!validationResult || this.isValidationStale()) {
      validationResult = await this.validateConfiguration();
    }

    const status = this.determineHealthStatus(validationResult);
    const environmentOverrides = this.getEnvironmentOverrides();

    return {
      status,
      score: validationResult.summary.configurationScore,
      lastValidation: this.lastValidationTime || new Date(),
      validationResult,
      environmentOverrides,
      watchedFiles: this.watchedFiles
    };
  }

  /**
   * Get environment-specific configuration
   */
  getEnvironmentConfig(environment?: string): any {
    const env = environment || this.getConfig().environment;

    switch (env) {
      case 'development':
        return developmentConfig;
      case 'production':
        return productionConfig;
      case 'test':
        return testConfig;
      default:
        return {};
    }
  }

  /**
   * Generate configuration documentation
   */
  generateDocumentation(): string {
    const config = this.getConfig();
    const validation = this.lastValidation;
    
    let doc = '# Configuration Documentation\n\n';
    
    // Environment info
    doc += `## Environment: ${config.environment}\n`;
    doc += `**Version**: ${config.version}\n\n`;
    
    // Database configuration
    doc += '## Database Configuration\n';
    doc += `- **URI**: ${config.database.uri.replace(/\/\/.*@/, '//***@')}\n`;
    doc += `- **Database**: ${config.database.database}\n`;
    doc += `- **Max Pool Size**: ${config.database.maxConnectionPoolSize}\n`;
    doc += `- **Connection Timeout**: ${config.database.connectionTimeout}ms\n\n`;
    
    // Server configuration
    doc += '## Server Configuration\n';
    doc += `- **Port**: ${config.server.port}\n`;
    doc += `- **Host**: ${config.server.host}\n`;
    doc += `- **Timeout**: ${config.server.timeout}ms\n`;
    doc += `- **Compression**: ${config.server.compression ? 'Enabled' : 'Disabled'}\n\n`;
    
    // Security configuration
    doc += '## Security Configuration\n';
    doc += `- **JWT Secret**: ${config.security.jwtSecret.length} characters\n`;
    doc += `- **Bcrypt Rounds**: ${config.security.bcryptRounds}\n`;
    doc += `- **Rate Limit**: ${config.security.rateLimitMax} requests per ${config.security.rateLimitWindowMs}ms\n\n`;
    
    // Feature flags
    doc += '## Feature Flags\n';
    Object.entries(config.featureFlags).forEach(([key, value]) => {
      doc += `- **${key}**: ${value}\n`;
    });
    doc += '\n';
    
    // Validation results
    if (validation) {
      doc += '## Validation Results\n';
      doc += `- **Score**: ${validation.summary.configurationScore}/100\n`;
      doc += `- **Critical Errors**: ${validation.summary.criticalErrors}\n`;
      doc += `- **Warnings**: ${validation.summary.warnings}\n\n`;
      
      if (validation.errors.length > 0) {
        doc += '### Errors\n';
        validation.errors.forEach(error => {
          doc += `- **${error.field}** (${error.severity}): ${error.message}\n`;
        });
        doc += '\n';
      }
    }
    
    return doc;
  }

  /**
   * Initialize file watchers for hot reloading
   */
  private initializeWatchers(): void {
    const envFiles = [
      '.env',
      '.env.local',
      `.env.${process.env.NODE_ENV}`,
      `.env.${process.env.NODE_ENV}.local`
    ];

    envFiles.forEach(file => {
      const filePath = resolve(process.cwd(), file);
      this.watchedFiles.push(filePath);
      
      try {
        const watcher = watch(filePath, { persistent: false }, (eventType) => {
          if (eventType === 'change') {
            this.logger.info(`Environment file changed: ${file}`, 'config-service');
            this.handleFileChange(file);
          }
        });
        
        this.fileWatchers.push(watcher);
      } catch (error) {
        // File doesn't exist, which is fine
      }
    });
  }

  /**
   * Handle file change events
   */
  private async handleFileChange(file: string): Promise<void> {
    try {
      // Debounce rapid file changes
      setTimeout(async () => {
        await this.reloadConfiguration();
      }, 1000);
    } catch (error) {
      this.logger.error(`Failed to handle file change for ${file}`, 'config-service', {
        error: (error as Error).message
      });
    }
  }

  /**
   * Detect changes between configurations
   */
  private detectChanges(oldConfig: AppConfig, newConfig: AppConfig): ConfigChange[] {
    const changes: ConfigChange[] = [];
    
    const compareObjects = (obj1: any, obj2: any, path: string = '') => {
      const keys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);
      
      for (const key of keys) {
        const currentPath = path ? `${path}.${key}` : key;
        const oldValue = obj1[key];
        const newValue = obj2[key];
        
        if (!(key in obj1)) {
          changes.push({
            path: currentPath,
            oldValue: undefined,
            newValue,
            type: 'added'
          });
        } else if (!(key in obj2)) {
          changes.push({
            path: currentPath,
            oldValue,
            newValue: undefined,
            type: 'removed'
          });
        } else if (typeof oldValue === 'object' && typeof newValue === 'object' && 
                   oldValue !== null && newValue !== null) {
          compareObjects(oldValue, newValue, currentPath);
        } else if (oldValue !== newValue) {
          changes.push({
            path: currentPath,
            oldValue,
            newValue,
            type: 'modified'
          });
        }
      }
    };
    
    compareObjects(oldConfig, newConfig);
    return changes;
  }

  /**
   * Determine health status from validation result
   */
  private determineHealthStatus(validation: ValidationResult): 'healthy' | 'degraded' | 'unhealthy' {
    if (validation.summary.criticalErrors > 0) {
      return 'unhealthy';
    }
    
    if (validation.summary.highErrors > 0 || validation.summary.configurationScore < 70) {
      return 'degraded';
    }
    
    return 'healthy';
  }

  /**
   * Check if validation is stale
   */
  private isValidationStale(): boolean {
    if (!this.lastValidationTime) return true;
    
    const staleThreshold = 5 * 60 * 1000; // 5 minutes
    return Date.now() - this.lastValidationTime.getTime() > staleThreshold;
  }

  /**
   * Get list of environment variable overrides
   */
  private getEnvironmentOverrides(): string[] {
    const overrides: string[] = [];
    const envVars = [
      'NODE_ENV', 'PORT', 'DATABASE_URI', 'JWT_SECRET', 'SESSION_SECRET',
      'LOG_LEVEL', 'CORS_ORIGINS', 'OPENAI_API_KEY', 'ANTHROPIC_API_KEY'
    ];
    
    envVars.forEach(varName => {
      if (process.env[varName]) {
        overrides.push(varName);
      }
    });
    
    return overrides;
  }

  /**
   * Cleanup watchers
   */
  destroy(): void {
    this.fileWatchers.forEach(watcher => {
      try {
        watcher.close();
      } catch (error) {
        // Ignore cleanup errors
      }
    });
    
    this.fileWatchers = [];
    this.removeAllListeners();
  }
}

// Export singleton instance
export const configService = ConfigService.getInstance();
