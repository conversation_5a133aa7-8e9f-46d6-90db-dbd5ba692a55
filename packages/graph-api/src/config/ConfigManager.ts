/**
 * Configuration Manager
 * 
 * Centralized configuration system for database, server, and environment settings.
 * Provides type-safe configuration loading with validation and environment variable handling.
 */

import { z } from 'zod';
import { config } from 'dotenv';
import { resolve } from 'path';
import { existsSync } from 'fs';
import { logger } from '@kg-visualizer/shared';

// Load environment variables from .env files
const loadEnvironmentFiles = () => {
  const environment = process.env.NODE_ENV || 'development';
  const envFiles = [
    `.env.${environment}.local`,
    `.env.local`,
    `.env.${environment}`,
    '.env'
  ];

  for (const envFile of envFiles) {
    const envPath = resolve(process.cwd(), envFile);
    if (existsSync(envPath)) {
      config({ path: envPath });
      logger.info(`Loaded environment file: ${envFile}`, 'config-manager');
    }
  }
};

// Initialize environment loading
loadEnvironmentFiles();

// Configuration schemas
const DatabaseConfigSchema = z.object({
  uri: z.string().url('Database URI must be a valid URL'),
  username: z.string().min(1, 'Database username is required'),
  password: z.string().min(1, 'Database password is required'),
  database: z.string().default('neo4j'),
  maxConnectionPoolSize: z.number().min(1).max(1000).default(50),
  connectionTimeout: z.number().min(1000).max(60000).default(30000),
  maxTransactionRetryTime: z.number().min(1000).max(60000).default(30000),
  encrypted: z.boolean().default(false),
  trust: z.enum(['TRUST_ALL_CERTIFICATES', 'TRUST_SYSTEM_CA_SIGNED_CERTIFICATES']).default('TRUST_ALL_CERTIFICATES')
});

const ServerConfigSchema = z.object({
  port: z.number().min(1000).max(65535).default(3002),
  host: z.string().default('0.0.0.0'),
  timeout: z.number().min(1000).max(300000).default(30000),
  keepAliveTimeout: z.number().min(1000).max(300000).default(5000),
  headersTimeout: z.number().min(1000).max(300000).default(60000),
  maxRequestSize: z.string().default('10mb'),
  compression: z.boolean().default(true),
  trustProxy: z.boolean().default(false)
});

const CorsConfigSchema = z.object({
  origins: z.array(z.string()).default(['http://localhost:3000', 'http://localhost:3001']),
  credentials: z.boolean().default(true),
  methods: z.array(z.string()).default(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS']),
  allowedHeaders: z.array(z.string()).default(['Content-Type', 'Authorization', 'X-Requested-With', 'X-Correlation-ID']),
  exposedHeaders: z.array(z.string()).default(['X-Total-Count', 'X-Rate-Limit-Remaining']),
  maxAge: z.number().default(86400)
});

const SecurityConfigSchema = z.object({
  jwtSecret: z.string().min(32, 'JWT secret must be at least 32 characters'),
  sessionSecret: z.string().min(32, 'Session secret must be at least 32 characters'),
  adminApiKey: z.string().optional(),
  bcryptRounds: z.number().min(8).max(15).default(12),
  rateLimitWindowMs: z.number().default(15 * 60 * 1000), // 15 minutes
  rateLimitMax: z.number().default(100),
  enableHttps: z.boolean().default(false),
  httpsOptions: z.object({
    keyPath: z.string().optional(),
    certPath: z.string().optional()
  }).optional()
});

const LoggingConfigSchema = z.object({
  level: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  format: z.enum(['json', 'text']).default('text'),
  enableConsole: z.boolean().default(true),
  enableFile: z.boolean().default(false),
  filePath: z.string().default('./logs/app.log'),
  maxFileSize: z.string().default('10MB'),
  maxFiles: z.number().default(5),
  enableColors: z.boolean().default(true),
  enableTimestamp: z.boolean().default(true)
});

const MonitoringConfigSchema = z.object({
  enableHealthChecks: z.boolean().default(true),
  healthCheckInterval: z.number().default(30000),
  enableMetrics: z.boolean().default(true),
  metricsPort: z.number().min(1000).max(65535).default(9090),
  enablePrometheus: z.boolean().default(false),
  enableTracing: z.boolean().default(false),
  tracingEndpoint: z.string().optional()
});

const FeatureFlagsConfigSchema = z.object({
  enableNewControllerLayer: z.boolean().default(true),
  enableNewServiceLayer: z.boolean().default(true),
  enableNewRepositoryPattern: z.boolean().default(true),
  enableNewMiddlewareStack: z.boolean().default(true),
  enableDualExecution: z.boolean().default(false),
  enablePerformanceMonitoring: z.boolean().default(true),
  enableAdvancedSecurity: z.boolean().default(true),
  trafficPercentageNewApi: z.number().min(0).max(100).default(100)
});

const LLMConfigSchema = z.object({
  primaryProvider: z.enum(['openai', 'anthropic', 'google', 'groq', 'ollama']).default('openai'),
  fallbackProviders: z.array(z.string()).default([]),
  retryAttempts: z.number().min(0).max(10).default(3),
  retryDelay: z.number().min(100).max(10000).default(1000),
  timeout: z.number().min(1000).max(300000).default(30000),
  providers: z.object({
    openai: z.object({
      apiKey: z.string().optional(),
      model: z.string().default('gpt-3.5-turbo'),
      maxTokens: z.number().default(4000),
      temperature: z.number().min(0).max(2).default(0.7)
    }).optional(),
    anthropic: z.object({
      apiKey: z.string().optional(),
      model: z.string().default('claude-3-sonnet-20240229'),
      maxTokens: z.number().default(4000),
      temperature: z.number().min(0).max(1).default(0.7)
    }).optional(),
    google: z.object({
      apiKey: z.string().optional(),
      model: z.string().default('gemini-pro'),
      maxTokens: z.number().default(4000),
      temperature: z.number().min(0).max(1).default(0.7)
    }).optional(),
    groq: z.object({
      apiKey: z.string().optional(),
      model: z.string().default('mixtral-8x7b-32768'),
      maxTokens: z.number().default(4000),
      temperature: z.number().min(0).max(2).default(0.7)
    }).optional(),
    ollama: z.object({
      baseUrl: z.string().url().default('http://localhost:11434'),
      model: z.string().default('llama2'),
      timeout: z.number().default(30000)
    }).optional()
  }).default({})
});

const AppConfigSchema = z.object({
  environment: z.enum(['development', 'staging', 'production', 'test']).default('development'),
  version: z.string().default('1.0.0'),
  database: DatabaseConfigSchema,
  server: ServerConfigSchema,
  cors: CorsConfigSchema,
  security: SecurityConfigSchema,
  logging: LoggingConfigSchema,
  monitoring: MonitoringConfigSchema,
  featureFlags: FeatureFlagsConfigSchema,
  llm: LLMConfigSchema
});

export type AppConfig = z.infer<typeof AppConfigSchema>;
export type DatabaseConfig = z.infer<typeof DatabaseConfigSchema>;
export type ServerConfig = z.infer<typeof ServerConfigSchema>;
export type CorsConfig = z.infer<typeof CorsConfigSchema>;
export type SecurityConfig = z.infer<typeof SecurityConfigSchema>;
export type LoggingConfig = z.infer<typeof LoggingConfigSchema>;
export type MonitoringConfig = z.infer<typeof MonitoringConfigSchema>;
export type FeatureFlagsConfig = z.infer<typeof FeatureFlagsConfigSchema>;
export type LLMConfig = z.infer<typeof LLMConfigSchema>;

export class ConfigManager {
  private static instance: ConfigManager;
  private config: AppConfig;
  private readonly logger = logger.child('config-manager');

  private constructor() {
    this.config = this.loadConfiguration();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * Get the complete application configuration
   */
  public getConfig(): AppConfig {
    return this.config;
  }

  /**
   * Get database configuration
   */
  public getDatabaseConfig(): DatabaseConfig {
    return this.config.database;
  }

  /**
   * Get server configuration
   */
  public getServerConfig(): ServerConfig {
    return this.config.server;
  }

  /**
   * Get CORS configuration
   */
  public getCorsConfig(): CorsConfig {
    return this.config.cors;
  }

  /**
   * Get security configuration
   */
  public getSecurityConfig(): SecurityConfig {
    return this.config.security;
  }

  /**
   * Get logging configuration
   */
  public getLoggingConfig(): LoggingConfig {
    return this.config.logging;
  }

  /**
   * Get monitoring configuration
   */
  public getMonitoringConfig(): MonitoringConfig {
    return this.config.monitoring;
  }

  /**
   * Get feature flags configuration
   */
  public getFeatureFlagsConfig(): FeatureFlagsConfig {
    return this.config.featureFlags;
  }

  /**
   * Get LLM configuration
   */
  public getLLMConfig(): LLMConfig {
    return this.config.llm;
  }

  /**
   * Reload configuration from environment variables
   */
  public reloadConfiguration(): void {
    loadEnvironmentFiles();
    this.config = this.loadConfiguration();
    this.logger.info('Configuration reloaded', 'config-manager');
  }

  /**
   * Validate configuration
   */
  public validateConfiguration(): { isValid: boolean; errors: string[] } {
    try {
      AppConfigSchema.parse(this.getRawConfiguration());
      return { isValid: true, errors: [] };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
        return { isValid: false, errors };
      }
      return { isValid: false, errors: [(error as Error).message] };
    }
  }

  /**
   * Get configuration for specific environment
   */
  public getEnvironmentConfig(environment: string): any {
    const envConfigs = {
      development: this.getDevelopmentConfig(),
      staging: this.getStagingConfig(),
      production: this.getProductionConfig(),
      test: this.getTestConfig()
    };

    return envConfigs[environment as keyof typeof envConfigs] || {};
  }

  /**
   * Load configuration from environment variables
   */
  private loadConfiguration(): AppConfig {
    try {
      const rawConfig = this.getRawConfiguration();
      const validatedConfig = AppConfigSchema.parse(rawConfig);
      
      this.logger.info('Configuration loaded successfully', 'config-manager', {
        environment: validatedConfig.environment,
        version: validatedConfig.version,
        databaseUri: validatedConfig.database.uri.replace(/\/\/.*@/, '//***@'), // Hide credentials
        serverPort: validatedConfig.server.port
      });

      return validatedConfig;
    } catch (error) {
      this.logger.error('Failed to load configuration', 'config-manager', {
        error: (error as Error).message
      });
      throw new Error(`Configuration validation failed: ${(error as Error).message}`);
    }
  }

  /**
   * Get raw configuration from environment variables
   */
  private getRawConfiguration(): any {
    return {
      environment: process.env.NODE_ENV || 'development',
      version: process.env.APP_VERSION || '1.0.0',
      database: {
        uri: process.env.DATABASE_URI || process.env.NEO4J_URI || 'neo4j://localhost:7687',
        username: process.env.DATABASE_USERNAME || process.env.NEO4J_USERNAME || 'neo4j',
        password: process.env.DATABASE_PASSWORD || process.env.NEO4J_PASSWORD || 'password',
        database: process.env.DATABASE_NAME || process.env.NEO4J_DATABASE || 'neo4j',
        maxConnectionPoolSize: parseInt(process.env.DATABASE_MAX_POOL_SIZE || '50', 10),
        connectionTimeout: parseInt(process.env.DATABASE_CONNECTION_TIMEOUT || '30000', 10),
        maxTransactionRetryTime: parseInt(process.env.DATABASE_MAX_RETRY_TIME || '30000', 10),
        encrypted: process.env.DATABASE_ENCRYPTED === 'true',
        trust: process.env.DATABASE_TRUST || 'TRUST_ALL_CERTIFICATES'
      },
      server: {
        port: parseInt(process.env.PORT || process.env.SERVER_PORT || '3002', 10),
        host: process.env.HOST || process.env.SERVER_HOST || '0.0.0.0',
        timeout: parseInt(process.env.SERVER_TIMEOUT || '30000', 10),
        keepAliveTimeout: parseInt(process.env.SERVER_KEEP_ALIVE_TIMEOUT || '5000', 10),
        headersTimeout: parseInt(process.env.SERVER_HEADERS_TIMEOUT || '60000', 10),
        maxRequestSize: process.env.SERVER_MAX_REQUEST_SIZE || '10mb',
        compression: process.env.SERVER_COMPRESSION !== 'false',
        trustProxy: process.env.SERVER_TRUST_PROXY === 'true'
      },
      cors: {
        origins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
        credentials: process.env.CORS_CREDENTIALS !== 'false',
        methods: process.env.CORS_METHODS?.split(',') || ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: process.env.CORS_ALLOWED_HEADERS?.split(',') || ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Correlation-ID'],
        exposedHeaders: process.env.CORS_EXPOSED_HEADERS?.split(',') || ['X-Total-Count', 'X-Rate-Limit-Remaining'],
        maxAge: parseInt(process.env.CORS_MAX_AGE || '86400', 10)
      },
      security: {
        jwtSecret: process.env.JWT_SECRET || this.generateSecret(),
        sessionSecret: process.env.SESSION_SECRET || this.generateSecret(),
        adminApiKey: process.env.ADMIN_API_KEY,
        bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
        rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
        rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
        enableHttps: process.env.ENABLE_HTTPS === 'true',
        httpsOptions: {
          keyPath: process.env.HTTPS_KEY_PATH,
          certPath: process.env.HTTPS_CERT_PATH
        }
      },
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'text',
        enableConsole: process.env.LOG_ENABLE_CONSOLE !== 'false',
        enableFile: process.env.LOG_ENABLE_FILE === 'true',
        filePath: process.env.LOG_FILE_PATH || './logs/app.log',
        maxFileSize: process.env.LOG_MAX_FILE_SIZE || '10MB',
        maxFiles: parseInt(process.env.LOG_MAX_FILES || '5', 10),
        enableColors: process.env.LOG_ENABLE_COLORS !== 'false',
        enableTimestamp: process.env.LOG_ENABLE_TIMESTAMP !== 'false'
      },
      monitoring: {
        enableHealthChecks: process.env.ENABLE_HEALTH_CHECKS !== 'false',
        healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000', 10),
        enableMetrics: process.env.ENABLE_METRICS !== 'false',
        metricsPort: parseInt(process.env.METRICS_PORT || '9090', 10),
        enablePrometheus: process.env.ENABLE_PROMETHEUS === 'true',
        enableTracing: process.env.ENABLE_TRACING === 'true',
        tracingEndpoint: process.env.TRACING_ENDPOINT
      },
      featureFlags: {
        enableNewControllerLayer: process.env.FEATURE_FLAG_NEW_CONTROLLER_LAYER !== 'false',
        enableNewServiceLayer: process.env.FEATURE_FLAG_NEW_SERVICE_LAYER !== 'false',
        enableNewRepositoryPattern: process.env.FEATURE_FLAG_NEW_REPOSITORY_PATTERN !== 'false',
        enableNewMiddlewareStack: process.env.FEATURE_FLAG_NEW_MIDDLEWARE_STACK !== 'false',
        enableDualExecution: process.env.FEATURE_FLAG_DUAL_EXECUTION === 'true',
        enablePerformanceMonitoring: process.env.FEATURE_FLAG_PERFORMANCE_MONITORING !== 'false',
        enableAdvancedSecurity: process.env.FEATURE_FLAG_ADVANCED_SECURITY !== 'false',
        trafficPercentageNewApi: parseInt(process.env.FEATURE_FLAG_TRAFFIC_PERCENTAGE || '100', 10)
      },
      llm: {
        primaryProvider: process.env.LLM_PRIMARY_PROVIDER || 'openai',
        fallbackProviders: process.env.LLM_FALLBACK_PROVIDERS?.split(',') || [],
        retryAttempts: parseInt(process.env.LLM_RETRY_ATTEMPTS || '3', 10),
        retryDelay: parseInt(process.env.LLM_RETRY_DELAY || '1000', 10),
        timeout: parseInt(process.env.LLM_TIMEOUT || '30000', 10),
        providers: {
          openai: {
            apiKey: process.env.OPENAI_API_KEY,
            model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
            maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '4000', 10),
            temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7')
          },
          anthropic: {
            apiKey: process.env.ANTHROPIC_API_KEY,
            model: process.env.ANTHROPIC_MODEL || 'claude-3-sonnet-20240229',
            maxTokens: parseInt(process.env.ANTHROPIC_MAX_TOKENS || '4000', 10),
            temperature: parseFloat(process.env.ANTHROPIC_TEMPERATURE || '0.7')
          },
          google: {
            apiKey: process.env.GOOGLE_API_KEY,
            model: process.env.GOOGLE_MODEL || 'gemini-pro',
            maxTokens: parseInt(process.env.GOOGLE_MAX_TOKENS || '4000', 10),
            temperature: parseFloat(process.env.GOOGLE_TEMPERATURE || '0.7')
          },
          groq: {
            apiKey: process.env.GROQ_API_KEY,
            model: process.env.GROQ_MODEL || 'mixtral-8x7b-32768',
            maxTokens: parseInt(process.env.GROQ_MAX_TOKENS || '4000', 10),
            temperature: parseFloat(process.env.GROQ_TEMPERATURE || '0.7')
          },
          ollama: {
            baseUrl: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
            model: process.env.OLLAMA_MODEL || 'llama2',
            timeout: parseInt(process.env.OLLAMA_TIMEOUT || '30000', 10)
          }
        }
      }
    };
  }

  /**
   * Generate a secure secret for development
   */
  private generateSecret(): string {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('JWT_SECRET and SESSION_SECRET must be provided in production');
    }
    return 'development-secret-' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * Get development environment configuration
   */
  private getDevelopmentConfig(): any {
    return {
      environment: 'development',
      logging: {
        level: 'debug',
        enableColors: true,
        enableConsole: true
      },
      security: {
        rateLimitMax: 1000 // More lenient in development
      },
      monitoring: {
        enableMetrics: false,
        enablePrometheus: false
      }
    };
  }

  /**
   * Get staging environment configuration
   */
  private getStagingConfig(): any {
    return {
      environment: 'staging',
      logging: {
        level: 'info',
        enableFile: true
      },
      monitoring: {
        enableMetrics: true,
        enablePrometheus: true
      }
    };
  }

  /**
   * Get production environment configuration
   */
  private getProductionConfig(): any {
    return {
      environment: 'production',
      logging: {
        level: 'warn',
        format: 'json',
        enableFile: true,
        enableColors: false
      },
      security: {
        rateLimitMax: 50 // Stricter in production
      },
      monitoring: {
        enableMetrics: true,
        enablePrometheus: true,
        enableTracing: true
      }
    };
  }

  /**
   * Get test environment configuration
   */
  private getTestConfig(): any {
    return {
      environment: 'test',
      logging: {
        level: 'error',
        enableConsole: false
      },
      monitoring: {
        enableHealthChecks: false,
        enableMetrics: false
      }
    };
  }
}

// Export singleton instance
export const configManager = ConfigManager.getInstance();
