/**
 * Configuration Validator
 * 
 * Validates configuration settings and provides detailed error reporting.
 * Ensures all required environment variables are present and valid.
 */

// import { z } from 'zod'; // Not used in this file
import { logger } from '@kg-visualizer/shared';
import { configManager, type AppConfig } from './ConfigManager';

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  summary: ValidationSummary;
}

export interface ValidationError {
  field: string;
  message: string;
  severity: 'critical' | 'high' | 'medium';
  suggestion?: string;
}

export interface ValidationWarning {
  field: string;
  message: string;
  recommendation: string;
}

export interface ValidationSummary {
  totalChecks: number;
  criticalErrors: number;
  highErrors: number;
  mediumErrors: number;
  warnings: number;
  configurationScore: number; // 0-100
}

export class ConfigValidator {
  private readonly logger = logger.child('config-validator');

  /**
   * Validate complete application configuration
   */
  async validateConfiguration(): Promise<ValidationResult> {
    const config = configManager.getConfig();
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Validate each configuration section
    errors.push(...this.validateDatabase(config));
    errors.push(...this.validateServer(config));
    errors.push(...this.validateSecurity(config));
    errors.push(...this.validateLLM(config));
    
    warnings.push(...this.validateEnvironmentSpecific(config));
    warnings.push(...this.validatePerformance(config));
    warnings.push(...this.validateSecurityWarnings(config));

    // Calculate summary
    const summary = this.calculateSummary(errors, warnings);

    const result: ValidationResult = {
      isValid: errors.filter(e => e.severity === 'critical').length === 0,
      errors,
      warnings,
      summary
    };

    this.logValidationResult(result);
    return result;
  }

  /**
   * Validate database configuration
   */
  private validateDatabase(config: AppConfig): ValidationError[] {
    const errors: ValidationError[] = [];

    // Check database URI format
    if (!config.database.uri.startsWith('neo4j://') && !config.database.uri.startsWith('bolt://')) {
      errors.push({
        field: 'database.uri',
        message: 'Database URI must use neo4j:// or bolt:// protocol',
        severity: 'critical',
        suggestion: 'Use format: neo4j://username:password@host:port or bolt://username:password@host:port'
      });
    }

    // Check for default credentials in production
    if (config.environment === 'production') {
      if (config.database.password === 'password' || config.database.password === 'neo4j') {
        errors.push({
          field: 'database.password',
          message: 'Default database password detected in production',
          severity: 'critical',
          suggestion: 'Use a strong, unique password for production database'
        });
      }

      if (config.database.username === 'neo4j' && config.database.password.length < 12) {
        errors.push({
          field: 'database.password',
          message: 'Database password is too weak for production',
          severity: 'high',
          suggestion: 'Use a password with at least 12 characters including numbers and symbols'
        });
      }
    }

    // Check connection pool settings
    if (config.database.maxConnectionPoolSize > 200) {
      errors.push({
        field: 'database.maxConnectionPoolSize',
        message: 'Connection pool size is very high and may cause resource issues',
        severity: 'medium',
        suggestion: 'Consider reducing to 50-100 connections for most applications'
      });
    }

    return errors;
  }

  /**
   * Validate server configuration
   */
  private validateServer(config: AppConfig): ValidationError[] {
    const errors: ValidationError[] = [];

    // Check port conflicts
    if (config.server.port === config.monitoring.metricsPort) {
      errors.push({
        field: 'server.port',
        message: 'Server port conflicts with metrics port',
        severity: 'high',
        suggestion: 'Use different ports for server and metrics endpoints'
      });
    }

    // Check timeout settings
    if (config.server.timeout < 5000) {
      errors.push({
        field: 'server.timeout',
        message: 'Server timeout is very low and may cause request failures',
        severity: 'medium',
        suggestion: 'Consider using at least 5 seconds for server timeout'
      });
    }

    // Check production settings
    if (config.environment === 'production') {
      if (config.server.host === '0.0.0.0') {
        errors.push({
          field: 'server.host',
          message: 'Server binding to all interfaces in production',
          severity: 'medium',
          suggestion: 'Consider binding to specific interface for better security'
        });
      }
    }

    return errors;
  }

  /**
   * Validate security configuration
   */
  private validateSecurity(config: AppConfig): ValidationError[] {
    const errors: ValidationError[] = [];

    // Check JWT secret strength
    if (config.security.jwtSecret.length < 32) {
      errors.push({
        field: 'security.jwtSecret',
        message: 'JWT secret is too short',
        severity: 'critical',
        suggestion: 'Use at least 32 characters for JWT secret'
      });
    }

    if (config.security.jwtSecret.includes('development') && config.environment === 'production') {
      errors.push({
        field: 'security.jwtSecret',
        message: 'Development JWT secret detected in production',
        severity: 'critical',
        suggestion: 'Generate a strong, unique JWT secret for production'
      });
    }

    // Check session secret
    if (config.security.sessionSecret.length < 32) {
      errors.push({
        field: 'security.sessionSecret',
        message: 'Session secret is too short',
        severity: 'critical',
        suggestion: 'Use at least 32 characters for session secret'
      });
    }

    // Check bcrypt rounds
    if (config.security.bcryptRounds < 10 && config.environment === 'production') {
      errors.push({
        field: 'security.bcryptRounds',
        message: 'Bcrypt rounds too low for production',
        severity: 'high',
        suggestion: 'Use at least 12 rounds for production environments'
      });
    }

    // Check rate limiting
    if (config.security.rateLimitMax > 1000 && config.environment === 'production') {
      errors.push({
        field: 'security.rateLimitMax',
        message: 'Rate limit is very high for production',
        severity: 'medium',
        suggestion: 'Consider lowering rate limit for better protection against abuse'
      });
    }

    return errors;
  }

  /**
   * Validate LLM configuration
   */
  private validateLLM(config: AppConfig): ValidationError[] {
    const errors: ValidationError[] = [];

    // Check if primary provider is configured
    const primaryProvider = config.llm.providers[config.llm.primaryProvider as keyof typeof config.llm.providers];
    if (!primaryProvider) {
      errors.push({
        field: 'llm.primaryProvider',
        message: `Primary LLM provider '${config.llm.primaryProvider}' is not configured`,
        severity: 'high',
        suggestion: 'Configure the primary provider or change to a configured provider'
      });
    }

    // Check API keys for cloud providers
    if (config.environment === 'production') {
      const cloudProviders = ['openai', 'anthropic', 'google', 'groq'];
      cloudProviders.forEach(provider => {
        if (config.llm.primaryProvider === provider) {
          const providerConfig = config.llm.providers[provider as keyof typeof config.llm.providers] as any;
          if (!providerConfig?.apiKey) {
            errors.push({
              field: `llm.providers.${provider}.apiKey`,
              message: `API key missing for primary provider '${provider}'`,
              severity: 'critical',
              suggestion: `Set ${provider.toUpperCase()}_API_KEY environment variable`
            });
          }
        }
      });
    }

    // Check timeout settings
    if (config.llm.timeout < 10000) {
      errors.push({
        field: 'llm.timeout',
        message: 'LLM timeout is very low and may cause request failures',
        severity: 'medium',
        suggestion: 'Consider using at least 10 seconds for LLM requests'
      });
    }

    return errors;
  }

  /**
   * Validate environment-specific settings
   */
  private validateEnvironmentSpecific(config: AppConfig): ValidationWarning[] {
    const warnings: ValidationWarning[] = [];

    if (config.environment === 'development') {
      if (config.logging.level !== 'debug') {
        warnings.push({
          field: 'logging.level',
          message: 'Debug logging not enabled in development',
          recommendation: 'Enable debug logging for better development experience'
        });
      }
    }

    if (config.environment === 'production') {
      if (config.logging.level === 'debug') {
        warnings.push({
          field: 'logging.level',
          message: 'Debug logging enabled in production',
          recommendation: 'Use info or warn level for production to reduce log volume'
        });
      }

      if (!config.monitoring.enableMetrics) {
        warnings.push({
          field: 'monitoring.enableMetrics',
          message: 'Metrics disabled in production',
          recommendation: 'Enable metrics for production monitoring and alerting'
        });
      }
    }

    return warnings;
  }

  /**
   * Validate security warnings
   */
  private validateSecurityWarnings(config: AppConfig): ValidationWarning[] {
    const warnings: ValidationWarning[] = [];

    if (config.environment === 'production' && config.security.rateLimitMax > 500) {
      warnings.push({
        field: 'security.rateLimitMax',
        message: 'Rate limit is very high for production',
        recommendation: 'Consider lowering rate limit for better protection against abuse'
      });
    }

    return warnings;
  }

  /**
   * Validate performance settings
   */
  private validatePerformance(config: AppConfig): ValidationWarning[] {
    const warnings: ValidationWarning[] = [];

    if (config.database.maxConnectionPoolSize < 10) {
      warnings.push({
        field: 'database.maxConnectionPoolSize',
        message: 'Database connection pool size is very low',
        recommendation: 'Consider increasing to at least 10 connections for better performance'
      });
    }

    if (config.server.keepAliveTimeout < 5000) {
      warnings.push({
        field: 'server.keepAliveTimeout',
        message: 'Keep-alive timeout is very low',
        recommendation: 'Consider increasing to at least 5 seconds for better connection reuse'
      });
    }

    return warnings;
  }

  /**
   * Calculate validation summary
   */
  private calculateSummary(errors: ValidationError[], warnings: ValidationWarning[]): ValidationSummary {
    const criticalErrors = errors.filter(e => e.severity === 'critical').length;
    const highErrors = errors.filter(e => e.severity === 'high').length;
    const mediumErrors = errors.filter(e => e.severity === 'medium').length;
    const totalChecks = 50; // Approximate number of validation checks

    // Calculate score (0-100)
    let score = 100;
    score -= criticalErrors * 25; // Critical errors heavily penalized
    score -= highErrors * 10;     // High errors moderately penalized
    score -= mediumErrors * 5;    // Medium errors lightly penalized
    score -= warnings.length * 2; // Warnings slightly penalized

    score = Math.max(0, Math.min(100, score)); // Clamp between 0-100

    return {
      totalChecks,
      criticalErrors,
      highErrors,
      mediumErrors,
      warnings: warnings.length,
      configurationScore: score
    };
  }

  /**
   * Log validation result
   */
  private logValidationResult(result: ValidationResult): void {
    const { summary, errors, warnings } = result;

    if (summary.criticalErrors > 0) {
      this.logger.error('Configuration validation failed with critical errors', 'config-validator', {
        criticalErrors: summary.criticalErrors,
        highErrors: summary.highErrors,
        mediumErrors: summary.mediumErrors,
        warnings: summary.warnings,
        score: summary.configurationScore
      });

      // Log critical errors
      errors.filter(e => e.severity === 'critical').forEach(error => {
        this.logger.error(`Critical configuration error: ${error.field}`, 'config-validator', {
          message: error.message,
          suggestion: error.suggestion
        });
      });
    } else if (summary.highErrors > 0 || summary.mediumErrors > 0) {
      this.logger.warn('Configuration validation completed with errors', 'config-validator', {
        highErrors: summary.highErrors,
        mediumErrors: summary.mediumErrors,
        warnings: summary.warnings,
        score: summary.configurationScore
      });
    } else {
      this.logger.info('Configuration validation passed', 'config-validator', {
        warnings: summary.warnings,
        score: summary.configurationScore
      });
    }

    // Log warnings
    warnings.forEach(warning => {
      this.logger.warn(`Configuration warning: ${warning.field}`, 'config-validator', {
        message: warning.message,
        recommendation: warning.recommendation
      });
    });
  }

  /**
   * Generate configuration report
   */
  generateReport(result: ValidationResult): string {
    const { summary, errors, warnings } = result;
    
    let report = '# Configuration Validation Report\n\n';
    
    // Summary
    report += `## Summary\n`;
    report += `- **Configuration Score**: ${summary.configurationScore}/100\n`;
    report += `- **Critical Errors**: ${summary.criticalErrors}\n`;
    report += `- **High Priority Errors**: ${summary.highErrors}\n`;
    report += `- **Medium Priority Errors**: ${summary.mediumErrors}\n`;
    report += `- **Warnings**: ${summary.warnings}\n\n`;

    // Errors
    if (errors.length > 0) {
      report += `## Errors\n\n`;
      errors.forEach(error => {
        report += `### ${error.field} (${error.severity.toUpperCase()})\n`;
        report += `**Message**: ${error.message}\n`;
        if (error.suggestion) {
          report += `**Suggestion**: ${error.suggestion}\n`;
        }
        report += '\n';
      });
    }

    // Warnings
    if (warnings.length > 0) {
      report += `## Warnings\n\n`;
      warnings.forEach(warning => {
        report += `### ${warning.field}\n`;
        report += `**Message**: ${warning.message}\n`;
        report += `**Recommendation**: ${warning.recommendation}\n\n`;
      });
    }

    return report;
  }
}

// Export singleton instance
export const configValidator = new ConfigValidator();
