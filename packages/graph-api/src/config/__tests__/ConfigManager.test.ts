/**
 * Tests for ConfigManager
 */

import { ConfigManager } from '../ConfigManager';

// Mock logger
jest.mock('@kg-visualizer/shared', () => ({
  logger: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }))
  }
}));

// Mock dotenv
jest.mock('dotenv', () => ({
  config: jest.fn()
}));

// Mock fs
jest.mock('fs', () => ({
  existsSync: jest.fn(() => true)
}));

describe('ConfigManager', () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    originalEnv = { ...process.env };
    // Clear environment variables
    Object.keys(process.env).forEach(key => {
      if (key.startsWith('DATABASE_') || key.startsWith('SERVER_') || key.startsWith('JWT_') ||
          key.startsWith('LLM_') || key.startsWith('OPENAI_') || key.startsWith('ANTHROPIC_') ||
          key.startsWith('CORS_') || key.startsWith('PORT') || key === 'NODE_ENV') {
        delete process.env[key];
      }
    });

    // Reset singleton instance
    (ConfigManager as any).instance = undefined;
  });

  afterEach(() => {
    process.env = originalEnv;
    // Reset singleton instance
    (ConfigManager as any).instance = undefined;
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = ConfigManager.getInstance();
      const instance2 = ConfigManager.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('configuration loading', () => {
    it('should load configuration with default values', () => {
      process.env.NODE_ENV = 'test';
      process.env.DATABASE_URI = 'neo4j://localhost:7687';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'test_password';
      process.env.JWT_SECRET = 'test-jwt-secret-32-characters-minimum';
      process.env.SESSION_SECRET = 'test-session-secret-32-characters-minimum';

      const configManager = ConfigManager.getInstance();
      const config = configManager.getConfig();

      expect(config.environment).toBe('test');
      expect(config.database.uri).toBe('neo4j://localhost:7687');
      expect(config.database.username).toBe('neo4j');
      expect(config.database.password).toBe('test_password');
      expect(config.server.port).toBe(3002);
      expect(config.security.jwtSecret).toBe('test-jwt-secret-32-characters-minimum');
    });

    it('should use environment-specific defaults', () => {
      process.env.NODE_ENV = 'production';
      process.env.DATABASE_URI = 'neo4j://prod:7687';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'secure_password';
      process.env.JWT_SECRET = 'production-jwt-secret-32-characters-minimum';
      process.env.SESSION_SECRET = 'production-session-secret-32-characters-minimum';

      const configManager = ConfigManager.getInstance();
      const config = configManager.getConfig();

      expect(config.environment).toBe('production');
      expect(config.database.encrypted).toBe(false); // Default for test
      expect(config.logging.level).toBe('info');
    });

    it('should parse numeric environment variables correctly', () => {
      process.env.PORT = '4000';
      process.env.DATABASE_MAX_POOL_SIZE = '75';
      process.env.BCRYPT_ROUNDS = '10';
      process.env.DATABASE_URI = 'neo4j://localhost:7687';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'test_password';
      process.env.JWT_SECRET = 'test-jwt-secret-32-characters-minimum';
      process.env.SESSION_SECRET = 'test-session-secret-32-characters-minimum';

      const configManager = ConfigManager.getInstance();
      const config = configManager.getConfig();

      expect(config.server.port).toBe(4000);
      expect(config.database.maxConnectionPoolSize).toBe(75);
      expect(config.security.bcryptRounds).toBe(10);
    });

    it('should parse boolean environment variables correctly', () => {
      process.env.SERVER_COMPRESSION = 'false';
      process.env.DATABASE_ENCRYPTED = 'true';
      process.env.CORS_CREDENTIALS = 'false';
      process.env.DATABASE_URI = 'neo4j://localhost:7687';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'test_password';
      process.env.JWT_SECRET = 'test-jwt-secret-32-characters-minimum';
      process.env.SESSION_SECRET = 'test-session-secret-32-characters-minimum';

      const configManager = ConfigManager.getInstance();
      const config = configManager.getConfig();

      expect(config.server.compression).toBe(false);
      expect(config.database.encrypted).toBe(true);
      expect(config.cors.credentials).toBe(false);
    });

    it('should parse array environment variables correctly', () => {
      process.env.CORS_ORIGINS = 'http://localhost:3000,http://localhost:3001,https://example.com';
      process.env.CORS_METHODS = 'GET,POST,PUT,DELETE';
      process.env.DATABASE_URI = 'neo4j://localhost:7687';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'test_password';
      process.env.JWT_SECRET = 'test-jwt-secret-32-characters-minimum';
      process.env.SESSION_SECRET = 'test-session-secret-32-characters-minimum';

      const configManager = ConfigManager.getInstance();
      const config = configManager.getConfig();

      expect(config.cors.origins).toEqual([
        'http://localhost:3000',
        'http://localhost:3001',
        'https://example.com'
      ]);
      expect(config.cors.methods).toEqual(['GET', 'POST', 'PUT', 'DELETE']);
    });
  });

  describe('configuration sections', () => {
    beforeEach(() => {
      process.env.DATABASE_URI = 'neo4j://localhost:7687';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'test_password';
      process.env.JWT_SECRET = 'test-jwt-secret-32-characters-minimum';
      process.env.SESSION_SECRET = 'test-session-secret-32-characters-minimum';
    });

    it('should return database configuration', () => {
      const configManager = ConfigManager.getInstance();
      const dbConfig = configManager.getDatabaseConfig();

      expect(dbConfig).toHaveProperty('uri');
      expect(dbConfig).toHaveProperty('username');
      expect(dbConfig).toHaveProperty('password');
      expect(dbConfig).toHaveProperty('maxConnectionPoolSize');
    });

    it('should return server configuration', () => {
      const configManager = ConfigManager.getInstance();
      const serverConfig = configManager.getServerConfig();

      expect(serverConfig).toHaveProperty('port');
      expect(serverConfig).toHaveProperty('host');
      expect(serverConfig).toHaveProperty('timeout');
    });

    it('should return security configuration', () => {
      const configManager = ConfigManager.getInstance();
      const securityConfig = configManager.getSecurityConfig();

      expect(securityConfig).toHaveProperty('jwtSecret');
      expect(securityConfig).toHaveProperty('sessionSecret');
      expect(securityConfig).toHaveProperty('bcryptRounds');
    });

    it('should return feature flags configuration', () => {
      const configManager = ConfigManager.getInstance();
      const featureFlags = configManager.getFeatureFlagsConfig();

      expect(featureFlags).toHaveProperty('enableNewControllerLayer');
      expect(featureFlags).toHaveProperty('enableNewServiceLayer');
      expect(featureFlags).toHaveProperty('trafficPercentageNewApi');
    });

    it('should return LLM configuration', () => {
      const configManager = ConfigManager.getInstance();
      const llmConfig = configManager.getLLMConfig();

      expect(llmConfig).toHaveProperty('primaryProvider');
      expect(llmConfig).toHaveProperty('providers');
      expect(llmConfig).toHaveProperty('timeout');
    });
  });

  describe('validation', () => {
    it('should validate correct configuration', () => {
      process.env.DATABASE_URI = 'neo4j://localhost:7687';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'test_password';
      process.env.JWT_SECRET = 'test-jwt-secret-32-characters-minimum';
      process.env.SESSION_SECRET = 'test-session-secret-32-characters-minimum';

      const configManager = ConfigManager.getInstance();
      const validation = configManager.validateConfiguration();

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect invalid database URI', () => {
      process.env.DATABASE_URI = 'invalid-uri';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'test_password';
      process.env.JWT_SECRET = 'test-jwt-secret-32-characters-minimum';
      process.env.SESSION_SECRET = 'test-session-secret-32-characters-minimum';

      expect(() => {
        ConfigManager.getInstance();
      }).toThrow();
    });

    it('should detect short JWT secret', () => {
      process.env.DATABASE_URI = 'neo4j://localhost:7687';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'test_password';
      process.env.JWT_SECRET = 'short';
      process.env.SESSION_SECRET = 'test-session-secret-32-characters-minimum';

      expect(() => {
        ConfigManager.getInstance();
      }).toThrow();
    });

    it('should detect invalid port numbers', () => {
      process.env.PORT = '99999';
      process.env.DATABASE_URI = 'neo4j://localhost:7687';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'test_password';
      process.env.JWT_SECRET = 'test-jwt-secret-32-characters-minimum';
      process.env.SESSION_SECRET = 'test-session-secret-32-characters-minimum';

      expect(() => {
        ConfigManager.getInstance();
      }).toThrow();
    });
  });

  describe('environment-specific configurations', () => {
    beforeEach(() => {
      process.env.DATABASE_URI = 'neo4j://localhost:7687';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'test_password';
      process.env.JWT_SECRET = 'test-jwt-secret-32-characters-minimum';
      process.env.SESSION_SECRET = 'test-session-secret-32-characters-minimum';
    });

    it('should return development configuration', () => {
      const configManager = ConfigManager.getInstance();
      const devConfig = configManager.getEnvironmentConfig('development');

      expect(devConfig.environment).toBe('development');
      expect(devConfig.logging?.level).toBe('debug');
    });

    it('should return production configuration', () => {
      const configManager = ConfigManager.getInstance();
      const prodConfig = configManager.getEnvironmentConfig('production');

      expect(prodConfig.environment).toBe('production');
      expect(prodConfig.logging?.format).toBe('json');
    });

    it('should return test configuration', () => {
      const configManager = ConfigManager.getInstance();
      const testConfig = configManager.getEnvironmentConfig('test');

      expect(testConfig.environment).toBe('test');
      expect(testConfig.logging?.level).toBe('error');
    });
  });

  describe('configuration reload', () => {
    it('should reload configuration from environment', () => {
      process.env.DATABASE_URI = 'neo4j://localhost:7687';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'test_password';
      process.env.JWT_SECRET = 'test-jwt-secret-32-characters-minimum';
      process.env.SESSION_SECRET = 'test-session-secret-32-characters-minimum';

      const configManager = ConfigManager.getInstance();
      const originalPort = configManager.getServerConfig().port;

      // Change environment variable
      process.env.PORT = '4000';

      // Reload configuration
      configManager.reloadConfiguration();

      const newPort = configManager.getServerConfig().port;
      expect(newPort).toBe(4000);
      expect(newPort).not.toBe(originalPort);
    });
  });

  describe('LLM provider configuration', () => {
    beforeEach(() => {
      process.env.DATABASE_URI = 'neo4j://localhost:7687';
      process.env.DATABASE_USERNAME = 'neo4j';
      process.env.DATABASE_PASSWORD = 'test_password';
      process.env.JWT_SECRET = 'test-jwt-secret-32-characters-minimum';
      process.env.SESSION_SECRET = 'test-session-secret-32-characters-minimum';
    });

    it('should configure OpenAI provider', () => {
      process.env.LLM_PRIMARY_PROVIDER = 'openai';
      process.env.OPENAI_API_KEY = 'sk-test-key';
      process.env.OPENAI_MODEL = 'gpt-4';

      const configManager = ConfigManager.getInstance();
      const llmConfig = configManager.getLLMConfig();

      expect(llmConfig.primaryProvider).toBe('openai');
      expect(llmConfig.providers.openai?.apiKey).toBe('sk-test-key');
      expect(llmConfig.providers.openai?.model).toBe('gpt-4');
    });

    it('should configure multiple providers', () => {
      process.env.LLM_PRIMARY_PROVIDER = 'anthropic';
      process.env.LLM_FALLBACK_PROVIDERS = 'openai,google';
      process.env.ANTHROPIC_API_KEY = 'sk-ant-test';
      process.env.OPENAI_API_KEY = 'sk-test-key';
      process.env.GOOGLE_API_KEY = 'google-test-key';

      const configManager = ConfigManager.getInstance();
      const llmConfig = configManager.getLLMConfig();

      expect(llmConfig.primaryProvider).toBe('anthropic');
      expect(llmConfig.fallbackProviders).toEqual(['openai', 'google']);
      expect(llmConfig.providers.anthropic?.apiKey).toBe('sk-ant-test');
      expect(llmConfig.providers.openai?.apiKey).toBe('sk-test-key');
      expect(llmConfig.providers.google?.apiKey).toBe('google-test-key');
    });
  });
});
