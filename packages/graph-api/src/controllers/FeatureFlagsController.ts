/**
 * Feature Flags Controller
 * 
 * Handles feature flag management and provides endpoints for
 * retrieving and updating feature flags during migration.
 */

import { Request, Response } from 'express';
import { BaseController } from './BaseController';
import { FeatureFlags } from '@kg-visualizer/shared';

export interface FeatureFlagsService {
  getFlags(userId?: string): Promise<FeatureFlags>;
  updateFlag(flagName: keyof FeatureFlags, value: boolean | number): Promise<void>;
  resetFlags(): Promise<void>;
  getFlagHistory(flagName?: keyof FeatureFlags): Promise<FlagHistoryEntry[]>;
  validateFlags(flags: Partial<FeatureFlags>): { isValid: boolean; errors: string[] };
}

export interface FlagHistoryEntry {
  flagName: keyof FeatureFlags;
  oldValue: boolean | number;
  newValue: boolean | number;
  changedBy: string;
  changedAt: string;
  reason?: string;
}

export interface FlagUpdateRequest {
  flagName: keyof FeatureFlags;
  value: boolean | number;
  reason?: string;
}

export class FeatureFlagsController extends BaseController {
  constructor(
    private featureFlagsService: FeatureFlagsService,
    private currentFlags: FeatureFlags
  ) {
    super();
  }

  /**
   * GET /api/feature-flags
   * Get current feature flags for a user
   */
  getFlags = this.asyncHandler(async (req: Request, res: Response) => {
    const userId = req.headers['x-user-id'] as string || req.ip;
    
    this.logAction('getFlags', req, { userId: userId ? userId.substring(0, 8) + '...' : 'anonymous' });

    const { result: flags, duration } = await this.withTiming(
      () => this.featureFlagsService.getFlags(userId),
      'getFlags',
      req
    );

    // Calculate migration phase
    const migrationPhase = this.calculateMigrationPhase(flags);

    // Set cache headers (short cache for feature flags)
    this.setCacheHeaders(res, 60); // 1 minute cache

    this.sendSuccess(res, {
      flags,
      migrationPhase,
      userId: userId ? userId.substring(0, 8) + '...' : 'anonymous'
    }, 200, {
      executionTime: duration
    });
  });

  /**
   * POST /api/feature-flags/update
   * Update a specific feature flag (admin only)
   */
  updateFlag = this.asyncHandler(async (req: Request, res: Response) => {
    // Check admin permissions
    const isAdmin = this.checkAdminPermissions(req);
    if (!isAdmin) {
      return this.sendError(res, 'Admin permissions required', 403);
    }

    const validation = this.validateRequestBody<FlagUpdateRequest>(
      req.body,
      this.validateFlagUpdate
    );

    if (!validation.isValid) {
      return this.sendError(res, 'Invalid flag update request', 400, {
        errors: validation.errors
      });
    }

    const { flagName, value, reason } = validation.data!;
    const userId = req.headers['x-user-id'] as string || 'unknown';

    this.logAction('updateFlag', req, { 
      flagName, 
      value, 
      reason,
      userId: userId.substring(0, 8) + '...'
    });

    const { duration } = await this.withTiming(
      () => this.featureFlagsService.updateFlag(flagName, value),
      'updateFlag',
      req
    );

    // Log the flag change for audit purposes
    this.logger.info('Feature flag updated', 'feature-flags-controller', {
      flagName,
      oldValue: this.currentFlags[flagName],
      newValue: value,
      changedBy: userId,
      reason,
      requestId: res.locals.requestId
    });

    this.sendSuccess(res, {
      flagName,
      oldValue: this.currentFlags[flagName],
      newValue: value,
      changedBy: userId.substring(0, 8) + '...',
      changedAt: new Date().toISOString()
    }, 200, {
      executionTime: duration
    });
  });

  /**
   * POST /api/feature-flags/batch-update
   * Update multiple feature flags at once (admin only)
   */
  batchUpdateFlags = this.asyncHandler(async (req: Request, res: Response) => {
    const isAdmin = this.checkAdminPermissions(req);
    if (!isAdmin) {
      return this.sendError(res, 'Admin permissions required', 403);
    }

    const { updates } = req.body;

    if (!Array.isArray(updates) || updates.length === 0) {
      return this.sendError(res, 'Updates array is required', 400);
    }

    if (updates.length > 10) {
      return this.sendError(res, 'Maximum 10 flag updates allowed per batch', 400);
    }

    // Validate all updates first
    const validationResults = updates.map(update => 
      this.validateFlagUpdate(update)
    );

    const hasErrors = validationResults.some(result => !result.isValid);
    if (hasErrors) {
      const allErrors = validationResults.flatMap(result => result.errors);
      return this.sendError(res, 'Invalid flag updates', 400, {
        errors: allErrors
      });
    }

    const userId = req.headers['x-user-id'] as string || 'unknown';
    this.logAction('batchUpdateFlags', req, { 
      updateCount: updates.length,
      userId: userId.substring(0, 8) + '...'
    });

    const startTime = Date.now();
    const results: Array<{ flagName: string; success: boolean; error?: string }> = [];

    // Apply updates sequentially to maintain consistency
    for (const update of updates) {
      try {
        await this.featureFlagsService.updateFlag(update.flagName, update.value);
        results.push({ flagName: update.flagName, success: true });

        this.logger.info('Feature flag updated in batch', 'feature-flags-controller', {
          flagName: update.flagName,
          oldValue: this.currentFlags[update.flagName as keyof FeatureFlags],
          newValue: update.value,
          changedBy: userId,
          reason: update.reason
        });
      } catch (error) {
        results.push({ 
          flagName: update.flagName, 
          success: false, 
          error: (error as Error).message 
        });
      }
    }

    const duration = Date.now() - startTime;

    this.sendSuccess(res, results, 200, {
      executionTime: duration,
      updateCount: updates.length,
      successCount: results.filter(r => r.success).length,
      errorCount: results.filter(r => !r.success).length
    });
  });

  /**
   * POST /api/feature-flags/reset
   * Reset all feature flags to default values (admin only)
   */
  resetFlags = this.asyncHandler(async (req: Request, res: Response) => {
    const isAdmin = this.checkAdminPermissions(req);
    if (!isAdmin) {
      return this.sendError(res, 'Admin permissions required', 403);
    }

    const userId = req.headers['x-user-id'] as string || 'unknown';
    this.logAction('resetFlags', req, { 
      userId: userId.substring(0, 8) + '...'
    });

    const { duration } = await this.withTiming(
      () => this.featureFlagsService.resetFlags(),
      'resetFlags',
      req
    );

    this.logger.warn('All feature flags reset to defaults', 'feature-flags-controller', {
      resetBy: userId,
      requestId: res.locals.requestId
    });

    this.sendSuccess(res, {
      message: 'All feature flags reset to default values',
      resetBy: userId.substring(0, 8) + '...',
      resetAt: new Date().toISOString()
    }, 200, {
      executionTime: duration
    });
  });

  /**
   * GET /api/feature-flags/history
   * Get feature flag change history (admin only)
   */
  getFlagHistory = this.asyncHandler(async (req: Request, res: Response) => {
    const isAdmin = this.checkAdminPermissions(req);
    if (!isAdmin) {
      return this.sendError(res, 'Admin permissions required', 403);
    }

    const flagName = req.query.flagName as keyof FeatureFlags;
    const { limit } = this.getPaginationParams(req);

    this.logAction('getFlagHistory', req, { flagName, limit });

    const { result: history, duration } = await this.withTiming(
      () => this.featureFlagsService.getFlagHistory(flagName),
      'getFlagHistory',
      req
    );

    // Apply limit to results
    const limitedHistory = history.slice(0, limit);

    this.sendSuccess(res, limitedHistory, 200, {
      executionTime: duration,
      totalEntries: history.length,
      returnedEntries: limitedHistory.length,
      flagName
    });
  });

  /**
   * GET /api/feature-flags/migration-status
   * Get current migration status based on feature flags
   */
  getMigrationStatus = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('getMigrationStatus', req);

    const flags = this.currentFlags;
    const migrationPhase = this.calculateMigrationPhase(flags);
    
    const status = {
      phase: this.determineMigrationPhase(migrationPhase),
      progress: this.calculateOverallProgress(migrationPhase),
      components: migrationPhase,
      recommendations: this.getMigrationRecommendations(migrationPhase),
      risks: this.assessMigrationRisks(migrationPhase)
    };

    this.setCacheHeaders(res, 300); // 5 minute cache

    this.sendSuccess(res, status, 200);
  });

  /**
   * Validate flag update request
   */
  private validateFlagUpdate(update: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!update.flagName || typeof update.flagName !== 'string') {
      errors.push('flagName is required and must be a string');
    }

    if (update.value === undefined || update.value === null) {
      errors.push('value is required');
    }

    if (typeof update.value !== 'boolean' && typeof update.value !== 'number') {
      errors.push('value must be a boolean or number');
    }

    if (typeof update.value === 'number' && (update.value < 0 || update.value > 100)) {
      errors.push('numeric values must be between 0 and 100');
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Check admin permissions
   */
  private checkAdminPermissions(req: Request): boolean {
    const adminKey = req.headers['x-admin-key'] as string;
    const expectedKey = process.env.ADMIN_API_KEY;
    
    return !!(adminKey && expectedKey && adminKey === expectedKey);
  }

  /**
   * Calculate migration phase from feature flags
   */
  private calculateMigrationPhase(flags: FeatureFlags) {
    return {
      backend: {
        enabled: flags.NEW_CONTROLLER_LAYER ? 1 : 0,
        total: 1,
        percentage: flags.NEW_CONTROLLER_LAYER ? 100 : 0
      },
      services: {
        chatService: flags.NEW_CHAT_SERVICE,
        circuitBreaker: flags.CIRCUIT_BREAKER_ENABLED
      },
      traffic: {
        api: flags.TRAFFIC_PERCENTAGE_NEW_API
      }
    };
  }

  /**
   * Determine current migration phase
   */
  private determineMigrationPhase(migrationPhase: any): string {
    const backendProgress = migrationPhase.backend.percentage;
    const trafficPercentage = migrationPhase.traffic.api;

    if (backendProgress === 0) return 'pre-migration';
    if (backendProgress === 100 && trafficPercentage === 100) return 'complete';
    if (backendProgress === 100 && trafficPercentage > 0) return 'traffic-migration';
    return 'in-progress';
  }

  /**
   * Calculate overall migration progress
   */
  private calculateOverallProgress(migrationPhase: any): number {
    const backendWeight = 0.6;
    const trafficWeight = 0.4;

    return Math.round(
      (migrationPhase.backend.percentage * backendWeight) +
      (migrationPhase.traffic.api * trafficWeight)
    );
  }

  /**
   * Get migration recommendations
   */
  private getMigrationRecommendations(migrationPhase: any): string[] {
    const recommendations: string[] = [];
    
    if (migrationPhase.backend.percentage === 0) {
      recommendations.push('Enable NEW_CONTROLLER_LAYER to start backend migration');
    }
    
    if (migrationPhase.backend.percentage === 100 && migrationPhase.traffic.api === 0) {
      recommendations.push('Start gradual traffic migration by increasing TRAFFIC_PERCENTAGE_NEW_API');
    }
    
    if (!migrationPhase.services.circuitBreaker) {
      recommendations.push('Enable CIRCUIT_BREAKER_ENABLED for better resilience');
    }

    return recommendations;
  }

  /**
   * Assess migration risks
   */
  private assessMigrationRisks(migrationPhase: any): string[] {
    const risks: string[] = [];
    
    if (migrationPhase.traffic.api > 50 && !migrationPhase.services.circuitBreaker) {
      risks.push('High traffic percentage without circuit breaker protection');
    }
    
    if (migrationPhase.traffic.api === 100 && migrationPhase.backend.percentage < 100) {
      risks.push('Full traffic migration without complete backend migration');
    }

    return risks;
  }
}
