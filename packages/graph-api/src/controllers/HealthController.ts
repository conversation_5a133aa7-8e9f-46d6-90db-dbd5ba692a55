/**
 * Health Controller
 * 
 * Handles health check endpoints and system status monitoring.
 * Provides detailed health information for load balancers and monitoring systems.
 */

import { Request, Response } from 'express';
import { BaseController } from './BaseController';
import { HealthStatus, HealthCheck, FeatureFlags } from '@kg-visualizer/shared';

export interface HealthService {
  checkDatabase(): Promise<HealthCheck>;
  checkMemory(): Promise<HealthCheck>;
  checkDisk(): Promise<HealthCheck>;
  checkExternalServices(): Promise<Record<string, HealthCheck>>;
  getSystemInfo(): Promise<SystemInfo>;
}

export interface SystemInfo {
  version: string;
  uptime: number;
  nodeVersion: string;
  platform: string;
  architecture: string;
  environment: string;
  processId: number;
  memoryUsage: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
    arrayBuffers: number;
  };
  cpuUsage: {
    user: number;
    system: number;
  };
}

export interface MigrationPhase {
  backend: {
    enabled: number;
    total: number;
    percentage: number;
  };
  services: {
    chatService: boolean;
    circuitBreaker: boolean;
  };
  traffic: {
    api: number;
  };
}

export class HealthController extends BaseController {
  constructor(
    private healthService: HealthService,
    private featureFlags: FeatureFlags
  ) {
    super();
  }

  /**
   * GET /api/health
   * Basic health check endpoint for load balancers
   */
  getHealth = this.asyncHandler(async (_req: Request, res: Response) => {
    const startTime = Date.now();

    try {
      // Run basic health checks
      const [database, memory, disk] = await Promise.all([
        this.healthService.checkDatabase(),
        this.healthService.checkMemory(),
        this.healthService.checkDisk()
      ]);

      const overallStatus = this.determineOverallStatus([database, memory, disk]);
      const responseTime = Date.now() - startTime;

      const health: HealthStatus = {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
        uptime: process.uptime(),
        checks: {
          database,
          memory,
          disk
        }
      };

      // Add migration phase information
      const migrationPhase = this.getMigrationPhase();
      
      // Don't cache health responses
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');

      this.sendSuccess(res, {
        ...health,
        migrationPhase
      }, overallStatus === 'healthy' ? 200 : 503, {
        responseTime
      });

    } catch (error) {
      this.logger.error('Health check failed', 'health-controller', {
        error: (error as Error).message,
        responseTime: Date.now() - startTime
      });

      this.sendError(res, 'Health check failed', 503, {
        responseTime: Date.now() - startTime
      });
    }
  });

  /**
   * GET /api/health/detailed
   * Detailed health check including external services
   */
  getDetailedHealth = this.asyncHandler(async (_req: Request, res: Response) => {
    const startTime = Date.now();

    try {
      // Run comprehensive health checks
      const [database, memory, disk, external, systemInfo] = await Promise.all([
        this.healthService.checkDatabase(),
        this.healthService.checkMemory(),
        this.healthService.checkDisk(),
        this.healthService.checkExternalServices(),
        this.healthService.getSystemInfo()
      ]);

      const allChecks = [database, memory, disk, ...Object.values(external)];
      const overallStatus = this.determineOverallStatus(allChecks);
      const responseTime = Date.now() - startTime;

      const health: HealthStatus = {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        version: systemInfo.version,
        uptime: systemInfo.uptime,
        checks: {
          database,
          memory,
          disk,
          external
        }
      };

      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');

      this.sendSuccess(res, {
        ...health,
        systemInfo,
        migrationPhase: this.getMigrationPhase(),
        featureFlags: this.getPublicFeatureFlags()
      }, overallStatus === 'healthy' ? 200 : 503, {
        responseTime
      });

    } catch (error) {
      this.logger.error('Detailed health check failed', 'health-controller', {
        error: (error as Error).message,
        responseTime: Date.now() - startTime
      });

      this.sendError(res, 'Detailed health check failed', 503, {
        responseTime: Date.now() - startTime
      });
    }
  });

  /**
   * GET /api/health/ready
   * Readiness probe for Kubernetes
   */
  getReadiness = this.asyncHandler(async (_req: Request, res: Response) => {
    try {
      // Check if the service is ready to accept traffic
      const database = await this.healthService.checkDatabase();
      
      if (database.status === 'pass') {
        res.status(200).json({ status: 'ready' });
      } else {
        res.status(503).json({ status: 'not ready', reason: 'database not available' });
      }
    } catch (error) {
      res.status(503).json({ 
        status: 'not ready', 
        reason: (error as Error).message 
      });
    }
  });

  /**
   * GET /api/health/live
   * Liveness probe for Kubernetes
   */
  getLiveness = this.asyncHandler(async (_req: Request, res: Response) => {
    try {
      // Basic liveness check - just verify the process is running
      const memory = await this.healthService.checkMemory();
      
      if (memory.status !== 'fail') {
        res.status(200).json({ status: 'alive' });
      } else {
        res.status(503).json({ status: 'not alive', reason: 'memory issues' });
      }
    } catch (error) {
      res.status(503).json({ 
        status: 'not alive', 
        reason: (error as Error).message 
      });
    }
  });

  /**
   * GET /api/health/metrics
   * Prometheus-style metrics endpoint
   */
  getMetrics = this.asyncHandler(async (_req: Request, res: Response) => {
    try {
      const systemInfo = await this.healthService.getSystemInfo();
      const database = await this.healthService.checkDatabase();
      
      // Generate Prometheus-style metrics
      const metrics = [
        `# HELP kg_visualizer_up Whether the service is up`,
        `# TYPE kg_visualizer_up gauge`,
        `kg_visualizer_up 1`,
        ``,
        `# HELP kg_visualizer_uptime_seconds Service uptime in seconds`,
        `# TYPE kg_visualizer_uptime_seconds counter`,
        `kg_visualizer_uptime_seconds ${systemInfo.uptime}`,
        ``,
        `# HELP kg_visualizer_memory_usage_bytes Memory usage in bytes`,
        `# TYPE kg_visualizer_memory_usage_bytes gauge`,
        `kg_visualizer_memory_usage_bytes{type="rss"} ${systemInfo.memoryUsage.rss}`,
        `kg_visualizer_memory_usage_bytes{type="heap_total"} ${systemInfo.memoryUsage.heapTotal}`,
        `kg_visualizer_memory_usage_bytes{type="heap_used"} ${systemInfo.memoryUsage.heapUsed}`,
        ``,
        `# HELP kg_visualizer_database_status Database connection status`,
        `# TYPE kg_visualizer_database_status gauge`,
        `kg_visualizer_database_status ${database.status === 'pass' ? 1 : 0}`,
        ``,
        `# HELP kg_visualizer_database_response_time_ms Database response time in milliseconds`,
        `# TYPE kg_visualizer_database_response_time_ms gauge`,
        `kg_visualizer_database_response_time_ms ${database.responseTime || 0}`,
        ``
      ].join('\n');

      res.setHeader('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
      res.send(metrics);

    } catch (error) {
      this.sendError(res, 'Failed to generate metrics', 500);
    }
  });

  /**
   * Determine overall health status from individual checks
   */
  private determineOverallStatus(checks: HealthCheck[]): 'healthy' | 'degraded' | 'unhealthy' {
    const hasFailures = checks.some(check => check.status === 'fail');
    const hasWarnings = checks.some(check => check.status === 'warn');

    if (hasFailures) {
      return 'unhealthy';
    } else if (hasWarnings) {
      return 'degraded';
    } else {
      return 'healthy';
    }
  }

  /**
   * Get migration phase information
   */
  private getMigrationPhase(): MigrationPhase {
    return {
      backend: {
        enabled: this.featureFlags.NEW_CONTROLLER_LAYER ? 1 : 0,
        total: 1,
        percentage: this.featureFlags.NEW_CONTROLLER_LAYER ? 100 : 0
      },
      services: {
        chatService: this.featureFlags.NEW_CHAT_SERVICE,
        circuitBreaker: this.featureFlags.CIRCUIT_BREAKER_ENABLED
      },
      traffic: {
        api: this.featureFlags.TRAFFIC_PERCENTAGE_NEW_API
      }
    };
  }

  /**
   * Get public feature flags (excluding sensitive ones)
   */
  private getPublicFeatureFlags(): Partial<FeatureFlags> {
    return {
      NEW_CONTROLLER_LAYER: this.featureFlags.NEW_CONTROLLER_LAYER,
      NEW_SERVICE_LAYER: this.featureFlags.NEW_SERVICE_LAYER,
      NEW_CHAT_SERVICE: this.featureFlags.NEW_CHAT_SERVICE,
      CIRCUIT_BREAKER_ENABLED: this.featureFlags.CIRCUIT_BREAKER_ENABLED,
      PERFORMANCE_MONITORING: this.featureFlags.PERFORMANCE_MONITORING,
      TRAFFIC_PERCENTAGE_NEW_API: this.featureFlags.TRAFFIC_PERCENTAGE_NEW_API
    };
  }
}
