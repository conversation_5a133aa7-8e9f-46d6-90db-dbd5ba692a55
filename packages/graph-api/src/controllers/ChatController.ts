import { Request, Response } from 'express';
import { BaseController } from './BaseController';
import { logger } from '@kg-visualizer/shared';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
}

export interface ChatRequest {
  message: string;
  sessionId?: string;
  options?: {
    temperature?: number;
    maxTokens?: number;
    includeHistory?: boolean;
  };
}

export class ChatController extends BaseController {
  private sessions: Map<string, ChatSession> = new Map();

  constructor() {
    super();
    // Initialize with a default session
    this.createDefaultSession();
  }

  private createDefaultSession(): void {
    const defaultSession: ChatSession = {
      id: 'default',
      title: 'New Conversation',
      messages: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    this.sessions.set('default', defaultSession);
  }

  private generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSessionId(): string {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get all chat conversations/sessions
   */
  public async getConversations(_req: Request, res: Response): Promise<void> {
    try {
      logger.info('Fetching chat conversations');
      
      const conversations = Array.from(this.sessions.values()).map(session => ({
        id: session.id,
        title: session.title,
        messageCount: session.messages.length,
        lastMessage: session.messages[session.messages.length - 1]?.content || '',
        createdAt: session.createdAt,
        updatedAt: session.updatedAt
      }));

      this.sendSuccess(res, {
        conversations,
        total: conversations.length
      });
    } catch (error) {
      logger.error('Error fetching conversations:', error instanceof Error ? error.message : String(error));
      this.sendError(res, 'Failed to fetch conversations', 500);
    }
  }

  /**
   * Get a specific conversation by ID
   */
  public async getConversation(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      logger.info(`Fetching conversation: ${id}`);

      const session = this.sessions.get(id);
      if (!session) {
        this.sendError(res, 'Conversation not found', 404);
        return;
      }

      this.sendSuccess(res, session);
    } catch (error) {
      logger.error('Error fetching conversation:', error instanceof Error ? error.message : String(error));
      this.sendError(res, 'Failed to fetch conversation', 500);
    }
  }

  /**
   * Create a new conversation
   */
  public async createConversation(req: Request, res: Response): Promise<void> {
    try {
      const { title } = req.body;
      logger.info('Creating new conversation');

      const sessionId = this.generateSessionId();
      const session: ChatSession = {
        id: sessionId,
        title: title || 'New Conversation',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      this.sessions.set(sessionId, session);
      this.sendSuccess(res, session, 201);
    } catch (error) {
      logger.error('Error creating conversation:', error instanceof Error ? error.message : String(error));
      this.sendError(res, 'Failed to create conversation', 500);
    }
  }

  /**
   * Send a message to a conversation
   */
  public async sendMessage(req: Request, res: Response): Promise<void> {
    try {
      const { message, sessionId = 'default', options = {} }: ChatRequest = req.body;
      
      if (!message?.trim()) {
        this.sendError(res, 'Message content is required', 400);
        return;
      }

      logger.info(`Processing chat message for session: ${sessionId}`);

      // Get or create session
      let session = this.sessions.get(sessionId);
      if (!session) {
        session = {
          id: sessionId,
          title: 'New Conversation',
          messages: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        this.sessions.set(sessionId, session);
      }

      // Create user message
      const userMessage: ChatMessage = {
        id: this.generateId(),
        role: 'user',
        content: message.trim(),
        timestamp: new Date().toISOString()
      };

      // Create assistant response (mock for now)
      const assistantMessage: ChatMessage = {
        id: this.generateId(),
        role: 'assistant',
        content: this.generateMockResponse(message),
        timestamp: new Date().toISOString(),
        metadata: {
          model: 'mock-assistant',
          processingTime: Math.random() * 1000 + 500,
          options
        }
      };

      // Add messages to session
      session.messages.push(userMessage, assistantMessage);
      session.updatedAt = new Date().toISOString();

      // Update session title if it's the first message
      if (session.messages.length === 2 && session.title === 'New Conversation') {
        session.title = message.substring(0, 50) + (message.length > 50 ? '...' : '');
      }

      this.sendSuccess(res, {
        message: assistantMessage,
        session: {
          id: session.id,
          title: session.title,
          messageCount: session.messages.length
        }
      });
    } catch (error) {
      logger.error('Error sending message:', error instanceof Error ? error.message : String(error));
      this.sendError(res, 'Failed to send message', 500);
    }
  }

  /**
   * Delete a conversation
   */
  public async deleteConversation(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      logger.info(`Deleting conversation: ${id}`);

      if (!this.sessions.has(id)) {
        this.sendError(res, 'Conversation not found', 404);
        return;
      }

      this.sessions.delete(id);
      this.sendSuccess(res, { message: 'Conversation deleted successfully' });
    } catch (error) {
      logger.error('Error deleting conversation:', error instanceof Error ? error.message : String(error));
      this.sendError(res, 'Failed to delete conversation', 500);
    }
  }

  /**
   * Generate a mock response for demonstration
   */
  private generateMockResponse(userMessage: string): string {
    const responses = [
      "I understand you're asking about the knowledge graph. Let me help you with that.",
      "Based on the graph data, I can provide some insights about the relationships and entities.",
      "That's an interesting question about the knowledge graph structure. Here's what I found:",
      "I can help you explore the connections in the knowledge graph. Let me analyze that for you.",
      "The knowledge graph contains rich information about entity relationships. Here's my analysis:"
    ];

    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    // Add some context based on the user message
    if (userMessage.toLowerCase().includes('node') || userMessage.toLowerCase().includes('entity')) {
      return `${randomResponse} The graph contains various types of nodes representing different entities and their interconnections.`;
    } else if (userMessage.toLowerCase().includes('relationship') || userMessage.toLowerCase().includes('connection')) {
      return `${randomResponse} The relationships in the graph show how different entities are connected and influence each other.`;
    } else {
      return `${randomResponse} Feel free to ask more specific questions about the graph structure, entities, or relationships.`;
    }
  }
}
