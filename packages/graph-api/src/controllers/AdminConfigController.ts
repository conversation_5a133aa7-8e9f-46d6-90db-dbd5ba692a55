/**
 * Admin Configuration Controller
 * 
 * Handles admin configuration endpoints for the Knowledge Graph Visualizer
 * Provides compatibility with the frontend AdminConfigPanel component
 */

import { Request, Response } from 'express';
import { BaseController } from './BaseController';
import { configManager } from '../config/ConfigManager';

export class AdminConfigController extends BaseController {
  constructor() {
    super();
  }

  /**
   * GET /api/admin/config
   * Get current admin configuration
   */
  getConfig = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('getConfig', req);

    const config = configManager.getConfig();
    
    // Return configuration in format expected by AdminConfigPanel
    const adminConfig = {
      searchRecipes: [
        { key: 'combined', name: 'Combined Search', description: 'Combines entity and edge search' },
        { key: 'entity', name: 'Entity Search', description: 'Search entities only' },
        { key: 'edge', name: 'Edge Search', description: 'Search relationships only' },
        { key: 'community', name: 'Community Search', description: 'Search by community' }
      ],
      llmProviders: [
        {
          key: 'ollama',
          name: 'Ollama',
          hasApiKey: true,
          models: ['gemma:2b', 'gemma:7b', 'llama3:8b'],
          enabled: config.llm?.primaryProvider === 'ollama'
        },
        {
          key: 'google',
          name: 'Google Gemini',
          hasApiKey: false,
          models: ['gemini-pro', 'gemini-pro-vision'],
          enabled: config.llm?.primaryProvider === 'google'
        },
        {
          key: 'openai',
          name: 'OpenAI',
          hasApiKey: false,
          models: ['gpt-4', 'gpt-3.5-turbo'],
          enabled: config.llm?.primaryProvider === 'openai'
        }
      ],
      currentConfig: {
        searchRecipe: 'combined',
        resultsLimit: 10,
        llmProvider: config.llm?.primaryProvider || 'ollama',
        llmModel: 'gemma:2b',
        customPrompt: ''
      },
      metrics: {
        totalQueries: 0,
        avgResponseTime: 0,
        errorRate: 0,
        uptime: process.uptime()
      }
    };

    this.sendSuccess(res, adminConfig);
  });

  /**
   * GET /api/config/search-recipes
   * Get available search recipes
   */
  getSearchRecipes = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('getSearchRecipes', req);

    const recipes = [
      { key: 'combined', name: 'Combined Search', description: 'Combines entity and edge search' },
      { key: 'entity', name: 'Entity Search', description: 'Search entities only' },
      { key: 'edge', name: 'Edge Search', description: 'Search relationships only' },
      { key: 'community', name: 'Community Search', description: 'Search by community' }
    ];

    this.sendSuccess(res, recipes);
  });

  /**
   * GET /api/config/llm-providers
   * Get available LLM providers
   */
  getLLMProviders = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('getLLMProviders', req);

    const config = configManager.getConfig();
    const providers = [
      {
        key: 'ollama',
        name: 'Ollama',
        hasApiKey: true,
        models: ['gemma:2b', 'gemma:7b', 'llama3:8b'],
        enabled: config.llm?.primaryProvider === 'ollama'
      },
      {
        key: 'google',
        name: 'Google Gemini',
        hasApiKey: false,
        models: ['gemini-pro', 'gemini-pro-vision'],
        enabled: config.llm?.primaryProvider === 'google'
      },
      {
        key: 'openai',
        name: 'OpenAI',
        hasApiKey: false,
        models: ['gpt-4', 'gpt-3.5-turbo'],
        enabled: config.llm?.primaryProvider === 'openai'
      }
    ];

    this.sendSuccess(res, providers);
  });

  /**
   * GET /api/config/current
   * Get current configuration
   */
  getCurrentConfig = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('getCurrentConfig', req);

    const config = configManager.getConfig();
    const currentConfig = {
      searchRecipe: 'combined',
      resultsLimit: 10,
      llmProvider: config.llm?.primaryProvider || 'ollama',
      llmModel: 'gemma:2b',
      customPrompt: ''
    };

    this.sendSuccess(res, currentConfig);
  });

  /**
   * GET /api/config/metrics
   * Get system metrics
   */
  getMetrics = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('getMetrics', req);

    const metrics = {
      totalQueries: 0,
      avgResponseTime: 0,
      errorRate: 0,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    };

    this.sendSuccess(res, metrics);
  });

  /**
   * GET /api/config/prompt
   * Get prompt configuration
   */
  getPromptConfig = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('getPromptConfig', req);

    const promptConfig = {
      customPrompt: '',
      defaultPrompt: 'You are a helpful assistant for analyzing knowledge graphs.',
      templates: [
        { name: 'Default', prompt: 'You are a helpful assistant for analyzing knowledge graphs.' },
        { name: 'Technical', prompt: 'You are a technical expert analyzing graph data structures.' },
        { name: 'Business', prompt: 'You are a business analyst interpreting knowledge graph insights.' }
      ]
    };

    this.sendSuccess(res, promptConfig);
  });

  /**
   * POST /api/config/update
   * Update configuration
   */
  updateConfig = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('updateConfig', req, req.body);

    // In a real implementation, this would update the configuration
    // For now, just acknowledge the update
    const { searchRecipe, resultsLimit, llmProvider, llmModel, customPrompt } = req.body;

    // Validate the input
    if (resultsLimit && (resultsLimit < 1 || resultsLimit > 100)) {
      return this.sendError(res, 'Results limit must be between 1 and 100', 400);
    }

    // Simulate configuration update
    const updatedConfig = {
      searchRecipe: searchRecipe || 'combined',
      resultsLimit: resultsLimit || 10,
      llmProvider: llmProvider || 'ollama',
      llmModel: llmModel || 'gemma:2b',
      customPrompt: customPrompt || ''
    };

    this.sendSuccess(res, { 
      message: 'Configuration updated successfully',
      config: updatedConfig 
    });
  });

  /**
   * POST /api/config/prompt/reset
   * Reset prompt to default
   */
  resetPrompt = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('resetPrompt', req);

    const defaultPrompt = 'You are a helpful assistant for analyzing knowledge graphs.';
    
    this.sendSuccess(res, { 
      message: 'Prompt reset to default',
      prompt: defaultPrompt 
    });
  });

  /**
   * GET /api/config/test
   * Test configuration endpoint
   */
  testConfig = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('testConfig', req);

    const testResults = {
      database: { status: 'connected', latency: '5ms' },
      llm: { status: 'available', provider: 'ollama' },
      search: { status: 'operational', recipes: 4 },
      timestamp: new Date().toISOString()
    };

    this.sendSuccess(res, testResults);
  });
}
