import { Request, Response } from 'express';
import { BaseController } from './BaseController';
import { logger } from '@kg-visualizer/shared';
import * as path from 'path';
import * as fs from 'fs';

export class DocumentationController extends BaseController {
  private docsCache: Map<string, { content: string; lastModified: number }> = new Map();
  private readonly docsDirectory: string;

  constructor() {
    super();
    // Set up docs directory - look for docs in multiple possible locations
    this.docsDirectory = this.findDocsDirectory();
    this.initializeDocumentation();
  }

  private findDocsDirectory(): string {
    const possiblePaths = [
      path.join(process.cwd(), 'docs'),
      path.join(process.cwd(), '..', '..', 'docs'),
      path.join(__dirname, '..', '..', 'docs'),
      path.join(__dirname, '..', '..', '..', 'docs')
    ];

    for (const docPath of possiblePaths) {
      if (fs.existsSync(docPath)) {
        logger.info(`Found docs directory at: ${docPath}`);
        return docPath;
      }
    }

    // If no docs directory found, create a default one
    const defaultPath = path.join(process.cwd(), 'docs');
    logger.warn(`No docs directory found, creating default at: ${defaultPath}`);
    return defaultPath;
  }

  private initializeDocumentation(): void {
    // Create default documentation if docs directory doesn't exist
    if (!fs.existsSync(this.docsDirectory)) {
      fs.mkdirSync(this.docsDirectory, { recursive: true });
      this.createDefaultDocs();
    }
  }

  private createDefaultDocs(): void {
    const defaultDocs = {
      'getting-started.md': `# Getting Started

Welcome to the 360T Knowledge Graph Visualizer!

## Overview

This application provides an interactive interface for exploring and analyzing knowledge graphs. You can:

- **Explore**: Navigate through nodes and relationships
- **Search**: Find specific entities and connections
- **Analyze**: Discover patterns and insights
- **Chat**: Ask questions about the graph data

## Quick Start

1. **Explorer Tab**: Browse the graph structure
2. **Analysis Tab**: Run advanced analytics
3. **Chat Tab**: Ask questions about the data
4. **Documentation Tab**: Access help and guides

## Features

### Graph Visualization
- Interactive node-link diagrams
- Zoom and pan capabilities
- Node filtering and highlighting
- Relationship exploration

### Advanced Analytics
- Community detection
- Centrality analysis
- Path finding
- Statistical insights

### Chat Interface
- Natural language queries
- Context-aware responses
- Conversation history
- Knowledge graph integration

## Need Help?

Check out the other documentation sections for detailed guides and API references.
`,

      'user-guide.md': `# User Guide

## Navigation

### Explorer Tab
The Explorer tab provides the main graph visualization interface.

**Controls:**
- **Mouse**: Click and drag to pan
- **Scroll**: Zoom in/out
- **Click Node**: Select and view details
- **Double-click**: Expand node connections

### Search Functionality
Use the search bar to find specific entities:
- Type entity names or keywords
- Use filters to narrow results
- Click results to navigate to nodes

### Node Details Panel
When you select a node, the details panel shows:
- Entity properties
- Connected relationships
- Metadata information

## Analysis Features

### Community Detection
Discover clusters and groups within the graph:
1. Go to Analysis tab
2. Select "Community Detection"
3. Adjust resolution parameter
4. View colored clusters

### Centrality Analysis
Find the most important nodes:
- **PageRank**: Overall importance
- **Betweenness**: Bridge nodes
- **Degree**: Most connected

### Path Finding
Discover connections between entities:
1. Select source node
2. Select target node
3. Find shortest paths
4. Explore relationship chains

## Chat Interface

### Asking Questions
The chat interface supports natural language queries:
- "What are the main entities?"
- "Show me connections to [entity]"
- "Find similar nodes to [entity]"

### Conversation Management
- Create new conversations
- View conversation history
- Delete old conversations
- Export chat logs

## Tips and Tricks

1. **Performance**: Use filters for large graphs
2. **Navigation**: Use breadcrumbs to track your path
3. **Analysis**: Combine multiple analytics for insights
4. **Chat**: Be specific in your questions for better results
`,

      'api-reference.md': `# API Reference

## Graph API

### Get Initial Graph
\`\`\`
GET /api/graph/initial
\`\`\`

Returns the initial graph data for visualization.

**Parameters:**
- \`limit\` (optional): Maximum number of nodes to return
- \`group_id\` (optional): Filter by group identifier

**Response:**
\`\`\`json
{
  "data": {
    "nodes": [...],
    "edges": [...]
  },
  "metadata": {
    "totalNodes": 100,
    "totalEdges": 150
  }
}
\`\`\`

### Search Graph
\`\`\`
GET /api/graph/search
\`\`\`

Search for nodes and relationships.

**Parameters:**
- \`term\`: Search term
- \`limit\`: Maximum results
- \`type\`: Node type filter

## Analysis API

### Community Detection
\`\`\`
GET /api/analysis/clusters
\`\`\`

Detect communities using Louvain algorithm.

**Parameters:**
- \`resolution\`: Resolution parameter (default: 1.0)

### Centrality Analysis
\`\`\`
GET /api/analysis/centrality
\`\`\`

Calculate node centrality measures.

**Parameters:**
- \`type\`: Centrality type (pagerank, betweenness, degree)
- \`topN\`: Number of top nodes to return

### Path Finding
\`\`\`
GET /api/analysis/paths
\`\`\`

Find paths between nodes.

**Parameters:**
- \`sourceId\`: Source node ID
- \`targetId\`: Target node ID
- \`maxPaths\`: Maximum paths to return

## Chat API

### Send Message
\`\`\`
POST /api/chat/message
\`\`\`

Send a chat message and get AI response.

**Request Body:**
\`\`\`json
{
  "message": "What are the main entities?",
  "sessionId": "optional-session-id",
  "options": {
    "temperature": 0.7,
    "maxTokens": 1000
  }
}
\`\`\`

### Get Conversations
\`\`\`
GET /api/chat/conversations
\`\`\`

List all chat conversations.

### Get Conversation
\`\`\`
GET /api/chat/conversations/{id}
\`\`\`

Get specific conversation details.

## Configuration API

### Get Current Config
\`\`\`
GET /api/config/current
\`\`\`

Get current system configuration.

### Update Config
\`\`\`
POST /api/config/update
\`\`\`

Update system configuration.

## Health API

### Health Check
\`\`\`
GET /api/health
\`\`\`

Check system health status.

### Detailed Health
\`\`\`
GET /api/health/detailed
\`\`\`

Get detailed health information including database status.
`,

      'data-model.md': `# Data Model

## Graph Structure

The knowledge graph follows a property graph model with nodes and relationships.

### Nodes (Entities)

Each node represents an entity with the following structure:

\`\`\`json
{
  "id": "unique-identifier",
  "label": "Entity Name",
  "type": "EntityType",
  "properties": {
    "name": "Display Name",
    "description": "Entity description",
    "category": "Category",
    "metadata": {...}
  },
  "group_id": "data-source-identifier"
}
\`\`\`

**Common Node Types:**
- \`Product\`: Business products and services
- \`Feature\`: Product features and capabilities
- \`Workflow\`: Business processes and workflows
- \`Company\`: Organizations and entities
- \`Person\`: Individuals and contacts

### Relationships (Edges)

Relationships connect nodes and have the following structure:

\`\`\`json
{
  "id": "relationship-id",
  "source": "source-node-id",
  "target": "target-node-id",
  "type": "RelationshipType",
  "properties": {
    "weight": 1.0,
    "description": "Relationship description",
    "metadata": {...}
  }
}
\`\`\`

**Common Relationship Types:**
- \`CONTAINS\`: Hierarchical containment
- \`RELATES_TO\`: General association
- \`DEPENDS_ON\`: Dependency relationship
- \`PART_OF\`: Component relationship
- \`SIMILAR_TO\`: Similarity relationship

## Data Sources

### Group IDs
Data is organized by source using group_id:
- \`user_guides\`: User documentation
- \`api_docs\`: API documentation
- \`product_specs\`: Product specifications
- \`workflows\`: Business processes

### Metadata
Each entity includes metadata about:
- Source document
- Creation timestamp
- Last updated
- Confidence scores
- Processing information

## Schema Validation

### Node Schema
Required fields:
- \`id\`: Unique identifier
- \`label\`: Display name
- \`type\`: Entity type

Optional fields:
- \`properties\`: Additional attributes
- \`group_id\`: Data source identifier

### Relationship Schema
Required fields:
- \`source\`: Source node ID
- \`target\`: Target node ID
- \`type\`: Relationship type

Optional fields:
- \`properties\`: Additional attributes
- \`weight\`: Relationship strength

## Query Patterns

### Basic Node Query
\`\`\`cypher
MATCH (n) 
WHERE n.group_id = 'user_guides'
RETURN n
LIMIT 10
\`\`\`

### Relationship Query
\`\`\`cypher
MATCH (n)-[r]->(m)
WHERE n.group_id = 'user_guides'
RETURN n, r, m
\`\`\`

### Path Query
\`\`\`cypher
MATCH path = (start)-[*1..3]-(end)
WHERE start.id = 'entity-1' AND end.id = 'entity-2'
RETURN path
\`\`\`

## Best Practices

1. **Consistent Naming**: Use consistent entity and relationship naming
2. **Rich Metadata**: Include descriptive properties
3. **Group Organization**: Use group_id for data source separation
4. **Relationship Types**: Use meaningful relationship type names
5. **Performance**: Index frequently queried properties
`
    };

    // Write default documentation files
    Object.entries(defaultDocs).forEach(([filename, content]) => {
      const filePath = path.join(this.docsDirectory, filename);
      fs.writeFileSync(filePath, content, 'utf8');
      logger.info(`Created default documentation: ${filename}`);
    });
  }

  /**
   * Get documentation file
   */
  public async getDocumentation(req: Request, res: Response): Promise<void> {
    try {
      const filename = req.params.filename;
      
      if (!filename || !filename.endsWith('.md')) {
        this.sendError(res, 'Invalid documentation file requested', 400);
        return;
      }

      // Security check - prevent directory traversal
      if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        this.sendError(res, 'Invalid file path', 400);
        return;
      }

      logger.info(`Fetching documentation: ${filename}`);

      const filePath = path.join(this.docsDirectory, filename);
      
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        this.sendError(res, 'Documentation file not found', 404);
        return;
      }

      // Check cache
      const stats = fs.statSync(filePath);
      const lastModified = stats.mtime.getTime();
      const cached = this.docsCache.get(filename);

      let content: string;
      if (cached && cached.lastModified >= lastModified) {
        content = cached.content;
        logger.debug(`Serving cached documentation: ${filename}`);
      } else {
        content = fs.readFileSync(filePath, 'utf8');
        this.docsCache.set(filename, { content, lastModified });
        logger.debug(`Loaded and cached documentation: ${filename}`);
      }

      // Set appropriate headers
      res.setHeader('Content-Type', 'text/markdown; charset=utf-8');
      res.setHeader('Cache-Control', 'public, max-age=300'); // 5 minutes cache
      res.send(content);

    } catch (error) {
      logger.error('Error fetching documentation:', error instanceof Error ? error.message : String(error));
      this.sendError(res, 'Failed to fetch documentation', 500);
    }
  }

  /**
   * List available documentation files
   */
  public async listDocumentation(_req: Request, res: Response): Promise<void> {
    try {
      logger.info('Listing available documentation');

      if (!fs.existsSync(this.docsDirectory)) {
        this.sendSuccess(res, { files: [] });
        return;
      }

      const files = fs.readdirSync(this.docsDirectory)
        .filter(file => file.endsWith('.md'))
        .map(file => {
          const filePath = path.join(this.docsDirectory, file);
          const stats = fs.statSync(filePath);
          return {
            name: file,
            title: file.replace('.md', '').replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            size: stats.size,
            lastModified: stats.mtime.toISOString()
          };
        });

      this.sendSuccess(res, { files });
    } catch (error) {
      logger.error('Error listing documentation:', error instanceof Error ? error.message : String(error));
      this.sendError(res, 'Failed to list documentation', 500);
    }
  }
}
