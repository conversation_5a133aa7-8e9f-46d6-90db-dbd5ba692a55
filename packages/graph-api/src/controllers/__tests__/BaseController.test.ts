/**
 * Tests for BaseController
 */

import { Request, Response, NextFunction } from 'express';
import { BaseController } from '../BaseController';

// Mock logger
jest.mock('@kg-visualizer/shared', () => ({
  logger: {
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }))
  }
}));

class TestController extends BaseController {
  testAsyncHandler = this.asyncHandler(async (req: Request, res: Response) => {
    this.sendSuccess(res, { message: 'test' });
  });

  testErrorHandler = this.asyncHandler(async (req: Request, res: Response) => {
    throw new Error('Test error');
  });

  testValidation(params: Record<string, any>, required: string[]) {
    return this.validateRequired(params, required);
  }

  testPagination(req: Request) {
    return this.getPaginationParams(req);
  }

  testSorting(req: Request) {
    return this.getSortParams(req);
  }

  testTiming<T>(operation: () => Promise<T>, name: string, req: Request) {
    return this.withTiming(operation, name, req);
  }

  testClientInfo(req: Request) {
    return this.getClientInfo(req);
  }

  testLegacyCheck(req: Request) {
    return this.isLegacyRequest(req);
  }
}

describe('BaseController', () => {
  let controller: TestController;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    controller = new TestController();
    
    mockReq = {
      path: '/test',
      method: 'GET',
      query: {},
      headers: {},
      ip: '127.0.0.1',
      connection: { remoteAddress: '127.0.0.1' } as any
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      setHeader: jest.fn().mockReturnThis(),
      end: jest.fn().mockReturnThis(),
      locals: { requestId: 'test-request-id' }
    };

    mockNext = jest.fn();
  });

  describe('asyncHandler', () => {
    it('should handle successful async operations', async () => {
      await controller.testAsyncHandler(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: { message: 'test' },
        timestamp: expect.any(String),
        requestId: 'test-request-id'
      });
    });

    it('should handle async errors', async () => {
      await controller.testErrorHandler(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('validateRequired', () => {
    it('should validate required parameters successfully', () => {
      const params = { name: 'test', value: 123 };
      const required = ['name', 'value'];

      const result = controller.testValidation(params, required);

      expect(result.isValid).toBe(true);
      expect(result.missing).toEqual([]);
    });

    it('should identify missing required parameters', () => {
      const params = { name: 'test' };
      const required = ['name', 'value', 'type'];

      const result = controller.testValidation(params, required);

      expect(result.isValid).toBe(false);
      expect(result.missing).toEqual(['value', 'type']);
    });

    it('should handle empty string as missing', () => {
      const params = { name: '', value: null, type: undefined };
      const required = ['name', 'value', 'type'];

      const result = controller.testValidation(params, required);

      expect(result.isValid).toBe(false);
      expect(result.missing).toEqual(['name', 'value', 'type']);
    });
  });

  describe('getPaginationParams', () => {
    it('should return default pagination parameters', () => {
      const result = controller.testPagination(mockReq as Request);

      expect(result).toEqual({
        limit: 20,
        offset: 0,
        page: 1
      });
    });

    it('should parse pagination parameters from query', () => {
      mockReq.query = { limit: '50', page: '3' };

      const result = controller.testPagination(mockReq as Request);

      expect(result).toEqual({
        limit: 50,
        offset: 100, // (3-1) * 50
        page: 3
      });
    });

    it('should enforce maximum limit', () => {
      mockReq.query = { limit: '200' };

      const result = controller.testPagination(mockReq as Request);

      expect(result.limit).toBe(100); // Maximum enforced
    });

    it('should enforce minimum page', () => {
      mockReq.query = { page: '0' };

      const result = controller.testPagination(mockReq as Request);

      expect(result.page).toBe(1); // Minimum enforced
    });
  });

  describe('getSortParams', () => {
    it('should return default sort parameters', () => {
      const result = controller.testSorting(mockReq as Request);

      expect(result).toEqual({
        sortBy: undefined,
        sortOrder: 'asc'
      });
    });

    it('should parse sort parameters from query', () => {
      mockReq.query = { sortBy: 'name', sortOrder: 'desc' };

      const result = controller.testSorting(mockReq as Request);

      expect(result).toEqual({
        sortBy: 'name',
        sortOrder: 'desc'
      });
    });

    it('should default to asc for invalid sort order', () => {
      mockReq.query = { sortOrder: 'invalid' };

      const result = controller.testSorting(mockReq as Request);

      expect(result.sortOrder).toBe('asc');
    });
  });

  describe('withTiming', () => {
    it('should measure operation timing', async () => {
      const operation = jest.fn().mockResolvedValue('result');

      const result = await controller.testTiming(operation, 'test-op', mockReq as Request);

      expect(result.result).toBe('result');
      expect(result.duration).toBeGreaterThanOrEqual(0);
      expect(operation).toHaveBeenCalled();
    });

    it('should handle operation errors', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('Test error'));

      await expect(
        controller.testTiming(operation, 'test-op', mockReq as Request)
      ).rejects.toThrow('Test error');
    });
  });

  describe('getClientInfo', () => {
    it('should extract client information', () => {
      mockReq.headers = {
        'user-agent': 'test-agent',
        'x-user-id': 'user123',
        'x-session-id': 'session456'
      };

      const result = controller.testClientInfo(mockReq as Request);

      expect(result).toEqual({
        ip: '127.0.0.1',
        userAgent: 'test-agent',
        userId: 'user123',
        sessionId: 'session456'
      });
    });

    it('should handle missing headers', () => {
      const result = controller.testClientInfo(mockReq as Request);

      expect(result).toEqual({
        ip: '127.0.0.1',
        userAgent: undefined,
        userId: undefined,
        sessionId: undefined
      });
    });
  });

  describe('isLegacyRequest', () => {
    it('should detect legacy request from header', () => {
      mockReq.headers = { 'x-legacy-api': 'true' };

      const result = controller.testLegacyCheck(mockReq as Request);

      expect(result).toBe(true);
    });

    it('should detect legacy request from query parameter', () => {
      mockReq.query = { legacy: 'true' };

      const result = controller.testLegacyCheck(mockReq as Request);

      expect(result).toBe(true);
    });

    it('should detect legacy request from user agent', () => {
      mockReq.headers = { 'user-agent': 'legacy-client/1.0' };

      const result = controller.testLegacyCheck(mockReq as Request);

      expect(result).toBe(true);
    });

    it('should return false for non-legacy requests', () => {
      const result = controller.testLegacyCheck(mockReq as Request);

      expect(result).toBe(false);
    });
  });

  describe('sendSuccess', () => {
    it('should send successful response with metadata', () => {
      const data = { test: 'data' };
      const metadata = { executionTime: 100 };

      controller['sendSuccess'](mockRes as Response, data, 201, metadata);

      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data,
        timestamp: expect.any(String),
        requestId: 'test-request-id',
        metadata
      });
    });
  });

  describe('sendError', () => {
    it('should send error response', () => {
      const error = 'Test error message';
      const details = { field: 'value' };

      controller['sendError'](mockRes as Response, error, 400, details);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        code: 'VALIDATION_ERROR',
        message: error,
        statusCode: 400,
        timestamp: expect.any(String),
        requestId: 'test-request-id',
        details
      });
    });

    it('should handle Error objects', () => {
      const error = new Error('Test error');

      controller['sendError'](mockRes as Response, error, 500);

      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Test error',
          code: 'INTERNAL_SERVER_ERROR'
        })
      );
    });
  });
});
