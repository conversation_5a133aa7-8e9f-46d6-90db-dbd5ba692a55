/**
 * Graph Controller
 * 
 * Handles graph-related endpoints including initial data, search, expand,
 * filter, and custom queries. Implements Strangler Fig pattern to gradually
 * replace legacy graph routes.
 */

import { Request, Response } from 'express';
import { BaseController } from './BaseController';
import { GraphData, GraphNode } from '@kg-visualizer/shared';

export interface GraphService {
  getInitialGraph(limit?: number): Promise<GraphData>;
  searchNodes(term: string, limit?: number): Promise<GraphNode[]>;
  expandNode(nodeId: string, limit?: number): Promise<GraphData>;
  filterGraph(filters: GraphFilters): Promise<GraphData>;
  executeQuery(query: string, parameters?: Record<string, any>): Promise<any[]>;
  getMetadata(): Promise<DatabaseMetadata>;
}

export interface GraphFilters {
  nodeLabels?: string[];
  relationshipTypes?: string[];
  properties?: Array<{
    key: string;
    operator: 'equals' | 'contains' | 'startsWith' | 'endsWith';
    value: any;
  }>;
  dateRange?: {
    start: string;
    end: string;
    property: string;
  };
}

export interface DatabaseMetadata {
  nodeLabels: Array<{
    label: string;
    count: number;
    properties: string[];
  }>;
  relationshipTypes: Array<{
    type: string;
    count: number;
    properties: string[];
  }>;
  indexes: Array<{
    name: string;
    type: string;
    labels: string[];
    properties: string[];
  }>;
  constraints: Array<{
    name: string;
    type: string;
    label: string;
    properties: string[];
  }>;
  statistics: {
    totalNodes: number;
    totalRelationships: number;
    databaseSize: string;
    lastUpdated: string;
  };
}

export class GraphController extends BaseController {
  constructor(private graphService: GraphService) {
    super();
  }

  /**
   * GET /api/graph/initial
   * Get initial graph data for visualization
   */
  getInitialGraph = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('getInitialGraph', req);

    const { limit } = this.getPaginationParams(req);

    const { result: graphData, duration } = await this.withTiming(
      () => this.graphService.getInitialGraph(limit),
      'getInitialGraph',
      req
    );

    // Set cache headers for initial graph data (5 minutes)
    this.setCacheHeaders(res, 300);

    // Check if legacy compatibility mode is requested
    const legacyMode = req.query.legacy === 'true' || req.headers['x-legacy-mode'] === 'true';

    if (legacyMode) {
      // Return legacy format: {nodes, edges}
      res.json({
        nodes: graphData.nodes,
        edges: graphData.relationships.map(rel => ({
          ...rel,
          // Ensure legacy field names
          from: rel.source || (rel as any).from,
          to: rel.target || (rel as any).to
        }))
      });
    } else {
      // Return new format with metadata
      this.sendSuccess(res, graphData, 200, {
        executionTime: duration,
        nodeCount: graphData.nodes.length,
        relationshipCount: graphData.relationships.length
      });
    }
  });

  /**
   * GET /api/graph/search
   * Search for nodes by term
   */
  searchNodes = this.asyncHandler(async (req: Request, res: Response) => {
    const { term } = req.query;
    
    if (!term || typeof term !== 'string') {
      return this.sendError(res, 'Search term is required', 400);
    }

    this.logAction('searchNodes', req, { term });

    const { limit } = this.getPaginationParams(req);
    
    const { result: nodes, duration } = await this.withTiming(
      () => this.graphService.searchNodes(term, limit),
      'searchNodes',
      req
    );

    this.sendSuccess(res, nodes, 200, {
      executionTime: duration,
      resultCount: nodes.length,
      searchTerm: term
    });
  });

  /**
   * GET /api/graph/expand
   * Expand a node to show connected nodes
   */
  expandNode = this.asyncHandler(async (req: Request, res: Response) => {
    const { nodeId } = req.query;
    
    if (!nodeId || typeof nodeId !== 'string') {
      return this.sendError(res, 'Node ID is required', 400);
    }

    this.logAction('expandNode', req, { nodeId });

    const { limit } = this.getPaginationParams(req);
    
    const { result: graphData, duration } = await this.withTiming(
      () => this.graphService.expandNode(nodeId, limit),
      'expandNode',
      req
    );

    this.sendSuccess(res, graphData, 200, {
      executionTime: duration,
      nodeCount: graphData.nodes.length,
      relationshipCount: graphData.relationships.length,
      expandedNodeId: nodeId
    });
  });

  /**
   * POST /api/graph/filter
   * Filter graph data based on criteria
   */
  filterGraph = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('filterGraph', req);

    const validation = this.validateRequestBody<GraphFilters>(
      req.body,
      this.validateGraphFilters
    );

    if (!validation.isValid) {
      return this.sendError(res, 'Invalid filter parameters', 400, {
        errors: validation.errors
      });
    }

    const { result: graphData, duration } = await this.withTiming(
      () => this.graphService.filterGraph(validation.data!),
      'filterGraph',
      req
    );

    this.sendSuccess(res, graphData, 200, {
      executionTime: duration,
      nodeCount: graphData.nodes.length,
      relationshipCount: graphData.relationships.length,
      filters: validation.data
    });
  });

  /**
   * POST /api/graph/query
   * Execute custom Cypher query
   */
  executeQuery = this.asyncHandler(async (req: Request, res: Response) => {
    const { query, parameters } = req.body;
    
    if (!query || typeof query !== 'string') {
      return this.sendError(res, 'Cypher query is required', 400);
    }

    this.logAction('executeQuery', req, { 
      queryLength: query.length,
      hasParameters: !!parameters 
    });

    // Validate query safety in production
    if (process.env.NODE_ENV === 'production') {
      const validation = this.validateCypherQuery(query);
      if (!validation.isValid) {
        return this.sendError(res, 'Query validation failed', 400, {
          errors: validation.errors
        });
      }
    }

    const { result: records, duration } = await this.withTiming(
      () => this.graphService.executeQuery(query, parameters),
      'executeQuery',
      req
    );

    this.sendSuccess(res, records, 200, {
      executionTime: duration,
      recordCount: records.length,
      queryLength: query.length
    });
  });

  /**
   * GET /api/graph/metadata
   * Get database metadata and statistics
   */
  getMetadata = this.asyncHandler(async (req: Request, res: Response) => {
    this.logAction('getMetadata', req);

    const { result: metadata, duration } = await this.withTiming(
      () => this.graphService.getMetadata(),
      'getMetadata',
      req
    );

    // Set cache headers for metadata (10 minutes)
    this.setCacheHeaders(res, 600);

    this.sendSuccess(res, metadata, 200, {
      executionTime: duration
    });
  });

  /**
   * Validate graph filters
   */
  private validateGraphFilters(filters: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (filters.nodeLabels && !Array.isArray(filters.nodeLabels)) {
      errors.push('nodeLabels must be an array');
    }

    if (filters.relationshipTypes && !Array.isArray(filters.relationshipTypes)) {
      errors.push('relationshipTypes must be an array');
    }

    if (filters.properties) {
      if (!Array.isArray(filters.properties)) {
        errors.push('properties must be an array');
      } else {
        filters.properties.forEach((prop: any, index: number) => {
          if (!prop.key || typeof prop.key !== 'string') {
            errors.push(`properties[${index}].key is required and must be a string`);
          }
          if (!prop.operator || !['equals', 'contains', 'startsWith', 'endsWith'].includes(prop.operator)) {
            errors.push(`properties[${index}].operator must be one of: equals, contains, startsWith, endsWith`);
          }
          if (prop.value === undefined || prop.value === null) {
            errors.push(`properties[${index}].value is required`);
          }
        });
      }
    }

    if (filters.dateRange) {
      if (!filters.dateRange.start || !filters.dateRange.end || !filters.dateRange.property) {
        errors.push('dateRange requires start, end, and property fields');
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Validate Cypher query for safety
   */
  private validateCypherQuery(query: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const upperQuery = query.toUpperCase();

    // Check for dangerous operations in production
    const dangerousOperations = [
      'DELETE',
      'REMOVE',
      'DROP',
      'CREATE CONSTRAINT',
      'DROP CONSTRAINT',
      'CREATE INDEX',
      'DROP INDEX',
      'LOAD CSV',
      'CALL apoc'
    ];

    for (const operation of dangerousOperations) {
      if (upperQuery.includes(operation)) {
        errors.push(`Dangerous operation '${operation}' not allowed`);
      }
    }

    // Check for basic syntax issues
    const openParens = (query.match(/\(/g) || []).length;
    const closeParens = (query.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      errors.push('Mismatched parentheses in query');
    }

    const openBrackets = (query.match(/\[/g) || []).length;
    const closeBrackets = (query.match(/\]/g) || []).length;
    if (openBrackets !== closeBrackets) {
      errors.push('Mismatched brackets in query');
    }

    // Check query length
    if (query.length > 10000) {
      errors.push('Query too long (maximum 10,000 characters)');
    }

    return { isValid: errors.length === 0, errors };
  }
}
