/**
 * Compatibility Router
 * 
 * Routes requests between legacy and new implementations based on feature flags
 * and provides fallback mechanisms during migration.
 */

import { Router, Request, Response, NextFunction } from 'express';
import { logger, FeatureFlags } from '@kg-visualizer/shared';
import { CompatibilityService } from '../services/CompatibilityService';
import axios from 'axios';

interface RouteConfig {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  legacyEndpoint: string;
  newHandler?: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  enableDualExecution?: boolean;
  enableFallback?: boolean;
}

export class CompatibilityRouter {
  private readonly router: Router;
  private readonly logger = logger.child('compatibility-router');
  private readonly compatibilityService: CompatibilityService;
  private readonly featureFlags: FeatureFlags;
  private readonly legacyApiUrl: string;

  constructor(
    compatibilityService: CompatibilityService,
    featureFlags: FeatureFlags,
    legacyApiUrl: string = 'http://localhost:3002'
  ) {
    this.router = Router();
    this.compatibilityService = compatibilityService;
    this.featureFlags = featureFlags;
    this.legacyApiUrl = legacyApiUrl;
  }

  /**
   * Register a route with compatibility handling
   */
  registerRoute(config: RouteConfig): void {
    const { path, method, legacyEndpoint, newHandler, enableDualExecution = true, enableFallback = true } = config;

    const handler = async (req: Request, res: Response, next: NextFunction) => {
      const requestId = req.headers['x-request-id'] as string || this.generateRequestId();
      const startTime = Date.now();

      try {
        // Determine routing strategy based on feature flags
        const useNewImplementation = this.shouldUseNewImplementation(path, req);
        
        this.logger.debug('Route decision made', 'compatibility-router', {
          path,
          method,
          requestId,
          useNewImplementation,
          hasNewHandler: !!newHandler
        });

        if (useNewImplementation && newHandler) {
          // Use new implementation with optional dual execution
          await this.executeWithCompatibility(
            req,
            res,
            next,
            newHandler,
            legacyEndpoint,
            enableDualExecution,
            enableFallback
          );
        } else {
          // Fallback to legacy implementation
          await this.proxyToLegacy(req, res, legacyEndpoint);
        }

        this.logger.info('Route completed', 'compatibility-router', {
          path,
          method,
          requestId,
          executionTime: Date.now() - startTime,
          useNewImplementation
        });

      } catch (error) {
        this.logger.error('Route execution failed', 'compatibility-router', {
          path,
          method,
          requestId,
          error: (error as Error).message,
          executionTime: Date.now() - startTime
        });

        if (enableFallback && newHandler) {
          this.logger.info('Attempting fallback to legacy', 'compatibility-router', { path, requestId });
          try {
            await this.proxyToLegacy(req, res, legacyEndpoint);
          } catch (fallbackError) {
            this.logger.error('Fallback also failed', 'compatibility-router', {
              path,
              requestId,
              fallbackError: (fallbackError as Error).message
            });
            next(error);
          }
        } else {
          next(error);
        }
      }
    };

    // Register the route with the appropriate HTTP method
    switch (method) {
      case 'GET':
        this.router.get(path, handler);
        break;
      case 'POST':
        this.router.post(path, handler);
        break;
      case 'PUT':
        this.router.put(path, handler);
        break;
      case 'DELETE':
        this.router.delete(path, handler);
        break;
    }

    this.logger.info('Route registered', 'compatibility-router', { path, method, legacyEndpoint });
  }

  /**
   * Execute new implementation with compatibility checking
   */
  private async executeWithCompatibility(
    req: Request,
    res: Response,
    next: NextFunction,
    newHandler: (req: Request, res: Response, next: NextFunction) => Promise<void>,
    legacyEndpoint: string,
    enableDualExecution: boolean,
    enableFallback: boolean
  ): Promise<void> {
    if (enableDualExecution && this.featureFlags.DUAL_EXECUTION_MODE) {
      // Execute both implementations and compare
      const newImplementation = async () => {
        return new Promise((resolve, reject) => {
          const mockRes = this.createMockResponse(resolve, reject);
          newHandler(req, mockRes, reject);
        });
      };

      try {
        const result = await this.compatibilityService.dualExecute(
          newImplementation,
          legacyEndpoint,
          this.extractLegacyPayload(req),
          { enableDualExecution, enableValidation: true }
        );

        this.compatibilityService.logCompatibilityMetrics(
          req.path,
          result,
          true
        );

        // Send the primary (new) result
        res.json(result.primary);

      } catch (error) {
        if (enableFallback) {
          this.logger.warn('New implementation failed, falling back to legacy', 'compatibility-router', {
            path: req.path,
            error: (error as Error).message
          });
          await this.proxyToLegacy(req, res, legacyEndpoint);
        } else {
          throw error;
        }
      }
    } else {
      // Execute only new implementation
      await newHandler(req, res, next);
    }
  }

  /**
   * Proxy request to legacy API
   */
  private async proxyToLegacy(req: Request, res: Response, legacyEndpoint: string): Promise<void> {
    const url = `${this.legacyApiUrl}${legacyEndpoint}`;
    const config = {
      timeout: 30000,
      headers: {
        ...req.headers,
        'X-Proxied-Request': 'true',
        host: undefined // Remove host header to avoid conflicts
      }
    };

    try {
      let response;

      switch (req.method) {
        case 'GET':
          response = await axios.get(url, { ...config, params: req.query });
          break;
        case 'POST':
          response = await axios.post(url, req.body, config);
          break;
        case 'PUT':
          response = await axios.put(url, req.body, config);
          break;
        case 'DELETE':
          response = await axios.delete(url, config);
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${req.method}`);
      }

      // Forward response headers
      Object.entries(response.headers).forEach(([key, value]) => {
        if (key.toLowerCase() !== 'content-encoding') {
          res.setHeader(key, value as string);
        }
      });

      res.status(response.status).json(response.data);

    } catch (error) {
      if (axios.isAxiosError(error)) {
        const status = error.response?.status || 500;
        const data = error.response?.data || { error: 'Legacy API unavailable' };
        res.status(status).json(data);
      } else {
        throw error;
      }
    }
  }

  /**
   * Determine if new implementation should be used
   */
  private shouldUseNewImplementation(path: string, req: Request): boolean {
    // Check for explicit override headers
    if (req.headers['x-use-legacy'] === 'true') {
      return false;
    }
    if (req.headers['x-use-new'] === 'true') {
      return true;
    }

    // Check feature flags for specific endpoints
    if (path.includes('/api/graph/')) {
      return this.featureFlags.NEW_CONTROLLER_LAYER;
    }
    if (path.includes('/api/analysis/')) {
      return this.featureFlags.NEW_SERVICE_LAYER;
    }
    if (path.includes('/api/chat/')) {
      return this.featureFlags.NEW_CHAT_SERVICE;
    }

    // Default to feature flag for new controller layer
    return this.featureFlags.NEW_CONTROLLER_LAYER;
  }

  /**
   * Extract payload for legacy API call
   */
  private extractLegacyPayload(req: Request): any {
    if (req.method === 'GET') {
      return req.query;
    }
    return req.body;
  }

  /**
   * Create mock response object for dual execution
   */
  private createMockResponse(resolve: (value: any) => void, reject: (error: any) => void): any {
    return {
      json: (data: any) => resolve(data),
      status: (code: number) => ({
        json: (data: any) => {
          if (code >= 400) {
            reject(new Error(`HTTP ${code}: ${JSON.stringify(data)}`));
          } else {
            resolve(data);
          }
        }
      }),
      setHeader: () => {},
      end: () => {}
    };
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Get the Express router
   */
  getRouter(): Router {
    return this.router;
  }

  /**
   * Register all legacy routes for compatibility
   */
  registerLegacyRoutes(): void {
    // Graph routes
    this.registerRoute({
      path: '/initial',
      method: 'GET',
      legacyEndpoint: '/api/graph/initial'
    });

    this.registerRoute({
      path: '/search',
      method: 'GET',
      legacyEndpoint: '/api/graph/search'
    });

    this.registerRoute({
      path: '/expand',
      method: 'GET',
      legacyEndpoint: '/api/graph/expand'
    });

    this.registerRoute({
      path: '/filter',
      method: 'POST',
      legacyEndpoint: '/api/graph/filter'
    });

    this.registerRoute({
      path: '/query',
      method: 'POST',
      legacyEndpoint: '/api/graph/query'
    });

    // Analysis routes
    this.registerRoute({
      path: '/centrality',
      method: 'GET',
      legacyEndpoint: '/api/analysis/centrality'
    });

    this.registerRoute({
      path: '/clusters',
      method: 'GET',
      legacyEndpoint: '/api/analysis/clusters'
    });

    this.registerRoute({
      path: '/hidden-links',
      method: 'GET',
      legacyEndpoint: '/api/analysis/hidden-links'
    });

    this.logger.info('All legacy routes registered for compatibility', 'compatibility-router');
  }
}
