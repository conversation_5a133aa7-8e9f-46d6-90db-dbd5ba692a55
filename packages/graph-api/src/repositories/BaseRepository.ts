/**
 * Base Repository
 * 
 * Abstract base class for all repositories providing common functionality
 * like connection management, query execution, and dual-execution validation.
 */

import { Driver, Session } from 'neo4j-driver';
import { logger } from '@kg-visualizer/shared';

export interface QueryResult {
  records: any[];
  summary: any;
  executionTime: number;
}

export interface DualExecutionResult<T> {
  primary: T;
  secondary?: T;
  isValid: boolean;
  differences: string[];
  executionTime: {
    primary: number;
    secondary?: number;
  };
}

export interface RepositoryConfig {
  enableDualExecution?: boolean;
  enableValidation?: boolean;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

export abstract class BaseRepository {
  protected readonly driver: Driver;
  protected readonly database: string;
  protected readonly config: RepositoryConfig;
  protected readonly logger = logger.child('repository');

  constructor(driver: Driver, database: string = 'neo4j', config: RepositoryConfig = {}) {
    this.driver = driver;
    this.database = database;
    this.config = {
      enableDualExecution: false,
      enableValidation: true,
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      ...config
    };
  }

  /**
   * Get a Neo4j session
   */
  protected getSession(): Session {
    return this.driver.session({ database: this.database });
  }

  /**
   * Execute a read query with optional dual execution
   */
  protected async executeRead<T>(
    query: string,
    parameters: Record<string, any> = {},
    transformer: (result: QueryResult) => T,
    legacyQuery?: string,
    legacyParameters?: Record<string, any>
  ): Promise<DualExecutionResult<T> | T> {
    if (this.config.enableDualExecution && legacyQuery) {
      return this.executeDual(
        query,
        parameters,
        transformer,
        legacyQuery,
        legacyParameters || parameters,
        'read'
      );
    }

    const result = await this.executeQuery(query, parameters, 'read');
    return transformer(result);
  }

  /**
   * Execute a write query with optional dual execution
   */
  protected async executeWrite<T>(
    query: string,
    parameters: Record<string, any> = {},
    transformer: (result: QueryResult) => T,
    legacyQuery?: string,
    legacyParameters?: Record<string, any>
  ): Promise<DualExecutionResult<T> | T> {
    if (this.config.enableDualExecution && legacyQuery) {
      return this.executeDual(
        query,
        parameters,
        transformer,
        legacyQuery,
        legacyParameters || parameters,
        'write'
      );
    }

    const result = await this.executeQuery(query, parameters, 'write');
    return transformer(result);
  }

  /**
   * Execute dual queries for validation
   */
  private async executeDual<T>(
    primaryQuery: string,
    primaryParameters: Record<string, any>,
    transformer: (result: QueryResult) => T,
    secondaryQuery: string,
    secondaryParameters: Record<string, any>,
    mode: 'read' | 'write'
  ): Promise<DualExecutionResult<T>> {
    const startTime = Date.now();

    try {
      // Execute primary (new) query
      const primaryStart = Date.now();
      const primaryResult = await this.executeQuery(primaryQuery, primaryParameters, mode);
      const primaryTime = Date.now() - primaryStart;
      const primary = transformer(primaryResult);

      // Execute secondary (legacy) query for comparison
      let secondary: T | undefined;
      let secondaryTime: number | undefined;
      let differences: string[] = [];
      let isValid = true;

      try {
        const secondaryStart = Date.now();
        const secondaryResult = await this.executeQuery(secondaryQuery, secondaryParameters, mode);
        secondaryTime = Date.now() - secondaryStart;
        secondary = transformer(secondaryResult);

        // Validate results if enabled
        if (this.config.enableValidation) {
          const validation = this.validateResults(primary, secondary);
          differences = validation.differences;
          isValid = validation.isValid;
        }

      } catch (error) {
        this.logger.warn('Secondary query execution failed', 'repository', {
          error: (error as Error).message,
          secondaryQuery: secondaryQuery.substring(0, 100) + '...'
        });
        isValid = false;
        differences.push(`Secondary execution failed: ${(error as Error).message}`);
      }

      // Log dual execution results
      this.logDualExecution({
        primaryTime,
        secondaryTime,
        isValid,
        differences,
        totalTime: Date.now() - startTime
      });

      return {
        primary,
        secondary,
        isValid,
        differences,
        executionTime: {
          primary: primaryTime,
          secondary: secondaryTime
        }
      };

    } catch (error) {
      this.logger.error('Dual execution failed', 'repository', {
        error: (error as Error).message,
        primaryQuery: primaryQuery.substring(0, 100) + '...'
      });
      throw error;
    }
  }

  /**
   * Execute a single query with retry logic
   */
  private async executeQuery(
    query: string,
    parameters: Record<string, any>,
    mode: 'read' | 'write'
  ): Promise<QueryResult> {
    let lastError: Error;

    for (let attempt = 1; attempt <= this.config.retryAttempts!; attempt++) {
      const session = this.getSession();
      const startTime = Date.now();

      try {
        const result = await this.withTimeout(
          mode === 'read' 
            ? session.executeRead(tx => tx.run(query, parameters))
            : session.executeWrite(tx => tx.run(query, parameters)),
          this.config.timeout!
        );

        const executionTime = Date.now() - startTime;

        return {
          records: result.records,
          summary: result.summary,
          executionTime
        };

      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.config.retryAttempts!) {
          this.logger.warn(`Query attempt ${attempt} failed, retrying`, 'repository', {
            error: lastError.message,
            attempt,
            query: query.substring(0, 100) + '...'
          });
          
          await this.delay(this.config.retryDelay! * attempt);
        }
      } finally {
        await session.close();
      }
    }

    throw lastError!;
  }

  /**
   * Execute query within a transaction
   */
  protected async executeInTransaction<T>(
    operation: (tx: any) => Promise<T>,
    mode: 'read' | 'write' = 'read'
  ): Promise<T> {
    const session = this.getSession();

    try {
      return await (mode === 'read'
        ? session.executeRead(operation)
        : session.executeWrite(operation));
    } finally {
      await session.close();
    }
  }

  /**
   * Validate dual execution results
   */
  private validateResults<T>(primary: T, secondary: T): { isValid: boolean; differences: string[] } {
    const differences: string[] = [];

    try {
      // Deep comparison of results
      const primaryJson = JSON.stringify(primary, this.sortKeys);
      const secondaryJson = JSON.stringify(secondary, this.sortKeys);

      if (primaryJson !== secondaryJson) {
        differences.push('Results differ between primary and secondary execution');
        
        // Try to identify specific differences
        if (Array.isArray(primary) && Array.isArray(secondary)) {
          if (primary.length !== secondary.length) {
            differences.push(`Array length differs: primary=${primary.length}, secondary=${secondary.length}`);
          }
        }

        if (typeof primary === 'object' && typeof secondary === 'object' && primary && secondary) {
          const primaryKeys = Object.keys(primary).sort();
          const secondaryKeys = Object.keys(secondary).sort();
          
          if (JSON.stringify(primaryKeys) !== JSON.stringify(secondaryKeys)) {
            differences.push('Object keys differ between results');
          }
        }
      }

    } catch (error) {
      differences.push(`Validation error: ${(error as Error).message}`);
    }

    return {
      isValid: differences.length === 0,
      differences
    };
  }

  /**
   * Sort object keys for consistent comparison
   */
  private sortKeys = (_key: string, value: any): any => {
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      return Object.keys(value)
        .sort()
        .reduce((sorted: any, k) => {
          sorted[k] = value[k];
          return sorted;
        }, {});
    }
    return value;
  };

  /**
   * Add timeout to promise
   */
  private async withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Query timed out after ${timeoutMs}ms`)), timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  /**
   * Delay execution
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Log dual execution results
   */
  private logDualExecution(metrics: {
    primaryTime: number;
    secondaryTime?: number;
    isValid: boolean;
    differences: string[];
    totalTime: number;
  }): void {
    this.logger.info('Dual execution completed', 'repository', {
      primaryTime: metrics.primaryTime,
      secondaryTime: metrics.secondaryTime,
      isValid: metrics.isValid,
      differenceCount: metrics.differences.length,
      totalTime: metrics.totalTime,
      overhead: metrics.totalTime - metrics.primaryTime - (metrics.secondaryTime || 0)
    });

    if (!metrics.isValid && metrics.differences.length > 0) {
      this.logger.warn('Dual execution validation failed', 'repository', {
        differences: metrics.differences.slice(0, 5) // Log first 5 differences
      });
    }
  }

  /**
   * Health check for repository
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: Record<string, any>;
  }> {
    try {
      const startTime = Date.now();
      await this.executeQuery('RETURN 1 as test', {}, 'read');
      const responseTime = Date.now() - startTime;

      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      if (responseTime > 5000) {
        status = 'unhealthy';
      } else if (responseTime > 1000) {
        status = 'degraded';
      }

      return {
        status,
        details: {
          responseTime,
          database: this.database,
          dualExecutionEnabled: this.config.enableDualExecution,
          validationEnabled: this.config.enableValidation
        }
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: (error as Error).message,
          database: this.database
        }
      };
    }
  }
}
