/**
 * Repository Factory
 * 
 * Factory for creating and managing repository instances with
 * feature flag-controlled dual execution and validation.
 */

import { Driver } from 'neo4j-driver';
import { FeatureFlags, logger } from '@kg-visualizer/shared';
import { GraphRepository } from './GraphRepository';
import { AnalysisRepository } from './AnalysisRepository';
import { RepositoryConfig } from './BaseRepository';

export interface RepositoryDependencies {
  driver: Driver;
  database?: string;
  featureFlags: FeatureFlags;
}

export interface RepositoryInstances {
  graph: GraphRepository;
  analysis: AnalysisRepository;
}

export class RepositoryFactory {
  private readonly driver: Driver;
  private readonly database: string;
  private readonly featureFlags: FeatureFlags;
  private readonly logger = logger.child('repository-factory');
  private repositories: Partial<RepositoryInstances> = {};

  constructor(dependencies: RepositoryDependencies) {
    this.driver = dependencies.driver;
    this.database = dependencies.database || 'neo4j';
    this.featureFlags = dependencies.featureFlags;
  }

  /**
   * Get or create graph repository
   */
  getGraphRepository(): GraphRepository {
    if (!this.repositories.graph) {
      const config = this.createRepositoryConfig();
      this.repositories.graph = new GraphRepository(this.driver, this.database, config);
      
      this.logger.info('Graph repository created', 'repository-factory', {
        database: this.database,
        dualExecution: config.enableDualExecution,
        validation: config.enableValidation
      });
    }

    return this.repositories.graph;
  }

  /**
   * Get or create analysis repository
   */
  getAnalysisRepository(): AnalysisRepository {
    if (!this.repositories.analysis) {
      const config = this.createRepositoryConfig();
      this.repositories.analysis = new AnalysisRepository(this.driver, this.database, config);
      
      this.logger.info('Analysis repository created', 'repository-factory', {
        database: this.database,
        dualExecution: config.enableDualExecution,
        validation: config.enableValidation
      });
    }

    return this.repositories.analysis;
  }

  /**
   * Get all repositories
   */
  getAllRepositories(): RepositoryInstances {
    return {
      graph: this.getGraphRepository(),
      analysis: this.getAnalysisRepository()
    };
  }

  /**
   * Create repository configuration based on feature flags
   */
  private createRepositoryConfig(): RepositoryConfig {
    return {
      enableDualExecution: this.featureFlags.NEW_REPOSITORY_PATTERN && this.featureFlags.DUAL_EXECUTION_MODE,
      enableValidation: this.featureFlags.NEW_REPOSITORY_PATTERN,
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000
    };
  }

  /**
   * Health check for all repositories
   */
  async healthCheck(): Promise<{
    overall: 'healthy' | 'degraded' | 'unhealthy';
    repositories: Record<string, any>;
  }> {
    const repositories = this.getAllRepositories();
    const healthChecks = await Promise.allSettled([
      repositories.graph.healthCheck(),
      repositories.analysis.healthCheck()
    ]);

    const results: Record<string, any> = {};
    const statuses: string[] = [];

    // Process graph repository health
    if (healthChecks[0].status === 'fulfilled') {
      results.graph = healthChecks[0].value;
      statuses.push(healthChecks[0].value.status);
    } else {
      results.graph = {
        status: 'unhealthy',
        details: { error: (healthChecks[0].reason as Error).message }
      };
      statuses.push('unhealthy');
    }

    // Process analysis repository health
    if (healthChecks[1].status === 'fulfilled') {
      results.analysis = healthChecks[1].value;
      statuses.push(healthChecks[1].value.status);
    } else {
      results.analysis = {
        status: 'unhealthy',
        details: { error: (healthChecks[1].reason as Error).message }
      };
      statuses.push('unhealthy');
    }

    // Determine overall health
    const hasUnhealthy = statuses.includes('unhealthy');
    const hasDegraded = statuses.includes('degraded');

    let overall: 'healthy' | 'degraded' | 'unhealthy';
    if (hasUnhealthy) {
      overall = 'unhealthy';
    } else if (hasDegraded) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    return {
      overall,
      repositories: results
    };
  }

  /**
   * Get migration status for repositories
   */
  getMigrationStatus(): {
    repositoryPatternEnabled: boolean;
    dualExecutionEnabled: boolean;
    validationEnabled: boolean;
    repositoriesCreated: string[];
    migrationPhase: string;
  } {
    const repositoriesCreated = Object.keys(this.repositories);
    const config = this.createRepositoryConfig();

    let migrationPhase = 'not-started';
    if (this.featureFlags.NEW_REPOSITORY_PATTERN) {
      if (config.enableDualExecution) {
        migrationPhase = 'dual-execution';
      } else {
        migrationPhase = 'complete';
      }
    }

    return {
      repositoryPatternEnabled: this.featureFlags.NEW_REPOSITORY_PATTERN,
      dualExecutionEnabled: config.enableDualExecution || false,
      validationEnabled: config.enableValidation || false,
      repositoriesCreated,
      migrationPhase
    };
  }

  /**
   * Update repository configuration when feature flags change
   */
  updateConfiguration(newFeatureFlags: FeatureFlags): void {
    this.logger.info('Updating repository configuration', 'repository-factory', {
      oldFlags: {
        repositoryPattern: this.featureFlags.NEW_REPOSITORY_PATTERN,
        dualExecution: this.featureFlags.DUAL_EXECUTION_MODE
      },
      newFlags: {
        repositoryPattern: newFeatureFlags.NEW_REPOSITORY_PATTERN,
        dualExecution: newFeatureFlags.DUAL_EXECUTION_MODE
      }
    });

    // Update feature flags
    Object.assign(this.featureFlags, newFeatureFlags);

    // Recreate repositories with new configuration if they exist
    if (this.repositories.graph) {
      const config = this.createRepositoryConfig();
      this.repositories.graph = new GraphRepository(this.driver, this.database, config);
      this.logger.info('Graph repository recreated with new configuration', 'repository-factory');
    }

    if (this.repositories.analysis) {
      const config = this.createRepositoryConfig();
      this.repositories.analysis = new AnalysisRepository(this.driver, this.database, config);
      this.logger.info('Analysis repository recreated with new configuration', 'repository-factory');
    }
  }

  /**
   * Reset all repositories (useful for testing)
   */
  reset(): void {
    this.repositories = {};
    this.logger.info('All repositories reset', 'repository-factory');
  }

  /**
   * Get repository statistics
   */
  getStatistics(): {
    totalRepositories: number;
    activeRepositories: number;
    configuration: RepositoryConfig;
    featureFlags: {
      repositoryPattern: boolean;
      dualExecution: boolean;
      validation: boolean;
    };
  } {
    const config = this.createRepositoryConfig();
    
    return {
      totalRepositories: 2, // graph + analysis
      activeRepositories: Object.keys(this.repositories).length,
      configuration: config,
      featureFlags: {
        repositoryPattern: this.featureFlags.NEW_REPOSITORY_PATTERN,
        dualExecution: this.featureFlags.DUAL_EXECUTION_MODE,
        validation: config.enableValidation || false
      }
    };
  }

  /**
   * Validate repository configuration
   */
  validateConfiguration(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check driver
    if (!this.driver) {
      errors.push('Neo4j driver is required');
    }

    // Check database name
    if (!this.database || this.database.trim() === '') {
      errors.push('Database name is required');
    }

    // Check feature flag consistency
    if (this.featureFlags.DUAL_EXECUTION_MODE && !this.featureFlags.NEW_REPOSITORY_PATTERN) {
      warnings.push('Dual execution is enabled but repository pattern is disabled');
    }

    // Check timeout configuration
    const config = this.createRepositoryConfig();
    if (config.timeout && config.timeout < 1000) {
      warnings.push('Query timeout is very low (< 1 second)');
    }

    if (config.retryAttempts && config.retryAttempts > 5) {
      warnings.push('Retry attempts is very high (> 5)');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get driver information
   */
  getDriverInfo(): {
    isConnected: boolean;
    database: string;
    supportsMultiDatabase: boolean;
  } {
    return {
      isConnected: !!this.driver,
      database: this.database,
      supportsMultiDatabase: true // Neo4j 4.0+
    };
  }
}
