/**
 * Analysis Repository
 * 
 * Repository for graph analysis operations with dual-execution validation.
 * Handles centrality measures, community detection, and path finding.
 */

import { Driver } from 'neo4j-driver';
import { BaseRepository, QueryResult, DualExecutionResult, RepositoryConfig } from './BaseRepository';

export type CentralityType = 'degree' | 'betweenness' | 'closeness' | 'pagerank' | 'eigenvector';
export type ClusteringAlgorithm = 'louvain' | 'leiden' | 'label_propagation' | 'weakly_connected';

export interface CentralityResult {
  nodeId: string;
  nodeName: string;
  score: number;
  rank: number;
  properties?: Record<string, any>;
}

export interface ClusterResult {
  clusterId: string;
  nodes: Array<{
    nodeId: string;
    nodeName: string;
    properties?: Record<string, any>;
  }>;
  size: number;
  density: number;
  modularity?: number;
}

export interface LinkPrediction {
  sourceNodeId: string;
  targetNodeId: string;
  sourceName: string;
  targetName: string;
  score: number;
  confidence: number;
  reasoning?: string;
}

export interface PathResult {
  path: Array<{
    nodeId: string;
    nodeName: string;
    relationshipType?: string;
  }>;
  length: number;
  weight?: number;
  cost?: number;
}

export interface GraphStatistics {
  nodeCount: number;
  relationshipCount: number;
  density: number;
  diameter: number;
  averagePathLength: number;
  clusteringCoefficient: number;
  connectedComponents: number;
  stronglyConnectedComponents: number;
  nodeTypeDistribution: Record<string, number>;
  relationshipTypeDistribution: Record<string, number>;
  degreeDistribution: {
    min: number;
    max: number;
    mean: number;
    median: number;
    standardDeviation: number;
  };
}

export interface SimilarityResult {
  nodeId: string;
  nodeName: string;
  similarity: number;
  commonNeighbors: number;
  sharedProperties: string[];
}

export class AnalysisRepository extends BaseRepository {
  constructor(driver: Driver, database: string = 'neo4j', config?: RepositoryConfig) {
    super(driver, database, config);
  }

  /**
   * Calculate node centrality metrics
   */
  async getCentrality(
    type: CentralityType, 
    limit: number = 20
  ): Promise<CentralityResult[] | DualExecutionResult<CentralityResult[]>> {
    const graphName = `centrality_${Date.now()}`;
    
    try {
      return await this.executeInTransaction(async (tx) => {
        // Create graph projection
        await this.createGraphProjection(tx, graphName);

        const newQuery = this.getCentralityQuery(type, graphName, 'new');
        const legacyQuery = this.getCentralityQuery(type, graphName, 'legacy');

        const result = await this.executeRead(
          newQuery.cypher,
          { ...newQuery.parameters, limit },
          this.transformToCentralityResults.bind(this),
          legacyQuery.cypher,
          { ...legacyQuery.parameters, limit }
        );

        // Clean up graph projection
        await this.dropGraphProjection(tx, graphName);

        return result;
      }, 'read');

    } catch (error) {
      // Ensure cleanup even on error
      try {
        const session = this.getSession();
        await session.run(`CALL gds.graph.drop($graphName, false)`, { graphName });
        await session.close();
      } catch {
        // Ignore cleanup errors
      }
      throw error;
    }
  }

  /**
   * Perform community detection
   */
  async getClusters(
    algorithm: ClusteringAlgorithm = 'louvain', 
    limit: number = 20
  ): Promise<ClusterResult[] | DualExecutionResult<ClusterResult[]>> {
    const graphName = `clustering_${Date.now()}`;
    
    try {
      return await this.executeInTransaction(async (tx) => {
        // Create graph projection
        await this.createGraphProjection(tx, graphName);

        const newQuery = this.getClusteringQuery(algorithm, graphName, 'new');
        const legacyQuery = this.getClusteringQuery(algorithm, graphName, 'legacy');

        const result = await this.executeRead(
          newQuery.cypher,
          { ...newQuery.parameters, limit },
          this.transformToClusterResults.bind(this),
          legacyQuery.cypher,
          { ...legacyQuery.parameters, limit }
        );

        // Clean up graph projection
        await this.dropGraphProjection(tx, graphName);

        return result;
      }, 'read');

    } catch (error) {
      // Ensure cleanup even on error
      try {
        const session = this.getSession();
        await session.run(`CALL gds.graph.drop($graphName, false)`, { graphName });
        await session.close();
      } catch {
        // Ignore cleanup errors
      }
      throw error;
    }
  }

  /**
   * Predict potential links
   */
  async predictLinks(
    topN: number = 20, 
    threshold: number = 0.4
  ): Promise<LinkPrediction[] | DualExecutionResult<LinkPrediction[]>> {
    const graphName = `linkpred_${Date.now()}`;
    
    try {
      return await this.executeInTransaction(async (tx) => {
        // Create graph projection
        await this.createGraphProjection(tx, graphName);

        // Try GDS-based approach first, fallback to simple approach
        const newQuery = `
          CALL gds.alpha.linkprediction.adamicAdar.stream($graphName)
          YIELD node1, node2, score
          WHERE score >= $threshold
          MATCH (n1) WHERE id(n1) = node1
          MATCH (n2) WHERE id(n2) = node2
          RETURN 
            id(n1) as sourceId,
            id(n2) as targetId,
            coalesce(n1.name, n1.title, 'Unknown') as sourceName,
            coalesce(n2.name, n2.title, 'Unknown') as targetName,
            score
          ORDER BY score DESC
          LIMIT $topN
        `;

        const legacyQuery = `
          MATCH (n1)-[:*2]-(n2)
          WHERE id(n1) < id(n2)
          AND NOT (n1)-[]-(n2)
          WITH n1, n2, count(*) as commonNeighbors
          WHERE commonNeighbors >= $threshold
          RETURN 
            id(n1) as sourceId,
            id(n2) as targetId,
            coalesce(n1.name, n1.title, 'Unknown') as sourceName,
            coalesce(n2.name, n2.title, 'Unknown') as targetName,
            toFloat(commonNeighbors) / 10.0 as score
          ORDER BY score DESC
          LIMIT $topN
        `;

        const result = await this.executeRead(
          newQuery,
          { graphName, threshold, topN },
          this.transformToLinkPredictions.bind(this),
          legacyQuery,
          { threshold, topN }
        );

        // Clean up graph projection
        await this.dropGraphProjection(tx, graphName);

        return result;
      }, 'read');

    } catch (error) {
      // Fallback to simple approach without GDS
      return this.executeRead(
        `
          MATCH (n1)-[:*2]-(n2)
          WHERE id(n1) < id(n2)
          AND NOT (n1)-[]-(n2)
          WITH n1, n2, count(*) as commonNeighbors
          WHERE commonNeighbors >= $threshold
          RETURN 
            id(n1) as sourceId,
            id(n2) as targetId,
            coalesce(n1.name, n1.title, 'Unknown') as sourceName,
            coalesce(n2.name, n2.title, 'Unknown') as targetName,
            toFloat(commonNeighbors) / 10.0 as score
          ORDER BY score DESC
          LIMIT $topN
        `,
        { threshold, topN },
        this.transformToLinkPredictions.bind(this)
      );
    }
  }

  /**
   * Find paths between two nodes
   */
  async findPaths(
    startNodeId: string, 
    endNodeId: string, 
    maxDepth: number = 5
  ): Promise<PathResult[] | DualExecutionResult<PathResult[]>> {
    const newQuery = `
      MATCH path = shortestPath((start)-[*1..$maxDepth]-(end))
      WHERE id(start) = toInteger($startNodeId) AND id(end) = toInteger($endNodeId)
      RETURN path
      LIMIT 10
    `;

    const legacyQuery = `
      MATCH path = (start)-[*1..$maxDepth]-(end)
      WHERE id(start) = toInteger($startNodeId) AND id(end) = toInteger($endNodeId)
      RETURN path
      ORDER BY length(path)
      LIMIT 10
    `;

    return this.executeRead(
      newQuery,
      { startNodeId, endNodeId, maxDepth },
      this.transformToPathResults.bind(this),
      legacyQuery,
      { startNodeId, endNodeId, maxDepth }
    );
  }

  /**
   * Get comprehensive graph statistics
   */
  async getStatistics(): Promise<GraphStatistics | DualExecutionResult<GraphStatistics>> {
    return this.executeInTransaction(async (tx) => {
      const [
        nodeCount,
        relCount,
        nodeTypes,
        relTypes,
        degreeStats
      ] = await Promise.all([
        this.getNodeCount(tx),
        this.getRelationshipCount(tx),
        this.getNodeTypeDistribution(tx),
        this.getRelationshipTypeDistribution(tx),
        this.getDegreeDistribution(tx)
      ]);

      const density = relCount > 0 ? (2 * relCount) / (nodeCount * (nodeCount - 1)) : 0;

      return {
        nodeCount,
        relationshipCount: relCount,
        density,
        diameter: 0, // Would need complex calculation
        averagePathLength: 0, // Would need complex calculation
        clusteringCoefficient: 0, // Would need complex calculation
        connectedComponents: 0, // Would need GDS
        stronglyConnectedComponents: 0, // Would need GDS
        nodeTypeDistribution: nodeTypes,
        relationshipTypeDistribution: relTypes,
        degreeDistribution: degreeStats
      };
    }, 'read');
  }

  /**
   * Find similar nodes to a given node
   */
  async getSimilarity(
    nodeId: string, 
    limit: number = 20
  ): Promise<SimilarityResult[] | DualExecutionResult<SimilarityResult[]>> {
    const newQuery = `
      MATCH (target) WHERE id(target) = toInteger($nodeId)
      MATCH (target)-[:*1..2]-(similar)
      WHERE id(similar) <> id(target)
      WITH similar, count(*) as commonConnections
      MATCH (similar)-[r]-()
      WITH similar, commonConnections, count(r) as totalConnections
      WHERE totalConnections > 0
      RETURN 
        id(similar) as nodeId,
        coalesce(similar.name, similar.title, 'Unknown') as nodeName,
        toFloat(commonConnections) / toFloat(totalConnections) as similarity,
        commonConnections,
        labels(similar) as labels
      ORDER BY similarity DESC
      LIMIT $limit
    `;

    const legacyQuery = `
      MATCH (target) WHERE id(target) = toInteger($nodeId)
      MATCH (target)-[*1..2]-(similar)
      WHERE id(similar) <> id(target)
      WITH similar, count(*) as commonConnections
      RETURN 
        id(similar) as nodeId,
        coalesce(similar.name, similar.title, 'Unknown') as nodeName,
        toFloat(commonConnections) / 10.0 as similarity,
        commonConnections,
        labels(similar) as labels
      ORDER BY similarity DESC
      LIMIT $limit
    `;

    return this.executeRead(
      newQuery,
      { nodeId, limit },
      this.transformToSimilarityResults.bind(this),
      legacyQuery,
      { nodeId, limit }
    );
  }

  // Helper methods for graph projections
  private async createGraphProjection(tx: any, graphName: string): Promise<void> {
    try {
      await tx.run(`CALL gds.graph.project($graphName, '*', '*')`, { graphName });
    } catch (error) {
      // Graph might already exist, try to drop and recreate
      try {
        await tx.run(`CALL gds.graph.drop($graphName, false)`, { graphName });
        await tx.run(`CALL gds.graph.project($graphName, '*', '*')`, { graphName });
      } catch (retryError) {
        this.logger.warn('Failed to create graph projection', 'analysis-repository', {
          graphName,
          error: (retryError as Error).message
        });
      }
    }
  }

  private async dropGraphProjection(tx: any, graphName: string): Promise<void> {
    try {
      await tx.run(`CALL gds.graph.drop($graphName, false)`, { graphName });
    } catch (error) {
      // Ignore errors when dropping
    }
  }

  // Query builders
  private getCentralityQuery(type: CentralityType, graphName: string, _version: 'new' | 'legacy') {
    const baseQueries = {
      degree: `CALL gds.degree.stream($graphName) YIELD nodeId, score`,
      pagerank: `CALL gds.pageRank.stream($graphName) YIELD nodeId, score`,
      betweenness: `CALL gds.betweenness.stream($graphName) YIELD nodeId, score`,
      closeness: `CALL gds.closeness.stream($graphName) YIELD nodeId, score`,
      eigenvector: `CALL gds.eigenvector.stream($graphName) YIELD nodeId, score`
    };

    const cypher = `
      ${baseQueries[type]}
      MATCH (n) WHERE id(n) = nodeId
      RETURN 
        nodeId,
        coalesce(n.name, n.title, 'Unknown') as nodeName,
        score,
        properties(n) as properties
      ORDER BY score DESC
      LIMIT $limit
    `;

    return {
      cypher,
      parameters: { graphName }
    };
  }

  private getClusteringQuery(algorithm: ClusteringAlgorithm, graphName: string, _version: 'new' | 'legacy') {
    const baseQueries = {
      louvain: `CALL gds.louvain.stream($graphName) YIELD nodeId, communityId`,
      leiden: `CALL gds.leiden.stream($graphName) YIELD nodeId, communityId`,
      label_propagation: `CALL gds.labelPropagation.stream($graphName) YIELD nodeId, communityId`,
      weakly_connected: `CALL gds.wcc.stream($graphName) YIELD nodeId, componentId as communityId`
    };

    const cypher = `
      ${baseQueries[algorithm]}
      MATCH (n) WHERE id(n) = nodeId
      RETURN 
        nodeId,
        communityId as clusterId,
        coalesce(n.name, n.title, 'Unknown') as nodeName,
        properties(n) as properties,
        0.0 as modularity
      ORDER BY clusterId, nodeName
      LIMIT $limit
    `;

    return {
      cypher,
      parameters: { graphName }
    };
  }

  // Transform methods
  private transformToCentralityResults(result: QueryResult): CentralityResult[] {
    return result.records.map((record, index) => ({
      nodeId: record.get('nodeId').toString(),
      nodeName: record.get('nodeName') || 'Unknown',
      score: record.get('score'),
      rank: index + 1,
      properties: record.get('properties') || {}
    }));
  }

  private transformToClusterResults(result: QueryResult): ClusterResult[] {
    const clusterMap = new Map<string, ClusterResult>();
    
    result.records.forEach(record => {
      const clusterId = record.get('clusterId').toString();
      const nodeId = record.get('nodeId').toString();
      const nodeName = record.get('nodeName') || 'Unknown';
      
      if (!clusterMap.has(clusterId)) {
        clusterMap.set(clusterId, {
          clusterId,
          nodes: [],
          size: 0,
          density: 0,
          modularity: record.get('modularity') || 0
        });
      }

      const cluster = clusterMap.get(clusterId)!;
      cluster.nodes.push({
        nodeId,
        nodeName,
        properties: record.get('properties') || {}
      });
      cluster.size = cluster.nodes.length;
    });

    return Array.from(clusterMap.values());
  }

  private transformToLinkPredictions(result: QueryResult): LinkPrediction[] {
    return result.records.map(record => ({
      sourceNodeId: record.get('sourceId').toString(),
      targetNodeId: record.get('targetId').toString(),
      sourceName: record.get('sourceName'),
      targetName: record.get('targetName'),
      score: record.get('score'),
      confidence: Math.min(record.get('score') * 100, 100),
      reasoning: 'Adamic Adar similarity'
    }));
  }

  private transformToPathResults(result: QueryResult): PathResult[] {
    return result.records.map(record => {
      const path = record.get('path');
      const nodes = path.segments?.map((segment: any) => ({
        nodeId: segment.start.identity.toString(),
        nodeName: segment.start.properties.name || 'Unknown',
        relationshipType: segment.relationship.type
      })) || [];

      // Add the end node
      if (path.segments && path.segments.length > 0) {
        const lastSegment = path.segments[path.segments.length - 1];
        nodes.push({
          nodeId: lastSegment.end.identity.toString(),
          nodeName: lastSegment.end.properties.name || 'Unknown'
        });
      }

      return {
        path: nodes,
        length: path.length || 0,
        weight: path.segments?.reduce((sum: number, seg: any) => 
          sum + (seg.relationship.properties.weight || 1), 0) || 0
      };
    });
  }

  private transformToSimilarityResults(result: QueryResult): SimilarityResult[] {
    return result.records.map(record => ({
      nodeId: record.get('nodeId').toString(),
      nodeName: record.get('nodeName'),
      similarity: record.get('similarity'),
      commonNeighbors: record.get('commonConnections'),
      sharedProperties: record.get('labels') || []
    }));
  }

  // Statistics helper methods
  private async getNodeCount(tx: any): Promise<number> {
    const result = await tx.run('MATCH (n) RETURN count(n) as count');
    return result.records[0]?.get('count')?.toNumber() || 0;
  }

  private async getRelationshipCount(tx: any): Promise<number> {
    const result = await tx.run('MATCH ()-[r]->() RETURN count(r) as count');
    return result.records[0]?.get('count')?.toNumber() || 0;
  }

  private async getNodeTypeDistribution(tx: any): Promise<Record<string, number>> {
    const result = await tx.run(`
      MATCH (n)
      RETURN labels(n)[0] as label, count(*) as count
      ORDER BY count DESC
    `);

    const distribution: Record<string, number> = {};
    result.records.forEach((record: any) => {
      const label = record.get('label') || 'Unknown';
      const count = record.get('count').toNumber();
      distribution[label] = count;
    });

    return distribution;
  }

  private async getRelationshipTypeDistribution(tx: any): Promise<Record<string, number>> {
    const result = await tx.run(`
      MATCH ()-[r]->()
      RETURN type(r) as type, count(*) as count
      ORDER BY count DESC
    `);

    const distribution: Record<string, number> = {};
    result.records.forEach((record: any) => {
      const type = record.get('type');
      const count = record.get('count').toNumber();
      distribution[type] = count;
    });

    return distribution;
  }

  private async getDegreeDistribution(tx: any) {
    const result = await tx.run(`
      MATCH (n)-[r]-()
      WITH n, count(r) as degree
      RETURN 
        min(degree) as min,
        max(degree) as max,
        avg(degree) as mean,
        percentileCont(degree, 0.5) as median,
        stDev(degree) as standardDeviation
    `);

    const record = result.records[0];
    return {
      min: record?.get('min')?.toNumber() || 0,
      max: record?.get('max')?.toNumber() || 0,
      mean: record?.get('mean') || 0,
      median: record?.get('median') || 0,
      standardDeviation: record?.get('standardDeviation') || 0
    };
  }
}
