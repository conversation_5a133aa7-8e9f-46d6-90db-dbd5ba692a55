/**
 * Tests for BaseRepository
 */

import { BaseRepository, QueryResult, DualExecutionResult } from '../BaseRepository';

// Mock Neo4j driver
const mockSession = {
  executeRead: jest.fn(),
  executeWrite: jest.fn(),
  close: jest.fn()
};

const mockDriver = {
  session: jest.fn(() => mockSession)
};

// Mock logger
jest.mock('@kg-visualizer/shared', () => ({
  logger: {
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }))
  }
}));

class TestRepository extends BaseRepository {
  async testExecuteRead<T>(
    query: string,
    parameters: Record<string, any>,
    transformer: (result: QueryResult) => T,
    legacyQuery?: string
  ): Promise<T | DualExecutionResult<T>> {
    return this.executeRead(query, parameters, transformer, legacyQuery);
  }

  async testExecuteWrite<T>(
    query: string,
    parameters: Record<string, any>,
    transformer: (result: QueryResult) => T,
    legacyQuery?: string
  ): Promise<T | DualExecutionResult<T>> {
    return this.executeWrite(query, parameters, transformer, legacyQuery);
  }

  async testExecuteInTransaction<T>(
    operation: (tx: any) => Promise<T>,
    mode: 'read' | 'write' = 'read'
  ): Promise<T> {
    return this.executeInTransaction(operation, mode);
  }

  async testHealthCheck() {
    return this.healthCheck();
  }
}

describe('BaseRepository', () => {
  let repository: TestRepository;

  beforeEach(() => {
    jest.clearAllMocks();
    mockDriver.session.mockReturnValue(mockSession);
    repository = new TestRepository(mockDriver as any, 'neo4j', {
      enableDualExecution: false,
      enableValidation: true,
      timeout: 5000,
      retryAttempts: 2
    });
  });

  describe('executeRead', () => {
    it('should execute read query successfully', async () => {
      const mockResult = {
        records: [{ get: jest.fn(() => 'test-data') }],
        summary: { resultAvailableAfter: 10 }
      };

      mockSession.executeRead.mockImplementation((fn) => 
        fn({ run: jest.fn().mockResolvedValue(mockResult) })
      );

      const transformer = (result: QueryResult) => result.records.map(r => r.get('data'));
      const result = await repository.testExecuteRead(
        'MATCH (n) RETURN n',
        {},
        transformer
      );

      expect(result).toEqual(['test-data']);
      expect(mockSession.executeRead).toHaveBeenCalled();
      expect(mockSession.close).toHaveBeenCalled();
    });

    it('should handle query errors with retry', async () => {
      mockSession.executeRead
        .mockRejectedValueOnce(new Error('Connection failed'))
        .mockImplementation((fn) => 
          fn({ run: jest.fn().mockResolvedValue({ records: [], summary: {} }) })
        );

      const transformer = (result: QueryResult) => result.records;
      const result = await repository.testExecuteRead(
        'MATCH (n) RETURN n',
        {},
        transformer
      );

      expect(result).toEqual([]);
      expect(mockSession.executeRead).toHaveBeenCalledTimes(2);
    });

    it('should fail after max retry attempts', async () => {
      mockSession.executeRead.mockRejectedValue(new Error('Persistent failure'));

      const transformer = (result: QueryResult) => result.records;
      
      await expect(
        repository.testExecuteRead('MATCH (n) RETURN n', {}, transformer)
      ).rejects.toThrow('Persistent failure');

      expect(mockSession.executeRead).toHaveBeenCalledTimes(2); // retryAttempts = 2
    });
  });

  describe('dual execution', () => {
    beforeEach(() => {
      repository = new TestRepository(mockDriver as any, 'neo4j', {
        enableDualExecution: true,
        enableValidation: true
      });
    });

    it('should execute both primary and secondary queries', async () => {
      const mockResult = {
        records: [{ get: jest.fn(() => 'test-data') }],
        summary: {}
      };

      mockSession.executeRead.mockImplementation((fn) => 
        fn({ run: jest.fn().mockResolvedValue(mockResult) })
      );

      const transformer = (result: QueryResult) => result.records.map(r => r.get('data'));
      const result = await repository.testExecuteRead(
        'MATCH (n) RETURN n',
        {},
        transformer,
        'MATCH (n) RETURN n' // legacy query
      ) as DualExecutionResult<string[]>;

      expect(result.primary).toEqual(['test-data']);
      expect(result.secondary).toEqual(['test-data']);
      expect(result.isValid).toBe(true);
      expect(result.differences).toEqual([]);
      expect(mockSession.executeRead).toHaveBeenCalledTimes(2);
    });

    it('should detect differences between primary and secondary results', async () => {
      let callCount = 0;
      mockSession.executeRead.mockImplementation((fn) => {
        callCount++;
        const data = callCount === 1 ? 'primary-data' : 'secondary-data';
        return fn({ 
          run: jest.fn().mockResolvedValue({
            records: [{ get: jest.fn(() => data) }],
            summary: {}
          })
        });
      });

      const transformer = (result: QueryResult) => result.records.map(r => r.get('data'));
      const result = await repository.testExecuteRead(
        'MATCH (n) RETURN n',
        {},
        transformer,
        'MATCH (n) RETURN n'
      ) as DualExecutionResult<string[]>;

      expect(result.primary).toEqual(['primary-data']);
      expect(result.secondary).toEqual(['secondary-data']);
      expect(result.isValid).toBe(false);
      expect(result.differences.length).toBeGreaterThan(0);
    });

    it('should handle secondary query failure gracefully', async () => {
      let callCount = 0;
      mockSession.executeRead.mockImplementation((fn) => {
        callCount++;
        if (callCount === 1) {
          return fn({ 
            run: jest.fn().mockResolvedValue({
              records: [{ get: jest.fn(() => 'primary-data') }],
              summary: {}
            })
          });
        } else {
          throw new Error('Secondary query failed');
        }
      });

      const transformer = (result: QueryResult) => result.records.map(r => r.get('data'));
      const result = await repository.testExecuteRead(
        'MATCH (n) RETURN n',
        {},
        transformer,
        'MATCH (n) RETURN n'
      ) as DualExecutionResult<string[]>;

      expect(result.primary).toEqual(['primary-data']);
      expect(result.secondary).toBeUndefined();
      expect(result.isValid).toBe(false);
      expect(result.differences).toContain('Secondary execution failed: Secondary query failed');
    });
  });

  describe('executeWrite', () => {
    it('should execute write query successfully', async () => {
      const mockResult = {
        records: [],
        summary: { counters: { nodesCreated: 1 } }
      };

      mockSession.executeWrite.mockImplementation((fn) => 
        fn({ run: jest.fn().mockResolvedValue(mockResult) })
      );

      const transformer = (result: QueryResult) => result.summary.counters;
      const result = await repository.testExecuteWrite(
        'CREATE (n:Test) RETURN n',
        {},
        transformer
      );

      expect(result).toEqual({ nodesCreated: 1 });
      expect(mockSession.executeWrite).toHaveBeenCalled();
    });
  });

  describe('executeInTransaction', () => {
    it('should execute operation in transaction', async () => {
      const mockTx = {
        run: jest.fn().mockResolvedValue({
          records: [{ get: jest.fn(() => 'transaction-result') }],
          summary: {}
        })
      };

      mockSession.executeRead.mockImplementation((fn) => fn(mockTx));

      const operation = async (tx: any) => {
        const result = await tx.run('MATCH (n) RETURN n');
        return result.records[0].get('data');
      };

      const result = await repository.testExecuteInTransaction(operation, 'read');

      expect(result).toBe('transaction-result');
      expect(mockSession.executeRead).toHaveBeenCalled();
    });

    it('should execute write transaction', async () => {
      const mockTx = {
        run: jest.fn().mockResolvedValue({
          records: [],
          summary: { counters: { nodesCreated: 1 } }
        })
      };

      mockSession.executeWrite.mockImplementation((fn) => fn(mockTx));

      const operation = async (tx: any) => {
        const result = await tx.run('CREATE (n:Test)');
        return result.summary.counters.nodesCreated;
      };

      const result = await repository.testExecuteInTransaction(operation, 'write');

      expect(result).toBe(1);
      expect(mockSession.executeWrite).toHaveBeenCalled();
    });
  });

  describe('healthCheck', () => {
    it('should return healthy status for fast queries', async () => {
      mockSession.executeRead.mockImplementation((fn) => 
        fn({ run: jest.fn().mockResolvedValue({ records: [], summary: {} }) })
      );

      const health = await repository.testHealthCheck();

      expect(health.status).toBe('healthy');
      expect(health.details.database).toBe('neo4j');
      expect(health.details.dualExecutionEnabled).toBe(false);
    });

    it('should return degraded status for slow queries', async () => {
      mockSession.executeRead.mockImplementation((fn) => {
        return new Promise(resolve => {
          setTimeout(() => {
            resolve(fn({ run: jest.fn().mockResolvedValue({ records: [], summary: {} }) }));
          }, 1500); // 1.5 seconds
        });
      });

      const health = await repository.testHealthCheck();

      expect(health.status).toBe('degraded');
    });

    it('should return unhealthy status for failed queries', async () => {
      mockSession.executeRead.mockRejectedValue(new Error('Database unavailable'));

      const health = await repository.testHealthCheck();

      expect(health.status).toBe('unhealthy');
      expect(health.details.error).toBe('Database unavailable');
    });
  });

  describe('timeout handling', () => {
    it('should timeout long-running queries', async () => {
      repository = new TestRepository(mockDriver as any, 'neo4j', {
        timeout: 100 // 100ms timeout
      });

      mockSession.executeRead.mockImplementation(() => {
        return new Promise(resolve => {
          setTimeout(resolve, 200); // 200ms delay
        });
      });

      const transformer = (result: QueryResult) => result.records;
      
      await expect(
        repository.testExecuteRead('MATCH (n) RETURN n', {}, transformer)
      ).rejects.toThrow('Query timed out after 100ms');
    });
  });

  describe('validation', () => {
    it('should validate identical results as valid', async () => {
      repository = new TestRepository(mockDriver as any, 'neo4j', {
        enableDualExecution: true,
        enableValidation: true
      });

      const mockResult = {
        records: [{ get: jest.fn(() => ({ id: 1, name: 'test' })) }],
        summary: {}
      };

      mockSession.executeRead.mockImplementation((fn) => 
        fn({ run: jest.fn().mockResolvedValue(mockResult) })
      );

      const transformer = (result: QueryResult) => result.records.map(r => r.get('data'));
      const result = await repository.testExecuteRead(
        'MATCH (n) RETURN n',
        {},
        transformer,
        'MATCH (n) RETURN n'
      ) as DualExecutionResult<any[]>;

      expect(result.isValid).toBe(true);
      expect(result.differences).toEqual([]);
    });

    it('should detect array length differences', async () => {
      repository = new TestRepository(mockDriver as any, 'neo4j', {
        enableDualExecution: true,
        enableValidation: true
      });

      let callCount = 0;
      mockSession.executeRead.mockImplementation((fn) => {
        callCount++;
        const records = callCount === 1 
          ? [{ get: jest.fn(() => 'item1') }, { get: jest.fn(() => 'item2') }]
          : [{ get: jest.fn(() => 'item1') }];
        
        return fn({ 
          run: jest.fn().mockResolvedValue({
            records,
            summary: {}
          })
        });
      });

      const transformer = (result: QueryResult) => result.records.map(r => r.get('data'));
      const result = await repository.testExecuteRead(
        'MATCH (n) RETURN n',
        {},
        transformer,
        'MATCH (n) RETURN n'
      ) as DualExecutionResult<string[]>;

      expect(result.isValid).toBe(false);
      expect(result.differences).toContain('Array length differs: primary=2, secondary=1');
    });
  });
});
