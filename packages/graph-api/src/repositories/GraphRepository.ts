/**
 * Graph Repository
 * 
 * Repository for graph data operations with dual-execution validation.
 * Provides abstraction over Neo4j graph queries with legacy compatibility.
 */

import { Driver } from 'neo4j-driver';
import { BaseRepository, QueryResult, DualExecutionResult, RepositoryConfig } from './BaseRepository';
import { GraphData, GraphNode, GraphRelationship } from '@kg-visualizer/shared';

export interface GraphFilters {
  nodeLabels?: string[];
  relationshipTypes?: string[];
  properties?: Array<{
    key: string;
    operator: 'equals' | 'contains' | 'startsWith' | 'endsWith';
    value: any;
  }>;
  dateRange?: {
    start: string;
    end: string;
    property: string;
  };
}

export interface DatabaseMetadata {
  nodeLabels: Array<{
    label: string;
    count: number;
    properties: string[];
  }>;
  relationshipTypes: Array<{
    type: string;
    count: number;
    properties: string[];
  }>;
  indexes: Array<{
    name: string;
    type: string;
    labels: string[];
    properties: string[];
  }>;
  constraints: Array<{
    name: string;
    type: string;
    label: string;
    properties: string[];
  }>;
  statistics: {
    totalNodes: number;
    totalRelationships: number;
    databaseSize: string;
    lastUpdated: string;
  };
}

export class GraphRepository extends BaseRepository {
  constructor(driver: Driver, database: string = 'neo4j', config?: RepositoryConfig) {
    super(driver, database, config);
  }

  /**
   * Get initial graph data for visualization
   */
  async getInitialGraph(limit: number = 100): Promise<GraphData | DualExecutionResult<GraphData>> {
    const newQuery = `
      MATCH (n)-[r]->(m)
      RETURN n, r, m
      LIMIT $limit
    `;

    const legacyQuery = `
      MATCH (n)-[r]->(m)
      RETURN n, r, m
      LIMIT $limit
    `;

    return this.executeRead(
      newQuery,
      { limit },
      this.transformToGraphData.bind(this),
      legacyQuery,
      { limit }
    );
  }

  /**
   * Search for nodes by term
   */
  async searchNodes(term: string, limit: number = 20): Promise<GraphNode[] | DualExecutionResult<GraphNode[]>> {
    const newQuery = `
      MATCH (n)
      WHERE toLower(n.name) CONTAINS toLower($term)
         OR toLower(n.title) CONTAINS toLower($term)
         OR toLower(n.description) CONTAINS toLower($term)
         OR any(label IN labels(n) WHERE toLower(label) CONTAINS toLower($term))
      RETURN n
      ORDER BY 
        CASE 
          WHEN toLower(n.name) = toLower($term) THEN 1
          WHEN toLower(n.name) STARTS WITH toLower($term) THEN 2
          ELSE 3
        END,
        n.name
      LIMIT $limit
    `;

    const legacyQuery = `
      MATCH (n)
      WHERE toLower(toString(n.name)) CONTAINS toLower($term)
         OR toLower(toString(n.title)) CONTAINS toLower($term)
         OR any(label IN labels(n) WHERE toLower(label) CONTAINS toLower($term))
      RETURN n
      ORDER BY n.name
      LIMIT $limit
    `;

    return this.executeRead(
      newQuery,
      { term, limit },
      this.transformToNodes.bind(this),
      legacyQuery,
      { term, limit }
    );
  }

  /**
   * Expand a node to show connected nodes
   */
  async expandNode(nodeId: string, limit: number = 50): Promise<GraphData | DualExecutionResult<GraphData>> {
    const newQuery = `
      MATCH (n)-[r]-(m)
      WHERE id(n) = toInteger($nodeId) 
         OR n.id = $nodeId 
         OR n.name = $nodeId
      RETURN n, r, m
      LIMIT $limit
    `;

    const legacyQuery = `
      MATCH (n)-[r]-(m)
      WHERE (id(n) = toInteger($nodeId) OR n.name = $nodeId OR n.id = $nodeId)
      RETURN n, r, m
      LIMIT $limit
    `;

    return this.executeRead(
      newQuery,
      { nodeId, limit },
      this.transformToGraphData.bind(this),
      legacyQuery,
      { nodeId, limit }
    );
  }

  /**
   * Filter graph data based on criteria
   */
  async filterGraph(filters: GraphFilters): Promise<GraphData | DualExecutionResult<GraphData>> {
    const { query: newQuery, parameters } = this.buildFilterQuery(filters, 'new');
    const { query: legacyQuery } = this.buildFilterQuery(filters, 'legacy');

    return this.executeRead(
      newQuery,
      parameters,
      this.transformToGraphData.bind(this),
      legacyQuery,
      parameters
    );
  }

  /**
   * Execute custom Cypher query
   */
  async executeCustomQuery(
    query: string, 
    parameters: Record<string, any> = {}
  ): Promise<any[] | DualExecutionResult<any[]>> {
    // For custom queries, we don't have a legacy equivalent
    // So we just execute the new query
    return this.executeRead(
      query,
      parameters,
      this.transformToRecords.bind(this)
    );
  }

  /**
   * Get database metadata and statistics
   */
  async getMetadata(): Promise<DatabaseMetadata | DualExecutionResult<DatabaseMetadata>> {
    // Metadata queries are typically new functionality
    // We'll implement without dual execution for now
    const result = await this.executeInTransaction(async (tx) => {
      const [nodeLabels, relationshipTypes, indexes, constraints, statistics] = await Promise.all([
        this.getNodeLabels(tx),
        this.getRelationshipTypes(tx),
        this.getIndexes(tx),
        this.getConstraints(tx),
        this.getStatistics(tx)
      ]);

      return {
        nodeLabels,
        relationshipTypes,
        indexes,
        constraints,
        statistics
      };
    });

    return result;
  }

  /**
   * Transform Neo4j records to GraphData format
   */
  private transformToGraphData(result: QueryResult): GraphData {
    const nodesMap = new Map<string, GraphNode>();
    const relationships: GraphRelationship[] = [];

    for (const record of result.records) {
      // Process nodes
      ['n', 'm'].forEach(key => {
        if (record.has && record.has(key)) {
          const node = this.transformNode(record.get(key));
          nodesMap.set(node.id, node);
        }
      });

      // Process relationships
      if (record.has && record.has('r')) {
        const relationship = this.transformRelationship(record.get('r'));
        relationships.push(relationship);
      }
    }

    return {
      nodes: Array.from(nodesMap.values()),
      relationships
    };
  }

  /**
   * Transform Neo4j records to GraphNode array
   */
  private transformToNodes(result: QueryResult): GraphNode[] {
    return result.records.map(record => this.transformNode(record.get('n')));
  }

  /**
   * Transform Neo4j records to generic records
   */
  private transformToRecords(result: QueryResult): any[] {
    return result.records.map(record => {
      const obj: Record<string, any> = {};
      record.keys.forEach((key: string) => {
        obj[key] = this.transformValue(record.get(key));
      });
      return obj;
    });
  }

  /**
   * Transform Neo4j node to GraphNode format
   */
  private transformNode(node: any): GraphNode {
    const properties = node.properties || {};
    const labels = node.labels || [];
    
    return {
      id: node.identity?.toString() || node.id?.toString() || properties.id?.toString(),
      label: properties.name || properties.title || properties.id || 'Unnamed',
      type: labels[0] || 'Unknown',
      properties: this.transformProperties(properties),
      metadata: {
        labels,
        identity: node.identity?.toString()
      }
    };
  }

  /**
   * Transform Neo4j relationship to GraphRelationship format
   */
  private transformRelationship(relationship: any): GraphRelationship {
    const properties = relationship.properties || {};
    
    return {
      id: relationship.identity?.toString() || `${relationship.start}-${relationship.end}`,
      source: relationship.start?.toString(),
      target: relationship.end?.toString(),
      type: relationship.type || 'RELATED',
      properties: this.transformProperties(properties),
      metadata: {
        identity: relationship.identity?.toString()
      }
    };
  }

  /**
   * Transform Neo4j properties
   */
  private transformProperties(properties: any): Record<string, any> {
    const transformed: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(properties)) {
      transformed[key] = this.transformValue(value);
    }
    
    return transformed;
  }

  /**
   * Transform Neo4j values to JavaScript values
   */
  private transformValue(value: any): any {
    if (value === null || value === undefined) {
      return value;
    }

    // Handle Neo4j integers
    if (value.toNumber && typeof value.toNumber === 'function') {
      return value.toNumber();
    }

    // Handle Neo4j dates
    if (value.toString && typeof value.toString === 'function') {
      const stringValue = value.toString();
      if (stringValue.includes('T') && stringValue.includes('Z')) {
        try {
          return new Date(stringValue).toISOString();
        } catch {
          return stringValue;
        }
      }
    }

    // Handle arrays
    if (Array.isArray(value)) {
      return value.map(item => this.transformValue(item));
    }

    // Handle objects
    if (typeof value === 'object') {
      const transformed: Record<string, any> = {};
      for (const [key, val] of Object.entries(value)) {
        transformed[key] = this.transformValue(val);
      }
      return transformed;
    }

    return value;
  }

  /**
   * Build filter query from filters
   */
  private buildFilterQuery(filters: GraphFilters, version: 'new' | 'legacy'): { 
    query: string; 
    parameters: Record<string, any> 
  } {
    let query = 'MATCH (n)-[r]->(m) WHERE 1=1';
    const parameters: Record<string, any> = {};

    if (filters.nodeLabels && filters.nodeLabels.length > 0) {
      query += ' AND (labels(n)[0] IN $nodeLabels OR labels(m)[0] IN $nodeLabels)';
      parameters.nodeLabels = filters.nodeLabels;
    }

    if (filters.relationshipTypes && filters.relationshipTypes.length > 0) {
      query += ' AND type(r) IN $relationshipTypes';
      parameters.relationshipTypes = filters.relationshipTypes;
    }

    if (filters.properties && filters.properties.length > 0) {
      filters.properties.forEach((prop, index) => {
        const paramKey = `propValue${index}`;
        const nodeCondition = version === 'new' 
          ? `n.${prop.key}` 
          : `toString(n.${prop.key})`;
        const targetCondition = version === 'new' 
          ? `m.${prop.key}` 
          : `toString(m.${prop.key})`;

        switch (prop.operator) {
          case 'equals':
            query += ` AND (${nodeCondition} = $${paramKey} OR ${targetCondition} = $${paramKey})`;
            break;
          case 'contains':
            query += ` AND (toLower(${nodeCondition}) CONTAINS toLower($${paramKey}) OR toLower(${targetCondition}) CONTAINS toLower($${paramKey}))`;
            break;
          case 'startsWith':
            query += ` AND (toLower(${nodeCondition}) STARTS WITH toLower($${paramKey}) OR toLower(${targetCondition}) STARTS WITH toLower($${paramKey}))`;
            break;
          case 'endsWith':
            query += ` AND (toLower(${nodeCondition}) ENDS WITH toLower($${paramKey}) OR toLower(${targetCondition}) ENDS WITH toLower($${paramKey}))`;
            break;
        }
        parameters[paramKey] = prop.value;
      });
    }

    if (filters.dateRange) {
      query += ` AND (n.${filters.dateRange.property} >= datetime($startDate) AND n.${filters.dateRange.property} <= datetime($endDate))`;
      parameters.startDate = filters.dateRange.start;
      parameters.endDate = filters.dateRange.end;
    }

    query += ' RETURN n, r, m LIMIT 100';
    return { query, parameters };
  }

  // Helper methods for metadata
  private async getNodeLabels(tx: any) {
    const result = await tx.run('CALL db.labels()');
    return result.records.map((record: any) => ({
      label: record.get('label'),
      count: 0, // Would need additional query to get counts
      properties: []
    }));
  }

  private async getRelationshipTypes(tx: any) {
    const result = await tx.run('CALL db.relationshipTypes()');
    return result.records.map((record: any) => ({
      type: record.get('relationshipType'),
      count: 0,
      properties: []
    }));
  }

  private async getIndexes(tx: any) {
    try {
      const result = await tx.run('SHOW INDEXES');
      return result.records.map((record: any) => ({
        name: record.get('name'),
        type: record.get('type'),
        labels: record.get('labelsOrTypes') || [],
        properties: record.get('properties') || []
      }));
    } catch {
      return [];
    }
  }

  private async getConstraints(tx: any) {
    try {
      const result = await tx.run('SHOW CONSTRAINTS');
      return result.records.map((record: any) => ({
        name: record.get('name'),
        type: record.get('type'),
        label: record.get('labelsOrTypes')?.[0] || '',
        properties: record.get('properties') || []
      }));
    } catch {
      return [];
    }
  }

  private async getStatistics(tx: any) {
    const nodeCountResult = await tx.run('MATCH (n) RETURN count(n) as count');
    const relCountResult = await tx.run('MATCH ()-[r]->() RETURN count(r) as count');
    
    return {
      totalNodes: nodeCountResult.records[0]?.get('count')?.toNumber() || 0,
      totalRelationships: relCountResult.records[0]?.get('count')?.toNumber() || 0,
      databaseSize: 'Unknown',
      lastUpdated: new Date().toISOString()
    };
  }
}
