#!/bin/bash

# Docker Development Environment Management Script
# Usage: ./scripts/docker-dev.sh [command]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.development.yml"
PROJECT_NAME="kg-visualizer-dev"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Build development images
build() {
    log_info "Building development images..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME build --no-cache
    log_success "Development images built successfully"
}

# Start development environment
start() {
    log_info "Starting development environment..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d
    
    log_info "Waiting for services to be healthy..."
    sleep 10
    
    # Check service health
    check_health
    
    log_success "Development environment started successfully"
    log_info "Services available at:"
    echo "  - Web UI: http://localhost:3000"
    echo "  - Graph API: http://localhost:3002"
    echo "  - Chat Service: http://localhost:8000"
    echo "  - Neo4j Browser: http://localhost:7474"
    echo "  - Redis: localhost:6379"
    echo "  - MailHog: http://localhost:8025"
    echo "  - Adminer: http://localhost:8080"
}

# Stop development environment
stop() {
    log_info "Stopping development environment..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down
    log_success "Development environment stopped"
}

# Restart development environment
restart() {
    log_info "Restarting development environment..."
    stop
    start
}

# View logs
logs() {
    local service=${1:-}
    if [ -n "$service" ]; then
        log_info "Showing logs for service: $service"
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f "$service"
    else
        log_info "Showing logs for all services"
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f
    fi
}

# Check service health
check_health() {
    log_info "Checking service health..."
    
    # Check Neo4j
    if curl -f http://localhost:7474 > /dev/null 2>&1; then
        log_success "Neo4j is healthy"
    else
        log_warning "Neo4j is not responding"
    fi
    
    # Check Redis
    if redis-cli -h localhost ping > /dev/null 2>&1; then
        log_success "Redis is healthy"
    else
        log_warning "Redis is not responding"
    fi
    
    # Check if app container is running
    if docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME ps app | grep -q "Up"; then
        log_success "Application container is running"
    else
        log_warning "Application container is not running"
    fi
}

# Clean up development environment
clean() {
    log_info "Cleaning up development environment..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down -v --remove-orphans
    docker system prune -f
    log_success "Development environment cleaned up"
}

# Reset development environment
reset() {
    log_warning "This will remove all development data. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Resetting development environment..."
        clean
        build
        start
        log_success "Development environment reset successfully"
    else
        log_info "Reset cancelled"
    fi
}

# Execute command in app container
exec_app() {
    local cmd=${1:-bash}
    log_info "Executing command in app container: $cmd"
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME exec app $cmd
}

# Show status
status() {
    log_info "Development environment status:"
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME ps
}

# Show help
show_help() {
    echo "Docker Development Environment Management"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  build     Build development images"
    echo "  start     Start development environment"
    echo "  stop      Stop development environment"
    echo "  restart   Restart development environment"
    echo "  logs      Show logs (optional: specify service name)"
    echo "  health    Check service health"
    echo "  clean     Clean up development environment"
    echo "  reset     Reset development environment (removes all data)"
    echo "  exec      Execute command in app container"
    echo "  status    Show service status"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs app"
    echo "  $0 exec npm test"
}

# Main script logic
main() {
    check_docker
    
    case "${1:-help}" in
        build)
            build
            ;;
        start)
            start
            ;;
        stop)
            stop
            ;;
        restart)
            restart
            ;;
        logs)
            logs "$2"
            ;;
        health)
            check_health
            ;;
        clean)
            clean
            ;;
        reset)
            reset
            ;;
        exec)
            shift
            exec_app "$*"
            ;;
        status)
            status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
