# Emergency Rollback Procedures

## 🚨 Quick Emergency Rollback

**If the system is experiencing critical issues, execute immediately:**

```bash
./tools/scripts/emergency-rollback.sh "Critical system failure"
```

This will:
1. ✅ Disable all migration features
2. ✅ Restart services in legacy mode  
3. ✅ Verify system health
4. ✅ Create incident report
5. ✅ Send notifications

## 📋 Emergency Response Checklist

### Immediate Actions (0-5 minutes)
- [ ] **Assess Severity**: Determine if rollback is necessary
- [ ] **Execute Rollback**: Run emergency rollback script
- [ ] **Verify System**: Confirm system is responding
- [ ] **Notify Team**: Alert relevant stakeholders

### Short-term Actions (5-30 minutes)
- [ ] **Investigate Root Cause**: Review logs and metrics
- [ ] **Document Incident**: Record timeline and impact
- [ ] **Communicate Status**: Update stakeholders on resolution
- [ ] **Monitor Stability**: Ensure system remains stable

### Follow-up Actions (30+ minutes)
- [ ] **Post-Incident Review**: Conduct thorough analysis
- [ ] **Implement Fixes**: Address root cause
- [ ] **Update Procedures**: Improve rollback processes
- [ ] **Plan Re-deployment**: Strategy for re-enabling features

## 🔧 Manual Rollback Steps

If the automated script fails, follow these manual steps:

### 1. Disable Feature Flags
```bash
# Create emergency environment file
cat > .env.emergency <<EOF
FEATURE_FLAG_NEW_CONTROLLER_LAYER=false
FEATURE_FLAG_NEW_SERVICE_LAYER=false
FEATURE_FLAG_NEW_REPOSITORY_PATTERN=false
FEATURE_FLAG_NEW_MIDDLEWARE_STACK=false
FEATURE_FLAG_NEW_CHAT_SERVICE=false
FEATURE_FLAG_TRAFFIC_PERCENTAGE_NEW_API=0
FEATURE_FLAG_CIRCUIT_BREAKER_ENABLED=true
FEATURE_FLAG_AUTOMATIC_ROLLBACK=true
EOF

# Backup current config and apply emergency settings
cp .env .env.backup.$(date +%Y%m%d-%H%M%S)
cat .env.emergency >> .env
```

### 2. Restart Services
```bash
# Stop current processes
pkill -f "node.*server.js"
pkill -f "npm.*start"

# Wait for processes to stop
sleep 5

# Start backend
cd 360t-kg-api
npm start &
cd ..

# Verify startup
sleep 10
curl http://localhost:3002/api/health
```

### 3. Verify Rollback
```bash
# Check feature flags are disabled
curl -s http://localhost:3002/api/feature-flags | jq '.migrationPhase'

# Should show:
# {
#   "backend": { "percentage": 0 },
#   "traffic": { "api": 0 }
# }
```

## 🎯 Rollback Triggers

### Automatic Rollback Conditions
The monitoring system will automatically trigger rollback if:

- **Performance Degradation**: API response time >3x baseline
- **High Error Rate**: >5% of requests failing
- **System Unresponsive**: Health checks failing
- **Memory/Disk Issues**: Resource usage >95%

### Manual Rollback Scenarios
Consider manual rollback for:

- **User Complaints**: Significant user-reported issues
- **Data Inconsistency**: Database integrity problems
- **Security Concerns**: Potential security vulnerabilities
- **Business Impact**: Revenue or operations affected

## 📊 Monitoring During Rollback

### Key Metrics to Watch
1. **API Response Time**: Should return to baseline
2. **Error Rates**: Should drop to normal levels
3. **Database Performance**: Query times stable
4. **User Traffic**: Normal patterns resumed

### Monitoring Tools
- **Dashboard**: http://localhost:3002/monitoring/migration-dashboard.html
- **Logs**: `tail -f tools/monitoring/migration-monitor.log`
- **Alerts**: `cat tools/monitoring/alerts.json`

## 🔄 Recovery Process

### After Successful Rollback

1. **Stabilization Period** (24-48 hours)
   - Monitor system closely
   - Ensure no residual issues
   - Collect performance data

2. **Root Cause Analysis**
   - Review logs and metrics
   - Identify failure points
   - Document lessons learned

3. **Fix Development**
   - Address identified issues
   - Test fixes thoroughly
   - Update migration procedures

4. **Gradual Re-deployment**
   - Start with 5% traffic
   - Monitor closely
   - Increase gradually

### Communication Templates

#### Initial Incident Notification
```
SUBJECT: [URGENT] Knowledge Graph System Rollback Executed

Team,

An emergency rollback has been executed for the Knowledge Graph system.

Reason: [REASON]
Time: [TIMESTAMP]
Impact: [DESCRIPTION]
Status: System restored to legacy mode

Next Steps:
- Investigating root cause
- Monitoring system stability
- Will provide updates every 30 minutes

[YOUR NAME]
```

#### Resolution Notification
```
SUBJECT: [RESOLVED] Knowledge Graph System Stable

Team,

The Knowledge Graph system has been successfully rolled back and is now stable.

Summary:
- Rollback completed at: [TIME]
- System verified healthy: [TIME]
- Root cause identified: [DESCRIPTION]
- Next deployment planned: [DATE]

Thank you for your patience.

[YOUR NAME]
```

## 🛠️ Testing Rollback Procedures

### Regular Testing Schedule
- **Weekly**: Test rollback script (dry run)
- **Monthly**: Full rollback simulation
- **Quarterly**: Team drill with all stakeholders

### Test Commands
```bash
# Dry run test
./tools/scripts/emergency-rollback.sh test

# Monitor status
node tools/monitoring/monitor.cjs status

# Check alerts
node tools/monitoring/monitor.cjs alerts
```

## 📞 Emergency Contacts

### Primary Contacts
- **System Administrator**: [NAME] - [PHONE] - [EMAIL]
- **Lead Developer**: [NAME] - [PHONE] - [EMAIL]
- **DevOps Engineer**: [NAME] - [PHONE] - [EMAIL]

### Escalation Path
1. **Level 1**: Development Team
2. **Level 2**: Technical Lead
3. **Level 3**: Engineering Manager
4. **Level 4**: CTO/VP Engineering

## 📝 Post-Incident Documentation

After each rollback, document:

1. **Timeline**: Detailed sequence of events
2. **Impact**: User and business impact assessment
3. **Root Cause**: Technical analysis of failure
4. **Response**: Effectiveness of rollback procedures
5. **Improvements**: Process and technical improvements needed

Store documentation in: `docs/incidents/rollback-YYYY-MM-DD.md`
