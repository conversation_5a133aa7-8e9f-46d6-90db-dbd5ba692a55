# Graphiti Standard Search Migration Plan

## Implementation Summary

This document outlines the complete migration from our custom search implementation to Graphiti's standard search framework with admin configuration capabilities.

## ✅ Completed Components

### 1. Backend Configuration Management (`360t-kg-api/routes/configRoutes.js`)
- **16 Search Recipes**: All Graphiti search recipes implemented with metadata
- **3 LLM Providers**: Ollama, Google Gemini, OpenAI support
- **Configuration Persistence**: JSON-based config storage
- **API Endpoints**: Complete REST API for configuration management
- **Validation**: Input validation and error handling

### 2. Graphiti Standard Search Implementation (`graphiti_standard_search.py`)
- **Standard Search Integration**: Uses `graphiti._search()` method
- **All 16 Search Recipes**: Complete recipe mapping and support
- **Multi-LLM Support**: Ollama, Google Gemini, OpenAI providers
- **Group ID Filtering**: Database isolation maintained
- **Performance Monitoring**: Timing integration preserved
- **Streaming Support**: Compatible with Node.js streaming architecture

### 3. Frontend Admin Interface (`360t-kg-api/public/admin-config.html` & `admin-config.js`)
- **Search Recipe Selector**: Categorized dropdown with 16 options
- **LLM Provider Management**: Dynamic provider/model selection
- **Real-time Configuration**: Live updates without server restart
- **Status Monitoring**: Connection status and health checks
- **Performance Metrics**: Live performance feedback
- **Responsive Design**: Mobile-friendly interface

### 4. System Integration
- **Server Integration**: Routes added to `server.js`
- **Chat Routes Updated**: Migration to new search script
- **Static File Serving**: Admin interface accessible at `/admin`
- **Backward Compatibility**: Existing functionality preserved

## 🔧 Technical Architecture

### Search Recipe Categories
1. **Combined Search (3 options)**:
   - COMBINED_HYBRID_SEARCH_RRF
   - COMBINED_HYBRID_SEARCH_MMR
   - COMBINED_HYBRID_SEARCH_CROSS_ENCODER

2. **Edge-Focused Search (5 options)**:
   - EDGE_HYBRID_SEARCH_RRF
   - EDGE_HYBRID_SEARCH_MMR
   - EDGE_HYBRID_SEARCH_NODE_DISTANCE
   - EDGE_HYBRID_SEARCH_EPISODE_MENTIONS
   - EDGE_HYBRID_SEARCH_CROSS_ENCODER

3. **Node-Focused Search (5 options)**:
   - NODE_HYBRID_SEARCH_RRF
   - NODE_HYBRID_SEARCH_MMR
   - NODE_HYBRID_SEARCH_NODE_DISTANCE
   - NODE_HYBRID_SEARCH_EPISODE_MENTIONS
   - NODE_HYBRID_SEARCH_CROSS_ENCODER

4. **Community Search (3 options)**:
   - COMMUNITY_HYBRID_SEARCH_RRF
   - COMMUNITY_HYBRID_SEARCH_MMR
   - COMMUNITY_HYBRID_SEARCH_CROSS_ENCODER

### LLM Provider Support
1. **Ollama (Local)**: gemma3:12b, deepseek-r1:7b, deepseek-r1:32b
2. **Google Gemini**: gemini-2.0-flash-exp, gemini-1.5-pro
3. **OpenAI**: gpt-4o-mini, gpt-4o, gpt-3.5-turbo

## 📋 Testing Strategy

### Phase 1: Unit Testing
- [ ] Test each search recipe individually
- [ ] Validate LLM provider switching
- [ ] Test configuration persistence
- [ ] Verify API endpoint functionality

### Phase 2: Integration Testing
- [ ] Test admin interface with backend
- [ ] Validate streaming functionality
- [ ] Test performance monitoring
- [ ] Verify database isolation (group_id filtering)

### Phase 3: End-to-End Testing
- [ ] Test complete chat workflow with new search
- [ ] Validate all 16 search recipes in production
- [ ] Test provider switching without restart
- [ ] Performance comparison with old system

### Phase 4: Load Testing
- [ ] Concurrent user testing
- [ ] Search performance under load
- [ ] Provider failover testing
- [ ] Memory and resource usage validation

## 🚀 Deployment Plan

### Step 1: Environment Setup
```bash
# Install required Python dependencies
pip install graphiti-core

# Ensure environment variables are set
export NEO4J_URI="bolt://localhost:7687"
export NEO4J_USER="neo4j"
export NEO4J_PASSWORD="your_password"
export OLLAMA_URL="http://localhost:11434/v1"
export GOOGLE_API_KEY="your_google_key"  # Optional
export OPENAI_API_KEY="your_openai_key"  # Optional
```

### Step 2: Server Restart
```bash
cd 360t-kg-api
npm restart
```

### Step 3: Admin Configuration
1. Navigate to `http://localhost:3002/admin/admin-config.html`
2. Configure desired search recipe
3. Set LLM provider and model
4. Test configuration

### Step 4: Validation
1. Test chat functionality
2. Verify search results quality
3. Monitor performance metrics
4. Validate streaming responses

## 🔄 Rollback Plan

### Immediate Rollback (if critical issues)
1. Revert `chatRoutes.js` to use `graphiti_hybrid_search.py`
2. Remove config routes from `server.js`
3. Restart server

### Files to Backup Before Migration
- `360t-kg-api/routes/chatRoutes.js`
- `360t-kg-api/server.js`
- `graphiti_hybrid_search.py`

### Rollback Commands
```bash
# Backup current files
cp 360t-kg-api/routes/chatRoutes.js 360t-kg-api/routes/chatRoutes.js.backup
cp 360t-kg-api/server.js 360t-kg-api/server.js.backup

# If rollback needed
git checkout HEAD~1 -- 360t-kg-api/routes/chatRoutes.js
git checkout HEAD~1 -- 360t-kg-api/server.js
npm restart
```

## 📊 Success Metrics

### Performance Metrics
- **Search Latency**: < 2 seconds for standard queries
- **Streaming Response**: First token within 500ms
- **Memory Usage**: No significant increase from baseline
- **Error Rate**: < 1% for search operations

### Quality Metrics
- **Search Relevance**: Improved semantic matching
- **Result Diversity**: Better result variety with MMR
- **Context Quality**: Richer context from hybrid search
- **User Satisfaction**: Subjective improvement in answers

### Operational Metrics
- **Configuration Changes**: Real-time without restart
- **Provider Switching**: < 5 seconds switchover
- **Admin Interface**: < 2 seconds load time
- **System Stability**: 99.9% uptime maintained

## 🔍 Monitoring and Alerting

### Key Metrics to Monitor
1. **Search Performance**: Query execution time
2. **LLM Response Time**: Token generation speed
3. **Error Rates**: Failed searches and LLM calls
4. **Configuration Changes**: Admin interface usage
5. **Resource Usage**: CPU, memory, Neo4j connections

### Alert Conditions
- Search latency > 5 seconds
- LLM provider failures > 5% error rate
- Memory usage > 80% of available
- Neo4j connection failures

## 📝 Documentation Updates

### User Documentation
- [ ] Update chat interface usage guide
- [ ] Create admin configuration manual
- [ ] Document search recipe differences
- [ ] LLM provider comparison guide

### Developer Documentation
- [ ] API endpoint documentation
- [ ] Configuration schema reference
- [ ] Troubleshooting guide
- [ ] Performance tuning guide

## 🎯 Next Steps

1. **Execute Testing Plan**: Run comprehensive tests
2. **Performance Baseline**: Establish current metrics
3. **Gradual Rollout**: Start with single search recipe
4. **Monitor and Optimize**: Continuous improvement
5. **User Training**: Admin interface training
6. **Documentation**: Complete user and developer docs

## 🔧 Configuration Examples

### Default Configuration
```json
{
  "searchRecipe": "COMBINED_HYBRID_SEARCH_RRF",
  "llmProvider": "ollama",
  "llmModel": "gemma3:12b",
  "resultsLimit": 10
}
```

### High-Performance Configuration
```json
{
  "searchRecipe": "EDGE_HYBRID_SEARCH_RRF",
  "llmProvider": "ollama",
  "llmModel": "gemma3:12b",
  "resultsLimit": 6
}
```

### Cloud Provider Configuration
```json
{
  "searchRecipe": "COMBINED_HYBRID_SEARCH_CROSS_ENCODER",
  "llmProvider": "google",
  "llmModel": "gemini-2.0-flash-exp",
  "resultsLimit": 10
}
```

This migration represents a significant upgrade from our custom implementation to Graphiti's proven, enterprise-grade search framework while maintaining all existing functionality and adding powerful new configuration capabilities.
