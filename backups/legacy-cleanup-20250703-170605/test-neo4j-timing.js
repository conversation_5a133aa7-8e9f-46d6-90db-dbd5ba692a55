#!/usr/bin/env node

/**
 * Test script to verify Neo4j timing functionality
 * Run with: node test-neo4j-timing.js
 */

require('dotenv').config();
const neo4j = require('neo4j-driver');
const Neo4jService = require('./services/neo4jService');

async function testNeo4jTiming() {
    console.log('🔍 Testing Neo4j Timing Implementation...\n');

    // Initialize Neo4j driver
    const driver = neo4j.driver(
        process.env.NEO4J_URI || 'bolt://localhost:7687',
        neo4j.auth.basic(
            process.env.NEO4J_USERNAME || 'neo4j',
            process.env.NEO4J_PASSWORD || 'password'
        )
    );

    const neo4jService = new Neo4jService(driver, process.env.NEO4J_DATABASE || 'neo4j');

    try {
        console.log('1. Testing connectivity...');
        const connectionTest = await neo4jService.testConnection('test_operation_1');
        console.log('   ✅ Connection test:', connectionTest.connected ? 'SUCCESS' : 'FAILED');
        console.log('   ⏱️  Connection timing:', connectionTest.timing.totalDuration + 'ms\n');

        console.log('2. Testing database statistics query...');
        const dbStats = await neo4jService.getDatabaseStats('test_operation_2');
        console.log('   📊 Node count:', dbStats.stats.nodeCount);
        console.log('   📊 Relationship count:', dbStats.stats.relationshipCount);
        console.log('   ⏱️  Query timing:', dbStats.timing.totalDuration + 'ms');
        console.log('   🔍 Queries executed:', dbStats.timing.queryCount + '\n');

        console.log('3. Testing simple read query...');
        const result = await neo4jService.executeRead(
            'MATCH (n) RETURN count(n) as total LIMIT 1',
            {},
            'test_operation_3'
        );
        console.log('   📊 Query result:', result.records[0]?.get('total')?.toNumber() || 0);

        console.log('\n4. Testing Entity search (if available)...');
        try {
            const entityResult = await neo4jService.executeRead(
                'MATCH (e:Entity) RETURN e.name as name LIMIT 5',
                {},
                'test_operation_4'
            );
            console.log('   📊 Entity count found:', entityResult.records.length);
            if (entityResult.records.length > 0) {
                console.log('   📊 Sample entities:', entityResult.records.map(r => r.get('name')).slice(0, 3));
            }
        } catch (error) {
            console.log('   ⚠️  No Entity nodes found (this is normal if using different schema)');
        }

        console.log('\n5. Performance statistics summary:');
        const perfStats = neo4jService.getPerformanceStats();
        console.log('   📈 Total queries executed:', perfStats.totalQueries);
        console.log('   📈 Average query time:', perfStats.averageQueryTime + 'ms');
        console.log('   📈 Recent queries:', perfStats.recentQueries.length);
        console.log('   📈 Slow queries detected:', perfStats.slowQueriesCount);

        if (perfStats.recentQueries.length > 0) {
            console.log('\n   📋 Recent query details:');
            perfStats.recentQueries.forEach((query, index) => {
                console.log(`      ${index + 1}. Operation: ${query.operationId}`);
                console.log(`         Duration: ${query.totalDuration}ms`);
                console.log(`         Queries: ${query.queryCount}`);
            });
        }

        console.log('\n✅ Neo4j timing test completed successfully!');
        console.log('🎯 The timing system is ready to track your chat performance.');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('   Stack:', error.stack);
    } finally {
        await driver.close();
        console.log('\n🔌 Neo4j connection closed.');
    }
}

// Run the test
if (require.main === module) {
    testNeo4jTiming().catch(console.error);
}

module.exports = testNeo4jTiming;
