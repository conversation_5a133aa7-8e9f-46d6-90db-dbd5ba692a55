{"model": "deepseek-r1:8b", "host": "http://localhost:11434", "options": {"temperature": 0.7, "top_p": 0.9, "top_k": 40, "num_predict": 4096, "num_ctx": 128000, "num_batch": 512, "num_gqa": 8, "num_gpu": 1, "num_thread": 0, "repeat_last_n": 64, "repeat_penalty": 1.1, "seed": -1, "stop": ["</s>", "[INST]", "[/INST]"], "tfs_z": 1.0, "mirostat": 0, "mirostat_eta": 0.1, "mirostat_tau": 5.0}, "keep_alive": "5m", "stream": true, "profiles": {"creative": {"temperature": 0.9, "top_p": 0.95, "top_k": 50, "repeat_penalty": 1.05}, "precise": {"temperature": 0.3, "top_p": 0.8, "top_k": 20, "repeat_penalty": 1.15}, "balanced": {"temperature": 0.7, "top_p": 0.9, "top_k": 40, "repeat_penalty": 1.1}, "reasoning": {"temperature": 0.5, "top_p": 0.85, "top_k": 30, "repeat_penalty": 1.1, "num_predict": 8192}}, "performance": {"development": {"num_ctx": 32000, "num_batch": 256, "num_gpu": 0}, "production": {"num_ctx": 128000, "num_batch": 1024, "num_gpu": 1, "keep_alive": "30m"}}}