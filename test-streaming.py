#!/usr/bin/env python3
"""
Test script for streaming functionality
Run with: python test-streaming.py "What is a knowledge graph?"
"""

import sys
import asyncio
from graphiti_streaming_search import print_streaming_demo

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python test-streaming.py 'Your question here'")
        print("Example: python test-streaming.py 'What is a knowledge graph?'")
        sys.exit(1)
    
    question = sys.argv[1]
    print_streaming_demo(question)
