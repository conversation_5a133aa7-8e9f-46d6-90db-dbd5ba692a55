#!/usr/bin/env python3
"""
Graphiti‑Powered Hybrid Knowledge‑Graph Search + DeepSeek Answer Generation
==========================================================================

* Replaces the custom Neo4j‑query / RRF / MMR code with Graphiti Core calls
* Keeps OpenAI embeddings (handled internally by Graphiti)
* Uses DeepSeek‑32B (via Ollama) only for final answer generation
* Compatible with FastAPI **or** CLI – see __main__ for CLI usage

Key Graphiti components leveraged
---------------------------------
- **Graphiti.search()** — one‑line hybrid retrieval (vector + BM25) with RRF
- **search_config_recipes.COMBINED_HYBRID_SEARCH_MMR** — optional if you want MMR instead of RRF
- **EntityEdge** / **SearchResults** Pydantic models for strong typing & JSON serialization

Environment variables expected
------------------------------
```
OPENAI_API_KEY      # for query embeddings (Graphiti internal)
NEO4J_URI           # bolt://localhost:7687 (default)
NEO4J_USER          # neo4j (default)
NEO4J_PASSWORD      # <your‑pw>
NEO4J_DATABASE      # neo4j (default)
OLLAMA_URL          # http://localhost:11434 (default)
OLLAMA_MODEL        # deepseek-r1:32b (default)
```
"""

from __future__ import annotations

import argparse
import asyncio
import json
import os
import sys
import textwrap
import time
from datetime import datetime, timezone
from typing import Dict, List, Tuple, Any

import requests
from graphiti_core import Graphiti

# ---------------------------------------------------------------------------
# Timing utility for Python
# ---------------------------------------------------------------------------

class PythonTimingTracker:
    """Python timing tracker to match the Node.js timing system"""

    def __init__(self, operation_id=None):
        self.operation_id = operation_id or f"py_{int(time.time() * 1000)}_{os.getpid()}"
        self.start_time = time.time()
        self.stages = {}
        self.metadata = {}
        self.neo4j_queries = []

    def start_stage(self, stage_name, metadata=None):
        timestamp = time.time()
        self.stages[stage_name] = {
            'start_time': timestamp,
            'metadata': metadata or {},
            'status': 'running'
        }
        return timestamp

    def end_stage(self, stage_name, result=None):
        timestamp = time.time()
        if stage_name not in self.stages:
            return None

        stage = self.stages[stage_name]
        duration = timestamp - stage['start_time']
        stage.update({
            'end_time': timestamp,
            'duration': duration,
            'duration_ms': duration * 1000,
            'result': result or {},
            'status': 'completed'
        })
        return duration

    def fail_stage(self, stage_name, error):
        timestamp = time.time()
        if stage_name in self.stages:
            stage = self.stages[stage_name]
            duration = timestamp - stage['start_time']
            stage.update({
                'end_time': timestamp,
                'duration': duration,
                'error': str(error),
                'status': 'failed'
            })

    def add_metadata(self, metadata):
        self.metadata.update(metadata)

    def track_neo4j_query(self, query_type, duration_ms, record_count=0, error=None):
        """Track a Neo4j query execution"""
        self.neo4j_queries.append({
            'query_type': query_type,
            'duration_ms': duration_ms,
            'record_count': record_count,
            'error': error,
            'timestamp': time.time()
        })

    def get_summary(self):
        total_duration = time.time() - self.start_time
        stages_summary = []

        for name, stage in self.stages.items():
            stages_summary.append({
                'name': name,
                'duration': stage.get('duration'),
                'duration_ms': stage.get('duration_ms'),
                'status': stage.get('status'),
                'percentage': (stage.get('duration', 0) / total_duration * 100) if total_duration > 0 else 0
            })

        # Calculate Neo4j query statistics
        neo4j_stats = {
            'total_queries': len(self.neo4j_queries),
            'total_duration_ms': sum(q['duration_ms'] for q in self.neo4j_queries),
            'average_duration_ms': 0,
            'slowest_query_ms': 0,
            'fastest_query_ms': 0,
            'failed_queries': len([q for q in self.neo4j_queries if q['error']]),
            'queries': self.neo4j_queries
        }

        if self.neo4j_queries:
            durations = [q['duration_ms'] for q in self.neo4j_queries if not q['error']]
            if durations:
                neo4j_stats['average_duration_ms'] = sum(durations) / len(durations)
                neo4j_stats['slowest_query_ms'] = max(durations)
                neo4j_stats['fastest_query_ms'] = min(durations)

        return {
            'operation_id': self.operation_id,
            'total_duration': total_duration,
            'total_duration_ms': total_duration * 1000,
            'stages': stages_summary,
            'neo4j_queries': neo4j_stats,
            'metadata': self.metadata,
            'completed_at': datetime.now(timezone.utc).isoformat()
        }
# Removed COMBINED_HYBRID_SEARCH_MMR import since we're using custom entity-only search

# ---------------------------------------------------------------------------
# DeepSeek via Ollama helper (sync HTTP → can be called from async code via run_in_executor)
# ---------------------------------------------------------------------------

def call_ollama(prompt: str, model: str = "deepseek-r1:8b", url: str = "http://localhost:11434/api/generate") -> str:  # noqa: E501
    """Send prompt to Ollama and return response text (non‑streaming)."""
    # Fix URL if it has /v1 suffix (Ollama doesn't use /v1)
    if url.endswith("/v1"):
        url = url[:-3] + "/api/generate"
    elif not url.endswith("/api/generate"):
        # If URL doesn't end with /api/generate, add it
        url = url.rstrip("/") + "/api/generate"

    payload = {
        "model": model,
        "prompt": prompt,
        "stream": False,
        "options": {"temperature": 0.3, "top_p": 0.9, "top_k": 40},
    }
    try:
        resp = requests.post(url, json=payload, timeout=180)
        resp.raise_for_status()
        data = resp.json()
        return data.get("response", "")
    except Exception as exc:  # pylint: disable=broad-except
        return f"❌ Ollama error: {exc}"


def call_ollama_streaming(prompt: str, model: str = "deepseek-r1:8b", url: str = "http://localhost:11434/api/generate", callback=None):
    """Send prompt to Ollama and stream response text in real-time."""
    # Fix URL if it has /v1 suffix (Ollama doesn't use /v1)
    if url.endswith("/v1"):
        url = url[:-3] + "/api/generate"
    elif not url.endswith("/api/generate"):
        # If URL doesn't end with /api/generate, add it
        url = url.rstrip("/") + "/api/generate"

    payload = {
        "model": model,
        "prompt": prompt,
        "stream": True,
        "options": {"temperature": 0.3, "top_p": 0.9, "top_k": 40},
    }

    try:
        resp = requests.post(url, json=payload, timeout=180, stream=True)
        resp.raise_for_status()

        full_response = ""
        for line in resp.iter_lines():
            if line:
                try:
                    chunk = json.loads(line.decode('utf-8'))
                    if 'response' in chunk:
                        token = chunk['response']
                        full_response += token

                        # Call callback with the new token if provided
                        if callback:
                            callback(token, chunk.get('done', False))

                        # If done, break
                        if chunk.get('done', False):
                            break

                except json.JSONDecodeError:
                    continue

        return full_response

    except Exception as exc:  # pylint: disable=broad-except
        error_msg = f"❌ Ollama error: {exc}"
        if callback:
            callback(error_msg, True)
        return error_msg

# ---------------------------------------------------------------------------
# Graphiti helpers
# ---------------------------------------------------------------------------

def build_graphiti_instance() -> Graphiti:
    """Initialise Graphiti (lazy embedder uses OPENAI_API_KEY)."""
    return Graphiti(
        uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        user=os.getenv("NEO4J_USER", "neo4j"),
        password=os.getenv("NEO4J_PASSWORD", "password"),
    )

async def fetch_context_entity_only(graphiti: Graphiti, query: str, edge_count: int = 4, node_count: int = 2, timer: PythonTimingTracker = None) -> Tuple[str, List[str]]:
    """Custom search function that only searches Entity nodes to avoid old GraphRAG nodes."""

    context_lines: List[str] = []
    citations: List[str] = []
    idx = 1

    if timer:
        timer.start_stage('neo4j_entity_search')

    # Use direct Neo4j queries to search only Entity nodes and their relationships
    driver = graphiti.driver

    # Split query into individual terms for better matching
    query_terms = query.lower().split()

    async with driver.session() as session:
        # Build flexible search conditions for Entity nodes
        entity_conditions = []
        edge_conditions = []

        for term in query_terms:
            # Escape single quotes in search terms for Cypher
            escaped_term = term.replace("'", "\\'")
            # Case-insensitive search in entity names and summaries
            entity_conditions.append(f"toLower(e.name) CONTAINS '{escaped_term}' OR toLower(e.summary) CONTAINS '{escaped_term}'")
            # Case-insensitive search in relationship facts
            edge_conditions.append(f"toLower(r.fact) CONTAINS '{escaped_term}'")

        entity_where_clause = " OR ".join(entity_conditions) if entity_conditions else "true"
        edge_where_clause = " OR ".join(edge_conditions) if edge_conditions else "true"

        # Search for relevant Entity nodes based on name/summary similarity
        entity_query = f"""
        MATCH (e:Entity)
        WHERE {entity_where_clause}
        RETURN e.name as name, e.summary as summary, e.uuid as uuid
        ORDER BY
            CASE WHEN toLower(e.name) CONTAINS '{query.lower()}' THEN 1 ELSE 2 END,
            size(e.name)
        LIMIT $node_limit
        """

        # Time the entity query
        entity_start_time = time.time()
        entity_result = await session.run(entity_query, {"node_limit": node_count})
        entity_records = await entity_result.data()
        entity_duration = (time.time() - entity_start_time) * 1000

        if timer:
            timer.track_neo4j_query('entity_search', entity_duration, len(entity_records))

        # Search for relevant edges connected to Entity nodes
        edge_query = f"""
        MATCH (source:Entity)-[r:RELATES_TO]->(target:Entity)
        WHERE {edge_where_clause}
        RETURN r.fact as fact, r.uuid as uuid
        ORDER BY
            CASE WHEN toLower(r.fact) CONTAINS '{query.lower()}' THEN 1 ELSE 2 END,
            size(r.fact)
        LIMIT $edge_limit
        """

        try:
            # Get edges first with timing
            edge_start_time = time.time()
            edge_result = await session.run(edge_query, {"edge_limit": edge_count})
            edge_records = await edge_result.data()
            edge_duration = (time.time() - edge_start_time) * 1000

            if timer:
                timer.track_neo4j_query('edge_search', edge_duration, len(edge_records))

            for record in edge_records:
                context_lines.append(f"[{idx}] {record['fact']}")
                citations.append(f"[{idx}]")
                idx += 1

            # Process entity records (already fetched above)
            for record in entity_records:
                if record['summary']:
                    context_lines.append(f"[{idx}] {record['summary']}")
                    citations.append(f"[{idx}]")
                    idx += 1

        except Exception as e:
            print(f"Entity-only search failed: {e}")
            if timer:
                timer.track_neo4j_query('search_error', 0, 0, str(e))
            # Return empty results if search fails
            pass

    if timer:
        timer.end_stage('neo4j_entity_search', {
            'context_lines': len(context_lines),
            'citations': len(citations)
        })

    context_md = "\n".join(context_lines) if context_lines else ""
    return context_md, citations


async def fetch_context(graphiti: Graphiti, query: str, edge_count: int = 4, node_count: int = 2, timer: PythonTimingTracker = None) -> Tuple[str, List[str]]:
    """Run Graphiti hybrid search and return (context_md, citations) with mixed edges and nodes.

    Uses entity-only search to avoid conflicts with old GraphRAG nodes.
    """

    # Use the custom entity-only search instead of the full Graphiti search
    return await fetch_context_entity_only(graphiti, query, edge_count, node_count, timer)



# ---------------------------------------------------------------------------
# Prompt template for DeepSeek
# ---------------------------------------------------------------------------

PROMPT_TEMPLATE = textwrap.dedent(
    """
    You are a FOREX domain knowledge expert answering questions using *only* the supplied context.

    === CONTEXT ===
    {context}
    === QUESTION ===
    {question}

    Instructions:
    1. Begin with a concise answer, then elaborate.
    2. Cite facts using the bracket numbers provided in CONTEXT (e.g. [1]).
    3. Format output in clean Markdown with headings, lists, and **bold** keywords.
    4. If context is insufficient, state so clearly.
    5. Finish with a "### 💡 Related" section suggesting 2 follow‑up questions.
    """
)

# ---------------------------------------------------------------------------
# Main entry point
# ---------------------------------------------------------------------------

async def run(query: str) -> None:  # noqa: D401
    """Execute end‑to‑end search + answer pipeline and print Markdown."""
    print(f"🔍 Searching for: {query}")
    g = build_graphiti_instance()
    try:
        print(" Fetching context from knowledge graph...")
        context_md, _cites = await fetch_context(g, query)
        if not context_md:
            print("❌ No relevant context found in the graph.")
            return

        print(f"✅ Found context ({len(context_md)} characters)")
        print(f"📝 Context preview: {context_md[:200]}...")

        prompt = PROMPT_TEMPLATE.format(context=context_md, question=query)

        print("🤖 Calling DeepSeek via Ollama...")
        # Call DeepSeek in a thread‑pool to avoid blocking event loop
        loop = asyncio.get_running_loop()
        answer_md = await loop.run_in_executor(
            None,
            call_ollama,
            prompt,
            os.getenv("OLLAMA_MODEL", "deepseek-r1:8b"),
            os.getenv("OLLAMA_URL", "http://localhost:11434/api/generate"),
        )

        timestamp = datetime.now(timezone.utc).isoformat()
        if answer_md.startswith("❌ Ollama error:"):
            print(f"\n---\n⏱️ {timestamp}")
            print("🔍 **Raw Context Found (Ollama unavailable):**\n")
            print(context_md)
            print("\n💡 **Note:** Install and start Ollama with DeepSeek model for AI-generated answers.")
        else:
            print(f"\n---\n⏱️ {timestamp}\n{answer_md}\n")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await g.close()

# ---------------------------------------------------------------------------
# GUI-compatible function (drop-in replacement for real_llm_kg_script.py)
# ---------------------------------------------------------------------------

def get_real_kg_data(question: str, uri: str, user: str, password: str, database: str) -> Dict[str, Any]:
    """
    GUI-compatible function that matches the signature of real_llm_kg_script.py
    Returns structured data instead of printing to console.
    """
    # Temporarily override environment variables for this call
    original_env = {}
    env_vars = {
        "NEO4J_URI": uri,
        "NEO4J_USER": user,
        "NEO4J_PASSWORD": password,
        "NEO4J_DATABASE": database
    }

    # Set environment variables
    for key, value in env_vars.items():
        original_env[key] = os.getenv(key)
        os.environ[key] = value

    try:
        # Run the async search and get results
        result = asyncio.run(_get_kg_data_async(question))
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "context": "",
            "answer": "",
            "citations": []
        }
    finally:
        # Restore original environment variables
        for key, original_value in original_env.items():
            if original_value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = original_value

async def _get_kg_data_async(question: str, enable_timing: bool = False) -> Dict[str, Any]:
    """Internal async function that does the actual work with optional timing"""
    # Initialize timing if requested
    timer = None
    if enable_timing:
        operation_id = os.getenv('OPERATION_ID', f"py_{int(time.time() * 1000)}")
        timer = PythonTimingTracker(operation_id)
        timer.add_metadata({
            'question_length': len(question),
            'model': os.getenv("OLLAMA_MODEL", "deepseek-r1:8b"),
            'ollama_url': os.getenv("OLLAMA_URL", "http://localhost:11434/api/generate")
        })
        timer.start_stage('graphiti_initialization')

    g = build_graphiti_instance()

    if timer:
        timer.end_stage('graphiti_initialization')
        timer.start_stage('knowledge_graph_search')

    try:
        context_md, citations = await fetch_context(g, question, 4, 2, timer)

        if timer:
            timer.end_stage('knowledge_graph_search', {
                'context_length': len(context_md) if context_md else 0,
                'citations_count': len(citations) if citations else 0
            })

        if not context_md:
            result = {
                "success": False,
                "error": "No relevant context found in the graph",
                "context": "",
                "answer": "",
                "citations": []
            }
            if timer:
                timer.fail_stage('knowledge_graph_search', 'No relevant context found')
                result['timing'] = timer.get_summary()
            return result

        if timer:
            timer.start_stage('prompt_preparation')

        prompt = PROMPT_TEMPLATE.format(context=context_md, question=question)

        if timer:
            timer.end_stage('prompt_preparation', {
                'prompt_length': len(prompt)
            })
            timer.start_stage('llm_generation')

        # Call DeepSeek
        loop = asyncio.get_running_loop()
        answer_md = await loop.run_in_executor(
            None,
            call_ollama,
            prompt,
            os.getenv("OLLAMA_MODEL", "deepseek-r1:8b"),
            os.getenv("OLLAMA_URL", "http://localhost:11434/api/generate"),
        )

        if timer:
            timer.end_stage('llm_generation', {
                'answer_length': len(answer_md),
                'ollama_error': answer_md.startswith("❌ Ollama error:")
            })

        # Handle Ollama errors
        if answer_md.startswith("❌ Ollama error:"):
            result = {
                "success": True,
                "context": context_md,
                "answer": f"**Context Found (AI unavailable):**\n\n{context_md}\n\n**Note:** Ollama/DeepSeek not available for answer generation.",
                "citations": citations,
                "ollama_error": True
            }
            if timer:
                result['timing'] = timer.get_summary()
            return result

        result = {
            "success": True,
            "context": context_md,
            "answer": answer_md,
            "citations": citations,
            "ollama_error": False
        }

        if timer:
            result['timing'] = timer.get_summary()

        return result

    finally:
        await g.close()

# ---------------------------------------------------------------------------
# CLI wrapper
# ---------------------------------------------------------------------------

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Graphiti search + DeepSeek answer generator")
    parser.add_argument("question", type=str, help="Question to ask the knowledge graph")
    parser.add_argument("--uri", type=str, default=os.getenv("NEO4J_URI"), help="Neo4j URI")
    parser.add_argument("--user", type=str, default=os.getenv("NEO4J_USER"), help="Neo4j Username")
    parser.add_argument("--password", type=str, default=os.getenv("NEO4J_PASSWORD"), help="Neo4j Password")
    parser.add_argument("--database", type=str, default=os.getenv("NEO4J_DATABASE", "neo4j"), help="Neo4j Database")
    parser.add_argument("--timing", type=str, default="false", help="Enable detailed timing (true/false)")

    args = parser.parse_args()

    # Check if timing is enabled
    enable_timing = args.timing.lower() == "true"

    # If CLI arguments are provided, use them instead of environment variables
    if args.uri or args.user or args.password or args.database:
        # Use the enhanced async function with timing support
        async def run_with_timing():
            return await _get_kg_data_async(args.question, enable_timing)

        result = asyncio.run(run_with_timing())
        # Output JSON for compatibility with Node.js backend
        print(json.dumps(result))
    else:
        # Use the original run function for interactive CLI usage
        try:
            asyncio.run(run(args.question))
        except KeyboardInterrupt:
            sys.exit(130)
