#!/usr/bin/env python3
"""
Streaming version of Graphiti Knowledge Graph Search
====================================================

This script provides real-time streaming of AI responses from Ollama,
allowing users to see the thinking process as it happens.

Key features:
- Real-time token streaming from Ollama
- Server-Sent Events (SSE) support for web clients
- Comprehensive timing with streaming metrics
- Compatible with existing knowledge graph search
"""

import argparse
import asyncio
import json
import os
import sys
import time
from datetime import datetime, timezone
from typing import Dict, List, Tuple, Any, Callable, Optional

import requests
from graphiti_core import Graphiti

# Import timing and context functions from the main script
from graphiti_hybrid_search import (
    PythonTimingTracker,
    build_graphiti_instance,
    PROMPT_TEMPLATE
)


def load_search_configuration():
    """Load search configuration from config file"""
    import json
    import os

    config_path = os.path.join(os.path.dirname(__file__), 'packages', 'graph-api', 'config', 'kg-config.json')

    # Default configuration
    config = {
        'searchRecipe': 'COMBINED_HYBRID_SEARCH_RRF',
        'llmProvider': 'ollama',
        'llmModel': 'gemma3:12b',
        'resultsLimit': 10,
        'customPrompt': ''
    }

    # Try to load from config file
    try:
        with open(config_path, 'r') as f:
            file_config = json.load(f)
            config.update(file_config)
    except FileNotFoundError:
        pass  # Use defaults

    return config


async def get_matching_group_ids(graphiti_client, filter_substring: str) -> list[str]:
    """
    Query the database to find all group_ids that contain the filter substring
    """
    try:
        # Use the Neo4j driver to query for group_ids containing the substring
        driver = graphiti_client.driver

        query = """
        MATCH (n)
        WHERE n.group_id IS NOT NULL AND n.group_id CONTAINS $filter_substring
        RETURN DISTINCT n.group_id as group_id
        ORDER BY n.group_id
        """

        records, _, _ = await driver.execute_query(
            query,
            filter_substring=filter_substring,
            database_="neo4j",
            routing_="r"
        )

        group_ids = [record["group_id"] for record in records]
        print(f"Found {len(group_ids)} group_ids containing '{filter_substring}': {group_ids}")
        return group_ids

    except Exception as e:
        print(f"Error querying for matching group_ids: {e}")
        return []


async def fetch_context_with_graphiti_search(graphiti_client, query: str, edge_count: int = 4, node_count: int = 2, group_id_filter: str = None, timer=None) -> tuple[str, list[str]]:
    """
    Use Graphiti's proper search methods instead of primitive text matching
    """
    print(f"\n=== GRAPHITI SEARCH DEBUG ===")
    print(f"Query: {query}")

    # Load search configuration
    config = load_search_configuration()
    search_recipe = config.get('searchRecipe', 'COMBINED_HYBRID_SEARCH_RRF')
    results_limit = config.get('resultsLimit', 10)

    print(f"Search Recipe: {search_recipe}")
    print(f"Results Limit: {results_limit}")

    context_lines = []
    citations = []
    detailed_results = []  # Store detailed result information
    idx = 1

    try:
        # Use Graphiti's search method
        print(f"Calling graphiti_client.search() with query: '{query}'")

        # Prepare group_ids for filtering if group_id_filter is provided
        group_ids = None
        if group_id_filter:
            # Use substring matching to find all group_ids containing the filter
            group_ids = await get_matching_group_ids(graphiti_client, group_id_filter)
            if group_ids:
                print(f"Filtering by {len(group_ids)} group_ids containing '{group_id_filter}': {group_ids}")
            else:
                print(f"No group_ids found containing '{group_id_filter}' - searching across all documents")
                group_ids = None
        else:
            print("No group_id filtering applied - searching across all documents")

        # Use the client's search method with optional group_ids filtering
        search_results = await graphiti_client.search(
            query=query,
            num_results=results_limit,
            group_ids=group_ids
        )

        print(f"Search results type: {type(search_results)}")
        print(f"Search results: {search_results}")

        # Process the results
        if hasattr(search_results, '__iter__'):
            for result in search_results:
                print(f"Processing result {idx}: {result}")

                # Create detailed result information
                result_detail = {
                    'index': idx,
                    'type': 'unknown',
                    'uuid': getattr(result, 'uuid', 'N/A'),
                    'group_id': getattr(result, 'group_id', 'N/A'),
                    'name': getattr(result, 'name', 'N/A'),
                    'fact': getattr(result, 'fact', None),
                    'summary': getattr(result, 'summary', None),
                    'source_node_uuid': getattr(result, 'source_node_uuid', None),
                    'target_node_uuid': getattr(result, 'target_node_uuid', None),
                    'created_at': str(getattr(result, 'created_at', 'N/A')),
                    'valid_at': str(getattr(result, 'valid_at', 'N/A')),
                    'invalid_at': str(getattr(result, 'invalid_at', 'N/A')),
                    'episodes': getattr(result, 'episodes', [])
                }

                # Handle different result types
                if hasattr(result, 'fact'):
                    # This is an edge/relationship (EntityEdge)
                    result_detail['type'] = 'Edge'
                    context_lines.append(f"[{idx}] {result.fact}")
                    citations.append(f"[{idx}]")
                elif hasattr(result, 'summary'):
                    # This is a node/entity (EntityNode)
                    result_detail['type'] = 'Node'
                    context_lines.append(f"[{idx}] {result.name}: {result.summary}")
                    citations.append(f"[{idx}]")
                else:
                    # Fallback - convert to string
                    result_detail['type'] = 'Unknown'
                    context_lines.append(f"[{idx}] {str(result)}")
                    citations.append(f"[{idx}]")

                detailed_results.append(result_detail)
                idx += 1

                # Limit results
                if idx > results_limit:
                    break
        else:
            print(f"Search results is not iterable: {search_results}")

    except Exception as e:
        print(f"Error in Graphiti search: {str(e)}")
        print(f"Exception type: {type(e)}")
        import traceback
        traceback.print_exc()

        # Fallback to empty results
        context_lines = [f"[1] Error in search: {str(e)}"]
        citations = ["[1]"]
        detailed_results = [{
            'index': 1,
            'type': 'Error',
            'uuid': 'N/A',
            'group_id': 'N/A',
            'name': 'Search Error',
            'fact': f"Error in search: {str(e)}",
            'summary': None,
            'source_node_uuid': None,
            'target_node_uuid': None,
            'created_at': 'N/A',
            'valid_at': 'N/A',
            'invalid_at': 'N/A',
            'episodes': []
        }]

    context_md = "\n".join(context_lines) if context_lines else ""

    print(f"Final context ({len(context_lines)} lines):")
    print(context_md)
    print(f"Citations: {citations}")

    # Output detailed search results for enhanced thinking process display
    print(f"Search Results Details: {json.dumps(detailed_results)}")
    print(f"Total Results Found: {len(detailed_results)}")

    print(f"=== END GRAPHITI SEARCH DEBUG ===\n")

    return context_md, citations


class StreamingResponse:
    """Handles streaming response with timing and metadata"""
    
    def __init__(self, operation_id: str = None):
        self.operation_id = operation_id or f"stream_{int(time.time() * 1000)}"
        self.start_time = time.time()
        self.tokens = []
        self.metadata = {}
        self.is_complete = False
        self.error = None
    
    def add_token(self, token: str, is_done: bool = False):
        """Add a token to the response"""
        self.tokens.append({
            'token': token,
            'timestamp': time.time(),
            'elapsed': (time.time() - self.start_time) * 1000
        })
        
        if is_done:
            self.is_complete = True
    
    def get_full_text(self) -> str:
        """Get the complete response text"""
        return ''.join(token['token'] for token in self.tokens)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get streaming summary with timing"""
        total_duration = time.time() - self.start_time
        
        return {
            'operation_id': self.operation_id,
            'total_duration_ms': total_duration * 1000,
            'token_count': len(self.tokens),
            'tokens_per_second': len(self.tokens) / total_duration if total_duration > 0 else 0,
            'first_token_latency': self.tokens[0]['elapsed'] if self.tokens else 0,
            'is_complete': self.is_complete,
            'error': self.error,
            'metadata': self.metadata
        }


def call_ollama_streaming_with_callback(
    prompt: str, 
    model: str = "gemma3:12b", 
    url: str = "http://localhost:11434/api/generate",
    token_callback: Optional[Callable[[str, bool], None]] = None
) -> str:
    """Stream response from Ollama with real-time token callback"""
    
    # Fix URL if it has /v1 suffix (Ollama doesn't use /v1)
    if url.endswith("/v1"):
        url = url[:-3] + "/api/generate"
    elif not url.endswith("/api/generate"):
        url = url.rstrip("/") + "/api/generate"

    payload = {
        "model": model,
        "prompt": prompt,
        "stream": True,
        "options": {"temperature": 0.3, "top_p": 0.9, "top_k": 40},
    }

    try:
        resp = requests.post(url, json=payload, timeout=180, stream=True)
        resp.raise_for_status()
        
        full_response = ""
        for line in resp.iter_lines():
            if line:
                try:
                    chunk = json.loads(line.decode('utf-8'))
                    if 'response' in chunk:
                        token = chunk['response']
                        full_response += token
                        
                        # Call the token callback if provided
                        if token_callback:
                            token_callback(token, chunk.get('done', False))
                        
                        # If done, break
                        if chunk.get('done', False):
                            break
                            
                except json.JSONDecodeError:
                    continue
                    
        return full_response
        
    except Exception as exc:
        error_msg = f"❌ Ollama error: {exc}"
        if token_callback:
            token_callback(error_msg, True)
        return error_msg


async def streaming_kg_search(
    question: str,
    enable_timing: bool = True,
    token_callback: Optional[Callable[[str, bool], None]] = None
) -> Dict[str, Any]:
    """
    Perform knowledge graph search with streaming LLM response
    
    Args:
        question: The question to search for
        enable_timing: Whether to enable detailed timing
        token_callback: Function to call for each token (token, is_done)
    
    Returns:
        Dictionary with answer, context, citations, and timing
    """
    
    # Initialize timing
    timer = None
    streaming_response = StreamingResponse()
    
    if enable_timing:
        operation_id = os.getenv('OPERATION_ID', streaming_response.operation_id)
        timer = PythonTimingTracker(operation_id)
        timer.add_metadata({
            'question_length': len(question),
            'model': os.getenv("OLLAMA_MODEL", "gemma3:12b"),
            'ollama_url': os.getenv("OLLAMA_URL", "http://localhost:11434"),
            'streaming': True
        })
        timer.start_stage('graphiti_initialization')
    
    g = build_graphiti_instance()

    if timer:
        timer.end_stage('graphiti_initialization')
        timer.start_stage('knowledge_graph_search')
    
    try:
        # Get context from knowledge graph using proper Graphiti search
        # Default to filtering for user_guides group_ids
        group_id_filter = "user_guides"  # This will be made more flexible later
        context_md, citations = await fetch_context_with_graphiti_search(g, question, 4, 2, group_id_filter, timer)

        if timer:
            timer.end_stage('knowledge_graph_search', {
                'context_length': len(context_md) if context_md else 0,
                'citations_count': len(citations) if citations else 0
            })

        if not context_md:
            result = {
                "success": False,
                "error": "No relevant context found in the graph",
                "context": "",
                "answer": "",
                "citations": [],
                "streaming": streaming_response.get_summary()
            }
            if timer:
                result['timing'] = timer.get_summary()
            return result

        if timer:
            timer.start_stage('prompt_preparation')

        # Load custom prompt from configuration
        config = load_search_configuration()
        custom_prompt = config.get('customPrompt', '')

        if custom_prompt.strip():
            # Use custom prompt if available
            prompt = custom_prompt.format(context=context_md, question=question)
        else:
            # Use default prompt template
            prompt = PROMPT_TEMPLATE.format(context=context_md, question=question)

        # Log the context and prompt for debugging
        print(f"\n=== CONTEXT RECEIVED ===")
        print(f"Context length: {len(context_md)}")
        print(f"Context content:\n{context_md}")
        print(f"\n=== CITATIONS ===")
        print(f"Citations: {citations}")
        print(f"\n=== FULL PROMPT SENT TO LLM ===")
        print(f"Prompt length: {len(prompt)}")
        print(f"Prompt content:\n{prompt}")
        print(f"=== END PROMPT ===\n")

        # Add LLM provider information
        llm_model = os.getenv("OLLAMA_MODEL", "gemma3:12b")
        llm_url = os.getenv("OLLAMA_URL", "http://localhost:11434")
        print(f"\n=== LLM PROVIDER INFO ===")
        print(f"Provider: Ollama")
        print(f"Model: {llm_model}")
        print(f"URL: {llm_url}")
        print(f"=== END LLM PROVIDER INFO ===\n")

        if timer:
            timer.end_stage('prompt_preparation', {
                'prompt_length': len(prompt)
            })
            timer.start_stage('llm_streaming')

        # Create a callback that updates our streaming response
        def combined_callback(token: str, is_done: bool):
            streaming_response.add_token(token, is_done)
            
            # Call the external callback if provided
            if token_callback:
                token_callback(token, is_done)

        # Stream the response from Ollama
        answer_md = call_ollama_streaming_with_callback(
            prompt,
            os.getenv("OLLAMA_MODEL", "gemma3:12b"),
            os.getenv("OLLAMA_URL", "http://localhost:11434/api/generate"),
            combined_callback
        )

        if timer:
            timer.end_stage('llm_streaming', {
                'answer_length': len(answer_md),
                'token_count': len(streaming_response.tokens),
                'tokens_per_second': streaming_response.get_summary()['tokens_per_second']
            })

        # Handle Ollama errors
        if answer_md.startswith("❌ Ollama error:"):
            result = {
                "success": True,
                "context": context_md,
                "answer": f"**Context Found (AI unavailable):**\n\n{context_md}\n\n**Note:** Ollama not available for answer generation.",
                "citations": citations,
                "ollama_error": True,
                "streaming": streaming_response.get_summary()
            }
            if timer:
                result['timing'] = timer.get_summary()
            return result

        result = {
            "success": True,
            "context": context_md,
            "answer": answer_md,
            "citations": citations,
            "ollama_error": False,
            "streaming": streaming_response.get_summary()
        }
        
        if timer:
            result['timing'] = timer.get_summary()
        
        return result

    finally:
        await g.close()


def print_streaming_demo(question: str):
    """Demo function that shows streaming in the terminal"""
    print(f"🔍 Streaming search for: {question}\n")
    print("📡 Streaming response:")
    print("-" * 50)
    
    def token_callback(token: str, is_done: bool):
        print(token, end='', flush=True)
        if is_done:
            print("\n" + "-" * 50)
            print("✅ Streaming complete!")
    
    # Run the streaming search
    result = asyncio.run(streaming_kg_search(question, True, token_callback))
    
    print(f"\n📊 Streaming Statistics:")
    streaming_stats = result.get('streaming', {})
    print(f"   • Total duration: {streaming_stats.get('total_duration_ms', 0):.1f}ms")
    print(f"   • Token count: {streaming_stats.get('token_count', 0)}")
    print(f"   • Tokens/second: {streaming_stats.get('tokens_per_second', 0):.1f}")
    print(f"   • First token latency: {streaming_stats.get('first_token_latency', 0):.1f}ms")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Streaming Graphiti search + Ollama answer generator")
    parser.add_argument("question", type=str, help="Question to ask the knowledge graph")
    parser.add_argument("--uri", type=str, default=os.getenv("NEO4J_URI"), help="Neo4j URI")
    parser.add_argument("--user", type=str, default=os.getenv("NEO4J_USER"), help="Neo4j Username")
    parser.add_argument("--password", type=str, default=os.getenv("NEO4J_PASSWORD"), help="Neo4j Password")
    parser.add_argument("--database", type=str, default=os.getenv("NEO4J_DATABASE", "neo4j"), help="Neo4j Database")
    parser.add_argument("--timing", type=str, default="true", help="Enable detailed timing (true/false)")
    parser.add_argument("--demo", action="store_true", help="Run terminal streaming demo")

    args = parser.parse_args()

    # Set environment variables if provided
    if args.uri:
        os.environ["NEO4J_URI"] = args.uri
    if args.user:
        os.environ["NEO4J_USER"] = args.user
    if args.password:
        os.environ["NEO4J_PASSWORD"] = args.password
    if args.database:
        os.environ["NEO4J_DATABASE"] = args.database

    enable_timing = args.timing.lower() == "true"

    if args.demo:
        # Run terminal demo
        print_streaming_demo(args.question)
    else:
        # Run for API integration with streaming to stdout
        def api_token_callback(token: str, is_done: bool):
            # Stream tokens to stdout for Node.js to capture
            print(token, end='', flush=True)

        result = asyncio.run(streaming_kg_search(args.question, enable_timing, api_token_callback))

        # After streaming is complete, output a separator and the JSON result
        print("\n---JSON_RESULT---")
        print(json.dumps(result))
