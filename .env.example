# =======================================================
# LLM Abstraction Layer Environment Configuration
# =======================================================

# Primary LLM Provider Selection
# Options: ollama, azure_openai, google_genai
LLM_PRIMARY_PROVIDER=ollama

# Global LLM Settings
LLM_RETRY_ATTEMPTS=3
LLM_RETRY_DELAY=1.0
LLM_ENABLE_LOGGING=true

# =======================================================
# Ollama Configuration (Primary Provider)
# =======================================================
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_ENABLED=true

# =======================================================
# Azure OpenAI Configuration (Fallback Provider)
# =======================================================
# Azure OpenAI API credentials
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4o

# Example Azure OpenAI configuration:
# AZURE_OPENAI_API_KEY=1234567890abcdef1234567890abcdef
# AZURE_OPENAI_ENDPOINT=https://my-openai-resource.openai.azure.com/
# AZURE_OPENAI_API_VERSION=2024-02-15-preview
# AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4o

# =======================================================
# Google Generative AI Configuration (Fallback Provider)
# =======================================================
# Google Generative AI (Gemini) API key
GOOGLE_API_KEY=your_google_api_key_here

# Example Google API configuration:
# GOOGLE_API_KEY=AIzaSyAbCdEfGhIjKlMnOpQrStUvWxYz1234567

# =======================================================
# Knowledge Graph & Neo4j Configuration
# =======================================================
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your_neo4j_password_here

# =======================================================
# FastAPI Chat Service Configuration
# =======================================================
CHAT_API_HOST=0.0.0.0
CHAT_API_PORT=8000
CHAT_API_RELOAD=true

# =======================================================
# Node.js Proxy Server Configuration
# =======================================================
PROXY_PORT=3000
PROXY_HOST=0.0.0.0

# API Endpoints
GRAPH_API_URL=http://localhost:3001
CHAT_API_URL=http://localhost:8000

# Session Configuration
SESSION_SECRET=your_session_secret_here
SESSION_MAX_AGE=3600000

# =======================================================
# Development & Debugging
# =======================================================
NODE_ENV=development
DEBUG=true
LOG_LEVEL=info

# =======================================================
# Docker Configuration
# =======================================================
COMPOSE_PROJECT_NAME=knowledge-graph-visualizer
DOCKER_BUILDKIT=1

# =======================================================
# Instructions for Setup
# =======================================================
# 1. Copy this file to .env: cp .env.example .env
# 2. Update the values with your actual credentials
# 3. For Azure OpenAI:
#    - Create an Azure OpenAI resource in Azure Portal
#    - Deploy a model (e.g., gpt-4o)
#    - Copy the API key, endpoint, and deployment name
# 4. For Google Generative AI:
#    - Enable the Generative AI API in Google Cloud Console
#    - Create an API key
# 5. For Neo4j:
#    - Install Neo4j locally or use a cloud instance
#    - Update the connection details
# 6. For Ollama:
#    - Install Ollama: https://ollama.ai/
#    - Pull the model: ollama pull deepseek-r1:8b
